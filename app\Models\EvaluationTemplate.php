<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Model;
use App\User;
use Kyslik\ColumnSortable\Sortable;

class EvaluationTemplate extends Model
{
    use Sortable;
    const IS_DRAFT = 1;
    const IS_NOT_DRAFT = 0;
    protected $table = 'evaluation_templates';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->created_by = auth()->id();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }

    /**
     * Get user that create the template.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class,'created_by');
    }
}
