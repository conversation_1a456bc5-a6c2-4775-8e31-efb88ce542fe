<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class District
 * @property string $id
 * @property string $name
 * @property string $type
 * @property string $prefecture_id
 */
class Department extends Model
{
    use SoftDeletes;
    protected $table = 'departments';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [

    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [

    ];

    /**
     * Get positions for the department
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function positions()
    {
    	return $this->hasMany('App\Models\Position');
    }
}
