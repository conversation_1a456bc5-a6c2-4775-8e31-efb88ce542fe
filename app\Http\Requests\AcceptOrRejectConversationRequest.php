<?php

namespace App\Http\Requests;

use App\Logics\ConversationManager;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\JoinConversation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class AcceptOrRejectConversationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "conversation_id" => ['required', function ($attribute, $value, $fail) {
                if (!is_numeric($value)) return $fail(trans('validation.exists', ['attribute'=> 'Conversation id']));
                $userId = Auth::guard('api')->user()->id;
                $conversation = ConversationManager::checkPermissionConversation($value, $userId, true, Conversation::TYPE_MULTI_USER);
                if(empty($conversation)) return $fail(trans('validation.exists', ['attribute'=> 'Conversation id']));
            }],
            "user_id" => ['required', 'exists:users,id', function ($attribute, $value, $fail) {
                $checkHaveRequest = JoinConversation::where('user_id', $value)->where('conversation_id', $this->conversation_id)->first();
                if(empty($checkHaveRequest)) return $fail(trans('validation.exists', ['attribute'=> 'User id']));
            }],
            "type" => ['required', Rule::in([ JoinConversation::STATUS_ACCEPT, JoinConversation::STATUS_REJECT ])],
            "role" => ['required', Rule::in([ ConversationParticipant::IS_ADMIN, ConversationParticipant::IS_MEMBER ])]
        ];
    }

    public function messages(): array
    {
        return [
            'user_id.required' => trans('validation.required', ['attribute'=> 'User id']),
            'user_id.exists' => trans('validation.exists', ['attribute'=> 'User id']),
        ];
    }
}
