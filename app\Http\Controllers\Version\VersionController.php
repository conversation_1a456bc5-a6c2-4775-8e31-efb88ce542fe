<?php

namespace App\Http\Controllers\Version;

use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\VersionRequest;
use App\Models\Tenant\AppVersion;
use App\Services\AppVersionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VersionController extends Controller
{
    const PER_PAGE = 15;
    private $versionService;

    /**
     * Constructor
     * 
     * @param AppVersionService $versionService
     */
    public function __construct(AppVersionService $versionService)
    {
        $this->versionService = $versionService;
    }

    /**
     * Display a listing of the resource.
     *
     * 
     */
    public function index(Request $request)
    {
        // Get versions with filters
        $query = $this->versionService->getAllVersions($request);
        $latestVersions = $this->versionService->getLatestVersions();

        // Pagination and sorting
        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $versions = $query->paginate($perPage)->withQueryString();

        // Handle invalid page
        if ($versions->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $versions->lastPage()]));
        }
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        // Filter view
        $isFilter = "";
        $fields = ['search','platform','status'];
        foreach ($fields as $field) {
            $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
            $tagSpanClose = '</span>';
            $value = '';    
            if ($request->has($field) && $request->$field !== null) {
                switch ($field) {
                    case 'platform':
                        $platform = $request->platform;
                        $platformName = $platform == AppVersion::IOS ? __('language.ios') : __('language.android');
                        $value = $tagSpanOpen.StringHelper::escapeHtml($platformName) . $tagSpanClose;
                        break;
                    case 'status':
                        $status = $request->status;
                        $statusName = $status == AppVersion::PUBLISHED ? __('language.published') : __('language.draft');
                        $value = $tagSpanOpen.StringHelper::escapeHtml($statusName) . $tagSpanClose;
                        break;
                    default:
                        $value = $tagSpanOpen.StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $isFilter .= $value;
            }
        }

        return view('versions.index', [
            'versions' => $versions,
            'isFilter' => $isFilter,
            'latestVersions' => $latestVersions
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $formData = $this->prepareFormData();
        return view('versions.form', $formData);
    }
    public function edit($id)
    {
        try {
            $version = AppVersion::findOrFail($id);
            if ($version == null) {
                return redirect()
                    ->route('versions.index')
                    ->with('status_failed', __('message.version_not_found'));
            }
            $formData = $this->prepareFormData($version);
            return view('versions.form', $formData);
        } catch (\Exception $e) {
            return redirect()
                ->route('versions.index')
                ->with('status_failed', __('message.server_error'));
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param VersionRequest $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(VersionRequest $request)
    {
        try {
            DB::beginTransaction();
            if ($request->input('platforms') == AppVersion::ANDROID_AND_IOS) {
                $platforms = [AppVersion::IOS, AppVersion::ANDROID];
            } else {
                $platforms = [$request->input('platforms')];
            }

            foreach ($platforms as $platform) {
                $versionData = [
                    'version' => $request->input('version'),
                    'platform' => (int) $platform,
                    'status' => (int) $request->input('status'),
                    'description' => $request->input('description'),
                    'release_notes' => $request->input('release_notes'),
                    'force_update' => $request->has('force_update'),
                    'published_at' => $request->input('status') == AppVersion::PUBLISHED ? now() : null,
                ];

                AppVersion::create($versionData);
                // Clear cache
                $this->versionService->clearAllVersionCache();
            }

            $platformNames = collect($platforms)->map(function ($platform) {
                return $platform == AppVersion::IOS ? __('language.ios') : __('language.android');
            })->join(__('message.version_connector'));
            DB::commit();

            return redirect()->route('versions.index')
                ->with('status_succeed', __('message.version_create_success', [
                    'version' => $request->input('version'),
                    'platforms' => $platformNames
                ]));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param VersionRequest $request
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update($id, VersionRequest $request)
    {
        try {
            DB::beginTransaction();
            $version = AppVersion::findOrFail($id);

            $versionData = [
                'version' => $request->input('version'),
                'platform' => $request->input('platforms'),
                'status' => (int) $request->input('status'),
                'description' => $request->input('description'),
                'release_notes' => $request->input('release_notes'),
                'force_update' => $request->has('force_update'),
                'published_at' => $request->input('status') == AppVersion::PUBLISHED ?
                    ($version->published_at ?? now()) : null,
            ];

            $version->update($versionData);

            // Clear cache 
            $this->versionService->clearAllVersionCache();

            $platformName = $version->platform == AppVersion::IOS ? __('language.ios') : __('language.android');
            DB::commit();

            return redirect()->route('versions.index')
                ->with('status_succeed', __('message.version_update_success', [
                    'version' => $version->version,
                    'platform' => $platformName
                ]));
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->withInput()
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Prepare form data for create/edit views
     * 
     * @param AppVersion|null $version
     * @return array
     */
    private function prepareFormData($version = null)
    {
        $isEdit = !is_null($version);

        return [
            'isEdit' => $isEdit,
            'version' => $version,
            'route' => $isEdit ? route('versions.update', $version->id) : route('versions.store'),
            'headerTitle' => $isEdit ? __('language.version_title_update') : __('language.version_title_create'),
            'defaultDescription' => $isEdit ? $version->description : '',
            'defaultReleaseNotes' => $isEdit ? $version->release_notes : '',
        ];
    }
}
