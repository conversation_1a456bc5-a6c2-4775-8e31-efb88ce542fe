<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Model;
use App\User;
use Kyslik\ColumnSortable\Sortable;

class EvaluationForm extends Model
{
    use Sortable;
    protected $table = 'evaluation_forms';
    public $timestamps = false;
    
    const ALL_EMPLOYEES = 1;
    const MANAGER = 2;
    const LEADER = 3;
    const STAFF = 4;
    
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->created_by = auth()->id();
        });
    }

     /**
     * Get user that create the evaluation form.
     */
    public function createdBy()
    {
        return $this->belongsTo(User::class,'created_by');
    }

    /**
     * Get apply_for.
     * 
     * @param int $applyFor
     * 
     * @return string
     */
    public static function applyFor($applyFor){
        $result = '';
        switch ($applyFor){
            case self::ALL_EMPLOYEES :
                $result = trans('language.all_employees');
                break;
            case self::MANAGER :
                $result = trans('language.project_manager');
                break;
            case self::LEADER :
                $result = trans('language.leader');
                break;
            case self::STAFF :
                $result = trans('language.staff');
                break;
            default:;
        }
        return $result ;

    }

}
