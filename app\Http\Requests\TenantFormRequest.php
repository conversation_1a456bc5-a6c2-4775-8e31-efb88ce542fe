<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TenantFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'sub_domain' => 'required|max:100',
            'name' => 'required|max:200',
            'email' => 'required|email|max:200',
            'address' => 'nullable|max:1000',
            'phone' => 'nullable|digits_between:10,11',
        ];
    }
}
