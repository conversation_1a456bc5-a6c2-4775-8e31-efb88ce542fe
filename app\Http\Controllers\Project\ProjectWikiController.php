<?php

namespace App\Http\Controllers\Project;

use App\Helpers\Helpers;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Logics\ProjectManager;
use App\Logics\ProjectWikiManager;
use App\Logics\WikiDocumentManager;
use App\Models\Wiki;
use App\Models\WikiDocument;
use Illuminate\Support\Facades\Storage;
use App\Traits\StorageTrait;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\ProjectWikiRequest;
use App\Http\Requests\WikiTitleRequest;
use App\Models\Project;
use App\Models\WikiVersion;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
class ProjectWikiController extends Controller
{
    use StorageTrait;

    const PER_PAGE = 10;
    const CHILD_TO_PARENT = 1;
    const DELETE_ALL = 2;
    const PARENT_TO = 3;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index($projectId)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        $wikiManager = new ProjectWikiManager();
        $wiki = $wikiManager->getWiki($projectId, null);
        if(!$wiki){
            return redirect()->route('project.wiki.createPage', ['projectId' => $project, 'title' => 'Wiki']);
        }
        $versionNumber = WikiVersion::where("wiki_versions.project_wiki_id", $wiki->id)->count('wiki_versions.version_id');
        $wikiFiles = $wikiManager->getWikiFiles($wiki->project_id, $wiki->id);

        return view("projectX.wiki", compact('project', 'wiki', 'wikiFiles', 'versionNumber'));
    }

    

    public function page($projectId, $slug)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }
        
        $wikiManager = new ProjectWikiManager();
        $wiki = $wikiManager->getWiki($projectId, $slug);
        if(!$wiki){
            return redirect()->route('project.wiki.getWikiPageList',['projectId' => $project->id]);
        }
        $versionNumber = WikiVersion::where("wiki_versions.project_wiki_id", $wiki->id)->count('wiki_versions.version_id');
        $wikiFiles = $wikiManager->getWikiFiles($wiki->project_id, $wiki->id);

        return view("projectX.wiki", compact('project', 'wiki', 'wikiFiles', 'versionNumber'));
    }

    public function createPage($projectId)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }
        $title = request()->title;
        $parent = request()->parent;

        $wikiPages = Wiki::select('id','title', 'parent','level')
                    ->where('project_id', $project->id)
                    ->with('childrenWikiPages')
                    ->get();

        $wikiManager = new ProjectWikiManager();
        $wikiList = $wikiManager->getPageParent($wikiPages->toArray(), null, 1);

        return view('projectX.partials.form-create-page', compact('project', 'title', 'parent', 'wikiList'));
    }

    public function storePage(ProjectWikiRequest $request, $projectId)
    {
        DB::beginTransaction();
        try {
            $stringHelper = new StringHelper();
            $wikiManager = new ProjectWikiManager();
            $wiki = $wikiManager->getWiki($projectId, null,isset($request->parent)?$request->parent:null);
            $title = preg_replace("/,.\?\;\:\|\/{}%#/i", '', $request->title);
            $slug = $stringHelper->create_slug($title,'_');
            if($title == null){
                return redirect()->back()->with([
                    'status_failed' => trans('message.server_error')
                ]);
            }
            $page = new Wiki();
            $page->title = $title;
            $page->slug = Str::slug($title,"_");
            $page->project_id = $projectId;
            $page->parent = isset($request->parent)?$request->parent:null;
            $page->default = isset($wiki) ? Wiki::NOT_DEFAULT :  Wiki::DEFAULT;
            $page->level = isset($wiki) && isset($request->parent) ? $wiki->level + 1 : 1;
            $page->slug = $slug;
            $page->save();

            $wikiVer = new WikiVersion();
            $wikiVer->project_wiki_id =  $page->id;
            $wikiVer->content = StringHelper::escapeHtmlForSummernote($request->content);
            $wikiVer->note = $request->note;
            $wikiVer->version_id = 1;
            $wikiVer->status = WikiVersion::ACTIVE;
            $wikiVer->save();

            $data = WikiVersion::select(
                'wiki_versions.id',
                'wiki_versions.project_wiki_id',
                'wiki_versions.content',
                'project_wikis.project_id',
                'project_wikis.id',
                )
            ->leftjoin('project_wikis', 'project_wikis.id', 'wiki_versions.project_wiki_id')
            ->where('wiki_versions.id',$wikiVer->id)
            ->first();

            $destinationPath = str_replace(['{project_id}','{page_id}'], [$data->project_id, $data->id], WIKI_DIR) . '/';
            (new WikiDocumentManager())->saveWikiDocument($data, $request->wiki, $request->descriptionDocument, $destinationPath, 'content');

            DB::commit();
            return redirect()->route('project.wiki.page', ['projectId' => $page->project_id, 'slug' => $page->slug])->with([
                'status_succeed' => trans('message.update_wiki_successd')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    public function editPage($projectId, $slug)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $wikiManager = new ProjectWikiManager();
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        $wiki = $wikiManager->getWiki($project->id, $slug);
        if ($project == null) {
            return redirect()->route('project.index');
        }
        if(!$wiki){
            return redirect()->route('project.wiki.getWikiPageList',['projectId' => $project->id]);
        }
        $wikiPages = Wiki::select('id','title', 'parent','level')
                    ->where('project_id', $project->id)
                    ->where('slug','!=',$slug)
                    ->with('childrenWikiPages')
                    ->get();
        $wikiList = $wikiManager->getPageParent($wikiPages->toArray(), null, 1);

        return view("projectX.partials.form-edit-wiki", compact("wiki", "project","wikiList"));
    }

    public function updatePage(ProjectWikiRequest $request, $projectId, $slug)
    {
        DB::beginTransaction();
        try{
            $wikiManager = new ProjectWikiManager();
            $wiki = $wikiManager->getWiki($projectId,$slug);
            $page = Wiki::where(['slug' => $slug, 'project_id' => $projectId])->first();   
            if(empty($wiki) || empty($page)){
                return redirect()->route('project.wiki.index', ['projectId' => $projectId])->with([
                    'status_failed' => trans('message.wiki_page_does_not_exist')
                ]);
            }
            if($wikiManager->checkChild($page,$request->parent)){
                return redirect()->route('project.wiki.index', ['projectId' => $projectId])->with([
                    'status_failed' => trans('validation.exists',['attribute' => trans('validation.attributes.parent')])
                ]);
            };
            $maxVersion = WikiVersion::where("wiki_versions.project_wiki_id", $wiki->id)->max('wiki_versions.version_id');
            $page->parent = $request->parent;
            $page->save();
            $wikiManager->updateLevel($page,$request->parent);

            if($request->content != $wiki->content){
                $wikiOld = WikiVersion::find($wiki->verId);
                $wikiOld->status = WikiVersion::NOT_ACTIVE;
                $wikiOld->save();

                $wikiNew = new WikiVersion();
                $wikiNew->project_wiki_id =  $wiki->id;
                $wikiNew->content = $request->content;
                $wikiNew->note = $request->note;
                $wikiNew->version_id = $maxVersion + 1 ?? 1;
                $wikiNew->status = WikiVersion::ACTIVE;
                $wikiNew->save();
            
                $data = WikiVersion::select(
                    'wiki_versions.id',
                    'wiki_versions.project_wiki_id',
                    'wiki_versions.content',
                    'project_wikis.project_id',
                    'project_wikis.id',
                    )
                ->leftjoin('project_wikis', 'project_wikis.id', 'wiki_versions.project_wiki_id')
                ->where('wiki_versions.id',$wikiNew->id)
                ->first();
            }else{
                $data = $wiki;
            }

            $destinationPath = str_replace(['{project_id}','{page_id}'], [$data->project_id, $data->id], WIKI_DIR) . '/';
            (new WikiDocumentManager())->saveWikiDocument($data, $request->wiki, $request->descriptionDocument, $destinationPath, 'content');
            DB::commit();
            return redirect()->route('project.wiki.page', ['projectId' => $projectId, 'slug' => $slug])->with([
                'status_succeed' => trans('message.update_wiki_successd')
            ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return redirect()->route('project.wiki.index', ['projectId' => $projectId])->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    public function submitTitle(WikiTitleRequest $request, $projectId)
    {
        return redirect(route('project.wiki.createPage',['title'=>$request->title, 'projectId'=>$projectId, 'parent' => $request->parent]));
    }

    /**
     * download file
     * @param Request $request
     * @return Response
     */
    public function downloadFile(Request $request)
    {
        $file = (new WikiDocumentManager())->getWikiDocument($request->id);

        if ($file == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.document_not_exist'),
                ]
            ];
        }

        $wiki = Wiki::find($file->project_wiki_id);

        $userPermission = Project::select('projects.id')
            ->where('projects.id', $wiki->project_id)
            ->checkUserPermission(Auth::id())
            ->first();

        if ($userPermission == null) {
            return redirect()->route('project.index');
        }
        if (!Storage::disk(FILESYSTEM)->exists($file->file_path)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->download($file->file_path, $file->file_name);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param $projectId
     * @param $pageId
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $projectId, $pageId)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        DB::beginTransaction();
        try{
            $wiki = Wiki::select(
                'project_wikis.id',
                'project_wikis.project_id',
            )
            ->where('project_wikis.id', $pageId)
            ->where('project_wikis.project_id', $project->id);
            
            $wikiVer = WikiVersion::where('wiki_versions.project_wiki_id', $pageId);
            $wikiFiles = WikiDocument::where('project_wiki_id', $pageId);
            if($wikiVer&&$wikiFiles){
                $wikiFiles->delete();
                $wikiVer->delete();
                $wiki->delete();
                switch ($request->delete)
                {
                    case self::CHILD_TO_PARENT:
                        $wikiChild = Wiki::select('id','title', 'parent','level')
                            ->where('project_id', $projectId)
                            ->with('childrenWikiPages')
                            ->get();

                        $wikiManager = new ProjectWikiManager();
                        $wikiList = $wikiManager->getPageParent($wikiChild->toArray(), $pageId, 1);
                        foreach($wikiList as $item){
                            $wiki = Wiki::find($item['id']);
                            $wiki->level = $item['level'];
                            $wiki->save();
                        }
                        $wiki = Wiki::where('parent', $pageId)->get();
                        foreach($wiki as $item){
                            $item->parent = null;
                            $item->save();
                        }
                        break;
                    case self::DELETE_ALL:
                        $wikiChild = Wiki::select('id','parent')
                            ->where('project_id', $projectId)
                            ->with('childrenWikiPages')
                            ->get();
                        $wikiManager = new ProjectWikiManager();
                        $wikiList = $wikiManager->getPageParent($wikiChild->toArray(), $pageId, 1);
                        foreach($wikiList as $item){
                            $wiki = Wiki::find($item['id']);
                            $wiki->delete();
                        }
                        break;
                    case self::PARENT_TO:
                        $wikiChild = Wiki::select('id','title', 'parent','level')
                            ->where('project_id', $projectId)
                            ->with('childrenWikiPages')
                            ->get();
                        $wikiParent = Wiki::find($request->parent);
                        $wikiManager = new ProjectWikiManager();
                        $wikiList = $wikiManager->getPageParent($wikiChild->toArray(), $pageId, $wikiParent->level + 1);
                        foreach($wikiList as $item){
                            $wiki = Wiki::find($item['id']);
                            $wiki->level = $item['level'];
                            $wiki->save();
                        }
                        $wiki = Wiki::where('parent', $pageId)->get();
                        foreach($wiki as $item){
                            $item->parent = $wikiParent->id;
                            $item->save();
                        }
                        break;
                    default:
                        $wikiChild = Wiki::select('id','title', 'parent','level')
                            ->where('project_id', $projectId)
                            ->with('childrenWikiPages')
                            ->get();

                        $wikiManager = new ProjectWikiManager();
                        $wikiList = $wikiManager->getPageParent($wikiChild->toArray(), $pageId, 1);
                        foreach($wikiList as $item){
                            $wiki = Wiki::find($item['id']);
                            $wiki->level = $item['level'];
                            $wiki->save();
                        }
                        break;
                }
                    
            }
                
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.success'),
                ],
            ];
        }catch(\Exception $e){
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroyDocument(Request $request)
    {
        $file = (new WikiDocumentManager())->getWikiDocument($request->id);
        
        if ($file == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.document_not_exist'),
                ]
            ];
        }

        $wiki = Wiki::find($file->project_wiki_id);

        $userPermission = Project::select('projects.id')
            ->where('projects.id', $wiki->project_id)
            ->checkUserPermission(Auth::id())
            ->first();

        if ($userPermission == null) {
            return response()->json([
                'status' => 403,
            ], Response::HTTP_FORBIDDEN);
        }    
        try{
            // Delete then delete url image description
            $hostname = URL::to('/');
            $imageUrl = $hostname. '/wiki-document/' . $file->id;
            if($file){
                $wikiFiles = WikiVersion::select(
                    'wiki_versions.id', 
                    'wiki_versions.content',
                    )->leftjoin('project_wikis', 'wiki_versions.project_wiki_id', 'project_wikis.id')
                    ->where('project_wikis.id', $file->project_wiki_id)
                    ->where('wiki_versions.status', WikiVersion::ACTIVE)
                    ->first();
                $wikiFiles->content = str_replace($imageUrl, '', $wikiFiles->content);
                $wikiFiles->save();
            }
            
            $this->deleteFile($file->file_path);
            $file->delete();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_document_succeed') ,
                ],
            ];
        } catch (\Exception $e){
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.server_error'),
            ]);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @param $projectId
     * @param $id
     * @return View
     */
    public function compare(Request $request, $projectId, $slug)
    {
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, Auth::id());
        if ($project == null) {
            return redirect()->route('project.index');
        }
        $allWikiManager = new ProjectWikiManager();
        $wikis = $allWikiManager->getWikiCompare($projectId, $slug);
        $wikiPage = $allWikiManager->getWiki($projectId, $slug);

        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $wikis = $wikis->sortable()->paginate($perPage);

        return view('projectX.wiki-compare', compact('project', 'wikis', 'wikiPage'));
    }

    //show modal compare version_id
    public function modal(Request $request)
    {
        if ($request->ajax()) {
            $data = Helpers::diff($request->value_old, $request->value_new);
            $old = $data['old'];
        }
        return response()->json($old, Response::HTTP_OK);
    }

    public function getVer(Request $request)
    {
        if ($request->ajax()) {
            $item_new = WikiVersion::Where('id', $request->value_new)->first();
            $item_old = WikiVersion::Where('id', $request->value_old)->first();
            $value_new = $item_new->content;
            $value_old = $item_old->content;
        }
        return response()->json(['new'=>$value_new, 'old'=>$value_old, Response::HTTP_OK]);
    }

    //show wiki document
    public function show($id)
    {
        $file = (new WikiDocumentManager())->getWikiDocument($id);
        if($file == null){
            return back();
        }
        $wiki = Wiki::find($file->project_wiki_id);

        $userPermission = Project::select('projects.id')
            ->where('projects.id', $wiki->project_id)
            ->checkUserPermission(Auth::id())
            ->first();
        
        if ($userPermission == null) {
            return redirect()->route('project.index');
        }

        if (!Storage::disk(FILESYSTEM)->exists($file->file_path)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->response($file->file_path);
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function getWikiPageList($projectId){
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        $wikiPages = Wiki::select('id','title', 'parent','level', 'project_id', 'slug')
                    ->whereNull('parent')
                    ->where('project_id', $project->id)
                    ->with('childrenWikiPages')
                    ->get();

        return view('projectX.wiki_page_list', compact('project', 'wikiPages'));
    }

    /**
     * Revert version
     * 
     * @param $projectId
     * @param $pageId
     * @param $id
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function revertVersion($projectId, $slug, $id){
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        DB::beginTransaction();
        try{
            $wikiActive = WikiVersion::select(
                'wiki_versions.id',
                'project_wikis.project_id',
                'wiki_versions.status',
            )
            ->leftJoin('project_wikis', 'wiki_versions.project_wiki_id', 'project_wikis.id')
            ->where('project_wikis.project_id', $project->id)
            ->where('project_wikis.slug', $slug)
            ->where('wiki_versions.status', WikiVersion::ACTIVE)
            ->first();
            if ($wikiActive == null) {
                return response()->json([
                    'code' => Response::HTTP_NOT_FOUND,
                    'message' => trans('message.wiki_version_not_exist')
                ], Response::HTTP_NOT_FOUND);
            }
            $wikiActive->status = WikiVersion::NOT_ACTIVE;
            $wikiActive->save();

            $wiki = WikiVersion::find($id);

            if ($wiki == null) {
                return response()->json([
                    'code' => Response::HTTP_NOT_FOUND,
                    'message' => trans('message.wiki_version_not_exist')
                ], Response::HTTP_NOT_FOUND);
            }
            $wiki->status = WikiVersion::ACTIVE;
            $wiki->save();
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.success'),
                ],
            ];
        }catch(\Exception $e){
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function parentPage(Request $request)
    {
        if($request->ajax()){
            $wikiPages = Wiki::select('id','title', 'parent','level')
            ->where('project_id', $request->project_id)
            ->whereBetween('level',[1, $request->level])
            ->where('id', '!=', $request->page_id)
            ->get();
            $wikiManager = new ProjectWikiManager();
            $wikiList = $wikiManager->getPageParent($wikiPages->toArray(), null, 1);
            $html = view('projectX.partials.wiki-parent',['wikiList' => $wikiList])->render();
        }
        return response()->json($html, Response::HTTP_OK);
    }

    /**
     * remove version
     * @param $projectId
     * @param $pageId
     * @param $verId
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    public function destroyVersion($projectId, $pageId, $verId)
    {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        $wikiPage = Wiki::find($pageId);
        if($wikiPage == null){
            return redirect()->route('project.wiki.compare', ['projectId' => $project->id, 'pageId' => $pageId]);
        }

        DB::beginTransaction();
        try{

            $wikiVer = WikiVersion::find($verId);

            if($wikiVer->status == WikiVersion::ACTIVE){

                $wikiVer->delete();
                $wikiActive = WikiVersion::select(
                    'wiki_versions.id',
                    'wiki_versions.status',
                    'projects.id as projectId',
                    'project_wikis.id as wikiId'
                    )
                    ->leftjoin('project_wikis','wiki_versions.project_wiki_id', 'project_wikis.id')
                    ->leftjoin('projects','project_wikis.project_id', 'projects.id')
                    ->where('projects.id', $project->id)
                    ->where('project_wikis.id', $pageId)
                    ->orderBy('wiki_versions.version_id', 'DESC')
                    ->first();
                    $wikiActive->update(['status' => WikiVersion::ACTIVE]);

            }else{
                $wikiVer->delete();
            }

            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.success'),
                ],
            ];
        }catch(\Exception $e){
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
