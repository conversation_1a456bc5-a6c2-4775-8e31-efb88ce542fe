<?php

namespace App\Console\Commands;

use App\Logics\EvaluationManager;
use App\Models\Evaluation;
use App\Models\EvaluationForm;
use App\Models\EvaluationResult;
use App\Models\ProjectGroupRole;
use App\Models\ProjectTask;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;

class CreateEvaluationWeek extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:create_evaluation_week {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create evaluation week';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        $from = Carbon::now()->startOfWeek()->format('Y-m-d 00:00:00');
        $to = Carbon::now()->startOfWeek()->addDays(6)->format('Y-m-d 23:59:59');
        $startedAt = Carbon::now()->startOfWeek()->subDays(7)->format('Y-m-d 00:00:00');
        $endedAt = Carbon::now()->startOfWeek()->subDay()->format('Y-m-d 23:59:59');

        $evaluationForm = EvaluationForm::create(
            [
                'name' => 'Đánh giá tuần ('.date('d/m/Y',strtotime($startedAt)).' - '.date('d/m/Y',strtotime($endedAt)).')',
                'apply_for'=> ProjectGroupRole::MEMBER,
                'evaluator'=> ProjectGroupRole::LEADER,
                'content' => '[{"label":"Điểm trung bình","percent":"100"}]',
                'started_at' => $startedAt,
                'ended_at' => $endedAt,
                'from' => $from,
                'to' => $to,
                'is_evaluation_week' => true,
            ]
        );
        $evaluationManager = new EvaluationManager();
        $evaluationManager->createEvaluate([ProjectGroupRole::MEMBER] ,$evaluationForm, [ProjectGroupRole::LEADER], 1);

        $tasks = ProjectTask::select('score','user_id','key_member')
            ->whereNotNull('score')
            ->where(function($query)  use ($startedAt){
                $query->where('project_tasks.started_at','>=', $startedAt)
                    ->orwhere('project_tasks.ended_at', '>=', $startedAt);
            })
            ->where(function($query)  use ($endedAt){
                $query->where('project_tasks.started_at','<=', $endedAt)
                    ->orwhere('project_tasks.ended_at', '<=', $endedAt);
            })
            ->where(function ($query) {
                $query->whereNotNull('user_id')
                    ->orWhereNotNull('key_member');
            })
            ->get();
        $userScore=[];
        foreach ($tasks as $task){
            $user_id = empty($task->key_member)?$task->user_id:$task->key_member;
            if(isset($userScore[$user_id])){
                $userScore[$user_id]['totalscore'] += $task->score;
                $userScore[$user_id]['task'] +=1;
            }else{
                $userScore[$user_id]['totalscore'] = $task->score;
                $userScore[$user_id]['task'] =1;
            }
        }
        foreach ($userScore as $user_id=>$infomation){
            $score = round($infomation['totalscore']/$infomation['task'],2);
            Evaluation::where('user_id',$user_id)
                ->where('form_id',$evaluationForm->id)
                ->update([
                    'content' => '[{"percent":"100","score":"'.$score.'","comment":""}]'
                ]);
            EvaluationResult::where('user_id',$user_id)
                ->where('form_id',$evaluationForm->id)
                ->update([
                    'score' => $score
                ]);
        }

    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->weeklyOn(1,"00:00");
    }
}
