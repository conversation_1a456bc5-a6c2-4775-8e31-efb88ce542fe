name: Development deploy

on:
  push:
    branches:
      - develop-multi-tenant
      - develop-tenancy/*

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    steps:
      - name: SSH to remote server and run script to deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }} #hostname of server
          username: ${{ secrets.USERNAME }} #username login to server
          password: ${{ secrets.PASSWORD }} #password login to server
          port: ${{ secrets.PORT }} #port of server - often port is 22
          command_timeout: "30m"   # Increase command timeout to 30 minutes
          script: | # run with multiple script
            cd /var/www/beeid.vn/htdocs
            git reset --hard
            echo ========== git pull ==========
            git pull
            echo ========== git fetch origin ==========
            git fetch origin
            CURRENT_BRANCH="origin/${{ github.ref_name }}"
            echo "========== Merging branch $CURRENT_BRANCH ========== "
            echo ========== git merge --no-ff "$CURRENT_BRANCH" ==========
            git merge --no-ff "$CURRENT_BRANCH" -m "Auto-merge $CURRENT_BRANCH"

            echo ========== /usr/local/bin/composer install ==========
            /usr/local/bin/composer install

            echo ========== nvm use 14.21.3 ==========
            nvm use 14.21.3
            echo ========== npm install ==========
            npm install
            echo ========== npm run plugins ==========
            npm run plugins
            echo ========== npm run prod ==========
            npm run prod

            echo ========== sudo systemctl restart supervisord ==========
            sudo systemctl restart supervisord
            echo ========== sudo systemctl restart crond ==========
            sudo systemctl restart crond

            echo ========== php artisan tenancy:run migrate==========
            php artisan tenancy:run migrate
            echo ========== php artisan tenancy:run db:seed --option="class=UpdateSeeder"==========
            php artisan tenancy:run db:seed --option="class=UpdateSeeder"

            echo ========== pm2 restart beechat==========
            pm2 restart beechat
