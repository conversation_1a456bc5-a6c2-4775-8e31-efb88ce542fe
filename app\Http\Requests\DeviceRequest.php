<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Hyn\Tenancy\Environment;
use App\Logics\TenantManager;

class DeviceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // Access to the database of the tenant
        // Check tenant exists
        $tenantManager = new TenantManager();
        $tenant = $tenantManager->checkTenantExists($this->tenant_id);
        if (empty($tenant)) {
            return redirect()->route('tenant.index')->with([
                'status_failed' => trans('message.company_not_exist')
            ]);
        }
        config(['database.default' => 'system']);

        $mac_address_unique_id = '';
        if (isset($this->id)) {
            $mac_address_unique_id = ','.$this->id;
        }
        return [
            'device_name' => 'max:200',
            'mac_address' => 'required|max:200|unique:devices,mac_address'.$mac_address_unique_id,
            'local_ip_address' => 'max:200',
            'public_ip_address' => 'max:200',
        ];
    }
}
