<?php


namespace App\Logics;


use App\Models\ProjectGroupRole;
use Illuminate\Support\Facades\DB;

class ProjectGroupRoleManager
{
    public function getListName($listId){
        $ids = explode(CONCAT_SEPARATOR,$listId);
        $name = '';
        if(count($ids) == ProjectGroupRole::TOTAL_GROUP_ROLE){
            return trans('language.all_employees');
        }else{
            $arrName = [];
            foreach ($ids as $id){
                $arrName[] = $this->getName($id);
            }
            $name = implode(', ',$arrName);
        }
        return $name;
    }

    public function getName($id){
        $name = '';
        switch ($id){
            case ProjectGroupRole::PROJECT_MANAGER:
                $name = trans('language.project_manager');
                break;
            case ProjectGroupRole::LEADER:
                $name = trans('language.leader');
                break;
            case ProjectGroupRole::MEMBER:
                $name = trans('language.staff');
                break;
            default:
                $name = '';
                break;
        }
        return $name;
    }
}