<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;

class AppVersion extends Model
{
    protected $connection = 'system';

    protected $table = 'app_versions';

    protected $fillable = [
        'version',
        'platform',
        'force_update',
        'status',
        'release_notes',
        'description',
        'published_at'
    ];

    protected $casts = [
        'force_update' => 'boolean',
        'status' => 'integer',
        'platform' => 'integer',
        'published_at' => 'datetime',
    ];

    const IOS = 1;
    const ANDROID = 2;
    const ANDROID_AND_IOS = 0;

    const DRAFT = 0;
    const PUBLISHED = 1;

    // Mode constants
    const MODE_PRODUCTION = 1;
    const MODE_PREVIEW = 2;

    public function scopePublished($query)
    {
        return $query->where('status', self::PUBLISHED);
    }

    public function scopePlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }
    
    // Platform name accessor
    public function getPlatformNameAttribute()
    {
        return $this->platform === self::IOS
            ? __('language.ios')
            : __('language.android');
    }

    // Status name accessor  
    public function getStatusNameAttribute()
    {
        return $this->status === self::PUBLISHED
            ? __('language.published')
            : __('language.draft');
    }
    
    // Helper methods
    public function isForceUpdate()
    {
        return $this->force_update;
    }

    public function isPublished()
    {
        return $this->status === self::PUBLISHED;
    }
}
