<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @param Illuminate\Http\Request $request
     * @return array
     */
    public function rules()
    {
        
        $userName = $this->input('email');
        $ruleUserName = 'required|email|max:200';

        if (!filter_var($userName, FILTER_VALIDATE_EMAIL)) {
            $ruleUserName = 'required|max:20|regex:/^0[0-9]{9}$/';
        }

        return [
            'email' => $ruleUserName,
            'password' => 'required'
        ];
    }

    /**
     * Get the message that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        $messages = [];

        return $messages;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'email' => __('validation.attributes.contact.email_or_phone'),
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator) {}
}
