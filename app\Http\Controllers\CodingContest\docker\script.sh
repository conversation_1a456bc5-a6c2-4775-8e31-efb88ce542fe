#!/bin/bash

# Sample execution command:   $: ./script.sh php main.php

compiler=$1
sourceFile=$2
testcases=$3
result=$4
# result1="$result-1"

runtime=0

response="{"

for input in $testcases/input*; do
    testcaseId=${input##*input}
    outputFile="output$testcaseId"
    # Run source file
    start=$(date +%s.%5N)
    if [[ $compiler == "java" ]]; then
        dirname=$(dirname -- "$sourceFile")
        filename=$(basename -- "$sourceFile")
        javaClassFile="${filename%.*}"
        output=$(javac $sourceFile && cd $dirname && java $javaClassFile < $input 2>&1)
    else
        output=$($compiler $sourceFile < $input 2>&1)
    fi
    # echo $output >> $result1;
    # echo "\n"
    end=$(date +%s.%5N)
    runtime=$(echo "$runtime + $end - $start" | bc)

    # Compare result
    response="$response\"Test case $testcaseId\":"
    # case $output in
    #     *"Error"*|*"error"*|*"Warning"*|*"warning"*)
    #         response="$response \"${output//\"/\\\"}\"" # Compile error
    #         ;;
    #     *)
    #         case $output in
    #             $(cat $testcases/$outputFile))
    #                 response="$response true"
    #                 ;;
    #             *)
    #                 response="$response false"
    #                 ;;
    #         esac
    #         ;;
    # esac
    case $output in
        $(cat $testcases/$outputFile))
            response="$response true"
            ;;
        *)
            response="$response false"
            ;;
    esac

    response="$response,"
done

#response="${response::-1}" # Remove last ',' character'
response="$response\"Runtime\":\"$runtime\""
response="$response}"
echo $response > $result
