<?php

namespace App\Http\Controllers\Project;

use App\Http\Controllers\Controller;
use App\Logics\TaskManager;
use App\Logics\UserManager;
use App\Models\Attendance;
use App\Models\Project;
use App\Models\ProjectMember;
use App\Models\ProjectRole;
use App\Models\Role;
use App\Models\SpecialRequest;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectManagementController extends Controller
{
    public function __construct()
    {
    }

    public function index(Request $request)
    {
        $auth = Auth::user();
        $date = $request->query('date') ?? Carbon::now()->format('Y-m-d');

        $requestDate = Carbon::parse($date);

        $monthRequest = $requestDate->copy()->format('Y-m');
        $timeStart = $requestDate->copy()->format('Y-m-d');
        $startDate = $requestDate->copy()->startOfMonth();
        $endDate = $requestDate->copy()->endOfMonth();
        $lastMonth = $requestDate->copy()->subMonth()->endOfMonth();

        $projectIds = ProjectMember::query()
            ->select('project_members.project_id')
            ->join('projects', 'project_members.project_id', '=', 'projects.id')
            ->where('started_at', '<=', $endDate)
            ->where(function($query) use ($startDate) {
                $query->where('ended_at', '>=', $startDate)
                    ->orWhereNull('ended_at');
            })
            ->where('project_members.role_id', ProjectRole::ProjectManager)
            ->where('project_members.user_id', $auth->id)
            ->distinct()
            ->pluck('project_members.project_id');

        $users = User::query()
            ->whereIn('id', function($query) use ($auth, $projectIds) {
                $query->select('user_id')
                    ->from('project_members')
                    ->where('user_id', '!=', $auth->id)
                    ->where('role_id', '!=', ProjectRole::ProjectManager)
                    ->whereIn('project_id', $projectIds)
                    ->distinct();
            })
            ->orderBy('id')
            ->get();
        $userIds = $users->pluck('id');
        $totalUsers = $users->count();

        // Get request
//        $userRequests = \App\Models\Request::query()->whereIn('created_by', $userIds)
//            ->where(function($query)  use ($timeStart){
//                $query->whereRaw("DATE(JSON_UNQUOTE(JSON_EXTRACT(content, '$.time_start'))) = ?", [$timeStart]);
//            })
//            ->get();
        
        $attendances = Attendance::query()->whereIn('user_id', $userIds)
            ->whereDate('checked_at', $timeStart)
            ->get();
        $filteredAttendances = $attendances->where('type', Attendance::CHECK_IN);
        $userIdsWithTypeCheckIn = $filteredAttendances->pluck('user_id')->toArray();
        $filteredAttendancesWithTypeCheckOut = $attendances->where('type', Attendance::CHECK_OUT);
        $userIdsWithTypeCheckOut = $filteredAttendancesWithTypeCheckOut->pluck('user_id')->toArray();
        $userIdsWithType0WithoutTypeCheckOut = array_diff($userIdsWithTypeCheckIn, $userIdsWithTypeCheckOut);

        $numberOfPeopleWorking = count($userIdsWithType0WithoutTypeCheckOut);
        $numberOfPeopleLeaving = count($userIdsWithTypeCheckOut);

        $specialRequests = SpecialRequest::query()->where('month', $timeStart)
            ->whereIn('user_id', $userIds)
            ->get();

        $userOverTimeSums = [];
        $overTimeSums = 0;
        $averageOverTimes = 0;
        foreach ($specialRequests as $request) {
            $userId = $request->user_id;
            $overTime = $request->ot;
            if (!isset($userOverTimeSums[$userId])) {
                $userOverTimeSums[$userId] = 0;
            }
            $userOverTimeSums[$userId] += $overTime;

            $overTimeSums+= $overTime;
        }

        if (count($specialRequests) > 0) {
            $averageOverTimes = round($overTimeSums / count($specialRequests));
        }

        $timeUserWorking = $this->attendances($userIds, $monthRequest);

        $requestManager = new \App\Logics\RequestManager;
        $numberRequestIsNotView = $requestManager->getNumberIsNotView();

        $averageOverTimeLastMonth = $this->getAverageLastMonth($lastMonth, $auth);
        
        $compareAverageOverTime = $averageOverTimes - $averageOverTimeLastMonth;
        
        $operatorOverTime = '+';
        if ($compareAverageOverTime < 0 ) {
            $operatorOverTime = '-';
        }
        
        return view('admin.management.index', [
            'users' => $users,
            'totalUsers' => $totalUsers,
            'numberOfPeopleWorking' => $numberOfPeopleWorking,
            'numberOfPeopleLeaving' => $numberOfPeopleLeaving,
            'userOverTimeSums' => $userOverTimeSums,
            'averageOverTimes' => $averageOverTimes,
            'timeUserWorking' => $timeUserWorking,
            'numberRequestIsNotView' => $numberRequestIsNotView,
            'operatorOverTime' => $operatorOverTime,
            'compareAverageOverTime' => abs($compareAverageOverTime),
        ]);
    }

    private function attendances($userIds, $checkedMonth): array
    {

        $attendances = Attendance::query()->whereIn('user_id', $userIds)
            ->whereMonth('checked_at', $checkedMonth)
            ->get();

        $attendanceMonths = [];
        foreach ($attendances as $attendance) {
            $date = Carbon::parse($attendance->checked_at)->toDateString();
            $attendanceMonths[$date][$attendance->user_id][$attendance->checked_type] =  $attendance;
        }

        $timeUserWorking = [];
        foreach ($attendanceMonths as $attendanceUsers) {
            foreach ($attendanceUsers as $userId => $attendanceUser) {
                // Case no checkin or checkout.
                if (count($attendanceUser) < 2) {
                    continue;
                }
                $checkIn = false;
                $checkOut = false;
                $attendanceCheckIn = null;
                $attendanceCheckOut = null;
                foreach ($attendanceUser as $attendance) {
                    if ($attendance->checked_type == Attendance::CHECK_IN) {
                        $checkIn = true;
                        $attendanceCheckIn = Carbon::parse($attendance->checked_at);
                    } elseif ($attendance->checked_type == Attendance::CHECK_OUT) {
                        $checkOut = true;
                        $attendanceCheckOut = Carbon::parse($attendance->checked_at);
                    }
                }

                $isValidWorkingDay = ($checkIn && $checkOut);
                // Case no checkin or checkout.
                if (!$isValidWorkingDay) {
                    continue;
                }

                $totalMinutes = $attendanceCheckOut->diffInMinutes($attendanceCheckIn);
                $totalTimeUserWorking = $timeWorks[$userId] ?? 0;
                $totalTimeUserWorking+=$totalMinutes;
                $timeUserWorking[$userId] = $totalTimeUserWorking;
            }
        }

        return $timeUserWorking;
    }
    
    private function getAverageLastMonth($endDate, $auth)
    {
        $projectIds = ProjectMember::query()
            ->select('project_members.project_id')
            ->join('projects', 'project_members.project_id', '=', 'projects.id')
            ->where('started_at', '<=', $endDate)
            ->where(function($query) use ($endDate) {
                $query->where('ended_at', '>', $endDate);
            })
            ->where('project_members.role_id', ProjectRole::ProjectManager)
            ->where('project_members.user_id', $auth->id)
            ->distinct()
            ->pluck('project_members.project_id');

        $users = User::query()
            ->whereIn('id', function($query) use ($auth, $projectIds) {
                $query->select('user_id')
                    ->from('project_members')
                    ->where('user_id', '!=', $auth->id)
                    ->where('role_id', '!=', ProjectRole::ProjectManager)
                    ->whereIn('project_id', $projectIds)
                    ->distinct();
            })
            ->orderBy('id')
            ->get();
        $userIds = $users->pluck('id');
        
        $specialRequests = SpecialRequest::query()->where('month', $endDate)
            ->whereIn('user_id', $userIds)
            ->get();

        $overTimeSums = 0;
        $averageOverTimes = 0;
        foreach ($specialRequests as $request) {
            $overTimeSums+= $request->ot;
        }

        if (count($specialRequests) > 0) {
            $averageOverTimes = round($overTimeSums / count($specialRequests));
        }
        return $averageOverTimes;
    }
}
