<?php

namespace App\Providers;

use Illuminate\Support\Facades\Event;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        'App\Events\Event' => [
            'App\Listeners\EventListener',
        ],
        'App\Events\UpdateTask' => [
            'App\Listeners\CreateTaskLog',
        ],
        'App\Events\UpdateAsset' => [
            'App\Listeners\CreateAssetLog',
        ],
        'App\Events\AssignTask' => [
            'App\Listeners\AssignTaskNotify',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
