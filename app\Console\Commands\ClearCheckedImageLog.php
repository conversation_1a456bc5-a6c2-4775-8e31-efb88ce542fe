<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Storage;

class ClearCheckedImageLog extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:clear_checked_image_logs  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear checked image logs';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        $files = Storage::disk(FILESYSTEM)->allFiles('checked_image_logs');
        foreach ($files as $file) {
            $time = Storage::disk(FILESYSTEM)->lastModified($file);
            if ($time < now()->subMonth(3)->getTimestamp()) {
                Storage::disk(FILESYSTEM)->delete($file);
            }
        }
        Attendance::whereRaw('checked_at < "'.date('Y-m-d H:i:s', now()->subMonth(3)->getTimestamp()).'"')->update(['checked_image' => null]);
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->monthly();
    }
}
