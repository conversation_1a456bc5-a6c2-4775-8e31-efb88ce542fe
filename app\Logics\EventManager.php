<?php

namespace App\Logics;

use App\Helpers\NotificationHelper;
use App\Http\Requests\EventRequest;
use App\Models\Event;
use App\Models\EventParticipant;
use App\Models\Project;
use App\Models\ProjectGroupRole;
use App\Models\ProjectMember;
use App\Models\Resource;
use App\Models\SiteSetting;
use App\Models\UserDevice;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class EventManager
{
    public function getListEventCalendar($params,$isWeb = false){
        if(isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y',$params['from']) > Carbon::createFromFormat('d/m/Y',$params['to'])){
            return [];
        }
        $eventCalendar = [];
        $events = Event::select(
                'events.id',
                'events.name',
                'events.description',
                'events.started_at',
                'events.ended_at',
                'events.date_limit',
                'events.location',
                'events.repeat',
                'group_resources.name as group_resource',
                'resources.name as resource',
                'events.project_id',
                'projects.name as project_name'
            )
            ->leftjoin('projects','projects.id','events.project_id')
            ->rightjoin('event_participants','event_participants.event_id','events.id')
            ->leftjoin('resources','resources.id','events.location')
            ->leftjoin('group_resources','group_resources.id','resources.group_id')
            ->where(function ($query) use ($params){
                $query->where(function ($subquery) use ($params){
                        $subquery->where('repeat','!=',Event::NO_REPEAT);
                        if(isset($params['to'])){
                            $subquery->where('events.started_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                        }
                    })
                    ->orwhere(function ($subquery) use ($params){
                        $subquery->where('repeat',Event::NO_REPEAT);
                        if(isset($params['from'])){
                            $subquery->where(function($subquery)  use ($params){
                                $subquery->where('events.started_at','>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'))
                                    ->orwhere('events.ended_at', '>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'));
                            });
                        }
                        if(isset($params['to'])){
                            $subquery->where(function($subquery)  use ($params){
                                $subquery->where('events.started_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'))
                                    ->orwhere('events.ended_at', '<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                            });
                        }
                    });
            })
            ->where('events.type', Event::MEETING_TYPE)
            ->distinct();
        if ($isWeb == true){
            $settingMeet = SiteSetting::settingMeet();
            if ($settingMeet == SiteSetting::MEETING_PRIVATE){
                // Get list project user has role pm, leader
                $projectsHasRolePMLeader = ProjectMember::join('project_roles', 'project_roles.id', 'project_members.role_id')
                    ->join('projects','projects.id','project_members.project_id')
                    ->where('project_members.user_id',Auth::id())
                    ->whereNull('projects.deleted_at')
                    ->groupBy('project_members.project_id')
                    ->havingRaw('min(project_roles.group_role_id) in ('.ProjectGroupRole::PROJECT_MANAGER.','.ProjectGroupRole::LEADER.')')
                    ->pluck('project_members.project_id')
                    ->toArray();
                //Get list user in projects
                if (empty($projectsHasRolePMLeader)){
                    $events = $events->where('event_participants.user_id', Auth::id());
                }else{
                    $users = ProjectMember::select('user_id')
                        ->whereIn('project_id',$projectsHasRolePMLeader)
                        ->distinct()
                        ->pluck('user_id')
                        ->toArray();
                    // get list project of current user
                    $projects = ProjectMember::join('projects','projects.id','project_members.project_id')
                        ->where('project_members.user_id',Auth::id())
                        ->whereNull('projects.deleted_at')
                        ->groupBy('project_members.project_id')
                        ->pluck('project_members.project_id')
                        ->toArray();
                    $events = $events->whereIn('event_participants.user_id', $users)
                        ->where(function ($query) use ($projects){
                            $query->whereIn('events.project_id', $projects)
                                    ->orWhereNull('events.project_id');
                        });
                }
            }
        }

        if(isset($params['member'])){
            $events = $events->whereIn('event_participants.user_id', $params['member']);
        }
        if(isset($params['project_id'])){
            $events = $events->where('events.project_id', $params['project_id']);
        } else {
            $events = $events->where('projects.deleted_at', null);
        }

        $events = $events->get();
        foreach ($events as $event){
            $paticipants = $this->getPaticipants($event->id);
            $dataEvent = [
                'is_event' => 1,
                'title' => $isWeb ? htmlspecialchars($event->name):$event->name,
                'id' => $event->id,
                'start' => empty($event->started_at)?'':date('Y-m-d H:i:s',strtotime($event->started_at)),
                'end' => empty($event->ended_at)?'':date('Y-m-d H:i:s',strtotime($event->ended_at)),
                'startTime' => empty($event->started_at)?'':date('H:i:s',strtotime($event->started_at)),
                'endTime' => empty($event->ended_at)?'':date('H:i:s',strtotime($event->ended_at)),
                'startRecur' => empty($event->started_at)?'':date('Y-m-d',strtotime($event->started_at)),
                'description' => $isWeb ? htmlspecialchars($event->description):$event->description,
                'location_id' => $event->location,
                'resources'=>$event->group_resource,
                'resource'=>$event->resource,
                'location' => $event->group_resource. ((isset($event->group_resource) && isset($event->resource) ) ? '-' : "").$event->resource,
                'project_id' => $event->project_id,
                'project_name' => $event->project_name,
                'repeat' => $event->repeat,
                'participants' => $paticipants,
                'started_at' => empty($event->started_at)?'':date('Y-m-d H:i:s',strtotime($event->started_at)),
                'ended_at' => empty($event->ended_at)?'':date('Y-m-d H:i:s',strtotime($event->ended_at)),
                'date_limit' => empty($event->date_limit)?'':date('Y-m-d',strtotime($event->date_limit)),
                'endRecur' => empty($event->date_limit)?'':date('Y-m-d',strtotime($event->date_limit)),
                'color' => '#8DC63F',
                'userHasPermissionUpdate' => (array_search(Auth::id(), array_column($paticipants, 'id'))===false?0:1),
            ];

            if($event->repeat == Event::NO_REPEAT){
                $dataEvent['endRecur'] = empty($event->started_at)?'':date('Y-m-d',strtotime($event->started_at.' + 1 days'));
            }else if($event->repeat == Event::REPEAT_EVERYDAY_OF_THE_WEEK){
                $dataEvent['daysOfWeek'] = ["1","2","3","4","5"];
            }else if($event->repeat == Event::REPEAT_EVERY_WEEKEND){
                $dataEvent['daysOfWeek'] = ["0","6"];
            }else if($event->repeat == Event::REPEAT_WEEKLY){
                $day = date("w", strtotime($event->started_at));
                $dataEvent['daysOfWeek'] = [$day];
            }
            if(!empty($event->started_at) && !empty($event->ended_at) && date('Y-m-d',strtotime($event->started_at))!=date('Y-m-d',strtotime($event->ended_at))&&$event->repeat == Event::NO_REPEAT) {
                $dataEvent['startTime'] = '';
                $dataEvent['endTime'] = '';
                $dataEvent['startRecur'] = '';
                $dataEvent['endRecur'] = '';
            }
            $eventCalendar[] = $dataEvent;
        }
        return $eventCalendar;
    }

    public function createEvent(EventRequest $request) {
        //--- 1. Create event ---
        $userId = Auth::id();
        $event = new Event();
        $event->name = $request->name;
        $event->description = $request->description;
        $event->started_at = empty($request->started_at)?$request->started_at:\Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->started_at);
        $event->ended_at = empty($request->ended_at)?$request->ended_at:\Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->ended_at);
        $event->repeat = $request->repeat;
        $event->type = isset($request->type)?$request->type:Event::MEETING_TYPE;
        $event->location = $request->location;
        $event->project_id =  $request->project_id;
        $event->date_limit = empty($request->date_limit)?$request->date_limit:\Carbon\Carbon::createFromFormat('d/m/Y', $request->date_limit);
        $event->save();

        $notification = trans('language.notification.invite_to_the_meeting') . ' ' . $request->name;
        $calendarMounth = '';
        if(!empty($request->project_id)){
            $project = Project::find($request->project_id);
            $notification .= "\n" . trans('language.project') . ": " . $project->name;
        }
        if(!empty($request->started_at)){
            $calendarMounth = \Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->started_at)->format('m/Y');
            $notification .= "\n" . trans('language.notification.from') . ' ' . $request->started_at;
        }
        if(!empty($request->ended_at)){
            $notification .= " " . trans('language.notification.to') . " " . $request->ended_at;
        }
        if(!empty($request->location)){
            $location = Resource::select(
                'group_resources.name as group_resource',
                'resources.name as resource',
                )->leftjoin('group_resources','group_resources.id','resources.group_id')
                ->where('resources.id',$request->location)
                ->first();
            $locationName  = $location->group_resource. ((isset($location->group_resource) && isset($location->resource) ) ? '-' : "").$location->resource;
            $notification .= "\n" . trans('language.notification.location') . ": " . $locationName;
        }

        //---- 2. Create event participants ---
        $members= empty($request->members)?[]:$request->members;
        $listMember = [];

        if (!in_array(Auth::id(), $members)){
            $listMember[] = [
                'user_id' => Auth::id(),
                'event_id' => $event->id,
            ];
        }

        foreach ($members as $member){
            $listMember[] = [
                'user_id' => $member,
                'event_id' => $event->id,
            ];
        }

        EventParticipant::insert($listMember);
        // Send notification
        $tokens = $this->getTokensByUser($members);
        $notificationHelper = new NotificationHelper();

        // $notificationHelper->sendNotificationDevice($tokens,
        //     [
        //         'title' =>  trans("language.notification.meeting"),
        //         'content' => $notification,
        //         'click_action' => route('projects.calendar', ['time'=>$calendarMounth]),
        //     ]
        // );

        return $event;
    }

    public function updateEvent($event, EventRequest $request) {
        //--- 1. Update event ---
        $userId = Auth::id();
        $event->name = $request->name;
        $event->description = $request->description;
        $event->started_at = empty($request->started_at)?$request->started_at:\Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->started_at);
        $event->ended_at = empty($request->ended_at)?$request->ended_at:\Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->ended_at);
        $event->repeat = $request->repeat;
        $event->type = isset($request->type)?$request->type:$event->type;
        $event->location = $request->location;
        $changProject = ($request->project_id != $event->project_id) ? true : false ;
        $event->project_id =  $request->project_id;
        $event->date_limit = empty($request->date_limit)?$request->date_limit:\Carbon\Carbon::createFromFormat('d/m/Y', $request->date_limit);
        $event->save();

        //---- 2. Create event participants ---
        $oldMember = EventParticipant::where('event_id',$event->id)->pluck('user_id')->toArray();
        $newMember = empty($request->members)?[]:$request->members;
        $deleteMember = array_diff($oldMember, $newMember);
        $createMember = array_diff($newMember, $oldMember);
        $updateMember = array_intersect($newMember, $oldMember);
        EventParticipant::where('event_id',$event->id)->whereIn('user_id', $deleteMember)->delete();
        if($changProject && !in_array(Auth::id(), $newMember)){
            array_push($createMember,Auth::id());
        }

        $titleNotification = trans("language.notification.meeting");
        $notificationCreate = trans('language.notification.invite_to_the_meeting') . ' ' . $request->name;
        $notificationUpdate = trans("language.notification.meeting") . " " . $request->name . " " . trans("language.notification.changed");

        $calendarMounth = '';
        if(!empty($request->project_id)){
            $project = Project::find($request->project_id);
            $notificationCreate .= "\n" . trans('language.project') . ": " . $project->name;
            $notificationUpdate .= "\n" . trans('language.project') . ": " . $project->name;
        }
        if(!empty($request->started_at)){
            $calendarMounth = \Carbon\Carbon::createFromFormat('d/m/Y H:i', $request->started_at)->format('m/Y');
            $notificationCreate .= "\n" . trans('language.notification.from') . ' ' . $request->started_at;
            $notificationUpdate .= "\n" . trans('language.notification.from') . ' ' . $request->started_at;
        }
        if(!empty($request->ended_at)){
            $notificationCreate .= " " . trans('language.notification.to') . " " . $request->ended_at;
            $notificationUpdate .= " " . trans('language.notification.to') . " " . $request->ended_at;
        }
        if(!empty($request->location)){
            $location = Resource::select(
                'group_resources.name as group_resource',
                'resources.name as resource',
                )->leftjoin('group_resources','group_resources.id','resources.group_id')
                ->where('resources.id',$request->location)
                ->first();
            $locationName  = $location->group_resource. ((isset($location->group_resource) && isset($location->resource) ) ? '-' : "").$location->resource;
            $notificationCreate .= "\n" . trans('language.notification.location') . ": " . $locationName;
            $notificationUpdate .= "\n" . trans('language.notification.location') . ": " . $locationName;
        }
        $listMember = [];
        foreach ($createMember as $member){
            if (intval($member)){
                $listMember[] = [
                    'user_id' => $member,
                    'event_id' => $event->id,
                ];
            }
        }
        EventParticipant::insert($listMember);

        $notificationHelper = new NotificationHelper();

        //send notification to new user
        $tokensCreate = $this->getTokensByUser($createMember);

        // $notificationHelper->sendNotificationDevice($tokensCreate,
        //     [
        //         'title' => $titleNotification,
        //         'content' => $notificationCreate,
        //         'click_action' => route('projects.calendar', ['time'=>$calendarMounth]),
        //     ]
        // );

        //send notification to old user
        $tokensUpdate = $this->getTokensByUser($updateMember);

        // $notificationHelper->sendNotificationDevice($tokensUpdate,
        //     [
        //         'title' => $titleNotification,
        //         'content' => $notificationUpdate,
        //         'click_action' => route('projects.calendar', ['time'=>$calendarMounth]),
        //     ]
        // );

        //send notification to delete user
        $tokensDelete = $this->getTokensByUser($deleteMember);

        // $notificationHelper->sendNotificationDevice($tokensDelete,
        //     [
        //         'title' => $titleNotification,
        //         'content' => trans("language.notification.meeting") . " " . $request->name . " " . trans("language.notification.canceled"),
        //         'click_action' => route('projects.calendar', ['time'=>date('m/Y')]),
        //     ]
        // );

        return $event;
    }

    function getPaticipants($eventId){
        $result = [];
        $participants = User::select('users.id','users.first_name','users.last_name','email')
            ->join('event_participants','users.id','event_participants.user_id')
            ->where('event_participants.event_id',$eventId)
            ->get();
        foreach ($participants as $participant){
            $result[] = [
                'id' => $participant->id,
                'full_name' => $participant->full_name,
                'email' => $participant->email,
                'avatar' => route('api.v1.user.avatar',['id'=>$participant->id])
            ];
        }
        return $result;
    }

    public function deleteEvent($event){

        $event->delete();

        // Send notification
        $members = EventParticipant::where('event_id',$event->id)->pluck('user_id')->toArray();
        $tokens = $this->getTokensByUser($members);

        $notificationHelper = new NotificationHelper();
        // $notificationHelper->sendNotificationDevice($tokens,
        //     [
        //         'title' => trans("language.notification.meeting"),
        //         'content' => trans("language.notification.meeting") . " " . $event->name . " " . trans("language.notification.canceled"),
        //         'click_action' => route('projects.calendar', ['time'=>date('m/Y')]),
        //     ]
        // );

        return $event;
    }

    public function getTokensByUser($users){
        return UserDevice::select('device_token')
            ->whereIn('user_id',$users)
            ->where('status',UserDevice::STATUS_ONLINE)
            ->pluck('device_token')
            ->toArray();
    }
}
