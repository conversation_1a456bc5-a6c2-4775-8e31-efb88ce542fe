<?php

namespace App\Http\Controllers\Project;

use App\Events\UpdateTask;
use App\Exports\TaskBugMultiSheetExport;
use App\Exports\TaskByChangeExport;
use App\Helpers\StringHelper;
use App\Http\Requests\TaskRequest;
use App\Logics\ProjectManager;
use App\Logics\TaskLogManager;
use App\Logics\TaskManager;
use App\Models\BugRange;
use App\Models\BugTag;
use App\Models\Project;
use App\Models\ProjectTask;
use App\Models\TaskAction;
use App\Models\TaskAttachment;
use App\Models\TaskLog;
use App\Models\TaskPriority;
use App\Models\TaskStatus;
use App\Models\TaskType;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\TaskBookmark;
use \Carbon\Carbon;
use Illuminate\Support\Facades\URL;
use App\Exports\TaskExport;
use App\Exports\TaskByProjectExport;
use App\Helpers\ZipHelper;
use App\Exports\TaskProblemMultiSheetExport;
use App\Logics\AttachmentManager;
use App\Traits\StorageTrait;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Route;
use App\Events\AssignTask;
use App\Models\ProjectSprint;
use App\Models\SiteSetting;
use App\Models\Sprint;

class TaskController extends Controller
{
    const PAGE_SIZE = 12;
    use StorageTrait;

    /**
     * Display a listing of the resource.
     *
     * @param Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $userId = Auth::id();

        // Get task list
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[$request->sort => $request->direction, 'id'=>'DESC']:['is_slow' => 'DESC','priority'=>'DESC','status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($request->all(), self::PAGE_SIZE, $orderBy);
        $filterHtml = $taskManager->getFilterHtml($request,['project', 'taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','from','to']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        //Get task is starting
        $taskCurrentOfUser = $taskManager->getActionTaskOfUser($userId);
        $taskStarting = null;
        if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type==TaskAction::START){
            $taskStarting = $taskCurrentOfUser->task_id ;
        }

        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $columnTaskView->value = json_decode($columnTaskView->value);
        return view('projects.tasks',[
            'tasks' => $tasks,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'projects' => $projects,
            'totalEstimatedTime' => $totalEstimatedTime,
            'filterHtml' => $filterHtml,
            'taskStarting' => $taskStarting,
            'columnTaskView' => $columnTaskView,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
        ]);
    }
        /**
     * Display a listing of the resource.
     *
     * @param Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function listTask(Request $request)
    {
        $userId = Auth::id();
        // Get task list
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[$request->sort => $request->direction, 'id'=>'DESC']:['is_slow' => 'DESC','priority'=>'DESC','status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($request->all(), self::PAGE_SIZE, $orderBy);
        $taskStarting = null;
        if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type==TaskAction::START){
            $taskStarting = $taskCurrentOfUser->task_id ;
        }
        $currentRouteName = Route::currentRouteName();
        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $columnTaskView->value = json_decode($columnTaskView->value);
        $html=  view('partials.list-tasks',[
            'tasks' => $tasks,
            'currentRouteName' => $currentRouteName ,
            'totalEstimatedTime' => $totalEstimatedTime,
            'taskStarting' => $taskStarting,
            'request' => $request,
            'columnTaskView' => $columnTaskView
        ])->render();
        return [
            'status' => Response::HTTP_OK,
            'html' => $html,
        ];
    }

    /**
     * Display the tasks of any project
     *
     * @param Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function listByProject(Request $request)
    {
        $userId = Auth::id();
        $params = $request->all();
        $params['project'] = $request->id;
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[$request->sort => $request->direction, 'id'=>'DESC']:['is_slow' => 'DESC','priority'=>'DESC','status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($params, self::PAGE_SIZE,$orderBy);
        $filterHtml = $taskManager->getFilterHtml($request,['taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','from','to','sprint_id']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get the project
        $project = Project::find($request->id, ['id', 'name']);

        // Get the Sprint
        $sprints = DB::table('project_sprints')
        ->where('project_id',$params['project'])
        ->get();
        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        //Get task is starting
        $taskCurrentOfUser = $taskManager->getActionTaskOfUser($userId);
        $taskStarting = null;
        if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type==TaskAction::START){
            $taskStarting = $taskCurrentOfUser->task_id ;
        }
        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $columnTaskView->value = json_decode($columnTaskView->value);

        return view('projectX.tasks', [
            'tasks' => $tasks,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'project' => $project,
            'projects' => $projects,
            'totalEstimatedTime' => $totalEstimatedTime,
            'filterHtml' => $filterHtml,
            'taskStarting' => $taskStarting,
            'columnTaskView' => $columnTaskView,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
            'sprints'=> $sprints
        ]);
    }
    /**
     * Display the tasks of any project
     *
     * @param Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function listTasksByProject(Request $request)
    {
        $userId = Auth::id();
        $params = $request->all();
        $params['project'] = $request->id;
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[$request->sort => $request->direction, 'id'=>'DESC']:['is_slow' => 'DESC','priority'=>'DESC','status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($params, self::PAGE_SIZE,$orderBy);

        // Get the project
        $project = Project::find($request->id, ['id', 'name']);

        //Get task is starting
        $taskCurrentOfUser = $taskManager->getActionTaskOfUser($userId);
        $taskStarting = null;
        if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type==TaskAction::START){
            $taskStarting = $taskCurrentOfUser->task_id ;
        }
        $currentRouteName = Route::currentRouteName();
        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $columnTaskView->value = json_decode($columnTaskView->value);

        $html=  view('partials.list-tasks',[
            'tasks' => $tasks,
            'currentRouteName' => $currentRouteName ,
            'totalEstimatedTime' => $totalEstimatedTime,
            'project' => $project,
            'taskStarting' => $taskStarting,
            'request' => $request,
            'columnTaskView' => $columnTaskView
        ])->render();
        return [
            'status' => Response::HTTP_OK,
            'html' => $html,
        ];
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $userId = Auth::id();
        $displayId = SiteSetting::displayTaskProjectId();
        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $projectManager = new ProjectManager();
        $hasRolePMLeader = true;
        if (isset($request->project_id) || old('project_id')){
            $projectId = old('project_id')?old('project_id'):$request->project_id;
            $hasRolePMLeader = (new ProjectManager())->UserHasGroupRolePMLeader($userId, $projectId);
            if($hasRolePMLeader){
                $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();
            }else{
                $taskTypes = TaskType::select(['id','name'])->where('id','!=',TaskType::PROBLEM)->orderby('sort_order')->get();
            }
            $projects = $projectManager->getProjectList($userId, null , $projectId);
        }else{
            $taskTypes = TaskType::select(['id','name'])->where('id','!=',TaskType::PROBLEM)->orderby('sort_order')->get();
            $projects = $projectManager->getProjectList($userId);
        }

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        $project = isset($request->project_id)?Project::find($request->project_id):null;

        return view('task.create', [
            'projects' => $projects,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'project' => $project,
            'displayId' => $displayId,
            'hasRolePMLeader' => $hasRolePMLeader
            ]);
    }
    

    /**
     * Store a newly created resource in storage.
     *
     * @param TaskRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(TaskRequest $request)
    {
        DB::beginTransaction();
        $request->description = StringHelper::escapeHtmlForSummernote($request->description);
        if(strpos($request->description, '<p>') === false)
            $request->description = '<p>                           '.$request->description.'</p>';
        try {
            $userId = Auth::id();
            $reason = $this->getReason($request);

            // Insert new project
            $task = new ProjectTask();
            $task->project_id = $request->project_id;
            $task->type = $request->type;
            $task->name = $request->name;
            $task->description = $request->description;
            $task->status = $request->status;
            $task->priority = $request->priority;
            $task->user_id = $request->user_id;
            $task->parent_task = $request->parent_task;
            $task->started_at = isset($request->started_at)?\Carbon\Carbon::createFromFormat('d/m/Y', $request->started_at)->format('Y-m-d 00:00:00'):$request->started_at;
            $task->ended_at = isset($request->ended_at)?\Carbon\Carbon::createFromFormat('d/m/Y', $request->ended_at)->format('Y-m-d 00:00:00'):$request->ended_at;
            $task->estimated_time = $request->estimated_time;
            $task->progress = $request->status==TaskStatus::TASK_STATUS_CLOSE?100:$request->progress;
            $task->created_by =  $userId;
            $task->updated_by =  $userId;
            $task->is_slow = (new TaskManager())->checkIsSlow($task)?ProjectTask::IS_SLOW:ProjectTask::NOT_SLOW;
            $task->limit = $request->has('limit');
            $task->key_member = $request->key_member;
            $task->sprint_id = $request->sprint_id;
            if(isset($request->parent_task)){
                $parent_task = ProjectTask::find($request->parent_task);
                $task->level = $parent_task->level + 1;
            } else {
                $task->level = 0;
            }
            if((new ProjectManager())->UserHasGroupRolePMLeader($userId, $request->project_id)){
                $task->score = $request->score;
            }
            if($task->type == TaskType::BUG){
                $bugTag = $this->getBugTag($request);
                $task->bug_tag_id = ($bugTag) ? $bugTag->id : null;
                
                $task->function_screen = $request->function_screen;
                $task->bug_classify_id = $request->bug_classify_id;
                $task->bug_reason_id = $request->bug_reason_id;
                $task->bug_range_id = $request->bug_range_id;
                $task->bug_severity_id = $request->bug_severity_id;
                $task->reason = $reason;
            }

            $task->save();

            if ($task->user_id) {
                (new TaskLogManager())->storeFirstAssignedUserId($task->id, $task->user_id);
            }
            // Save attachments
            $data = ProjectTask::find($task->id);
            $destinationPath = str_replace(['{project_id}', '{task_id}'], [$data->project_id, $task->id], TASK_ATTACHMENT_DIR).'/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_PROJECT_TASK,$request->documents, $request->descriptionDocument, $destinationPath, 'description');

            $taskManager = new TaskManager();
            $fieldsChange = $taskManager->getFieldsChange($task, true);

            if(isset($fieldsChange['description'])){
                $task = ProjectTask::find($task->id);
                $fieldsChange['description']["new_value"] = $task->description;
            }
            event(new updateTask(json_encode($fieldsChange),$task->id,TaskLog::ATTRIBUTE));

            // send mail to assigned user
            if (isset($task->user_id)) {
                $user = User::find($task->user_id);
                if (isset($user->email)) {
                    $websiteId = null;
                    if (env('USE_TENANT', false)) {
                        $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                    }
                    $taskInfor['userEmail'] = $user->email;
                    $taskInfor['url'] = route("task.show", ['id' => $task->id]);
                    $taskInfor['language_id'] = $user->language_id;
                    $taskInfor['title'] =  '#'.$task->id.' - '.$task->name;
                    event(new AssignTask($taskInfor, $websiteId));
                }
            }

            DB::commit();

            if (isset($_GET['parent_task'])) {
                return redirect()->route('task.show', ['id' => $request->parent_task])->with([
                    'status_succeed' => trans('message.create_task_succeed',['id' => $task->id]),
                    'task_id' => $task->id
                ]);
            } elseif (isset($_GET['project_id'])) {
                return back()->with([
                    'status_succeed' => trans('message.create_task_succeed',['id' => $task->id]),
                    'task_id' => $task->id
                ]);
            } else {
                return redirect()->route('task.create',['projectId'=>$request->project_id])->with([
                    'status_succeed' => trans('message.create_task_succeed',['id' => $task->id]),
                    'task_id' => $task->id
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $userId = Auth::id();
        $columns = [
            'projects.id as project_id',
            'projects.name as project_name',
            'projects.public as project_public',
            'project_tasks.id',
            'project_tasks.project_id',
            'project_tasks.name',
            'project_tasks.description',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.limit',
            'project_tasks.score',
            'task_types.name as type_name',
            'task_types.id as type_id',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
            'users.first_name',
            'users.last_name',
            'users.avatar',
            'users.id as user_id',
            'project_tasks.estimated_time',
            'project_tasks.progress',
            'project_tasks.parent_task',
            'userCreateTask.first_name as first_name_created',
            'userCreateTask.last_name as last_name_created',
            'userCreateTask.avatar as avatar_created',
            'userCreateTask.id as id_created',
            'project_tasks.updated_at',
            'project_tasks.created_at',
            'project_tasks.created_by',
            'project_tasks.key_member',
            'project_tasks.function_screen',
            'project_tasks.reason',
            'project_tasks.bug_classify_id',
            'project_tasks.bug_range_id',
            'project_tasks.bug_reason_id',
            'project_tasks.bug_severity_id',
            'project_tasks.bug_tag_id',
            'project_tasks.sprint_id',
            'project_sprints.name as sprint_name'
        ];
        $task = ProjectTask::select($columns)
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->leftJoin('users as userCreateTask','project_tasks.created_by','userCreateTask.id')
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->leftJoin('task_status','project_tasks.status','task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
            ->leftJoin('project_sprints', 'project_sprints.id', 'project_tasks.sprint_id')
            ->where('project_tasks.id',$id)
            ->first();

        // Check task exist
        if(empty($task)){
            return redirect()->route('projects.tasks');
        }

        //get task parent
        $taskParent = null;
        if(!empty($task->parent_task)){
            $taskParent = ProjectTask::select('task_types.name as type_name','project_tasks.name','project_tasks.id')
                ->checkUserPermission($userId)
                ->leftJoin('task_types','project_tasks.type','task_types.id')
                ->where('project_tasks.id',$task->parent_task)
                ->first();
        }

        //get task files
        $taskFiles = TaskAttachment::leftJoin('users','task_attachments.created_by','users.id')
            ->leftJoin('task_logs',function ($join) {
                $join->on('task_logs.id','=', 'task_attachments.related_id')
                     ->where('task_attachments.type', '=',  TaskAttachment::TYPE_TASK_LOG);
            })
            ->where(function($query) use($id){
                $query->where([
                    ['task_attachments.related_id',$id],
                    ['task_attachments.type', TaskAttachment::TYPE_PROJECT_TASK]
                ])
                ->orWhere([
                    ['task_logs.task_id',$id],
                    ['task_attachments.type', TaskAttachment::TYPE_TASK_LOG]
                ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();
        //get list task child
        $taskManager = new TaskManager();
        $taskChilds = $taskManager->getTaskChildList($task->id);

        // Get list projects
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId ,null, $task->project_id);

        //Get list task_status
        $chooseStatus = old('status')?old('status'):$task->status_id;
        $taskStatuses = TaskStatus::withTrashed()->select(['id','name','deleted_at'])
            ->where(function($query) use ($chooseStatus){
                $query->where('id',$chooseStatus)
                ->orWhereNull('deleted_at');
            })
            ->orderby('sort_order');

        $taskChildsNotClose = ProjectTask::where('status','!=',TaskStatus::TASK_STATUS_CLOSE)->where('parent_task',$task->id)->count();
        if ($taskChildsNotClose > 0){
            $taskStatuses = $taskStatuses->where('id','!=',TaskStatus::TASK_STATUS_CLOSE)->orderby('sort_order')->get();
        }else{
            $taskStatuses = $taskStatuses->get();
        }

        //Get list task_type
        $projectId = old('project_id')?old('project_id'):$task->project_id;
        $permissionEditTaskProblem = (new ProjectManager())->UserHasGroupRolePMLeader($userId, $projectId);
        $chooseType = old('type')?old('type'):$task->type_id;
        $taskTypes = TaskType::withTrashed()->select(['id','name','deleted_at'])->orderby('sort_order');
        if(!$permissionEditTaskProblem){
            $taskTypes = $taskTypes->where('id','!=',TaskType::PROBLEM);
        }
        $taskTypes = $taskTypes->where(function($query) use($chooseType){
             $query->where('id',$chooseType)
                ->orWhereNull('deleted_at');
        })->get();

        // Get list task_priorities
        $choosePriority = old('priority')?old('priority'):$task->priority_id;
        $taskPriorities = TaskPriority::withTrashed()->select(['id','name','deleted_at'])
        ->where('id',$choosePriority)
        ->orWhereNull('deleted_at')
        ->orderby('sort_order')
        ->get();

        //Get project
        $project = Project::find($task->project_id);

        $taskLogManager = new TaskLogManager();
        //Get logs
        $logs = $taskLogManager->getListLogs($task->id,TaskLog::ATTRIBUTE);
      
        //Get notes
        $notes = $taskLogManager->getListLogs($task->id,TaskLog::NOTE);

        // Get history
        $histories = $taskLogManager->getListLogs($task->id,[TaskLog::NOTE,TaskLog::ATTRIBUTE]);

        //Get Task current
        $taskCurrentOfUser = $taskManager->getActionTaskOfUser($userId);
        $action = null;
        if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type==TaskAction::START && $taskCurrentOfUser->task_id==$id){
            $action = $task->user_id == $userId? TaskAction::STOP:null;
        }else if(!isset($taskCurrentOfUser) || $taskCurrentOfUser->type==TaskAction::STOP){
            $action = $task->user_id == $userId? TaskAction::START:null;
        }
        // Get permision edit task
        $permissionEditTask = true;
        if (!$permissionEditTaskProblem && $task->type_id == TaskType::PROBLEM){
            $permissionEditTask = false;
        }
        $displayId = SiteSetting::displayTaskProjectId();
        // Get the Sprint
        $sprints = ProjectSprint::query()
        ->where(function ($query) use ($task){
            $query->where('project_sprints.id', $task->sprint_id)
                ->orWhere('project_sprints.status', '!=', ProjectTask::SPRINT_COMPLETE);
        })
        ->where('project_id', $project->id)
        ->get();
        return view('task.view',[
            'task' => $task,
            'taskParent' => $taskParent,
            'taskChilds' => $taskChilds,
            'projects' => $projects,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'project' => $project,
            'logs' => $logs,
            'notes' => $notes,
            'histories' => $histories,
            'action' => $action,
            'taskFiles' => $taskFiles,
            'permissionEditTask' => $permissionEditTask,
            'displayId' => $displayId,
            'hasRolePMLeader' => $permissionEditTaskProblem,
            'sprints' => $sprints
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\TaskRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(TaskRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            // Get the id of the user who is logged in
            $userId = Auth::id();
            $reason = $this->getReason($request);

            // Check task exist
            $task = ProjectTask::select('project_tasks.*')->checkUserPermission($userId)->find($id);
            $oldProject = $task->project_id;
            $oldParent = $task->parent_task;
            if ($task == null) {
                return redirect()->route('projects.tasks');
            }
            $request->description = StringHelper::escapeHtmlForSummernote($request->description);
            if($request->description != null && strpos($request->description, '<p>') === false)
                $request->description = '<p>                           '.$request->description.'</p>';
            $oldTaskUserId = $task->user_id;
            // Get number child of task
            $numberChild = ProjectTask::where('parent_task',$task->id)->count();

            // Update task
            $task->project_id = $request->project_id;
            $task->type = $request->type;
            $task->name = $request->name;
            $task->description = $request->description;
            $task->status = $request->status;
            $task->priority = $request->priority;
            $task->sprint_id = $request->sprint_id;
            if ($request->status == TaskStatus::TASK_STATUS_CLOSE) {
                if ($task->user_id == $request->user_id) {
                    $firstAssignedUserId = (new TaskLogManager())->getFirstAssignedUserId($task->id);
                    if ($firstAssignedUserId) {
                        $task->user_id = $firstAssignedUserId;
                    }
                }
            }
            if (empty($firstAssignedUserId)) {
                $task->user_id = $request->user_id;
            }
            if(isset($request->parent_task)){
                if($request->parent_task != $task->parent_task){
                    $parent= ProjectTask::find($request->parent_task);
                    $level = $parent->level + 1;
                    $task->level = $level;
                    (new TaskManager())->updateLevelTaskChild([$task->id],$level);
                }
            } else {
                $task->level = 0;
                (new TaskManager())->updateLevelTaskChild([$task->id],0 );
            }
            $task->parent_task = $request->parent_task;
            if ($numberChild == 0 || ($task->created_by == Auth::id() && $request->has('limit'))){
                $task->started_at = isset($request->started_at)?\Carbon\Carbon::createFromFormat('d/m/Y', $request->started_at)->format('Y-m-d 00:00:00'):$request->started_at;
                $task->ended_at = isset($request->ended_at)?\Carbon\Carbon::createFromFormat('d/m/Y', $request->ended_at)->format('Y-m-d 00:00:00'):$request->ended_at;
                if ($numberChild == 0){
                    $task->estimated_time = $request->estimated_time;
                    $task->progress = $request->status==TaskStatus::TASK_STATUS_CLOSE?100:$request->progress;
                }
            }else{
                $taskChildsNotClose = ProjectTask::where('status','!=',TaskStatus::TASK_STATUS_CLOSE)->where('parent_task',$task->id)->count();
                if($taskChildsNotClose > 0 && $request->status==TaskStatus::TASK_STATUS_CLOSE){
                    return redirect()->route('task.show',['id' => $id])->with([
                        'status_failed' => trans('message.can_not_close_task')
                    ]);
                }elseif ($taskChildsNotClose == 0 && $request->status==TaskStatus::TASK_STATUS_CLOSE){
                    $task->progress = 100;
                }
            }
            if((new ProjectManager())->UserHasGroupRolePMLeader($userId, $request->project_id)){
                $task->score = $request->score;
            }
            $task->updated_by =  $userId;
            // check key member change
            if(isset($task->key_member) && $task->key_member != $request->key_member){
                $task->warning = true;
            }
            $task->key_member = $request->key_member;

            if($task->type == TaskType::BUG){
                $bugTag = $this->getBugTag($request);
                $task->bug_tag_id = ($bugTag) ? $bugTag->id : null;
                
                $task->function_screen = $request->function_screen;
                $task->bug_classify_id = $request->bug_classify_id;
                $task->bug_reason_id = $request->bug_reason_id;
                $task->bug_range_id = $request->bug_range_id;
                $task->bug_severity_id = $request->bug_severity_id;
                $task->reason = $reason;
            }

            $taskManager = new TaskManager();
            $fieldsChange = $taskManager->getFieldsChange($task);

            // if user_id change that the task is starting, it will stop
            if (isset($fieldsChange['user_id'])){
                $oldUserId = $task->getOriginal('user_id');
                $userTaskAction = $taskManager->getActionTaskOfUser($oldUserId);
                if (isset($userTaskAction) && $userTaskAction->task_id == $id && $userTaskAction->type==TaskAction::START){
                    $taskAction = new TaskAction();
                    $taskAction->task_id = $id;
                    $taskAction->user_id = $oldUserId;
                    $taskAction->type = TaskAction::STOP;
                    $taskAction->action_at = date('Y-m-d H:i:s');
                    $taskAction->save();
                }
            }

            $task->is_slow = (new TaskManager())->checkIsSlow($task)?ProjectTask::IS_SLOW:ProjectTask::NOT_SLOW;
            if($task->created_by == Auth::id()){
                $task->limit = $request->has('limit');
            }

            $task->save();
            // Update progress, estimated_time, started_at, ended_at for parent task old
            if (isset($fieldsChange['parent_task']) && $oldParent!=null){
                $taskManager->updateParentTask($oldParent);
            }
            // Update progress, estimated_time, started_at, ended_at for project old
            if (isset($fieldsChange['project_id'])){
                (new ProjectManager())->updateProject($oldProject);
            }

            // Save attachments
            $data = ProjectTask::find($task->id);
            $destinationPath = str_replace(['{project_id}', '{task_id}'], [$data->project_id, $task->id], TASK_ATTACHMENT_DIR).'/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_PROJECT_TASK,$request->documents, $request->descriptionDocument, $destinationPath, 'description');

            if ($request->user_id) {
                $taskLogManager = new TaskLogManager();
                $firstAssignedUserId = $taskLogManager->getFirstAssignedUserId($task->id);
                if (!$firstAssignedUserId) {
                    $taskLogManager->storeFirstAssignedUserId($task->id, $request->user_id);
                }
            }

            if(isset($fieldsChange['description'])){
                $task = ProjectTask::find($task->id);
                $fieldsChange['description']["new_value"] = $task->description;
            }

            //call event updateTask
            if (!empty($fieldsChange)){
                event(new updateTask(json_encode($fieldsChange),$task->id,TaskLog::ATTRIBUTE));
            }

            // send mail to assigned user
            if (isset($task->user_id) && ($task->user_id != $oldTaskUserId)) {
                $user = User::find($task->user_id);
                if (isset($user->email)) {
                    $websiteId = null;
                    if (env('USE_TENANT', false)) {
                        $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                    }
                    $taskInfor['userEmail'] = $user->email;
                    $taskInfor['language_id'] = $user->language_id;
                    $taskInfor['url'] = route("task.show", ['id' => $task->id]);
                    $taskInfor['title'] =  '#'.$task->id.' - '.$task->name;
                    event(new AssignTask($taskInfor, $websiteId));
                }
            }

            DB::commit();
            return redirect()->route('task.show',['id' => $id])->with([
                'status_succeed' => trans('message.edit_succeed')
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->route('task.show',['id' => $id])->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $userId = Auth::id();
        $task = ProjectTask::select('project_tasks.*')
            ->checkUserPermission($userId)
            ->find($id);

        // Return error message if task not exist
        if ($task == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.task_not_exist'),
                ]
            ];
        }

        // Return error message if task started
        $taskManager = new TaskManager();
        $userTaskAction = $taskManager->getActionTaskOfUser($task->user_id);
        if (isset($userTaskAction) && $userTaskAction->task_id == $id && $userTaskAction->type == TaskAction::START){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.task_is_starting'),
                    'text' => ''
                ]
            ];
        }

        // get number child of task
        $taskManager = new TaskManager();
        $numberChild = $taskManager->getNumberChild($id);
        $permissionDeleteTaskProblem = (new ProjectManager())->UserHasGroupRolePMLeader($userId, $task->project_id);

        //if task has children, do not delete
        if($numberChild > 0 || (!$permissionDeleteTaskProblem && $task->type==TaskType::PROBLEM)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.can_not_delete_task'),
                    'msg' => ''
                ]
            ];
        }
        $projectId = $task->project_id;
        $task->delete();
        if (app('router')->getRoutes()->match(app('request')->create(URL::previous()))->getName() == 'task.show'){
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_task_succeed') ,
                ],
                'redirect' => route('project.tasks',['id'=> $projectId ]),
            ];
        }
        else{
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_task_succeed') ,
                ],
                'redirect' => route('projects.tasks'),
            ];
        }

    }

    /**
     * Get task
     *
     * @param Illuminate\Http\Request $request
     * @param $taskId
     */
    public function ajaxGetTasks(Request $request){
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q);
        $projectId = $request->projectId;
        $item = [];

        $tasks = ProjectTask::select('task_types.name as type_name','project_tasks.name','project_tasks.id')
            ->checkUserPermission($userId)
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->where(function($q) use ($keyword_search) {
                $q->where('project_tasks.name', 'LIKE', '%'.$keyword_search.'%')
                    ->orwhere('project_tasks.id', 'LIKE', '%'.$keyword_search.'%');
            })
            ->where('project_tasks.project_id',$projectId)
            ->get();

        if ($tasks != null){
            foreach($tasks as $task){
                $item[] = [
                    'id' => $task->id,
                    'name' => $task->name,
                    'type' => $task->type_name,
                ];
            }
        }
        $result= [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($tasks)
        ];

        return response()->json($result);
    }

    /**
     * Get sprints
     *
     * @param Illuminate\Http\Request $request
     * @param $taskId
     */
    public function ajaxGetSprints(Request $request){
        $stringHelper = new StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q);
        $projectId = $request->projectId;
        $item = [];

        $sprints = Sprint::select('project_sprints.name as sprint_name','project_sprints.id as sprint_id')
            ->where(function($q) use ($keyword_search) {
                $q->where('project_sprints.name', 'LIKE', '%'.$keyword_search.'%')
                    ->orwhere('project_sprints.id', 'LIKE', '%'.$keyword_search.'%');
            })
            ->where('project_sprints.project_id',$projectId)
            ->get();

        if ($sprints != null){
            foreach($sprints as $sprint){
                $item[] = [
                    'id' => $sprint->sprint_id,
                    'name' => $sprint->sprint_name,
                ];
            }
        }
        $result= [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($sprints)
        ];

        return response()->json($result);
    }

    /**
     * Unlink task child
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function unlinkChild($id)
    {
        $userId = Auth::id();
        $task = ProjectTask::select('project_tasks.*')
            ->checkUserPermission($userId)
            ->find($id);

        // Return error message if task not exist
        if ($task == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.task_not_exist'),
                    'msg' => ''
                ]
            ];
        }
        $task->parent_task = null;

        $taskManager = new TaskManager();
        $fieldsChange = $taskManager->getFieldsChange($task);

        $task->save();

        //call event updateTask
        if (!empty($fieldsChange)){
            event(new updateTask(json_encode($fieldsChange),$task->id,TaskLog::ATTRIBUTE));
        }

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('language.success') ,
                'msg' => trans('message.unlink_task_child_success')
            ],
        ];
    }

    /**
     * Update action
     *
     * @param Illuminate\Http\Request $request
     * @param  int  $id
     */
    public function action(Request $request,$id){
        $userId = Auth::id();
        $task = ProjectTask::select('project_tasks.*')
            ->checkUserPermission($userId)
            ->find($id);
        if (!isset($task)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.task_not_exist')
                ]
            ];
        }

        $type = $request->type;
        //Get Task current
        $taskManager = new TaskManager();
        $userTaskAction = $taskManager->getActionTaskOfUser($userId);

        if ($userId!=$task->user_id){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.not_permission')
                ]
            ];
        }

        if ($type == TaskAction::STOP && (!isset($userTaskAction) || $userTaskAction->task_id!=$id || $userTaskAction->type==TaskAction::STOP)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.task_has_not_yet_started')
                ]
            ];
        }

        if($type == TaskAction::START && isset($userTaskAction) && $userTaskAction->type==TaskAction::START){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.another_task_is_not_over_yet')
                ]
            ];
        }

        $taskAction = new TaskAction();
        $taskAction->task_id = $id;
        $taskAction->user_id = $userId;
        $taskAction->type = $type;
        $taskAction->action_at = date('Y-m-d H:i:s');
        $taskAction->save();

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('language.success')
            ],
            'type' => $type
        ];
    }

    public function bookmark($taskId){
        $userId = Auth::id();
        $columns = [
            'project_tasks.id',
            'project_tasks.name',
            'project_tasks.updated_at',
            'projects.name as project_name',
            'projects.id as project_id',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'users.avatar'
        ];
        $task  = ProjectTask::select($columns)
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->where("project_tasks.id", $taskId)
            ->first();

        if (!isset($task)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.task_not_exist')
                ]
            ];
        }

        $taskBookmark = TaskBookmark::where('task_id', $taskId)
                        ->where('user_id', $userId);
        $pin = false;
        $original_title = trans('language.bookmark_task');

        if ($taskBookmark->count()){
            $taskBookmark->delete();
        } else {
            TaskBookmark::create([
                'task_id' => $taskId,
                'user_id' => $userId,
                'created_by' => $userId,
                'created_at' => Carbon::now(),
            ]);
            $pin = true;
            $original_title = trans('language.remove_mark_task');
        }

        //Get html
        $html = view('partials.task-pin-sidebar',[
            'currentUrl' => URL::previous(),
            'bookmarkedTask' => $task
        ])->render();

        return [
            'status' => Response::HTTP_OK,
            'pin' => $pin,
            'taskId' => $taskId,
            'original_title' => $original_title,
            'html' => $html,
        ];
    }

    /**
     *
     * export list tasks to excel
     *
     * @param  Request $request
     * @return Responses
     */
    public function exportTasks(Request $request){
        // Get task list
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[]:['status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($request->all(), null, $orderBy);

        $pathPrefix = env('USE_TENANT', false) ? app(\Hyn\Tenancy\Website\Directory::class)->path() : '';
        $path = TEMP_DIR . '/'. Str::random(25).'.xlsx';
        $nameFile = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
        // Export task list with TaskType::CHANGE
        if(isset($request->type) && in_array(TaskType::CHANGE, $request->type) && count($request->type) == 1){

            $pathZip =  TEMP_DIR . '/'. Str::random(25);
            $result = (new TaskManager())->listTaskWithTypeChange($tasks, $pathZip);
            $nameFileExcel = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
            Excel::store(new TaskByChangeExport($result,$totalEstimatedTime), $pathPrefix.$pathZip.'/'.$nameFileExcel);

            $path = TEMP_DIR . '/'. Str::random(25).'.zip';
            $nameFile = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.zip';
            $zipHelper = new ZipHelper();
            $zipHelper->zip($pathZip, $path);
        }
        // Export task list
        else if (isset($request->type) && in_array(TaskType::PROBLEM, $request->type) && count($request->type) == 1){
            $data = (new TaskManager())->convertDataExportProblem($tasks);
            $users = User::select(['id', 'first_name','last_name'])->get()->toArray();
            Excel::store(new TaskProblemMultiSheetExport($data,$users), $pathPrefix.$path);
        }
        // Export bug list
        else if(isset($request->type) && in_array(TaskType::BUG, $request->type) && count($request->type) == 1){
            $users = User::select(DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'), 'users.id')->pluck('user_name','id');
            $result = (new TaskManager())->listTaskBug($tasks,  $users);
            Excel::store(new TaskBugMultiSheetExport($result), $pathPrefix.$path);
        }
        else{
            $sumEstimate = new Collection();
            $sumEstimate->id = null;
            $sumEstimate->project_name = null;
            $sumEstimate->type_name = null;
            $sumEstimate->status_name = null;
            $sumEstimate->priority_name = null;
            $sumEstimate->estimated_time = $totalEstimatedTime;
            $sumEstimate->name = null;
            $sumEstimate->user_name = null;
            $sumEstimate->updated_at = null;
            $tasks->push($sumEstimate);

            Excel::store(new TaskExport($tasks), $pathPrefix.$path);
        }
        return Storage::disk(FILESYSTEM)->download($path, $nameFile);
    }

        /**
     *
     * export list tasks by project to excel
     *
     * @param  Request $request
     * @return Responses
     */
    public function exportTasksByProject(Request $request){
        $params = $request->all();
        $params['project'] = $request->id;
        $taskManager = new TaskManager();
        $orderBy = isset($request->sort)?[]:['status'=>'ASC','id'=>'DESC'];
        [$tasks, $totalEstimatedTime] = $taskManager->getTaskList($params, null,$orderBy);

        $pathPrefix = env('USE_TENANT', false) ? app(\Hyn\Tenancy\Website\Directory::class)->path() : '';
        $path = TEMP_DIR . '/'. Str::random(25).'.xlsx';
        $nameFile = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';

        // Export task list with TaskType::CHANGE
        if(isset($request->type) && in_array(TaskType::CHANGE, $request->type) && count($request->type) == 1){
            $pathZip =  TEMP_DIR . '/'. Str::random(25);

            $result = (new TaskManager())->listTaskWithTypeChange($tasks, $pathZip);
            $nameFileExcel = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
            Excel::store(new TaskByChangeExport($result,$totalEstimatedTime), $pathPrefix.$pathZip.'/'.$nameFileExcel);

            $path = TEMP_DIR . '/'. Str::random(25).'.zip';
            $nameFile = trans('language.statistical_task').'_'.Carbon::now()->format('d-m-Y-H-i').'.zip';
            $zipHelper = new ZipHelper();
            $zipHelper->zip($pathZip , $path);
        }
        // Export task list
        else if (isset($request->type) && in_array(TaskType::PROBLEM, $request->type) && count($request->type) == 1){
            $data = (new TaskManager())->convertDataExportProblem($tasks);
            $users = User::select(['id', 'first_name','last_name'])->get()->toArray();
            Excel::store(new TaskProblemMultiSheetExport($data,$users), $pathPrefix.$path);
        }
        // Export bug list
        else if(isset($request->type) && in_array(TaskType::BUG, $request->type) && count($request->type) == 1){
            $result = (new TaskManager())->listTaskBug($tasks);
            Excel::store(new TaskBugMultiSheetExport($result), $pathPrefix.$path);
        }
        else {
            $sumEstimate = new Collection();
            $sumEstimate->id = null;
            $sumEstimate->project_name = null;
            $sumEstimate->type_name = null;
            $sumEstimate->status_name = null;
            $sumEstimate->priority_name = null;
            $sumEstimate->estimated_time = $totalEstimatedTime;
            $sumEstimate->name = null;
            $sumEstimate->user_name = null;
            $sumEstimate->updated_at = null;
            $tasks->push($sumEstimate);
            Excel::store(new TaskByProjectExport($tasks), $pathPrefix.$path);
        }
        return Storage::disk(FILESYSTEM)->download($path, $nameFile);
    }

    //get reason
    protected function getReason($request)
    {
        $data = [];
        $data[]= [
            'reasons' => $request->reasons,
            'fix' => $request->fix,
            'range' => $request->range,
        ];

        return $data;
    }

    /**
     * get the bugtag, if not, will create a new one
     * @param $request
     * @return BugTag|null
     */
    private function getBugTag($request) {
        $bugTag = null;
        // Get or insert tag
        $bugTagID = $request->bug_tag_id;
        if($bugTagID) {
            // find by id case data is numeric
            if(is_numeric($bugTagID)) {
                $bugTag = BugTag::where('id', $bugTagID)
                    ->first();
            }
            // search by name if searching by number has no results or the data is a string
            if(!$bugTag) {
                $bugTag = BugTag::where('name', $bugTagID)
                    ->first();
            }
            // case no data in db
            if(!$bugTag) {
                $bugTag = new BugTag();
                $bugTag->name = $bugTagID;
                $bugTag->save();
            }
        }
        return $bugTag;
    }

    public function getTagUser(Request $request)
    {
        $userId = Auth::id();
        $projectId = $request->projectId;
        $arr = [];
        $users = null;
        $stringHelper = new StringHelper();

        if($projectId) {
            $project = Project::select('projects.id')
            ->checkUserPermission($userId)
            ->find($projectId);
            // Get list member in project
            $users = $project->members()->select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
                ->leftjoin('positions', 'positions.id','users.position_id')
                ->distinct();
        } else {
            $users = User::select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
            ->leftJoin('positions','positions.id','users.position_id');
            if ($request->has('deleted') && $request->deleted) {
                $users = $users->onlyTrashed();
            }
        }
        if (isset($request->keyword)) {
            $keyword = $stringHelper->formatStringWhereLike($request->keyword);
            $users->where(DB::raw('CONCAT_WS(" ",first_name,last_name)'), 'LIKE', "%$keyword%");
        };

        $users = $users->get();
        foreach($users as $index => $user){
            $arr[$index] = [
                $user->id,
                $user->full_name,
                $user->email,
                route('user.avatar',['id' => $user->id]),
                $user->position
            ];
        }
        return $arr;
    }
}
