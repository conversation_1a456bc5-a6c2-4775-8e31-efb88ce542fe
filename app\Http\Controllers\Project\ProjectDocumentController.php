<?php

namespace App\Http\Controllers\Project;

use App\Logics\UserManager;
use App\Models\ProjectRole;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Logics\ProjectDocumentManager;
use App\Models\ProjectDocument;
use App\Traits\StorageTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;
use App\Helpers\RequestHelper;
use App\Http\Requests\ProjectDocumentRequest;
use App\Logics\ProjectManager;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ProjectDocumentController extends Controller
{
    use StorageTrait;
    /**
     * view file project documents
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function index(Request $request, $id)
    {
        $userId = Auth::id();
        $params = (new RequestHelper())->getParamsFromRequest($request);
        //check if current user is project manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($id, $userId);
        if($project == null)
            return redirect()->route('project.index');

        // Get data project document
        $projectDocumentManager = new ProjectDocumentManager;
        $projectDocuments = $projectDocumentManager->getAllDocument($request->search_document,$id);
        return view('projectX.documents', [
            'projectDocument'=> $projectDocuments,
            'project'=> $project,
            'isManager' => $isManager
        ]);
    }

    /**
     * Save document
     *
     * @param Request $request
     * @return Response
     */
    public function store(ProjectDocumentRequest $request)
    {
        $userId = Auth::id();
        DB::beginTransaction();
        try{
            $documents = $request->documents;
            $projectId = $request->projectId;

            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if($project == null)
                return redirect()->route('project.index');

            foreach( $documents as $key => $document ){
                // Move document file from temporary directory to document directory
                $document = json_decode($document, true);
                $fileName = basename($document['file_path']);
                $destinationPath = str_replace('{project_id}', $projectId, DOCUMENT_DIR).'/'.$fileName;
                $fileSize = $this->moveFile($document['file_path'],$destinationPath);

                // Save document
                $projectDocument = new ProjectDocument();
                $projectDocument->project_id  = $projectId;
                $projectDocument->file_size = $fileSize;
                $projectDocument->file_name = $document['file_name'];
                $projectDocument->file_path = $destinationPath;
                $projectDocument->created_by = $userId;
                $projectDocument->created_at = Carbon::now();
                $projectDocument->description = $request->descriptionDocument[$key];
                $projectDocument->save();
            }
            DB::commit();
            return redirect()->route('project.document.index', ['id'=>$projectId])->with([
                'status_succeed' => trans('message.add_documents_success')
            ]);

        } catch (\Exception $e){
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.add_documents_failed'),
            ]);
        }
    }

    /**
     * download file
     * @param Request $request
     * @return Response
     */
    public function downloadFile(Request $request)
    {
        $path = ProjectDocument::where('id',$request->id)->first();

        if ($path == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.document_not_exist'),
                ]
            ];
        }
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($path->project_id, Auth::id());
        if($project == null)
            return redirect()->route('project.index');

        if (!Storage::disk(FILESYSTEM)->exists($path->file_path)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->download($path->file_path, $path->file_name);
    }

    /**
     * delete document by id
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request)
    {
        // Check project permission
        $projectManager = new ProjectManager;
        //get user id
        $userId = auth()->id();
        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$request->projectId,ProjectRole::ProjectManager);
        $project = $projectManager->checkProjectPermission($request->projectId, Auth::id());
        if($project == null)
            return response()->json([
                'status' => 403,
            ], Response::HTTP_FORBIDDEN);

        $data = ProjectDocument::findOrFail($request->id);
        if ($data == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.document_not_exist'),
                ]
            ];
        }
        // Check user have permission to delete document
        if($userId != $data->created_by && !$isManager){
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'msg' => [
                    'title' => trans('message.document_not_allowed_to_delete'),
                ]
            ];
        }
        $this->deleteFile($data->file_path);
        $data->delete();

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_document_succeed') ,
            ],
        ];
    }

    /**
     * Thumbtack project document
     *
     * @param int $id
     */

    public function thumbtack($projectId, $id){
        $document = ProjectDocument::find($id);
        if ($document){
            $userId = Auth::id();

            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);

            if ($project){
                if (!$document->pinned_at){
                    $document->pinned_at = Carbon::now();
                } else {
                    $document->pinned_at = null;
                }
                $document->save();
            }
        }
        return back();
    }
}
