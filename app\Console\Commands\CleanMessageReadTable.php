<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CleanMessageReadTable extends Command
{
    protected $signature = 'migrate:clean-message-read';
    protected $description = 'Migrate message_read table to update thread_id and keep only one record per user, conversation, and thread_id';

    public function handle()
    {
        $this->info('Removing duplicate message_read records...');

        DB::statement("
            DELETE mr FROM message_read mr
            JOIN (
                SELECT user_id, conversation_id, COALESCE(thread_id, -1) AS group_thread_id,
                       MAX(message_id) AS max_message_id
                FROM message_read
                GROUP BY user_id, conversation_id, group_thread_id
            ) latest
            ON mr.user_id = latest.user_id
            AND mr.conversation_id = latest.conversation_id
            AND COALESCE(mr.thread_id, -1) = latest.group_thread_id
            AND mr.message_id < latest.max_message_id
        ");
        
        $this->info('Done removing duplicate message_read records!');
        return 0;
    }
}