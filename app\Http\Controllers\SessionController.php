<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;

class SessionController extends Controller
{

    /**
     * Back to previous url which is stored in session
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function backToUrl(Request $request)
    {
        // Get previous url
        $key = $request->key;
        if (session($key)) {
            $redirect_url = session($key);
            session()->forget($key);
        } else {
            $redirect_url = $request->default_redirect_url;
        }

        return redirect()->to($redirect_url);
    }
}
