<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class District
 * @property string $user_id
 * @property date $month
 * @property string $hours
 */
class UserRemainingLeaveDay extends Model
{
    public $timestamps = false;

    protected $table = 'user_remaining_leave_days';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'month',
        'hours'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [

    ];

    /**
     * Get user for the user remaining leave day
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function users()
    {
    	return $this->belongsTo('App\User');
    }
}
