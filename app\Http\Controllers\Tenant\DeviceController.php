<?php

namespace App\Http\Controllers\Tenant;

use App\Models\Tenant\Devices;
use Hyn\Tenancy\Environment;
use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\DeviceRequest;
use App\Logics\TenantManager;

class DeviceController extends Controller
{
    /**
     * Update device
     * @param Request $request
     * @return RedirectResponse
     */
    public function update(DeviceRequest $request)
    {
        try {
            // Check tenant exists
            $tenantManager = new TenantManager();
            $tenant = $tenantManager->checkTenantExists($request->tenant_id);

            if (empty($tenant)) {
                return redirect()->route('tenant.index')->with([
                    'status_failed' => trans('message.company_not_exist')
                ]);
            }
            $method = 'store';
            if ($request->input('id') != null) {
                $method = 'update';
            }

            app(Environment::class)->tenant($tenant);
            if ($method == 'store') {
                $device = new Devices();
            } else {
                $device = Devices::find($request->id);
                if (empty($device)) {
                    return back()->with([
                        'status_failed' => trans('message.device_not_exist')
                    ]);
                }
            }

            DB::beginTransaction();
            $device->name = $request->device_name;
            $device->mac_address = $request->mac_address;
            $device->device_id = hash('sha256', $request->mac_address);
            $device->local_ip_address = $request->local_ip_address;
            $device->public_ip_address = $request->public_ip_address;
            $device->website_id = $request->tenant_id;
            $device->save();
            DB::commit();

            if ($method == 'store') {
                return redirect()->route('tenant.edit', ['id' => $tenant->id])->with('status_succeed', trans('message.create_device_succeed'));
            } else {
                return redirect()->route('tenant.edit', ['id' => $tenant->id])->with('status_succeed', trans('message.update_device_succeed'));
            }
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    public function destroy(Request $request)
    {
        try {
            // Check tenant exists
            $tenantManager = new TenantManager();
            $tenant = $tenantManager->checkTenantExists($request->tenant_id);
            if (empty($tenant)) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'msg' => [
                        'title' => trans('language.failure'),
                        'text' => trans('message.company_not_exist'),
                    ],
                ];
            }

            $device = Devices::find($request->id);
            if (empty($device)) {
                return [
                    'status' => Response::HTTP_BAD_REQUEST,
                    'msg' => [
                        'title' => trans('language.failure'),
                        'text' => trans('message.device_not_exist'),
                    ],
                ];
            }

            DB::beginTransaction();
            $device->delete();
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success'),
                    'text' => trans('message.delete_device_succeed'),
                ],
            ];
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollBack();
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.server_error'),
                ],
            ];
        }
    }
}
