<?php

namespace App\Http\Requests\Admin\SiteSetting;

use Illuminate\Foundation\Http\FormRequest;

class CalendarRequest extends FormRequest
{
      /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'start_morning_time' => 'required|date_format:H:i:s',
            'end_morning_time' => 'required|date_format:H:i:s',
            'start_afternoon_time' => 'required|date_format:H:i:s',
            'end_afternoon_time' => 'required|date_format:H:i:s',
        ];
    }
}
