<?php

namespace App\Models;

/**
 * Class Language
 * @property integer $id
 * @property string $name
 */
class Language extends Model
{
    /**
     * @var string
     */
    protected $table = 'languages';

    const DEFAULT_LANGUAGE_ID = 1;
    const ENGLISH_LANGUAGE = 2;
    const JAPAN_LANGUAGE = 3;

    const COUNT_LANGUAGE = 3;

    const FLAG_ACTIVE = 1;

    const JAPAN_CODE = 'ja';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'code',
        'name',
        'display_name',
        'flag',
        'sort_no',
        'is_active'
    ];

    /**
     * Scope query active.
     *
     * @param $query
     * @return mixed
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', self::FLAG_ACTIVE);
    }
}
