<?php

namespace App\Http\Requests;

use App\Helpers\StringHelper;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Http\Response;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'old_password' => ['required'],
            'new_password' => ['required', 'regex:/^[\S]+$/', 'min:8', 'max:20'],
            'confirm_password' => ['required', 'same:new_password'],
        ];
    }

    /**
     * @return array
     */
    public function attributes()
    {
        return [
            'old_password' => __('message.old_password_attribute'),
            'new_password' => __('message.new_password_attribute'),
            'confirm_password' => __('message.confirm_password_attribute'),
        ];
    }


    protected function failedValidation(Validator $validator)
    {
        $helper = new StringHelper();
        $data = [
            'status' => Response::HTTP_FOUND,
            'msg' => $helper->upperCaseFirst($validator->errors()->first()),
        ];

        $res = response()->json($data);
        throw new HttpResponseException($res);
    }
}
