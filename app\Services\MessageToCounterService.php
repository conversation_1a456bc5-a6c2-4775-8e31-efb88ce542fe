<?php

namespace App\Services;

use App\Models\Conversation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

/**
 * Author: BaoDV
 * Author Email: <EMAIL>
 *
 * Created on May, 5/26/2025, by vanbao
 */
class MessageToCounterService
{
    protected $redisPrefix = 'chat:to_count:';

    /**
     * Get redis key for a specific user.
     * 
     * @param int $userId
     * @return string
     */
    protected function getRedisKey(int $userId): string
    {
        return $this->redisPrefix . $userId;
    }

    /**
     * Increase the number of TO messages for 1 user.
     *
     * @param $userId
     * @param int $amount
     * @return void
     */
    public function incrementForUser($userId, int $amount = 1): void
    {
        Redis::incrby($this->getRedisKey($userId), $amount);
    }

    /**
     * Reduce the number of TO messages for 1 or more users.
     * 
     * @param $userIds
     * @param int $amount
     * @return void
     */
    public function decrementForUsers($userIds, int $amount = 1): void
    {
        foreach ($userIds as $userId) {
            $key = $this->getRedisKey($userId);
            $current = (int) Redis::get($key);
            $new = max(0, $current - $amount);
            Redis::set($key, $new);
        }
    }

    /**
     * Reset specific value.
     * 
     * @param int $userId
     * @param int $count
     * @return void
     */
    public function setTotalMessageToForUser(int $userId, int $count): void
    {
        Redis::set($this->getRedisKey($userId), max(0, $count));
    }

    /**
     * Count message to specific user.
     * 
     * @param int $userId
     * @return int
     */
    public function countMessagesToUser(int $userId): int
    {
        return $this->getTotalMessageToForUser($userId);
    }

    /**
     * Get total message to for users.
     * 
     * @param $userIds
     * @return array
     */
    public function getTotalMessageToForUsers($userIds): array
    {
        $latestReads = DB::table('message_read')
            ->select('user_id', 'conversation_id', DB::raw('MAX(message_id) as message_id'))
            ->whereIn('user_id', $userIds)
            ->groupBy('user_id', 'conversation_id');

        // 2.1. Conversation 2 member
        $twoUserMessages = DB::table('messages')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->join('conversation_participants', function ($join) use ($userIds) {
                $join->on('conversation_participants.conversation_id', '=', 'conversations.id')
                    ->whereIn('conversation_participants.user_id', $userIds);
            })
            ->leftJoinSub($latestReads, 'message_read', function ($join) {
                $join->on('message_read.conversation_id', '=', 'conversations.id')
                    ->on('message_read.user_id', '=', 'conversation_participants.user_id');
            })
            ->where('conversations.type', Conversation::TYPE_TWO_USER)
            ->whereColumn('messages.user_id', '!=', 'conversation_participants.user_id')
            ->where(function ($query) {
                $query->whereNull('message_read.message_id')
                    ->orWhereColumn('messages.id', '>', 'message_read.message_id');
            })
            ->select('conversation_participants.user_id', DB::raw('COUNT(*) as cnt'))
            ->groupBy('conversation_participants.user_id')
            ->pluck('cnt', 'user_id');

        // 2.2. Thread created by others + TO me (remind_users directly or via reply)
        $multiUserMessagesTo = DB::table('messages')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->leftJoin('messages as parent', 'messages.reply_id', '=', 'parent.id')
            ->leftJoinSub($latestReads, 'mr', function ($join) {
                $join->on('mr.conversation_id', '=', 'messages.conversation_id');
                // Cần join thêm user_id của mr, ta sẽ join theo user_id của người trong whereIn
                // Nhưng bảng latestReads có user_id rồi
            })
            ->where('conversations.type', Conversation::TYPE_MULTI_USER)
            ->where('messages.user_id', '!=', DB::raw('mr.user_id'))
            ->where(function ($q) use ($userIds) {
                $q->where(function ($q1) use ($userIds) {
                    foreach ($userIds as $userId) {
                        $q1->orWhereJsonContains('messages.remind_users', $userId);
                    }
                })
                    ->orWhere(function ($q2) use ($userIds) {
                        foreach ($userIds as $userId) {
                            $q2->orWhereJsonContains('parent.remind_users', $userId);
                        }
                    });
            })
            ->where(function ($q) {
                $q->whereNull('mr.message_id')
                    ->orWhereColumn('messages.id', '>', 'mr.message_id');
            })
            ->select(DB::raw('mr.user_id'), DB::raw('COUNT(*) as cnt'))
            ->groupBy('mr.user_id')
            ->pluck('cnt', 'user_id');

        // 2.3 Thread created by me, others reply to
        $multiUserMessagesReply = DB::table('messages')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->leftJoinSub($latestReads, 'mr', function ($join) {
                $join->on('mr.conversation_id', '=', 'messages.conversation_id');
            })
            ->where('conversations.type', Conversation::TYPE_MULTI_USER)
            ->where('messages.user_id', '!=', DB::raw('mr.user_id'))
            ->whereNotNull('messages.reply_id')
            ->whereIn('messages.reply_id', function ($query) use ($userIds) {
                $query->select('id')
                    ->from('messages')
                    ->whereIn('user_id', $userIds);
            })
            ->where(function ($query) {
                $query->whereNull('mr.message_id')
                    ->orWhereColumn('messages.id', '>', 'mr.message_id');
            })
            ->select(DB::raw('mr.user_id'), DB::raw('COUNT(*) as cnt'))
            ->groupBy('mr.user_id')
            ->pluck('cnt', 'user_id');

        // 3. Calculate the total for each user
        $result = [];
        foreach ($userIds as $userId) {
            $result[$userId] =
                ($twoUserMessages[$userId] ?? 0) +
                ($multiUserMessagesTo[$userId] ?? 0) +
                ($multiUserMessagesReply[$userId] ?? 0);
        }
        
        return $result;
    }

    /**
     * Get total message to for user.
     * 
     * @param int $userId
     * @return int
     */
    public function getTotalMessageToForUser(int $userId): int
    {
        $total = 0;
        $latestReads = DB::table('message_read')
            ->select('conversation_id', 'user_id', 'thread_id', DB::raw('MAX(message_id) as message_id'))
            ->where('user_id', $userId)
            ->groupBy('conversation_id', 'user_id', 'thread_id');
        
        // Conversation messages 2 member 
        $total += DB::table('messages')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->join('conversation_participants', function ($join) use ($userId) {
                $join->on('conversation_participants.conversation_id', '=', 'conversations.id')
                    ->where('conversation_participants.user_id', '=', $userId);
            })
            ->leftJoinSub($latestReads, 'mr', function ($join) {
                $join->on('mr.conversation_id', '=', 'messages.conversation_id')
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->whereNull('mr.thread_id')
                                ->whereNull('messages.reply_id');
                        })
                        ->orWhereColumn('mr.thread_id', 'messages.reply_id')
                        ->orWhere(function ($q) {
                            $q->whereNull('messages.reply_id')
                                ->whereColumn('mr.thread_id', 'messages.id');
                        });
                    });
            })
            ->where('conversations.type', Conversation::TYPE_TWO_USER)
            ->where('messages.user_id', '!=', $userId)
            ->where(function ($query) {
                $query->whereNull('mr.message_id')
                    ->orWhereColumn('messages.id', '>', 'mr.message_id');
            })
            ->whereNull('conversations.deleted_at')
            ->whereNull('messages.deleted_at')
            ->count();

        // Conversation messages multiple member 
        $latestReads = DB::table('message_read')
            ->select('conversation_id', 'user_id', 'thread_id', DB::raw('MAX(message_id) as message_id'))
            ->groupBy('conversation_id', 'user_id', 'thread_id');
        
        $remindQuery = DB::table('messages')
            ->select('messages.id')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->leftJoinSub($latestReads, 'mr', function ($join) use ($userId) {
                $join->on('mr.conversation_id', '=', 'messages.conversation_id')
                    ->where('mr.user_id', '=', $userId)
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->whereNull('mr.thread_id')
                                ->whereNull('messages.reply_id');
                        })->orWhereRaw('mr.thread_id = IFNULL(messages.reply_id, messages.id)');
                    });
            })
            ->where('conversations.type', Conversation::TYPE_MULTI_USER)
            ->where('messages.user_id', '!=', $userId)
            ->whereJsonContains('messages.remind_users', $userId)
            ->where(function ($q) {
                $q->whereNull('mr.message_id')
                    ->orWhereColumn('messages.id', '>', 'mr.message_id');
            })
            ->where(function ($q) use ($userId) {
                $q->whereNull('messages.reply_id')
                    ->orWhereNotIn('messages.reply_id', function ($sub) use ($userId) {
                        $sub->select('id')->from('messages')->where('user_id', $userId)->whereNull('deleted_at');
                    });
            })
            ->whereExists(function ($query) use ($userId) {
                $query->select(DB::raw(1))
                    ->from('conversation_participants')
                    ->whereColumn('conversation_participants.conversation_id', 'conversations.id')
                    ->where('conversation_participants.user_id', $userId);
            })
            ->whereNull('conversations.deleted_at')
            ->whereNull('messages.deleted_at');
        
        $replyQuery = DB::table('messages')
            ->select('messages.id')
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->leftJoinSub($latestReads, 'mr', function ($join) use ($userId) {
                $join->on('mr.conversation_id', '=', 'messages.conversation_id')
                    ->where('mr.user_id', '=', $userId)
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->whereNull('mr.thread_id')
                                ->whereNull('messages.reply_id');
                        })->orWhereRaw('mr.thread_id = IFNULL(messages.reply_id, messages.id)');
                    });
            })
            ->where('conversations.type', Conversation::TYPE_MULTI_USER)
            ->where('messages.user_id', '!=', $userId)
            ->whereNotNull('messages.reply_id')
            ->whereIn('messages.reply_id', function ($query) use ($userId) {
                $query->select('id')->from('messages')->where('user_id', $userId)->whereNull('deleted_at');
            })
            ->where(function ($query) {
                $query->whereNull('mr.message_id')
                    ->orWhereColumn('messages.id', '>', 'mr.message_id');
            })
            ->whereExists(function ($query) use ($userId) {
                $query->select(DB::raw(1))
                    ->from('conversation_participants')
                    ->whereColumn('conversation_participants.conversation_id', 'conversations.id')
                    ->where('conversation_participants.user_id', $userId);
            })
            ->whereNull('conversations.deleted_at')
            ->whereNull('messages.deleted_at');

        $union = $replyQuery->union($remindQuery);
        $total += DB::query()
            ->fromSub($union, 'all_related_messages')
            ->distinct()
            ->count('id');

        $this->setTotalMessageToForUser($userId, $total);
        return $total;
    }

    /**
     * Clear cache total message to user.
     * 
     * @param int $userId
     * @return void
     */
    public function clear(int $userId): void
    {
        Redis::del($this->getRedisKey($userId));
    }
}