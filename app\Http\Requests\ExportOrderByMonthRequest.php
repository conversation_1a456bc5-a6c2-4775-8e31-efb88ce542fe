<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ExportOrderByMonthRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'choose_month' => 'required|date_format:m/Y',
        ];
        return $rules;
    }
}
