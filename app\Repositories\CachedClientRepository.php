<?php

namespace App\Repositories;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Passport\ClientRepository;

class CachedClientRepository extends ClientRepository
{
    /**
     * Cache key prefix for clients
     */
    protected const CACHE_PREFIX = 'passport_client_';

    /**
     * Check if the token is revoked, using cache to reduce database access.
     *
     * @param string $id
     * @return bool
     */
    public function find($id) 
    {
        $cacheKey = self::CACHE_PREFIX . $id;
        
        return Cache::remember($cacheKey, now()->addMinutes(config('passport.cache_ttl', 10)), function() use ($id) {
            Log::debug('[CachedClientRepository][find] Client cached: ' . $id);
            return parent::find($id);
        });
    }
}