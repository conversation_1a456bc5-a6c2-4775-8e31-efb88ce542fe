<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Activation extends Model
{
    /**
     * @var string
     */
    protected $table = 'activations';

    public $timestamps = true;

    protected $guarded = [];

    const ACTIVATION_TOKEN_KEY = 'activation_token';
    const ACTIVATION_COMPLETED = 1;
    const ACTIVATION_NOT_COMPLETED = 0;
    const LIMIT_SEND_CODE = 5;
    const RESET_TIME_SEND_CODE = 60; // minute
    const EXPIRED_TIME_CODE = 2; // minute
    const EXPIRED_TIME_TOKEN = 60; // minute
}
