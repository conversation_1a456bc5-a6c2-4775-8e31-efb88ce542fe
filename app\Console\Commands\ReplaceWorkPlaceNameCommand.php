<?php

namespace App\Console\Commands;

use App\Helpers\StringHelper;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ReplaceWorkPlaceNameCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:replace-work-place-name';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Replace workplace name command.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $helper = new StringHelper();
        $replaceKey = $helper->formatStringWhereLike('Văn phòng');
        $query = DB::table('work_places');
        $query->where('name', 'LIKE', "%" . $replaceKey . "%");
        $query->update([
            'name' => DB::raw("TRIM(REPLACE(name, '{$replaceKey}', ''))"),
        ]);

        $this->info('Done!');
    }
}
