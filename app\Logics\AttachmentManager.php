<?php

namespace App\Logics;

use App\Models\TaskAttachment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\URL;
use App\Models\Project;
use App\Models\ProjectTask;
use App\Models\TaskLog;
use App\Traits\StorageTrait;
use Illuminate\Support\Facades\Auth;

class AttachmentManager
{
    use StorageTrait;
    /**
     * get file attachment
     */
    public function getAttachment($id)
    {
        $file = TaskAttachment::select(
            'task_attachments.id',
            'task_attachments.type',
            'task_attachments.related_id AS taskLog_id',
            'task_attachments.file_name',
            'task_attachments.file_path',
            DB::raw('(CASE
                        WHEN task_attachments.type = '.TaskAttachment::TYPE_TASK_LOG.' THEN  task_logs.task_id
                        ELSE task_attachments.related_id
                        END) AS related_id'))
        ->leftJoin('task_logs', function($join){
            $join->on('task_logs.id','task_attachments.related_id');
            $join->on('task_attachments.type',DB::raw(TaskAttachment::TYPE_TASK_LOG));
            })
        ->where('task_attachments.id',$id)->first();

        return $file;
    }
    /**
     * Save attachments
     */
    public function saveAttachments($data, $type ,$attachments, $descriptions, $path, $updateColumn=null) {
        $files = [];
        $hostname = URL::to('/');
        if($attachments){
            foreach( $attachments as $key => $attachment){
                // Move attachment files from temporary directory to attachment directory
                $attachment = json_decode($attachment, true);
                $fileName = basename($attachment['file_path']);
                $destinationPath = $path.$fileName;
                $fileSize = $this->moveFile($attachment['file_path'],$destinationPath);
                // Save attachments
                $taskAttachment = new TaskAttachment();
                $taskAttachment->related_id  = $data->id;
                $taskAttachment->type = $type;
                $taskAttachment->file_size = $fileSize;
                $taskAttachment->file_name = $attachment['file_name'];
                $taskAttachment->file_path = $destinationPath;
                if($descriptions){
                    $taskAttachment->description = $descriptions[$key];
                }
                $taskAttachment->save();
                array_push($files, $taskAttachment);
                // Change url image description task
                if(isset($updateColumn)){
                    $imageUrlPrevious = $hostname. "/tmp/" . $fileName;
                    $imageUrlCurrent = $hostname. "/attachment/" . $taskAttachment->id;
                    $data->$updateColumn = str_replace($imageUrlPrevious, $imageUrlCurrent, $data->$updateColumn);
                }
            }
        }
        $data->save();
        return $files;
    }
}
