<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use App\Models\Inventory;
use Carbon\Carbon;

class InventoryManager
{
    /**
     * get list inventory
     */
    public function getListInventory($params, $pageSize, $orderBy = [])
    {
        if (isset($params['start_date']) && isset($params['end_date']) && Carbon::createFromFormat('d/m/Y', $params['start_date']) > Carbon::createFromFormat('d/m/Y', $params['end_date'])) {
            return [];
        }

        $stringHelper = new StringHelper();
        $columns = [
            'inventories.id',
            'inventories.name',
            'inventories.start_date',
            'inventories.end_date',
            'inventories.status',
        ];

        $inventory = Inventory::query()->select($columns);

        if (isset($orderBy)) {
            foreach ($orderBy as $key => $value) {
                $inventory = $inventory->orderBy($key, $value);
            }
        }

        if (isset($params['name'])) {
            $search = $stringHelper->formatStringWhereLike($params['name']);
            $inventory = $inventory->where('inventories.name', 'LIKE', '%' . $search . '%');
        }

        if (isset($params['status'])) {
            $inventory = $inventory->where('inventories.status', $params['status']);
        }

        if (isset($params['start_at'])) {
            $inventory = $inventory->where(function($query) use ($params) {
                $query->where('inventories.start_date', '>=', Carbon::createFromFormat('d/m/Y', $params['start_at'])->format('Y-m-d 00:00:00'));
            });
        }
        if (isset($params['end_at'])) {
            $inventory = $inventory->where(function($query) use ($params) {
                $query->where('inventories.end_date', '<=', Carbon::createFromFormat('d/m/Y', $params['end_at'])->format('Y-m-d 23:59:59'));
            });
        }

        if ($pageSize == null) {
            $inventory = $inventory->get();
        } else {
            $inventory = $inventory->paginate($pageSize);
        }
        return $inventory;
    }

    /**
     * Get filter HTML
     * @param $request
     * @param $fields
     * @return string
     */
    public function getFilterHtml($request, $fields = [])
    {
        $filterHtml = "";
        $dateFormatManager = new DateFormatManager();
        foreach ($fields as $field) {
            $value = '';
            if ($request->has($field) && $request->$field != null) {
                $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';    
                $tagSpanClose = '</span>';
                switch ($field) {
                    case 'status':
                        $status = [
                            Inventory::STATUS_OPEN => trans('language.opening'),
                            Inventory::STATUS_CLOSE => trans('language.closed'),
                        ];
                        if (isset($status[$request->$field])) {
                            $value .= $tagSpanOpen . $status[$request->$field] . $tagSpanClose;
                        }
                        break;
                    case 'name':
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->name) . $tagSpanClose;
                        break;
                    case 'start_date':
                    case 'end_date':
                        $typeFormat = 'd/m/Y';
                        $value .=  $tagSpanOpen . $dateFormatManager->dateFormatInput($request->$field, $typeFormat) . $tagSpanClose;
                    default:
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $filterHtml .= $value;
            }
        }
        return $filterHtml;
    }

    /**
     * Set data inventory
     * @param $params
     * @return array
     */
    public function getAllDataInParams($params): array
    {
        $startDate = !empty($params['start_date']) ? Carbon::createFromFormat('d/m/Y', $params['start_date'])->format('Y-m-d') : null;
        $endDate = !empty($params['end_date']) ? Carbon::createFromFormat('d/m/Y', $params['end_date'])->format('Y-m-d') : null;
        $dataInventory = [
            'name' => $params['inventory_name'],
            'note' => $params['note'] ?? null,
            'status' => $params['status'] ?? null,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
        return $dataInventory;
    }
}