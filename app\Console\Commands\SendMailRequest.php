<?php

namespace App\Console\Commands;

use App\Jobs\SendMailGetRequest;
use App\Mail\NotifyMailRequest;
use App\Models\Request;
use App\Models\Role;
use App\User;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SendMailRequest extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:send_mail_request {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail request to approvers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        if (!env('USE_TENANT', false) || (env('USE_TENANT', false) && $this->option('website_id') == env('BEETECH_COMPANY_ID', ''))) {
            $arrRequest = [];
            $getRequest = Request::select('status','requests.approvers', 'requests.followers')
                ->where('requests.status', Request::STATUS_WAITING)
                ->orWhere('requests.status', Request::STATUS_ACCEPT)
                ->get()
                ->toArray();
            foreach($getRequest as $item){
                if($item['status'] == Request::STATUS_WAITING){
                    $arrRequest[] = $item['approvers'];
                }else if($item['status'] == Request::STATUS_ACCEPT){
                    $arrRequest[] = $item['followers'];
                }
            }
            $arrRequest = array_merge(...$arrRequest);
            $arrCountData = array_count_values($arrRequest);
            $arrRequest = array_unique($arrRequest);
            $users = User::select('users.id', 'users.language_id', 'users.email')
                    ->whereIn('users.id', $arrRequest)->get();

            if($users && $arrRequest){
                foreach ($users as $user) {
                    dispatch(new SendMailGetRequest($user, $arrCountData[$user->id]))->onQueue(QUEUE_MAIL);
                }
            }
        }
    }
    
    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->dailyAt('8:00');
    }
}
