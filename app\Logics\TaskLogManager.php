<?php

namespace App\Logics;

use App\Models\TaskLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TaskLogManager
{
    const PAGE_SIZE_ACTIVITY = 15;
    /**
     * Get list logs
     * @param int $taskId
     * @param int $type
     * @param int $pageSize
     */
    public function getListLogs($taskId=null,$type=null) {
        $logs = TaskLog::select([
                'task_logs.id',
                'task_logs.log',
                'task_logs.type',
                'task_logs.created_at as time',
                'users.id as user_id',
                'users.avatar',
                DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as user_name')
            ])
            ->leftjoin('users','users.id','task_logs.created_by')

            ->orderBy('id','asc');

        if (isset($taskId))  {
            $logs = $logs->where('task_id',$taskId);
        }
        if (isset($type))  {
            $logs = is_array($type)?$logs->whereIn('type',$type):$logs->where('type',$type);
        }

        // Pagination
        $logs = $logs->with('logAttachments')->get();

        return $logs;
    }

    /**
     * Get html task note
     * @param int $id
     */
    public function getLog($id){
        $log = TaskLog::select([
            'task_logs.id',
            'task_logs.log',
            'task_logs.type',
            'task_logs.created_at as time',
            'users.id as user_id',
            'users.avatar',
            DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as user_name')
        ])
            ->leftjoin('users','users.id','task_logs.created_by')
            ->where('task_logs.id',$id)
            ->first();
        return $log;
    }
     /**
     * Get icon tasklog
     * @param integer $type
     */
    public function getIconType($type){
        $icon = '';

        switch ($type){
            case 1:
                $icon = 'far fa-comment-alt-edit text-gray';
                break;
            case 2:
                $icon = 'far fa-edit text-primary';
                break;
            case 3:
                $icon = 'fal fa-users text-bee';
                break;
            case 4:
                $icon = 'far fa-folder-open text-yellow';
                break;
            default:
                break;
        }
        return $icon;
    }
    /**
     * Get data activities by project_id
     *
     * @param Request $request
     * @param int $projectId;
     * 
     * @return array
     */
    public function getDataActivity(Request $request, $projectId){
        $userId = Auth::id();
        $taskLogs = TaskLog::checkUserPermission($userId)
                            ->leftjoin('users', 'task_logs.created_by', 'users.id')
                            ->leftjoin('task_types', 'project_tasks.type', 'task_types.id')
                            ->leftjoin('task_status', 'project_tasks.status', 'task_status.id');            
        if ($projectId){
            $taskLogs = $taskLogs->where('projects.id', $projectId);
        } else{
            $taskLogs = $taskLogs->where('projects.deleted_at', null);
        }
        if ($request->start_date){
            $startDate = date_create_from_format("d/m/Y",$request->start_date);
            $startDate = date_create_from_format("d/m/Y",$request->start_date);
            if ($startDate){
                $startDate = date_format($startDate,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '>=', $startDate);
            }     
        }
        if ($request->end_date){
            $endDate = date_create_from_format("d/m/Y",$request->end_date);
            $endDate = date_create_from_format("d/m/Y",$request->end_date);
            if ($endDate){
                date_modify($endDate, "+1 days");
                $endDate = date_format($endDate,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '<', $endDate);
            }
        }
        $taskLogs = $taskLogs->where('task_logs.type', '!=' , TaskLog::FIRST_ASSIGNED_USER)
                    ->orderBy('task_logs.updated_at', 'desc')
                    ->select('task_logs.*', 'project_tasks.name as task_name', 'project_tasks.status', 'projects.name as project_name',
                    'users.id as userId','users.first_name','users.last_name', 'users.avatar', 'task_types.name as task_type', 'task_types.id as type_id',
                    'task_status.name as task_status_name')
                    ->paginate(self::PAGE_SIZE_ACTIVITY);

        $tasks = TaskLog::checkUserPermission($userId);
        if ($projectId){
            $tasks = $tasks->where('projects.id', $projectId);
        }
        $tasks=$tasks->groupBy('task_logs.task_id')
                    ->groupBy(DB::raw('date(task_logs.updated_at)'));
       
        if ($taskLogs->count()){
            $tasks = $tasks->havingRaw('max(task_logs.updated_at)>=?', [$taskLogs->last()->updated_at])
                            ->havingRaw('min(task_logs.updated_at)<=?', [$taskLogs->first()->updated_at]);
        }
        
        $tasks=$tasks   ->orderBy('last', 'desc')
                        ->select("task_logs.task_id", DB::raw('max(task_logs.updated_at) as last'))
                        ->get();

                    
        return [$taskLogs, $tasks];  
    }
    
     /**
     * Get first assigned user id
     *
     * @param int $taskId
     * @return mix
     */
    public function getFirstAssignedUserId($taskId){   
        $tasklog = TaskLog::where('task_id', $taskId)->where('type', TaskLog::FIRST_ASSIGNED_USER)->first();
        if ($tasklog) {
            return $tasklog->log;
        } else {
            return null;
        }
    }

    /**
     * Store first assigned user id
     *
     * @param  int  $taskId
     * @param  int  $assignedUserId
     * @return mix
     */
    public function storeFirstAssignedUserId($taskId, $assignedUserId){  
        $userId = Auth::id();
        TaskLog::create([
            'task_id' => $taskId,
            'log' => $assignedUserId,
            'type' => TaskLog::FIRST_ASSIGNED_USER,
            'created_by' => $userId,
            'updated_by' => $userId
        ]);
    }
}
