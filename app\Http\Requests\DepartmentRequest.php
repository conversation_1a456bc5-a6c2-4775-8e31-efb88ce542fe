<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DepartmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        
        $rules = [
            'department_name' => 'required|max:100',
            'department_code' => 'nullable|max:50'
        ];
        
        return $rules;
    }
    
    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'department_name.required' => trans('validation.required', ['attribute' => trans('language.site_setting_departments.name')]),
            'department_name.max' => trans('validation.max', ['attribute' => trans('language.site_setting_departments.name')]),
            'department_code.max' => trans('validation.max', ['attribute' => trans('language.site_setting_departments.code')]),
        ];
    }
}
