<?php

namespace App\Http\Controllers\Request;

use App\Http\Controllers\Controller;
use App\Http\Requests\RequireRequest;
use App\Logics\AttachmentManager;
use App\Logics\DateFormatManager;
use App\Logics\RequestManager;
use App\Logics\UserManager;
use App\Models\Attendance;
use App\Models\Request as ModelsRequest;
use App\Models\RequestLog;
use App\Models\Role;
use App\Models\SeenRequestLog;
use App\Models\SiteSetting;
use App\Models\SpecialRequest;
use App\Models\TaskAttachment;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpParser\Node\Expr\AssignOp\Mod;
use App\Http\Requests\ActionRequest;

class RequestController extends Controller
{
    const PAGE_SIZE = 12;
    /**
     * view request
     */
    public function index(Request $request)
    {
        // Get task list
        $requestManager = new RequestManager();
        $orderBy = isset($request->sort)?[$request->sort => $request->direction]:['updated_at' => 'DESC'];
        [$requestAll, $requestMySelfs , $requestOthers] = $requestManager->getRequestList($request->all(), self::PAGE_SIZE, $orderBy );
        $filterHtml = $requestManager->getFilterHtml($request,['title','type','status','member','from','to']);
        $usersHasRoleOperationManager = User::select('first_name','last_name','email','id')->whereHas(
            'roles', function($q){
                $q->where('name', Role::ROLE_OPERATION_MANAGER);
            }
        )->get();

        $pageActive = $requestManager->pageActive($request);
        $idDefaultFollower = env('DEFAULT_FOLLOWER');
        $defaultFollower = User::select('id')
        ->where('id',$idDefaultFollower)->whereHas(
            'roles', function($q){
                $q->where('name', Role::ROLE_OPERATION_MANAGER);
            }
        )
        ->first();
        return view('request.index',[
            'requestAll' => $requestAll,
            'requestMySelfs' => $requestMySelfs,
            'requestOthers' => $requestOthers,
            'filterHtml' => $filterHtml,
            'usersHasRoleOperationManager' => $usersHasRoleOperationManager,
            'pageActive' => $pageActive,
            'defaultFollower'=>$defaultFollower
        ]);
    }

    /**
     * render data in table
     * @param \Illuminate\Http\Request $request
     */
    public function fetchData(Request $request)
    {
        if ($request->ajax()) 
        {
            // Get task list
            $requestManager = new RequestManager();
            $orderBy = isset($request->sort)?[$request->sort => $request->direction]:['updated_at' => 'DESC'];
            [$requestAll, $requestMySelfs , $requestOthers] = $requestManager->getRequestList($request->all(), self::PAGE_SIZE, $orderBy );
            $filterHtml = $requestManager->getFilterHtml($request,['title','type','status','member','from','to']);
            $usersHasRoleOperationManager = User::select('first_name','last_name','email','id')->whereHas(
                'roles', function($q){
                    $q->where('name', Role::ROLE_OPERATION_MANAGER);
                }
            )->get();

            $data = $requestAll;
            if(isset($request->page_active)){
                switch ($request->page_active){
                    case 'myself':
                        $data = $requestMySelfs;
                        break;
                    case 'other':
                        $data = $requestOthers;
                        break;
                    default:
                        $data = $requestAll;
                }
            }

            return view('request.partials.list-request', compact('data'))->render();
        }
    }

    /**
     * save request
     */
    public function store(RequireRequest $request)
    {
        DB::beginTransaction();
        try {
            $userId = Auth::id();
            $content = [];
            $atributeNotInContent = [
                '_token',
                '_method',
                'name',
                'type',
                'approvers',
                'followers',
                'note',
                'documents',
                'descriptionDocument',
                'status',
                'user_id',
                'shift',
                'request_type',
                'collab_starttime_request',
                'collab_endtime_request',
            ];
            $dateFormatManager = new DateFormatManager();
            foreach ($request->all() as $attribute =>$value){
                if(!in_array($attribute, $atributeNotInContent)){
                    switch ($attribute){
                        case 'time_checkin':
                        case 'time_checkout':
                        case 'time_start':
                        case 'time_end':
                        $value = Carbon::createFromFormat('d/m/Y H:i',$value)->format('Y-m-d H:i:s');
                        break;
                        case 'time_family_business':
                            $arrTimeFamilyBusiness = explode(", ", $request->time_family_business);
                            if ($request->type != ModelsRequest::TYPE_FAMILY_BUSINESS || count($arrTimeFamilyBusiness) > 3) {
                                return redirect()
                                    ->route('request.index')
                                    ->with('status_failed', trans('message.server_error'));
                            }
                            $value = $dateFormatManager->dateFormatLanguageArray($value);
                            break;
                        case 'hour':
                        case 'mentor':
                        case 'receive_arrear_cash':
                        case 'receive_arrear':
                        case 'collect_arrear':
                        case 'btaBonus':    
                        case 'percent_work':
                            $arrUserID = $request->user_id;
                            $month = $request->choose_month;
                            $arrRequest = [];
                            foreach($arrUserID as $key => $item){
                                $arrRequest[] = [
                                    'month' => $month,
                                    'user_id' => $item,
                                    'value' => isset($value[$key]) ? $value[$key] : 0
                                    ];
                                }
                            $value = $arrRequest;
                            break;
                        case 'collaborator_date':
                            $arrRequest = [];
                            foreach($value as $key => $item){
                                $arrRequest[] = [
                                    'collaborator_date' => !empty($request['collaborator_date'][$key]) 
                                    ? Carbon::createFromFormat('d/m/Y', $request['collaborator_date'][$key])->format('Y-m-d') 
                                    : '',
                                    'shift' => $request['shift'][$key],
                                    'request_type' => $request['request_type'][$key],
                                    'collab_starttime_request' => in_array(
                                        $request['request_type'][$key], 
                                        [ModelsRequest::COLLABORATOR_WORK]
                                    ) && !empty($request['collab_starttime_request'][$key])
                                        ? Carbon::parse($request['collab_starttime_request'][$key])->format('H:i:s')
                                        : '',
                                    'collab_endtime_request' => in_array(
                                        $request['request_type'][$key], 
                                        [ModelsRequest::COLLABORATOR_WORK]
                                    ) && !empty($request['collab_endtime_request'][$key])
                                        ? Carbon::parse($request['collab_endtime_request'][$key])->format('H:i:s')
                                        : '',
                                ];
                            }
                            $value = $arrRequest;
                            break;
                        default:
                            $value = $value;
                    }
                    $arrAttributeRequest = [
                        "user_id",
                        "choose_month",
                        "hour",
                        "receive_arrear_cash",
                        "receive_arrear",
                        "percent_work",
                        "mentor",
                        "collect_arrear",
                        "btaBonus",
                        "collaborator_date",
                    ];
                    if (!in_array($attribute,$arrAttributeRequest)){
                    $content[$attribute] = $value;
                    }else{
                    $content = $value;
                    }
                }
            }
            $requestStore = new ModelsRequest();
            $requestStore->name = $request->name;
            $requestStore->type = $request->type;
            $requestStore->content = $content;
            $requestStore->approvers = $request->approvers;
            $requestStore->followers = $request->followers;
            $requestStore->note =  $request->note;
            $requestStore->save();
            $data = ModelsRequest::find($requestStore->id);
            $destinationPath = str_replace(['{request_id}'], [$data->id], REQUEST_DIR).'/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_REQUEST,$request->documents, $request->descriptionDocument, $destinationPath);

            $requestLog = new RequestLog();
            $requestLog->request_id =  $requestStore->id;
            $requestLog->type = RequestLog::TYPE_CREATE;
            $requestLog->save();
            // Send mail for Approvers
            $requestManager = new RequestManager();
            $requestManager->sendMailRequest($requestStore->type, $requestStore->id, $requestStore->approvers);
            
            DB::commit();
            return redirect()
                ->route('request.index')
                ->with('status_succeed', trans('message.create_request_succeed'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('request.index')
                ->with('status_failed', trans('message.server_error'));
        }
    }
    public function show($id){
        $userId = Auth::id();
        $request = ModelsRequest::checkUserPermission($userId)->where('id',$id)
            ->with(['userCreate'=> function($query){
                $query->withTrashed();
            }])
            ->first();
        if ($request == null){
            return redirect()->route('request.index');
        }
        $requestSeenLogs = SeenRequestLog::where('request_id',$id)->where('user_id',$userId)->first();
        if ($requestSeenLogs == null){
            SeenRequestLog::create([
                'request_id' => $id,
                'user_id' => $userId,
                'seen_at' => Carbon::now()
            ]);
        }
        $requestLog = RequestLog::where('request_id'.$id);
        $files = TaskAttachment::where('related_id',$id)->where('type',TaskAttachment::TYPE_REQUEST)->get();
        $userManager = new UserManager();
        $approvers = isset($request->approvers)?$userManager->getUsersByIDs($request->approvers):null;
        $followers = isset($request->followers)?$userManager->getUsersByIDs($request->followers):null;
        $logs = RequestLog::where('request_id',$id)
            ->with(['userCreate'=> function($query){
                $query->withTrashed();
            }])
            ->get();
        
        $arrUserRequest = [];
        $arrUserIdRequest = [];
        $arrValueUserRequest = [];
        if ($request->type == ModelsRequest::TYPE_FREELANCER || $request->type == ModelsRequest::TYPE_OT ||
            $request->type == ModelsRequest::TYPE_PERCENT_WORK || $request->type == ModelsRequest::TYPE_MENTOR ||
            $request->type == ModelsRequest::TYPE_RECEIVE_ARREAR_CASH || $request->type == ModelsRequest::TYPE_RECEIVE_ARREAR  ||
            $request->type == ModelsRequest::TYPE_COLLECT_ARREAR||$request->type == ModelsRequest::TYPE_BTA){
            foreach ($request->content as $key => $item){
                $arrUserIdRequest[] = $item['user_id'];
                $arrValueUserRequest[$key]['user_id'] = $item['user_id'];
                $arrValueUserRequest[$key]['value'] = $item['value'];
            }
            $arrValueUserRequest = array_column($arrValueUserRequest,'value','user_id');
            $arrUserRequest = User::withTrashed()
                ->select(['id','first_name','last_name'])
                ->whereIn('id',$arrUserIdRequest)->get();
        }
        if ($request->type == ModelsRequest::TYPE_COLLABORATOR) {
            foreach ($request->content as $key => $item){
                $arrValueUserRequest[$key]['collaborator_date'] = $item['collaborator_date'];
                $arrValueUserRequest[$key]['shift'] = $item['shift'];
                $arrValueUserRequest[$key]['request_type'] = $item['request_type'];
                $arrValueUserRequest[$key]['collab_starttime_request'] = $item['collab_starttime_request'];
                $arrValueUserRequest[$key]['collab_endtime_request'] = $item['collab_endtime_request'];
            }
        }

        return view('request.view',[
            'request' => $request,
            'log' => $requestLog,
            'approvers' => $approvers,
            'followers' => $followers,
            'logs' => $logs,
            'files' => $files,
            'arrUserRequest' => $arrUserRequest,
            'arrUserIdRequest' => $arrUserIdRequest,
            'arrValueUserRequest' => $arrValueUserRequest
        ]);
    }

    public function update(RequireRequest $request,$id){
        $userId = Auth::id();
        $requestUpdate = ModelsRequest::checkUserPermission($userId)->where('id',$id)->first();
        if ($requestUpdate == null){
            return redirect()->route('request.index');
        }
        $statusNotUpdate = [ModelsRequest::STATUS_WAITING, ModelsRequest::STATUS_REFUSE];
        if (!in_array($requestUpdate->status, $statusNotUpdate) || $requestUpdate->created_by != $userId){
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_failed', trans('message.can_not_update_request'));
        }
        try {
            $content = [];
            $atributeNotInContent = [
                '_token',
                '_method',
                'name',
                'type',
                'approvers',
                'followers',
                'note',
                'documents',
                'descriptionDocument',
                'status',
                'user_id',
                'shift',
                'request_type',
                'collab_starttime_request',
                'collab_endtime_request',
            ];
            foreach ($request->all() as $attribute =>$value){
                if(!in_array($attribute, $atributeNotInContent)){
                    switch ($attribute){
                        case 'time_start':
                        case 'time_end':
                        case 'time_checkin':
                        case 'time_checkout':
                            $value = Carbon::createFromFormat('d/m/Y H:i',$value)->format('Y-m-d H:i:s');
                            break;
                        case 'time_family_business':
                            $dateFormatManager = new DateFormatManager();
                            $value = $dateFormatManager->dateFormatLanguageArray($value);
                            break;
                        case 'hour':
                        case 'mentor':
                        case 'receive_arrear_cash':
                        case 'receive_arrear':
                        case 'collect_arrear':
                        case 'btaBonus':
                        case 'percent_work':
                            $arrUserID = $request->user_id;
                            $month = $request->choose_month;
                            $arrRequest = [];
                            foreach($arrUserID as $key => $item){
                                $arrRequest[] = [
                                    'month' => $month,
                                    'user_id' => $item,
                                    'value' => isset($value[$key]) ? $value[$key] : 0
                                ];
                            }
                            $value = $arrRequest;
                            break;
                        case 'collaborator_date':
                            $arrRequest = [];
                            foreach($value as $key => $item){
                                $arrRequest[] = [
                                    'collaborator_date' => !empty($request['collaborator_date'][$key]) 
                                    ? Carbon::createFromFormat('d/m/Y', $request['collaborator_date'][$key])->format('Y-m-d') 
                                    : '',
                                    'shift' => $request['shift'][$key],
                                    'request_type' => $request['request_type'][$key],
                                    'collab_starttime_request' => in_array(
                                        $request['request_type'][$key], 
                                        [ModelsRequest::COLLABORATOR_WORK]
                                    ) && !empty($request['collab_starttime_request'][$key])
                                        ? Carbon::parse($request['collab_starttime_request'][$key])->format('H:i:s')
                                        : '',
                                    'collab_endtime_request' => in_array(
                                        $request['request_type'][$key], 
                                        [ModelsRequest::COLLABORATOR_WORK]
                                    ) && !empty($request['collab_endtime_request'][$key])
                                        ? Carbon::parse($request['collab_endtime_request'][$key])->format('H:i:s')
                                        : '',
                                ];
                            }
                            $value = $arrRequest;
                            break;
                        default:
                            $value = $value;
                            break;
                    }
                    $arrAttributeRequest = array(
                        "user_id",
                        "choose_month",
                        "hour",
                        "receive_arrear_cash",
                        "receive_arrear",
                        "percent_work",
                        "mentor",
                        "collect_arrear",
                        "btaBonus",
                        "collaborator_date",
                    );
                    if (!in_array($attribute,$arrAttributeRequest)){
                        $content[$attribute] = $value;
                    }else{
                        $content = $value;
                    }
                }
            }
            $requestUpdate->name = $request->name;
            $requestUpdate->type = $request->type;
            $requestUpdate->approvers = $request->approvers;
            $requestUpdate->followers = $request->followers;
            $requestUpdate->note =  $request->note;
            $requestUpdate->content = $content;
            if(isset($request->status)){
                $requestUpdate->status = $request->status;
            }
            $requestUpdate->save();
            $destinationPath = str_replace(['{request_id}'], [$requestUpdate->id], REQUEST_DIR).'/';
            (new AttachmentManager())->saveAttachments($requestUpdate, TaskAttachment::TYPE_REQUEST,$request->documents, $request->descriptionDocument, $destinationPath);
            RequestLog::create([
                'request_id' => $id,
                'type' => RequestLog::TYPE_UPDATE
            ]);
            // Send mail for Approvers
            $requestManager = new RequestManager();
            $requestManager->sendMailRequest($requestUpdate->type, $requestUpdate->id, $request->approvers);
            DB::commit();
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_succeed', trans('message.update_request_succeed'));

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_failed', trans('message.server_error'));
        }

    }

    public function destroy($id){
        $userId = Auth::id();
        $request = ModelsRequest::checkUserPermission($userId)->where('id',$id)->first();
        if ($request == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'text' => trans('message.request_not_exist'),
                ]
            ];
        }
        $statusNotDelete = [ModelsRequest::STATUS_WAITING, ModelsRequest::STATUS_REFUSE];
        if (!in_array($request->status, $statusNotDelete) || $request->created_by != $userId){
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_failed', trans('message.can_not_delete_request'));
        }
        $request->delete();
        RequestLog::create([
            'request_id' => $id,
            'type' => RequestLog::TYPE_CANCEL
        ]);
        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_request_succeed') ,
            ],
            'redirect' => route('request.index'),
        ];
    }

    public function action(ActionRequest $request, $id,$status){
        $userId = Auth::id();
        $noteRequest = $request->log;
        $requestModel = ModelsRequest::checkUserPermission($userId)->where('id',$id)->first();
        if ($request == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.request_not_exist'),
                ]
            ];
        }
        //trường hợp từ chối và không nhập lý do
        if($status == ModelsRequest::STATUS_REFUSE && !isset($noteRequest)){
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_failed',trans('language.reason_placeholder'));
        }
        if((in_array($status,[ModelsRequest::STATUS_ACCEPT,ModelsRequest::STATUS_REFUSE])
            && in_array($userId,$requestModel->approvers)
            && (in_array($requestModel->status, [\App\Models\Request::STATUS_WAITING, \App\Models\Request::STATUS_ACCEPT]) || (in_array($requestModel->updated_by, $requestModel->approvers) && $requestModel->status == \App\Models\Request::STATUS_REFUSE))
            ) || (in_array($userId,$requestModel->followers) && in_array($status,[ModelsRequest::STATUS_CONFIRM,ModelsRequest::STATUS_REFUSE]) && in_array($requestModel->status, [ModelsRequest::STATUS_ACCEPT]))){
            // add record attendance when type is checkin or checkout and status is confirm
            if ($status == ModelsRequest::STATUS_CONFIRM && ($requestModel->type == ModelsRequest::TYPE_CHECKIN || $requestModel->type == ModelsRequest::TYPE_CHECKOUT)){
                $content = $requestModel->content;
                Attendance::create([
                    'user_id' => $requestModel->created_by,
                    'checked_at' => $requestModel->type == ModelsRequest::TYPE_CHECKIN ? $content['time_checkin']:$content['time_checkout'],
                    'checked_type' => Attendance::MANUAL_CHECK
                ]);
            }
            // Update status request
            $requestModel->status = $status;
            $requestModel->save();
            // In case of Applying for Leave
            //Check request is confirmed and request type is request_family_business and request content non-empty
            if($status == ModelsRequest::STATUS_CONFIRM && ModelsRequest::TYPE_FAMILY_BUSINESS == $requestModel->type && !empty($requestModel->content['time_family_business'])){
                $data = array();
                $timeStart = SiteSetting::morningStart();
                $timeEnd = SiteSetting::afternoonEnd();
                foreach ($requestModel->content['time_family_business'] as $value) {
                    $dateTimeStart = Carbon::createFromFormat('Y-m-d H:i:s', $value.' '.$timeStart)->format('Y-m-d H:i:s'); //set time start working
                    $dateTimeEnd = Carbon::createFromFormat('Y-m-d H:i:s', $value.' '.$timeEnd)->format('Y-m-d H:i:s');  //set time end working
                    $data[] = [
                        'user_id' => $requestModel->created_by,
                        'checked_at' => $dateTimeStart,
                        'checked_type' => Attendance::MANUAL_CHECK
                    ];
                    $data[] =[
                        'user_id' => $requestModel->created_by,
                        'checked_at' => $dateTimeEnd,
                        'checked_type' => Attendance::MANUAL_CHECK
                    ];
                }
                 Attendance::insert($data); //insert multiple 
            }
            //handle data to insert special request table
            $arrTypeRequest = array(
                ModelsRequest::TYPE_COLLECT_ARREAR,
                ModelsRequest::TYPE_RECEIVE_ARREAR_CASH,
                ModelsRequest::TYPE_RECEIVE_ARREAR,
                ModelsRequest::TYPE_MENTOR,
                ModelsRequest::TYPE_FREELANCER,
                ModelsRequest::TYPE_OT,
                ModelsRequest::TYPE_PERCENT_WORK,
                ModelsRequest::TYPE_BTA
            );
            $type = $requestModel->type;
            if(in_array($type,$arrTypeRequest)){
               
                if($requestModel->status == ModelsRequest::STATUS_CONFIRM && !empty($requestModel->content)){

                    $type = $requestModel->type;

                    $dateMonthArray = explode('/', $requestModel->content[0]['month']);
                    $month = $dateMonthArray[0];
                    $year = $dateMonthArray[1];
                    $getMonth = Carbon::create($year, $month)->startOfMonth()->format('Y-m-d');
                    $requestArr = [];
                    $getRequest = SpecialRequest::whereMonth('month', $month)->whereYear('month', $year)->get();
                    foreach ($getRequest as $request){
                        $requestArr[$request->user_id] = $request;
                    }
                    switch ($type) {
                        case ModelsRequest::TYPE_COLLECT_ARREAR:
                            foreach($requestModel->content as $content){
                                if(isset($requestArr[$content['user_id']])){
                                    $requestArr[$content['user_id']]->collect_arrear += $content['value'];
                                    $requestArr[$content['user_id']]->save();
                                }else{
                                    SpecialRequest::create([
                                        'user_id' => $content['user_id'],
                                        'month' => $getMonth,
                                        'collect_arrear' => $content['value']
                                    ]);   
                                }
                            }
                            break;
                            case ModelsRequest::TYPE_RECEIVE_ARREAR_CASH:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->receive_arrear_cash += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'receive_arrear_cash' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_BTA:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->bta_bonus += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'bta_bonus' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_RECEIVE_ARREAR:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->receive_arrear += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'receive_arrear' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_MENTOR:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->mentor += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'mentor' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_FREELANCER:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->freelancer += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'freelancer' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_OT:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->ot += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'ot' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                            case ModelsRequest::TYPE_PERCENT_WORK:
                                foreach($requestModel->content as $content){
                                    if(isset($requestArr[$content['user_id']])){
                                        $requestArr[$content['user_id']]->percent_work += $content['value'];
                                        $requestArr[$content['user_id']]->save();
                                    }else{
                                        SpecialRequest::create([
                                            'user_id' => $content['user_id'],
                                            'month' => $getMonth,
                                            'percent_work' => $content['value']
                                        ]);   
                                    }
                                }
                            break;
                        default:
                    }
                }
            }
            if($status==ModelsRequest::STATUS_ACCEPT){
                $type = RequestLog::TYPE_ACCEPT;
                $text = trans('message.accept_request_success');
//              // Send mail for Followers
                $requestManager = new RequestManager();
                $requestManager->sendMailRequest($requestModel->type, $requestModel->id, $requestModel->followers);
            
            }elseif ($status==ModelsRequest::STATUS_REFUSE){
                $type = RequestLog::TYPE_REFUSE;
                $text = trans('message.refuse_request_success');
            }else{
                $type = RequestLog::TYPE_CONFIRM;
                $text = trans('message.confirm_request_success');
            }
            // create log
            RequestLog::create([
                'request_id' => $id,
                'type' => $type,
                'log' => isset($noteRequest)?$noteRequest:null,
            ]);

            if($status==ModelsRequest::STATUS_REFUSE){
                return redirect()
                    ->route('request.show',['id'=>$id])
                    ->with('status_succeed', $text);
            }else{
                return [
                    'status' => Response::HTTP_OK,
                    'msg' => [
                        'text' => $text,
                    ],
                    'redirect' => route('request.show',['id'=>$id]),
                ];
            }
        }else{
            return redirect()
                ->route('request.show',['id'=>$id])
                ->with('status_failed', trans('message.not_permission_action_request'));
        }
    }

    public function getHtmlByTypeRequest(Request $request){
        $html = '';
        if ($request->type == ModelsRequest::TYPE_CHECKIN){
            $html = view('request.partials.form-checkin')->render();
        }elseif ($request->type == ModelsRequest::TYPE_CHECKOUT){
            $html = view('request.partials.form-checkout')->render();
        }elseif ($request->type == ModelsRequest::TYPE_VACATION){
            $html = view('request.partials.form-vacation')->render();
        }elseif ($request->type == ModelsRequest::TYPE_FAMILY_BUSINESS){
            $html = view('request.partials.form-family-business')->render();
        }elseif ($request->type == ModelsRequest::TYPE_BTA){
            $html = view('request.partials.form-bta')->render();
        }elseif ($request->type == ModelsRequest::TYPE_FREELANCER || $request->type == ModelsRequest::TYPE_OT || 
            $request->type == ModelsRequest::TYPE_PERCENT_WORK || $request->type == ModelsRequest::TYPE_MENTOR || 
            $request->type == ModelsRequest::TYPE_RECEIVE_ARREAR_CASH || $request->type == ModelsRequest::TYPE_RECEIVE_ARREAR|| 
            $request->type == ModelsRequest::TYPE_COLLECT_ARREAR){
            $html = view('request.partials.form-employee')->render();
        }elseif ($request->type == ModelsRequest::TYPE_COLLABORATOR) {
            $html = view('request.partials.form-collaborator-request')->render();
        }
        return [
            'status' => Response::HTTP_OK,
            'html' => $html
        ];
    }
}
