<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BugClassify extends Model
{
    use SoftDeletes;

    protected $table = 'bug_classifies';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
            $data->created_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }
}
