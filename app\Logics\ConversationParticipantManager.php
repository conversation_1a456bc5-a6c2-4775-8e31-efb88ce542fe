<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Helpers\NotificationHelper;
use App\Helpers\StringHelper;
use App\Jobs\Notification\sendNotificationConversation;
use App\Models\Contacts;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\MessageBookmark;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ConversationParticipantManager
{
    const PERPAGE = 20;

    /**
     * check permission message
     * @param $id
     * @param $idUser
     * @param string[] $column
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */

    public static function GetConversationParticipant($idConversation, $idUser = null, $column = ['user_id'])
    {
        $condition = [];
        if (!empty($idUser)) {
            $condition[] = ['conversation_participants.user_id', '!=', $idUser];
        }
        return ConversationParticipant::query()
            ->where('conversation_participants.conversation_id', $idConversation)
            ->where($condition)
            ->select($column)
            ->get();
    }

    /**
     * get all participants of conversation
     * @param $request
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null|array
     */

    public static function getAllParticipantsOfConversation($request)
    {
        $currentUserId = Auth::guard('api')->user()->id;
        $perPage = is_numeric($request->get('per_page')) ? (int) $request->get('per_page') : self::PERPAGE;
        $statusConversationParticipant = ConversationManager::checkPermissionConversation($request->input('conversation_id'), $currentUserId);
        if (empty($statusConversationParticipant)) {
            return ['status_error' => ConversationParticipant::ERROR_PERMISSION];
        }
        $query = User::query();
        if (!empty($request->flag_edit)) {
            $query = $query->leftJoin('conversation_participants', function ($join) use ($request) {
                $join->on('conversation_participants.user_id', '=', 'users.id')
                    ->where('conversation_participants.conversation_id', $request->conversation_id);
            });
        } else {
            $query = $query->join('conversation_participants', function ($join) {
                $join->on('users.id', '=', 'conversation_participants.user_id');
            })
            ->where('conversation_participants.conversation_id', $request->conversation_id);
        }

        if (isset($request->user_name)) {
            $stringHelper = new StringHelper();
            $nameSearch = $stringHelper->formatStringWhereLike($request->input('user_name'));
            $nameQuery = '%' . $stringHelper->transformSearchFullname($nameSearch) . '%';
            $query->where('users.name_search', 'LIKE', $nameQuery);
        }
       // The query join with table contact query returns the user who sent the invitation to connect you with the currently logged in user
        $query = $query->leftJoin('contacts as sent_contacts', function($joinSub) use ($currentUserId) {
            $joinSub->on('sent_contacts.receiver_user_id', '=', DB::raw($currentUserId))
                ->on('sent_contacts.user_id', '=', 'users.id')
                ->where('sent_contacts.status', Contacts::STATUS_SENDED);
        })
       // The query join with table contact query returns the user who has been sent a friend request by the currently logged in user
        ->leftJoin('contacts as received_contacts', function($joinSub) use ($currentUserId) {
            $joinSub->on('received_contacts.user_id', '=', DB::raw($currentUserId))
                ->on('received_contacts.receiver_user_id', '=', 'users.id')
                ->where('received_contacts.status', Contacts::STATUS_SENDED);
        });
        $query->leftJoin('positions', 'positions.id', 'users.position_id');
        $query = $query->select([
            'users.id',
            'conversation_participants.admin',
            DB::Raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
            'users.avatar',
            'users.company_contract',
            DB::raw('(SELECT c.id
            FROM conversations c
               INNER JOIN conversation_participants cp1 ON c.id = cp1.conversation_id AND cp1.user_id = '. $currentUserId .' AND cp1.deleted_at IS NULL
               INNER JOIN conversation_participants cp2 ON c.id = cp2.conversation_id AND cp2.user_id = users.id AND cp2.deleted_at IS NULL
            WHERE c.type = '. Conversation::TYPE_TWO_USER .' LIMIT 1) AS conversation_id'),
            DB::raw('CASE 
                WHEN sent_contacts.id IS NOT NULL THEN ' .ConversationParticipant::SENT_INVITE_CONTACT . 
               ' WHEN received_contacts.id IS NOT NULL THEN ' . ConversationParticipant::RECEIVED_INVITE_CONTACT.' 
                ELSE NULL 
                END as contact_status'),
            'positions.name as position',
        ])
        ->whereNull('conversation_participants.deleted_at')
        ->orderByDesc('conversation_participants.admin')
        ->orderBy('users.last_name')
        ->paginate($perPage);
        if (!empty($query->items())) {
            foreach($query->items() as $member) {
                if ($member->id === $currentUserId) {
                    $member->conversation_id = null;
                }
            }
        }
        return $query;
    }

    /**
     * update participants of conversation
     * @param $conversationId
     * @param $user
     * @param $request
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function update($conversationId, $user, $request)
    {
        try {
            $rollback = false;
            //Check has role admin in conversation
            $result = ConversationManager::checkPermissionConversation($conversationId, $user->id, true);
            if (empty($result)) {
                return ['status_error' => ConversationParticipant::ERROR_PERMISSION];
            }
            DB::beginTransaction();
            $rollback = true;
            $addMembers = json_decode($request->add);
            $updatedMembers = json_decode($request->update);
            $delete = json_decode($request->delete);

            $listMember = self::GetConversationParticipant($conversationId, $user->id);
            $listMember = $listMember->pluck('user_id')->toArray();
            if (!empty($addMembers)) {
                //Check member exists
                $dataInsert = array();
                foreach ($addMembers as $idx => $insertItem) {
                    //Check member exists in group
                    if (!in_array($insertItem->id, $listMember)) {
                        $dataInsert[] = array(
                            'conversation_id' => $conversationId,
                            'user_id' => (int) $insertItem->id,
                            'admin' => (int) $insertItem->admin,
                            'status' => 1,
                            'created_at' => Carbon::now()
                        );
                    } else {
                        // Remove exists members from $addMembers
                        unset($addMembers[$idx]);
                    }
                }
                // Reset index of array
                $addMembers = array_values($addMembers);
                ConversationParticipant::insert($dataInsert);
                // // Gửi thông báo qua firebase
                $conversationItem = Conversation::query()->find($conversationId);
                $title = $conversationItem->name;
                $dataNotification = [
                    'id' => $conversationItem->id,
                    'title' => $title,
                    'content' => trans('message.conversation.notification.join'),
                    'avatar' => $conversationItem->avatar,
                    'notification_type' => NotificationHelper::NOTIFICATION_TYPE_CONVERSATION,
                ];
                $websiteId = null;
                if (env('USE_TENANT', false)) {
                    $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                }
                dispatch(new sendNotificationConversation($dataNotification, $dataInsert, $websiteId))->onQueue(config('queue.queueType.conversation'));
            }
            if (!empty($updatedMembers)) {
                $sql = '';
                foreach ($updatedMembers as $updateItem) {
                    $dataUpdate = '';
                    foreach ($updateItem as $key => $value) {
                        if ($key === 'id') {
                            if (!in_array($value, array_merge($listMember, [$user->id]))) {
                                DB::rollBack();
                                return ['status_error' => ConversationParticipant::ERROR_NOT_FOUND];
                            }
                            $dataWhere = '(`user_id` = ' . $value . ' AND `conversation_id` = ' . $conversationId . ')';
                        } else {
                            $dataUpdate .= '`' . $key . '` = ' . $value;
                        }
                    }
                    $dataUpdate .= ', `updated_at` = \'' . Carbon::now() . '\'';
                    $sql .= 'UPDATE `conversation_participants` SET ' . $dataUpdate . ' WHERE ' . $dataWhere . ';';
                }
                DB::unprepared($sql);
                //Emit event updateConversation
                $updatedMembers = collect($updatedMembers)->pluck('id')->toArray();
                $conversationManager = new ConversationManager();
                $conversationManager->sendNotificationSocketUpdateConversation($conversationId, $updatedMembers, ['is_admin']);

                // Generate content text message update members in group
                $this->storeMessagesGenarateInConversationParticipant(
                    $conversationId, 
                    $user, 
                    $updatedMembers, 
                    ConversationParticipant::UPDATE_MEMBERS_GROUP
                );
            }

            $dataSocketOutConversation = [];
            if (!empty($delete)) {
                $dataDelete = array();
                foreach ($delete as $deleteItem) {
                    $memberId = $deleteItem->id;
                    if (!in_array($memberId, $listMember)) {
                        DB::rollBack();
                        return ['status_error' => ConversationParticipant::ERROR_NOT_FOUND];
                    }
                    $dataDelete[] = (int) $memberId;
                }
                ConversationParticipant::query()
                    ->where('conversation_id', $conversationId)
                    ->whereIn('user_id', $dataDelete)
                    ->forceDelete();
                //Emit event outConversation to the deleted person
                $dataSocketOutConversation = [
                    'conversation_id' => $conversationId,
                    'receiver' => $dataDelete,
                    'delete_by' => $user->id
                ];

                // Generate content text message delete members in group
                $this->storeMessagesGenarateInConversationParticipant(
                    $conversationId, 
                    $user, 
                    $dataDelete, 
                    ConversationParticipant::REMOVE_MEMBERS_GROUP
                );


                //delete bookmark message
                MessageBookmark::query()
                    ->join('messages', 'messages.id', '=', 'message_bookmarks.message_id')
                    ->where('messages.conversation_id', $conversationId)
                    ->whereIn('message_bookmarks.user_id', $dataDelete)
                    ->delete();
            }
            //Check if admin is empty in the group
            $checkEmptyAdmin = ConversationParticipant::query()
                ->where('conversation_id', $conversationId)
                ->where('admin', '=', ConversationParticipant::IS_ADMIN)
                ->select()
                ->count('id');
            if (empty($checkEmptyAdmin)) {
                DB::rollBack();
                return ['status_error' => ConversationParticipant::ERROR_EMPTY_ADMIN];
            }
            DB::commit();

            // Send socket out conversation.
            if(!empty($dataSocketOutConversation)) {
                app(SocketManager::class)->emit(SocketEvent::OUT_CONVERSATION, $dataSocketOutConversation);
            }
            
            /// Send notification socket - userInConversation
            $this->sendNotificationSocketUpdateMembersInConversation(intval($conversationId));
            
            // Get members's info added to the group
            $addMembers = collect($addMembers)->pluck('id')->toArray();
            
            // Generate content text message members join group
            if ($addMembers) {
                $this->storeMessagesGenarateInConversationParticipant($conversationId, $user, $addMembers);
            }

            return $result;
        } catch (Exception $e) {
            Log::error('[ConversationParticipantManager][update] line ' . $e->getLine() . ' error ' . $e->getMessage());
            if ($rollback) {
                DB::rollBack();
            }
            throw new Exception('[ConversationParticipantManager][update] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Generates and stores a notification message about group membership changes in a conversation.
     *
     * This function constructs a notification message based on the members being added, updated, or deleted from
     * a conversation group. It retrieves user information for the specified members, generates a notification 
     * content string, and stores the message in the conversation.
     *
     * @param int $conversationId The ID of the conversation.
     * @param \App\User $user The user performing the action.
     * @param array $arrayMembers An array of member IDs involved in the update.
     * @param int|null $type The type of action performed: 1 for update, 2 for delete, or null for add.
    */
    private function storeMessagesGenarateInConversationParticipant(
        $conversationId,
        $user,
        $arrayMembers,
        $type = ConversationParticipant::ADD_MEMBERS_GROUP
    ) {
        $arrMembersInfo = User::query()
            ->select([
                'id',
                DB::raw('IFNULL(CONCAT_WS(" ", users.first_name, users.last_name),"") AS user_name')
            ])
            ->whereIn('id', $arrayMembers)
            ->get();
        
        if (empty($arrMembersInfo)) {
            return;
        }
        
        // Get message content text for add/edit/delete members in group
        $contentTextGenerateInGroup = '';
        foreach ($arrMembersInfo as $i => $member) {
            // Concat text with ',', if is the last member then concat text add/update/edit member 
            $lastContentTextMember = ', ';
            if ($i == count($arrMembersInfo) - 1) {
                $lastContentTextMember = trans('message.conversation.notification.addNewMembers');
                if ($type == ConversationParticipant::UPDATE_MEMBERS_GROUP) {
                    $lastContentTextMember = trans('message.conversation.notification.updateMembers');
                } else if ($type == ConversationParticipant::REMOVE_MEMBERS_GROUP) {
                    $lastContentTextMember = trans('message.conversation.notification.deleteMembers');
                }
            }
            $contentTextMember = "[Noti:{$member->id}-{$member->user_name}{$lastContentTextMember}]";
            $contentTextGenerateInGroup .= $contentTextMember;
        }

        // Store this message
        $request = new Request();
        $request->replace([
            'content_text' => $contentTextGenerateInGroup,
            'reply_id' => null
        ]);
        $messageManager = new MessageManager();
        $messageManager->store($conversationId, $user, $request);
    }

    /**
     * Get list member not been added in group conversation
     * @param $conversationId
     * @param $request
     * @return array|LengthAwarePaginator
     * @throws Exception
     */
    public function listMemberNotBeenAddInConversation($conversationId, $request)
    {
        try {
            $keyword = $request->get('keyword');
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $currentUserId = Auth::guard('api')->user()->id;
            $conversation = Conversation::query()
                ->whereId($conversationId)
                ->where('type', Conversation::TYPE_MULTI_USER)
                ->first();
            if (empty($conversation)) {
                return ['status_error' => Response::HTTP_NOT_FOUND];
            }
            $listMemberInConversation = self::GetConversationParticipant($conversationId, null, ['user_id']);
            $listMemberInConversation = $listMemberInConversation->pluck('user_id')->toArray();
            $listConversations = Conversation::query()
                ->leftJoin('conversation_participants', function($join) use ($currentUserId){
                    $join->on('conversations.id', '=', 'conversation_participants.conversation_id')
                        ->where('conversation_participants.user_id', '!=', $currentUserId);
                })
                ->leftJoin('users', 'conversation_participants.user_id', '=', 'users.id')
                ->where('conversations.type', '=', Conversation::TYPE_TWO_USER)
                ->whereHas('conversationParticipant', function($query) use ($currentUserId) {
                    $query->where('conversation_participants.user_id', '=', $currentUserId);
                })
                ->select([
                    'users.id'
                ])
                ->groupBy('users.id')
                ->pluck('id')
                ->toArray();
            //Compare the chat list and the list of current members in the chat
            $arrMemberNotAdd = array_diff($listConversations, $listMemberInConversation);
            $result = User::query()
                ->whereIn('users.id', $arrMemberNotAdd)
                ->when(isset($keyword), function(Builder $query) use ($keyword) {
                    $stringHelpers = new StringHelper();
                    $paramSearch = $stringHelpers->formatStringWhereLike($keyword);
                    $paramSearch = $stringHelpers->transformSearchFullname($paramSearch);
                    $query->where('users.name_search', 'LIKE', "%$paramSearch%");
                })
                ->leftJoin('positions', 'positions.id', 'users.position_id')
                ->select([
                    'users.id AS user_id',
                    'users.avatar',
                    'users.company_contract',
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                    DB::raw(User::SORT_NAME_ALPHA_BET),
                    'positions.name as position'
                ])
                ->orderBy('sort_name')
                ->paginate($perPage);
            return $result;
        } catch (Exception $e) {
            Log::error('[ConversationParticipantManager][listMemberNotBeenAddInConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
            throw new Exception('[ConversationParticipantManager][listMemberNotBeenAddInConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Send notification socket userInConversation - conversation participant
     * @param int $conversationId
     * @return void
     */
    public function sendNotificationSocketUpdateMembersInConversation(int $conversationId)
    {
        $listMembersInConversation = self::GetConversationParticipant($conversationId);
        $listMembersInConversation = $listMembersInConversation->pluck('user_id')->toArray();

        $dataSocket = [
            'conversation_id' => $conversationId,
            'receiver' => $listMembersInConversation,
        ];
        $conversation = Conversation::query()
            ->where('id', $conversationId)
            ->select(['id', 'name', 'type', 'avatar'])
            ->first();

        if ($conversation) {
            $dataSocket['type'] = $conversation['type'];
            $dataSocket['conversation_avatar'] = $conversation['avatar'];
            $dataSocket['conversation_name'] = $conversation['name'];
        }
        app(SocketManager::class)->emit(SocketEvent::UPDATE_MEMBERS_IN_CONVERSATION, $dataSocket);
    }

    /**
     * Get a list of users in the same group 
     * @param Request $request
     * @return array
     */
    public function listSenders($request) {
        $userID = Auth::guard('api')->user()->id;
        $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
        $senders = User::select([
            "users.id", 
            DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
            DB::raw(User::SORT_NAME_ALPHA_BET),
            "users.avatar"
        ])
        ->join("conversation_participants", function($join) use ($userID) {
            $join->on("conversation_participants.user_id", "=", "users.id")
                ->whereIn("conversation_participants.conversation_id", function($query) use ($userID) {
                    $query->select("conversation_id")
                    ->from('conversation_participants')
                    ->where('user_id', $userID);
            })
            ->join("conversations", function($join) {
                $join->on("conversation_participants.conversation_id", "=" , "conversations.id")
                    ->where("conversations.is_hide", Conversation::NOT_HIDE);
            });
        })
        ->orderByRaw("users.id = ? DESC", $userID)
        ->orderBy('sort_name')
        ->distinct("users.id")
        ->paginate($perPage);

        return $senders;
    }

    /**
     * Check user is admin in conversation
     *
     * @param int $userId
     * @param int $conversationId
     * @return bool
     */
    public function isAdminInConversation($userId, $conversationId)
    {
        $getConversationParticipants = self::GetConversationParticipant($conversationId, null, ['user_id', 'admin'])->toArray();
        if (!empty($getConversationParticipants)) {
            foreach ($getConversationParticipants as $participant) {
                if ($participant['user_id'] == $userId) {
                    if ($participant['admin'] == ConversationParticipant::IS_ADMIN) {
                        return true;
                    }
                    break;
                }
            }
        }
        return false; 
    }
    /**
     * disband members in group
     * @param int $conversationId
     * @return array
     */
    public static function disbandMembers($conversationId): array
    {
        $query = ConversationParticipant::query()->where('conversation_id', $conversationId);
        $userIds = clone ($query)->pluck('user_id');
        $query->delete();
        return $userIds->toArray();
    }
}
