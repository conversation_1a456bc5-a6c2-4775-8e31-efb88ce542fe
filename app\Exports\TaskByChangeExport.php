<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Cell\Hyperlink;
use Maatwebsite\Excel\Events\AfterSheet;

class TaskByChangeExport implements WithHeadings, WithColumnWidths, WithStyles
{
    function __construct($result,$totalEstimatedTime) {
        $this->result = $result;
        $this->totalEstimatedTime = $totalEstimatedTime;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->result;
    }

    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.number_order'),
            trans('language.project'),
            trans('language.name'),
            trans('language.detail_description'),
            trans('language.estimate_time'),
            trans('language.attachments')
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 50,
            'C' => 50,
            'D' => 50,
            'E' => 10,
            'F' => 50,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $num=2;
        foreach($this->result as $item){
            $sheet->setCellValue('A'.$num, $item['number_order']);
            $sheet->setCellValue('B'.$num, $item['project']);
            $sheet->setCellValue('C'.$num, $item['name']);
            $sheet->setCellValue('D'.$num, $item['detail_description']);
            $sheet->setCellValue('E'.$num, $item['estimated_time']);
            if(!empty($item['attachments'])){
                $numberFile = count($item['attachments']);
                $sheet->mergeCells('A'.$num.':A'.($num+$numberFile-1));
                $sheet->mergeCells('B'.$num.':B'.($num+$numberFile-1));
                $sheet->mergeCells('C'.$num.':C'.($num+$numberFile-1));
                $sheet->mergeCells('D'.$num.':D'.($num+$numberFile-1));
                $sheet->mergeCells('E'.$num.':E'.($num+$numberFile-1));
                foreach($item['attachments'] as $key=>$value){
                    $sheet->setCellValue(('F'.($num+$key)), $value);
                    $sheet->getCell('F'.($num+$key))->getHyperlink()->setUrl('./files/'.$value);
                    $sheet->getStyle('F'.($num+$key))->applyFromArray([
                        'font' => [
                            'color' => ['rgb' => '0000FF'],
                            'underline' => 'single'
                        ]
                    ]);
                }
                $num+= count($item['attachments']);
            }else{
                $num+= 1;
            }
        }

        $sheet->getStyle('A1:F1')->applyFromArray(array(
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ));

        // $num +=1;

        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ]
        ];
        $sheet->getStyle('A1:F'.$num)->applyFromArray($styleArray);
        $sheet->getStyle('A1:F'.$num)->getAlignment()->setWrapText(true);
        $sheet->mergeCells('A'.$num.':D'.$num);
        $sheet->setCellValue('A'.$num, trans('language.total_estimate'));
        $sheet->setCellValue('E'.$num, $this->totalEstimatedTime);
        $sheet->getStyle('A'.$num)->applyFromArray(array(
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ));
    }
}
