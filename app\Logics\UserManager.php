<?php

namespace App\Logics;

use App\Enums\DeviceEndpointEnum;
use App\Enums\GenderEnum;
use App\Helpers\NotificationHelper;
use App\Helpers\StringHelper;
use App\Models\ProjectMember;
use App\Models\UserDevice;
use App\Traits\StorageTrait;
use App\Traits\ImageTrait;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\File;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use League\Flysystem\FileNotFoundException;

class UserManager
{
    use StorageTrait;
    use ImageTrait;

    /**
     * Get all users
     * @param $keyword
     * @param $deleted
     * @return User|\Illuminate\Database\Eloquent\Builder
     */
    public function getAllUsers(Request $request) {
        $users = User::select('users.*', 'work_places.name as work_place_name', 'departments.name as department_name',
                        'positions.name as position_name', 'users.working_type', 'communes.name as commune_name',
                        'districts.name as district_name', 'prefectures.name as prefecture_name')
                ->where('users.id', '!=', 0)
                ->with('prefecture', 'district', 'commune', 'position')
                ->leftjoin('communes', 'communes.id','users.commune_id')
                ->leftjoin('districts', 'districts.id','users.district_id')
                ->leftjoin('prefectures', 'prefectures.id','users.prefecture_id')
                ->leftjoin('work_places', 'work_places.id', 'users.work_place_id')
                ->leftjoin('departments', 'departments.id', 'users.department_id')
                ->leftjoin('positions', 'positions.id', 'users.position_id')
        ;
        $stringHelper = new StringHelper();
        if(isset($request->id)) {
            $items = array_filter(array_map("trim",explode(",", $request->id)));
            if(!empty($items)){
                $users->whereIn('users.id', explode(",", $request->id));
            }
        }
        if(isset($request->keyword)) {
            $keyword = $stringHelper->formatStringWhereLike($request->keyword);
            $users->where(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%'.$keyword.'%');
        }
        if(isset($request->email)) {
            $email = $stringHelper->formatStringWhereLike($request->email);
            $users->where('email', 'LIKE', '%'.$email.'%');
        }
        if(isset($request->phone)) {
            $phone = $stringHelper->formatStringWhereLike($request->phone);
            $users->where('phone', 'LIKE', '%'.$phone.'%');
        }
        if(isset($request->gender)) {
            $users->whereIn('gender', $request->gender);
        }
        if(isset($request->position)) {
            $users->whereIn('position_id', $request->position);
        }
        if(isset($request->work_place_id)) {
            $users->whereIn('work_place_id', $request->work_place_id);
        }
        if(isset($request->start_at)) {
            $startedAt = Carbon::createFromFormat('d/m/Y',$request->start_at)->format('Y-m-d');
            $users->where('started_at', $startedAt);
        }
        if($request->has('deleted')) {
            $users = $users->onlyTrashed();
        }

        if ($request->sort === 'hometown') {
            $direction = in_array($request->direction, ['asc', 'desc']) ? $request->direction : 'asc';
            $users = $users->orderBy('prefectures.name', $request->direction)
                        ->orderBy('districts.name', $request->direction)
                        ->orderBy('communes.name', $request->direction);
        }
        return $users;
    }

    /**
     * Get user with remaining leave day of the user
     *
     * @param $deleted
     * @param null $user_id
     * @return mixed
     */
    public function getUserWithLeaveDay($user_id, $deleted) {
        $users = User::where('id', $user_id);

        if ($deleted) {
            $users = $users->onlyTrashed();
        }

        // Get remaining leave day for user
        $users = $users->with(['userRemainingLeaveDay' => function ($q) {
            $q->orderBy('month', 'DESC')->first();
        }]);

        return $users;
    }

    /**
     * Create a new user
     *
     * @param $parameters : update parameters
     * @param null $avatar : file request for avatar
     * @param null $face_image
     * @return User
     */
    public function createUser($parameters, $avatar=null, $face_image=null) {
        //--- 1. Create user ---
        $user =  User::create($parameters);

        //--- 2. Update avatar + face image ---
        // Store avatar image
        $avatar_path = null;
        if($avatar) {
            $avatar = $this->resizeImage($avatar->getRealPath(), USER_AVATAR_WIDTH);
            $avatar_path = $this->uploadFileByStream($avatar, USER_DIR.'/'.$user->id.'/'.Str::random(25).'.jpg');
        }
        // Store face image
        $face_path = null;
        if($face_image) {
            $face_image = $this->resizeImage($face_image->getRealPath(), USER_FACE_IMAGE_WIDTH);
            $face_path = $this->uploadFileByStream($face_image, USER_DIR.'/'.$user->id.'/'.Str::random(25).'.jpg');
        }

        $user->update([
            'avatar' => $avatar_path,
            'face_image' => $face_path
        ]);

        return $user;
    }

    /**
     * Update user profile
     *
     * @param $user: User
     * @param $parameters: update parameters
     * @param null $avatar: file request for avatar
     * @param null face_image: file request for face_image
     */
    public function updateUserProfile($user, $parameters, $avatar=null, $face_image=null) {
        //--- 1. Update user ---
        // Save upload image from request
        $old_avatar_path = null;
        $avatar_path = $user->avatar;
        if($avatar) {
            $old_avatar_path = $user->avatar;
            $avatar = $this->resizeImage($avatar->getRealPath(), USER_AVATAR_WIDTH);
            $avatar_path = $this->uploadFileByStream($avatar, USER_DIR.'/'.$user->id.'/'.Str::random(25).'.jpg');
        }
        $old_face_path = null;
        $face_path = $user->face_image;
        if($face_image) {
            $old_face_path = $user->face_image;
            $face_image = $this->resizeImage($face_image->getRealPath(), USER_FACE_IMAGE_WIDTH);
            $face_path = $this->uploadFileByStream($face_image, USER_DIR.'/'.$user->id.'/'.Str::random(25).'.jpg');
        }

        // Add avatar and face_image to update parameters
        $parameters += [
            'avatar' => $avatar_path,
            'face_image' => $face_path
        ];

        $user->update($parameters);

        // --- 2.Remove old file ---
        if($old_avatar_path) {
            // Remove old file
            $this->deleteFile($old_avatar_path);
        }
        if($old_face_path) {
            $this->deleteFile($old_face_path);
        }
    }

    /**
     * Check user has role
     *
     * @param $userId
     * @param $projectId
     */

    public function hasProjectRole($userId,$projectId,$roleId){
        $numberRole = ProjectMember::where('user_id',$userId)
            ->where('project_id',$projectId)
            ->where('role_id',$roleId)->count();
        if ($numberRole == 0){
            return false;
        }
        return true;
    }

    /**
     * Retrieves the image for a user based on the provided ID, image type, and site.
     *
     * @param int $id The ID of the user whose image is to be retrieved.
     * @param string $typeImage The type of image to retrieve (default is 'avatar').
     * @param string $site The site context for the image retrieval (default is BEEID).
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|null The image file response or null if the file is not found.
     *
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException If the file cannot be found.
     */
    public function getImage($id, $typeImage = 'avatar', $site = BEEID)
    {
        try {
            $user = User::withTrashed()->find($id, [$typeImage . ' as image', 'gender']);

            // If user not found or image is empty, return default image
            if (empty($user) || empty($user->image)) {
                $defaultImages = [
                    BEECHAT => '/public/images/avatar_default.png',
                    GenderEnum::MALE => '/public/images/default-male.jpg',
                    GenderEnum::FEMALE => '/public/images/default-female.jpg',
                    'default' => '/public/images/user-default.png',
                ];

                // Default image for BeeID
                $defaultImagePath = $defaultImages['default'];

                if ($site === BEECHAT) {
                    // Default image for BEECHAT
                    $defaultImagePath = $defaultImages[BEECHAT];
                } elseif ($user) {
                    // Default image for BEEID for the user's gender
                    $defaultImagePath = $defaultImages[$user->gender] ?? $defaultImages['default'];
                }

                return response()->file(base_path() . $defaultImagePath);
            }

            // If user found and image is not empty, return the the user's image
            return Storage::disk(FILESYSTEM)->response($user->image);
        } catch (FileNotFoundException $e) {
            return null;
        }
    }

    public function getUsersByIDs($ids){
        return User::withTrashed()->select('id','first_name','last_name')->whereIn('id',$ids)->get();
    }

    /**
     * Save device token by user
     * @param $deviceToken
     * @return \Illuminate\Database\Eloquent\Model|\Illuminate\Database\Eloquent\Builder $userDevice
     */
    public function saveDeviceTokenByUser($deviceToken)
    {
        $userDevice = UserDevice::query()->updateOrCreate(
            [
                'user_id' => Auth::id(),
                'device_token' => $deviceToken,
            ],
            [
                'device_information' => NotificationHelper::DEVICE_WEB,
                'status' => UserDevice::STATUS_ONLINE,
                'last_connection' => Carbon::now(),
            ]
        );
        return $userDevice;
    }


    /**
     * Delete device token
     * @param $deviceToken
     * @param $userId
     * @return bool
     */
    public function deleteDeviceToken($deviceToken, $userId)
    {
        UserDevice::query()
            ->where('device_token', $deviceToken)
            ->where('user_id', $userId)
            ->delete();
        return true;
    }

    /**
     * Get user by role
     *
     * @param $role
     * @return mixed
     */
    public function getUsersByRole($role)
    {
        return User::select('users.id','users.first_name','users.last_name')
        ->join("model_has_roles", "model_has_roles.model_id", "users.id")
        ->join('roles','roles.id','model_has_roles.role_id')
        ->where('roles.name', $role)
        ->distinct()
        ->get();
    }

    /**
     * Get user by email or phone
     * @param $username
     * @return mixed
     */
    public function getUserByEmail($username)
    {
        return User::select('id', 'email', 'phone', 'language_id')->where('email', $username)->first();
    }

    /**
     * update user info to device database.
     *
     * @param $user User
     * @param $isUpdate boolean
     * @param $faceImage File|UploadedFile
     * @return array|false|mixed
     * @throws Exception
     */
    public function updateUserInfoToTimekeepingDevice($user, $isUpdate, $faceImage)
    {
        // Get device info from env.
        $deviceIp    = config('timekeeping.device_ip');
        $deviceUser  = config('timekeeping.device_auth_user');
        $devicePass  = config('timekeeping.device_auth_pass');
        $deviceId    = config('timekeeping.device_id');

        $endpoint = $isUpdate ? DeviceEndpointEnum::EditPerson : DeviceEndpointEnum::AddPerson;

        $userInfo = [
            'DeviceID'     => $deviceId,
            'IdType'       => 0,
            'CustomizeID'  => $user->id,
            'Name'         => $user->name,
            'Gender'       => $user->gender ?? GenderEnum::MALE,
        ];

        $payload = [
            'operator' => $endpoint,
            'info'     => $userInfo,
        ];
        
        // if create and have face image: send to device
        if ($faceImage != null) {
            $resizedImage = $this->resizeImage($faceImage, 320);
            $mime = $resizedImage->mime();
            $base64 = base64_encode((string)$resizedImage);
            $payload['picinfo'] = "data:{$mime};base64,{$base64}";
        }
        
        $apiUrl = "http://{$deviceIp}/action/{$endpoint}";

        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
            ])
                ->withBasicAuth($deviceUser, $devicePass)
                ->timeout(10)
                ->send('POST', $apiUrl, [
                    'body' => json_encode($payload, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE)
                ]);
            Log::info("UserManager@updateUserInfoToTimekeepingDevice with response: " . $response);
            return $response->json();
        } catch (Exception $e) {
            Log::error($e);
        }
    }
}
