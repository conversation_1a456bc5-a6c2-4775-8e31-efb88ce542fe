<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Inventory extends Model
{
    use SoftDeletes;

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'inventories';

    public $sortable = [
        'id',
        'name',
        'start_at',
        'end_at',
        'status',
    ];

    protected $fillable = [
        'name',
        'note',
        'status',
        'start_date',
        'end_date'
    ];

    const STATUS_OPEN = 0;
    const STATUS_CLOSE = 1;

    /**
     * Get users
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function users()
    {
        return $this->belongsToMany('App\User', 'user_inventories', 'inventories_id', 'user_id');
    }
}
