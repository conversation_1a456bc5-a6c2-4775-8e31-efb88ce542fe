<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\GetFileConversationRequest;
use App\Http\Requests\MessageRequest;
use App\Http\Requests\SearchMessageRequest;
use App\Logics\MessageManager;
use App\Models\ConversationParticipant;
use App\Models\Message;
use App\Services\MessageToCounterService;
use App\Utils\LogUtil;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MessageController extends AbstractApiController
{
    /**
     * @var MessageManager
     */
    protected $messageManager;

    public function __construct(MessageManager $messageManager)
    {
        $this->messageManager = $messageManager;
    }
    
    /**
     * list message of nurse
     * @param $conversationId
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function list($conversationId, Request $request)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            $data = $this->messageManager->list($conversationId, $idUser, $request, $request->messageId);
            if (isset($data['status_error'])) {
                if ($data['status_error'] == Message::ERROR_PERMISSION) {
                    return $this->respondForbidden(__('message.message.fail'));
                }
                return $this->respondNotFound(__('message.message.notFound'));
            }
            return $this->respond($data);
        } catch (Exception $e) {
            Log::error("[MessageController][listMessage] error " . $e->getMessage() . ' Line ' . $e->getLine());
            throw new Exception('[MessageController][listMessage] error ' . $e->getMessage() . ' Line ' . $e->getLine());
        }
    }

    /**
     * Search message
     */
    public function search(SearchMessageRequest $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $params = $request->all();

            $messages = $this->messageManager->searchMessage($userId, $params);

            return $this->respondWithPagination($messages, __('message.message.success'));
        } catch (Exception $e) {
            Log::error('[MessageController][search] File: ' . $e->getFile() . ' -- Line: ' . $e->getLine() . ' -- Message: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create message in conversation by User
     * @param $conversationId
     * @param MessageStoreRequest $request
     * @return json
     * @throws Exception
     */
    public function store($conversationId, MessageRequest $request)
    {
        try {
            $data = $this->messageManager->store($conversationId, Auth::guard('api')->user(), $request);
            $msg =  __('message.message.create.success');
            if (isset($data['status_error'])) {
                $msg = __('message.message.create.fail');
                if ($data['status_error'] == Message::ERROR_DATA) {
                    return $this->respondBadRequest($msg);
                }
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][storeMessageOfUser] error " . $e->getMessage());
            throw new Exception('[MessageController][storeMessageOfUser] error ' . $e->getMessage());
        }
    }

    /**
     * update message in conversation by User
     * @param $messageId
     * @param MessageStoreRequest $request
     * @return json
     * @throws Exception
     */
    public function update($messageId, MessageRequest $request)
    {
        try {
            $data = $this->messageManager->update($messageId, Auth::guard('api')->user(), $request);
            $msg =  __('message.message.update.success');
            if (isset($data['status_error'])) {
                $msg = __('message.message.update.fail');
                if ($data['status_error'] == Message::ERROR_DATA) {
                    return $this->respondBadRequest($msg);
                }
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][updateMessageOfUser] error " . $e->getMessage());
            throw new Exception('[MessageController][updateMessageOfUser] error ' . $e->getMessage());
        }
    }

    /**
     * destroy message in conversation by User
     * @param $messageId
     * @param MessageStoreRequest $request
     * @return json
     * @throws Exception
     */
    public function destroy($messageId)
    {
        try {
            $data = $this->messageManager->destroy($messageId, Auth::guard('api')->user());
            $msg =  __('message.message.destroy.success');
            if (isset($data['status_error'])) {
                $msg = __('message.message.destroy.fail');
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][updateMessageOfUser] error " . $e->getMessage());
            throw new Exception('[MessageController][updateMessageOfUser] error ' . $e->getMessage());
        }
    }

    /**
     * read message of nurse
     * @param $conversationId
     * @param $messageId
     * @return json
     * @throws Exception
     */
    public function read($conversationId, $messageId, Request $request)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            $isReadInThread = $request->boolean('is_read_in_thread');
            $data = $this->messageManager->read($conversationId, $messageId, $idUser, true, $isReadInThread);
            $msg = __('message.message.readMessage.success');
            if ($data === null) {
                $msg = __('message.message.readMessage.fail');
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][readMessageOfNurse] error " . $e->getMessage());
            throw new Exception('[MessageController][readMessageOfNurse] error ' . $e->getMessage());
        }
    }

    /**
     * pin message
     * @param $request
     * @return json
     * @throws Exception
     */
    public function pinMessage($messageId)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            [$data,$action] = $this->messageManager->pinMessage($idUser, $messageId);
            $msg =  __('message.message.pin.success', ['action' => $action]);
            if (isset($data['status_error'])) {
                $msg = __('message.message.pin.fail', ['action' => $action]);
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
                if ($data['status_error'] == Response::HTTP_BAD_REQUEST) {
                    return $this->respondBadRequest(__('message.message.pin.max'));
                }
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][pinMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[MessageController][pinMessage] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * getListFileMessage
     * @param $request
     * @return json
     * @throws Exception
     */
    public function getListFileMessage(GetFileConversationRequest $request)
    {
        try {
            $data = $this->messageManager->getListFileMessage($request);
            return $this->respond($data);
        } catch (Exception $e) {
            Log::error("[MessageController][getListFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[MessageController][getListFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
    /**
     * Show list pin message
     * @param         $conversationId
     * @return json
     * @throws Exception
     */
    public function listPinMessage($conversationId)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $data = $this->messageManager->getListPinMessage($userId, $conversationId);
            $msg =  __('message.message.listPinMessage.success');
            if (isset($data['error_status'])) {
                $msg = __('message.message.listPinMessage.fail');
                if ($data['error_status'] == Message::ERROR_PERMISSION) {
                    return $this->respondForbidden($msg);
                }
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][listPinMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[MessageController][listPinMessage] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
    /**
     * Delete file
     * @return json
     * @throws Exception
     */
    public function deleteFileMessage($fileId)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $data = $this->messageManager->deleteFileMessage($userId, $fileId);
            $msg =  __('message.message.delete.success', ['attribute' => 'File']);
            if (isset($data['error_status'])) {
                $msg = __('message.message.delete.fail', ['attribute' => 'File']);
                if ($data['error_status'] == ConversationParticipant::ERROR_PERMISSION) {
                    return $this->respondForbidden($msg);
                }
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][deleteFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[MessageController][deleteFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get list thread of the user
     */
    public function getListThread(Request $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $params = $request->all();

            $data = $this->messageManager->getListThread($userId, $params);

            $msg = __('message.message.thread.success');

            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Get total message to user.
     * 
     * @param Request $request
     * @return json
     */
    public function totalMessageTo(Request $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $messageToCounterService = new MessageToCounterService();
            $countMessagesToUser = $messageToCounterService->countMessagesToUser($userId);
            return $this->renderJsonResponse([
                'badge' => $countMessagesToUser
            ], __('language.success'));
        } catch (Exception $exception) {
            LogUtil::error($exception->getMessage());
            throw new Exception($exception->getMessage());
        }
    }
}
