<?php

namespace App\Jobs;

use App\Logics\EvaluationManager;

class CreateEvaluation extends BaseQueue
{
    public $applyFor;
    public $evaluationForm;
    public $evaluator;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($applyFor, $evaluationForm, $evaluator, $websiteId=null)
    {
        parent::__construct($websiteId);

        $this->applyFor = $applyFor;
        $this->evaluationForm = $evaluationForm;
        $this->evaluator = $evaluator;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        parent::handle();

        $evaluationManager = new EvaluationManager();
        $evaluationManager->createEvaluate($this->applyFor ,$this->evaluationForm, $this->evaluator);
    }
}
