<?php

namespace App\Jobs;

use App\Jobs\BaseQueue;
use App\Mail\MailForgotPassword;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendEmailForgotPassword extends BaseQueue
{
    /**
     * @var
     */
    protected $user;
    protected $code;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user, $code, $websiteId = null)
    {
        parent::__construct($websiteId);
        $this->user = $user;
        $this->code = $code;
    }

    /**
     * Execute the job.
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        parent::handle();
        parent::setLocaleByLanguageId($this->user->language_id);
        try {
            $content = new MailForgotPassword($this->code);
            Mail::to($this->user->email)->send($content);
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
