<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OrderByMonthExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    protected $data;
    function __construct($data) {
        $this->data = $data;
    }
    
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return (collect($this->data));
    }
    
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.date'),
            trans('language.order_lunch.amount'),
        ];
    }
    
    public function columnWidths(): array
    {
        return [
            'A' => 15,
            'B' => 15,
        ];
    }
    
    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:B1')->applyFromArray(array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ));
        
        $qtt = count($this->data);
        $sum = 0;
        foreach ($this->data as $val){
            $sum+=$val['count'];
        }
        $sheet->setCellValue('A'.($qtt+2), trans('language.total'));
        $sheet->setCellValue('B'.($qtt+2), $sum);
        
        $sheet->getStyle('A1:B'.($qtt+2))->getAlignment()->setWrapText(true);
        $alignCenter = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ];
        $sheet->getStyle("A:A")->applyFromArray($alignCenter);
        $sheet->getStyle("B:B")->applyFromArray($alignCenter);
        
        $sheet->getStyle('A'.($qtt+2).':B'.($qtt+2).'')->applyFromArray(array(
            'font' => [
                'bold' => true,
                'color' => [
                    'argb' => 'FF0000',
                ],
            ]
        ));
    }
}
