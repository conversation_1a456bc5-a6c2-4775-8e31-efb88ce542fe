<?php

namespace App\Jobs;

use App\Mail\ReportTestMail;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendMailReportTest extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $info;
    protected $filePath;
    protected $check;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($info, $filepath, $check, $websiteId=null)
    {
        parent::__construct($websiteId);

        $this->info = $info;
        $this->filePath = $filepath;
        $this->check = $check;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //
        $email = new ReportTestMail($this->info, $this->filePath);
        $mail = Mail::to($this->info['PM_email']);

        if($this->check) {
            $mail->cc([REPORT_TEST_CC_TO_MAIL]);
        }
        $mail->send($email);
    }
}
