<?php

namespace App\Logics;

use App\Models\TaskPriority;
use App\Models\TaskStatus;
use App\Models\TaskType;
use Illuminate\Support\Facades\DB;

class SortableManager
{
    public function sort($model, $currentId, $prevId, $type = null) {
        DB::beginTransaction();
        try {
            $getAllRecord = $model::when(!empty($type), function($qr) use ($type){
                $qr->where('type', $type);
            })->get();
            $prev = $getAllRecord->find($prevId);
            $curent = $getAllRecord->find($currentId);
            $newSortOrder = $prev->sort_order??0;
            $curentSortOrder = $curent->sort_order;
    
            if ($curentSortOrder < $newSortOrder) {
                $model::withTrashed()->where('sort_order', '>', $curentSortOrder)
                ->where('sort_order', '<=', $newSortOrder)
                ->when(!empty($type), function ($query) use ($type){
                    $query->where('type', $type);
                })
                ->withTrashed()
                ->decrement('sort_order');
                $curent->update(['sort_order' => $newSortOrder]);
            } else {
                $model::withTrashed()->where('sort_order', '>', $newSortOrder)
                ->where('sort_order', '<', $curentSortOrder)
                ->when(!empty($type), function ($query) use ($type){
                    $query->where('type', $type);
                })
                ->withTrashed()
                ->increment('sort_order');
                $curent->update(['sort_order' => $newSortOrder+1]);
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }    
    }
}
