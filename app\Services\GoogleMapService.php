<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class GoogleMapService
{
    /**
     * Retrieves the location name (formatted address) based on the given latitude and longitude.
     *
     * @param float $latitude  The latitude of the location.
     * @param float $longitude The longitude of the location.
     * 
     * @return string The formatted address of the location if found, otherwise 'Location not found'.
     */
    public static function getLocationName($latitude, $longitude)
    {
        try {
            $response = Http::get(GG_MAP_API_URL, [
                'latlng' => "{$latitude},{$longitude}",
                'key' => GOOGLE_MAPS_API_KEY,
                'location_type' => GG_MAP_API_OPTION_LOCATION_TYPE
            ]);
            Log::debug('Cornected to Google Maps API:' . $response);
            $responseData = $response->json();

            if (!$response->ok()) {
                Log::error('Fail connect to Google Maps API:' . PHP_EOL . $responseData);
                throw new Exception('Fail connect to Google Maps API');
            }
            if (isset($responseData['results'][0])) {
                return $responseData['results'][0]['formatted_address'];
            } else {
                return __('message.location_not_found');
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Retrieves list location based on the given latitude and longitude.
     *
     * @param float $latitude  The latitude of the location.
     * @param float $longitude The longitude of the location.
     */
    public static function getListLocation($latitude, $longitude)
    {
        try {
            $response = Http::get(GG_MAP_API_URL, [
                'latlng' => "{$latitude},{$longitude}",
                'key' => GOOGLE_MAPS_API_KEY,
                'location_type' => GG_MAP_API_OPTION_LOCATION_TYPE
            ]);
            Log::debug('Cornected to Google Maps API:' . $response);
            $responseData = $response->json();

            if (!$response->ok()) {
                Log::error('Fail connect to Google Maps API:' . PHP_EOL . $responseData);
                throw new Exception('Fail connect to Google Maps API');
            }

            if (isset($responseData['results'])) {
                return $responseData['results'];
            }

            return __('message.location_not_found');
        } catch (\Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
