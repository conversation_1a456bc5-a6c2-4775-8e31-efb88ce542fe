<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use App\User;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;



class TaskByProjectExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths, WithStyles
{
    function __construct($tasks) {
            $this->tasks = $tasks;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->tasks;
    }

     /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.project'),
            trans('language.type'),
            trans('language.staff'),
            trans('language.estimate_time')
        ];
    }


    /**
     * Mapping data
     *
     * @return array
     */
    public function map($tasks): array
    {
        return [
            $tasks->project_name,
            $tasks->type_name,
            $tasks->user_name,
            $tasks->estimated_time
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 40,
            'B' => 20,
            'C' => 35,
            'D' => 15
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:D1')->applyFromArray(array(
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));

        $num = $this->tasks->count()+1;

        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        $sheet->getStyle('A1:D'.$num)->applyFromArray($styleArray);
        $sheet->mergeCells('A'.$num.':C'.$num);
        $sheet->setCellValue('A'.$num, trans('language.total_estimate'));

        $sheet->getStyle('A'.$num)->applyFromArray(array(
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));
    }
}
