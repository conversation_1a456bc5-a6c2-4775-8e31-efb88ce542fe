<?php

namespace App\Exports\Asset;

use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AssetDataSheetExport implements WithEvents, WithStyles, WithColumnWidths
{
    protected $sheetName;
    protected $dataTable;

    function __construct($sheetName, $dataTable) {
        $this->sheetName = $sheetName;
        $this->dataTable = $dataTable;
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 25,
            'G' => 25,
            'H' => 25,
            'I' => 25,
            'J' => 25,
            'K' => 25,
            'L' => 25,
            'M' => 25,
            'N' => 25,
            'O' => 25,
            'P' => 25,
            'Q' => 25,
            'R' => 25,
            'S' => 25,
            'T' => 25,
            'U' => 25,
            'V' => 25,
        ];
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        $style = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
        ];
        return [
            AfterSheet::class => function (AfterSheet $event) use ($style){

                $rowTable1 = (count($this->dataTable['table1'])) + 2;
                $rowTable2 = (count($this->dataTable['table2'])) + 2;
                $rowTable3 = (count($this->dataTable['table3'])) + 2;
                $rowTable4 = (count($this->dataTable['table4'])) + 2;
                $rowTable5 = (count($this->dataTable['table5'])) + 2;
                $rowTable6 = (count($this->dataTable['table6'])) + 2;
                $rowTableSupplier = (count($this->dataTable['tableSupplier'])) + 2;
                $rowTableBiddingPackage = (count($this->dataTable['tableBiddingPackages'])) + 2;
                $rowTableUsers = (count($this->dataTable['tableUsers'])) + 2;
                $rowTableShortNames = (count($this->dataTable['tableShortName'])) + 2;
                $event->getSheet()->getDelegate()->getStyle('B2:B'.$rowTable1)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('D2:D'.$rowTable2)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('F2:F'.$rowTable3)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('H2:H'.$rowTable4)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('J2:J'.$rowTable5)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('L2:L'.$rowTable6)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('N2:N'.$rowTableSupplier)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('P2:P'.$rowTableBiddingPackage)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('R2:R'.$rowTableUsers)->applyFromArray($style);
                $event->getSheet()->getDelegate()->getStyle('T2:T'.$rowTableShortNames)->applyFromArray($style);

                $protection = $event->getSheet()->getDelegate()->getProtection();
                $protection->setSheet(true);
                $protection->setSort(true);
                $protection->setInsertRows(true);
                $protection->setFormatCells(true);
            },

        ];
    }

    /**
     * @param Worksheet $sheet
     * @return mixed
     * @throws Exception
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->setTitle($this->sheetName);

        $style = array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'font' => [
                'name' => 'Times New Roman',
                'bold' => true,
                'size' => 11
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ]
        );
        $sheet->getStyle('B2')->applyFromArray($style);
        $sheet->getStyle('D2')->applyFromArray($style);
        $sheet->getStyle('F2')->applyFromArray($style);
        $sheet->getStyle('H2')->applyFromArray($style);
        $sheet->getStyle('J2')->applyFromArray($style);
        $sheet->getStyle('L2')->applyFromArray($style);
        $sheet->getStyle('N2')->applyFromArray($style);
        $sheet->getStyle('P2')->applyFromArray($style);
        $sheet->getStyle('R2')->applyFromArray($style);
        $sheet->getStyle('T2')->applyFromArray($style);

        $sheet->getStyle('A:Z')->applyFromArray([
            'font' => [
                'name' => 'Times New Roman',
                'size' => 11
            ],
            'alignment' => [
                'wrapText' => true
            ]
        ]);

        $row = 3;
        $listHeading = array(
            trans('language.management_unit_id'),
            trans('language.type_asset'),
            trans('language.department_use_asset'),
            trans('language.condition'),
            trans('language.source_of_origin'),
            trans('language.premises'),
            trans('language.asset_supplier'),
            trans('language.bidding_package_asset'),
            trans('language.asset_user'),
            trans('language.name_short')
        );
        $sheet->setCellValue('B2', $listHeading[0]);
        $sheet->setCellValue('D2', $listHeading[1]);
        $sheet->setCellValue('F2', $listHeading[2]);
        $sheet->setCellValue('H2', $listHeading[3]);
        $sheet->setCellValue('J2', $listHeading[4]);
        $sheet->setCellValue('L2', $listHeading[5]);
        $sheet->setCellValue('N2', $listHeading[6]);
        $sheet->setCellValue('P2', $listHeading[7]);
        $sheet->setCellValue('R2', $listHeading[8]);
        $sheet->setCellValue('T2', $listHeading[9]);

        $listColumn = $this->columnWidths();
        $listColumn = array_keys($listColumn);
        foreach ($listColumn as $key => $value) {
            $sheet->setCellValue(($value . 1), '');
        }

        foreach ($this->dataTable['table1'] as $key => $value){
            $sheet->setCellValue(('B'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['table2'] as $key => $value){
            $sheet->setCellValue(('D'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['table3'] as $key => $value){
            $sheet->setCellValue(('F'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['table4'] as $key => $value){
            $sheet->setCellValue(('H'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['table5'] as $key => $value){
            $sheet->setCellValue(('J'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['table6'] as $key => $value){
            $sheet->setCellValue(('L'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['tableSupplier'] as $key => $value){
            $sheet->setCellValue(('N'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['tableBiddingPackages'] as $key => $value){
            $sheet->setCellValue(('P'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['tableUsers'] as $key => $value){
            $sheet->setCellValue(('R'.($row+$key)), $value['name']);
        }

        foreach ($this->dataTable['tableShortName'] as $key => $value){
            $sheet->setCellValue(('T'.($row+$key)), $value['name']);
        }
    }
}