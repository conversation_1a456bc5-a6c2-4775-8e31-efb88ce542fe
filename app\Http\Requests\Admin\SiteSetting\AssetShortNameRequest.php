<?php

namespace App\Http\Requests\Admin\SiteSetting;

use Illuminate\Foundation\Http\FormRequest;

class AssetShortNameRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'max:200'],
            'note' => ['max:1000']
        ];
    }

     public function attributes()
    {
        return [
            'name' => trans('language.site_setting_asset_short_name.name'),
            'note' => trans('language.site_setting_asset_short_name.note')
        ];
    }
}
