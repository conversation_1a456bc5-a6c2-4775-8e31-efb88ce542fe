<?php

namespace App\Console\Commands;

use Illuminate\Console\Scheduling\Schedule;
use App\Services\BackupJobFactory;

class DatabaseBackup extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:backup_db_everyday {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dump DB at 00:00 daily.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    protected function _handle()
    {
        if(env('USE_TENANT', false)){
            $backupJob = BackupJobFactory::createFromArray(config('backup'));
            $backupJob->dontBackupFilesystem()->run();
        }else{
            $this->call('backup:run', ['--only-db' => 1]);
        }

        $this->call('backup:clean');
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)
            ->dailyAt('00:00');
    }
}
