<?php

namespace App\Http\Controllers\User;

use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateUserFormRequest;
use App\Jobs\SetUserFaceRecognition;
use App\Logics\AssetManager;
use App\Logics\ProjectManager;
use App\Logics\TaskManager;
use App\Logics\UserManager;
use App\Models\Department;
use App\Models\Position;
use App\Models\Project;
use App\Models\ProjectMember;
use App\Models\PropertyManagementAgency;
use App\Models\Role;
use App\Models\TaskLog;
use App\Models\TaskStatus;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Language;
use Illuminate\Support\Facades\App;
use App\Jobs\SendMailUpdateUser;
use App\Models\SiteSetting;


class UserController extends Controller
{
    const FAGE_TASK_LOG = 15;

    protected $assetManager;

    public function __construct(AssetManager $assetManager)
    {
        $this->assetManager = $assetManager;
    }

    /**
     * Edit the specified resource.
     *
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function edit()
    {
        $user = auth()->user()->load(['roles','department', 'position']);

        $userRole = $user->roles->pluck('id')->toArray();
        $languages = Language::select('id','name','display_name')->get();
        return view('user.edit', [
            'user' => $user,
            'languages' => $languages,
            'userRole' => $userRole,
        ]);
    }

    /**
     * Display a information of the user.
     *
     * @return \Illuminate\Http\Response
     * */
    public function information($id, Request $request)
    {
        $user = $this->checkUserExist($id);
        $idUserLogin = auth()->user()->id;
        $displayId = SiteSetting::displayTaskProjectId();

        if (!$user instanceof User) {
            return back()->with([
                'status_failed' => isset($user['msg']) ? $user['msg'] : ''
            ]);
        }
        $taskManager = new TaskManager;
        $filterHtml = $taskManager->getFilterHtml($request,['start_date','end_date']);
        // Get information of member
        $startedAt = isset($user->started_at) ? Carbon::parse($user->started_at)->format('d/m/Y') : null;
        $position =  isset($user->position_id) ? Position::withTrashed()->where('id', $user->position_id)->select('name','department_id')->first() : null;
        $department = isset($position->department_id) ? Department::withTrashed()->where('id', $position->department_id)->select('name')->first() : null;

        // get all project of manage and member
        $projects = ProjectMember::select(DB::raw("GROUP_CONCAT(project_roles.name SEPARATOR ', ') AS roleName"),
        'project_members.project_id AS project_id', 'projects.name AS name', 'projects.deleted_at',
        DB::raw('MIN(project_members.created_at) AS created_at'))
        ->join('projects', 'project_members.project_id', 'projects.id')
        ->whereRaw(DB::raw('project_members.project_id in (Select project_id from project_members where user_id = '. $idUserLogin .')'))
        ->where('project_members.user_id', $user->id)
        ->where('projects.deleted_at', null)
        ->join('project_roles', 'project_roles.id', 'project_members.role_id')
        ->groupBy('project_members.project_id')
        ->get();

        $idProjects = [];
        foreach ($projects as $project) {
            $idProjects[] = $project->project_id;
        }

        // Counts tasks status of the project
        $countTasks = TaskStatus::select(
            DB::raw('COUNT(IF(projects.deleted_at is null and projects.id is not null, 1, null)) AS taskOpen'),
            DB::raw('COUNT(IF(projects.deleted_at is not null, 1, null)) AS taskClose'),
            'task_status.name')
        ->leftJoin('project_tasks', function ($q) use ($user, $idProjects){
            $q->on('project_tasks.status', 'task_status.id')
            ->join('projects', 'projects.id', 'project_tasks.project_id')
            ->where('project_tasks.user_id', $user->id)
            ->whereIn('project_tasks.project_id', $idProjects);
        })
        ->groupBy('task_status.id')
        ->get();

        $taskLogs = TaskLog::join('project_tasks','task_logs.task_id', 'project_tasks.id')
        ->join('projects', 'project_tasks.project_id', 'projects.id')
        ->leftJoin('users', 'task_logs.created_by', 'users.id')
        ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
        ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
        ->where('task_logs.created_by', $user->id)
        ->whereIn('project_tasks.project_id',$idProjects);

        if ($request->start_date) {
            $startDate  = date_create_from_format("d/m/Y",$request->start_date );
            if ($startDate ) {
                $startDate  = date_format($startDate ,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '>=', $startDate );
            }
        }
        if ($request->end_date){
            $endDate = date_create_from_format("d/m/Y",$request->end_date);
            if ($endDate){
                date_modify($endDate, "+1 days");
                $endDate = date_format($endDate,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '<', $endDate);
            }
        }

        $taskLogs = $taskLogs->orderBy('task_logs.updated_at', 'desc')
        ->select('task_logs.*', 'project_tasks.name as task_name', 'project_tasks.status', 'projects.name as project_name',
        'users.id as userId', 'users.first_name', 'users.last_name', 'users.avatar', 'task_types.name as task_type', 'task_types.id as type_id',
        'task_status.name as task_status_name')
        ->paginate(self::FAGE_TASK_LOG);

        $tasks = TaskLog::join('project_tasks','task_logs.task_id', 'project_tasks.id')
        ->groupBy('task_logs.task_id')->groupBy(DB::raw('date(task_logs.updated_at)'))
        ->where('task_logs.created_by', $user->id)
        ->whereIn('project_tasks.project_id',$idProjects);

        if ($taskLogs->count()){
            $tasks = $tasks->havingRaw('max(task_logs.updated_at)>=?', [$taskLogs->last()->updated_at])
            ->havingRaw('min(task_logs.updated_at)<=?', [$taskLogs->first()->updated_at]);
        }
        $tasks = $tasks->select("task_logs.task_id", DB::raw('max(task_logs.updated_at) as last'))
        ->orderBy('last', 'desc')
        ->get();
        return view('user.public', [
            'user' => $user,
            'position' => $position,
            'department' => $department,
            'startedAt' => $startedAt,
            'countTasks' => $countTasks,
            'projects' => $projects,
            'tasks' => $tasks,
            'taskLogs' => $taskLogs,
            'filterHtml' => $filterHtml,
            'displayId'=>$displayId
        ]);
    }

    /**
     * Update the specified resource.
     *
     * @param \App\Http\Requests\Admin\UpdateUserFormRequest $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserFormRequest $request)
    {
        $hasManager = auth()->user()->hasRole([\App\Models\Role::ROLE_OPERATION_MANAGER, \App\Models\Role::ROLE_SYSTEM_MANAGER]);
        if ($hasManager){
            $user = $this->checkUserExist($request->id);
        }else{
            $user = Auth::user();
        }

        // Return error message if user not exist

        if (!$user instanceof User) {
            return back()->with([
                'status_failed' => isset($user['msg']) ? $user['msg'] : ''
            ]);
        }

        DB::beginTransaction();
        try {
            $oldEmail = $user->email;
            $param = [
                'language_id' => $request->language
            ];
            if ($hasManager) {
                $managementAgencyValue = $request->input('management_agency') ?? [];
                $dataInsertManagementAgency = $this->assetManager->getDataInsertManagementAgency($managementAgencyValue, $request->role, true);
                if ($request->filled('password')) {
                    $param['password'] = Hash::make(trim($request->input('password')));
                }

                $fullName = $request->input('first_name') . ' ' . $request->input('last_name');

                $param += [
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'name' => $request->check_name,
                    'name_search' => (new StringHelper)->transformSearchFullname($fullName),
                    'email' => $request->email,
                    'personal_email' => $request->personal_email,
                    'identity_card' => $request->identity_card,
                    'id_issued_place' => $request->id_issued_place,
                    'id_issued_at' => isset($request->id_issued_at)?Carbon::createFromFormat('d/m/Y', $request->id_issued_at):$request->id_issued_at,
                    'banking_account' => $request->banking_account,
                    'prefecture_id' => $request->prefecture_id,
                    'district_id' => $request->district_id,
                    'commune_id' => $request->commune_id,
                    'address' => $request->address,
                    'birthday' => isset($request->birthday)?Carbon::createFromFormat('d/m/Y', $request->birthday):$request->birthday,
                    'gender' => $request->gender,
                    'phone' => $request->phone,
                    'department_id' => $request->department,
                    'working_type' => $request->working_type,
                    'position_id' => $request->position,
                    'started_at' => isset($request->started_at)?Carbon::createFromFormat('d/m/Y', $request->started_at):$request->started_at,
                    'signed_at' => isset($request->signed_at)?Carbon::createFromFormat('d/m/Y', $request->signed_at):$request->signed_at,
                    'ended_at' => isset($request->ended_at)?Carbon::createFromFormat('d/m/Y', $request->ended_at):$request->ended_at,
                    'work_place_id' => $request->work_place_id,
                    'number_dependents' => $request->number_dependents,
                    'number_social_insurance' => $request->number_social_insurance,
                    'list_dependents' => $request->list_dependents,
                    'company_insurance' => $request->company_insurance,
                    'company_contract' => $request->company_contract,
                    'monthly_allowance'=> $request->monthly_allowance,
                    'personnel_status' => $request->personnel_status,
                    'salary_calculation_method' => $request->salary_calculation_method,
                    'hour_register' => $request->hour_register,
                    'percent_finish' => $request->percent_finish,
                    'outbound_parking' => $request->has('outbound_parking'),
                    'position_allowance' => $request->has('position_allowance'),
                    'bta_allowance' => $request->has('bta_allowance'),
                    'started_at_phase2' => isset($request->started_at_phase2)?Carbon::createFromFormat('d/m/Y', $request->started_at_phase2):$request->started_at_phase2,
                    'vehicle_type' =>  $request->vehicle_type,
                    'social_insurance_fee' => $request->social_insurance_fee,
                    'asset_property_management_agency' => $dataInsertManagementAgency
                ];
            }
            //--- 1. Update user in database ---
            $userManager = new UserManager();
            $userManager->updateUserProfile(
                $user,
                $param,
                $request->avatar,
                $hasManager ? $request->face_image : ''
            );

            // update user info to timekeeping device.
            $isBeeTechCompany = env('USE_TENANT', false)
                && app(\Hyn\Tenancy\Environment::class)->website()->id == env('BEETECH_COMPANY_ID', '');
            if ($hasManager && $isBeeTechCompany) {
                $userManager->updateUserInfoToTimekeepingDevice($user, true, $request->face_image);
            }
            
            //--- Update role---
            $hasRoleSystemManager = auth()->user()->hasRole(\App\Models\Role::ROLE_SYSTEM_MANAGER);
            $hasRoleOperationManager = auth()->user()->hasRole(\App\Models\Role::ROLE_OPERATION_MANAGER);
            if ($hasRoleSystemManager || ($hasRoleOperationManager && !$user->hasRole([\App\Models\Role::ROLE_PROJECT_MANAGER, \App\Models\Role::ROLE_SYSTEM_MANAGER]))){
                $user->syncRoles($request->role);
            }

            //--- 2. Add set_user_face_recognition job to queue ---
            if ($request->face_image) {
                // dispatch(new SetUserFaceRecognition($user))->onQueue(QUEUE_SET_USER_FACE_RECOGNITION);
            }

            if ($oldEmail !== $user->email) {
                $userInfo['email'] = $user->email;
                $userInfo['oldEmail'] = $oldEmail;
                $userInfo['language_id'] = $user->language_id;
                $userInfo['domain'] = route('login');
                $websiteId = null;
                if (env('USE_TENANT', false)) {
                    $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                }
                dispatch(new SendMailUpdateUser($userInfo, $websiteId))->onQueue(QUEUE_MAIL);
            }

            DB::commit();
            $userLogin = User::find(Auth::id());
            $language = Language::find($userLogin->language_id);
            if($language){
                $locale = $language->name;
                App::setLocale($locale);
                session()->put('locale', $locale);
            }
            return back()->with([
                'status_succeed' => trans('message.update_profile_succeed')
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Check if user exist then return User, else return error message
     *
     * @param $user_id
     * @param bool $deleted
     * @return array|User
     */
    private function checkUserExist($user_id, $deleted=false) {
        if ($deleted) {
            $user = User::onlyTrashed()->find($user_id);
        } else {
            $user = User::find($user_id);
        }

        if ($user == null) {
            return [
                'status' => 302,
                'msg' => trans('message.user_not_exist'),
                'url_callback' => back()->getTargetUrl(),
            ];
        }

        return $user;
    }

    /**
     * Get all members of the projects
     *
     * @param Request $request
     */
    public function ajaxUsers(Request $request){
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q);
        $projectId = $request->projectId;
        $members = [];
        $item = [];
        if ($projectId == 'false'){
            // Get all users
            $members = User::select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
                ->leftjoin('positions', 'positions.id','users.position_id');
            if ($request->has('deleted') && $request->deleted) {
                $members = $members->onlyTrashed();
            }
        }
        else if ($projectId == ''){
            // Get a list of members with the same project or who are members of the public project
            $projectManager = new ProjectManager();
            $members = $projectManager->getListUserWithProject($userId);
        } else{
            $project = Project::select('projects.id', 'projects.name')
                ->checkUserPermission($userId)
                ->find($projectId);
            // Get list member in project
            $members = $project->members()->select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
                ->leftjoin('positions', 'positions.id','users.position_id')
                ->distinct();
        }

        // search by key word
        $members = $members->where(function($query) use($keyword_search) {
            $query->orwhere('users.id', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere('users.email', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%'.$keyword_search.'%');
        });

        $members = $members->orderByRaw('FIELD(users.id, '.$userId.',users.id), users.last_name ASC')->get();

        foreach ($members as $member) {
            $item[] = [
                'id' => $member->id,
                'name' => $member->first_name . " " . $member->last_name,
                'avatar' => route('user.avatar',['id' => $member->id]),
                'email' => $member->email,
                'position' => $member->position,
            ];
        }
        $result= [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($members)
        ];

        return response()->json($result);
    }
    /**
     * Get all members of the projects
     *
     * @param Request $request
     */
    public function ajaxManager(Request $request){
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q);
        $members = [];
        $item = [];
        $members = User::select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
                ->leftjoin('positions', 'positions.id','users.position_id');
        // search by key word
        $members = $members->where(function($query) use($keyword_search) {
            $query->orwhere('users.id', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere('users.email', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%'.$keyword_search.'%');
        });

        $members = $members->whereHas(
            'roles', function($q){
                $q->where('name', Role::ROLE_OPERATION_MANAGER);
            }
        )
        ->orderByRaw('FIELD(users.id, '.$userId.',users.id), users.last_name ASC')->get();

        foreach ($members as $member) {
            $item[] = [
                'id' => $member->id,
                'name' => $member->first_name . " " . $member->last_name,
                'avatar' => route('user.avatar',['id' => $member->id]),
                'email' => $member->email,
                'position' => $member->position,
            ];
        }
        $result= [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($members)
        ];

        return response()->json($result);
    }

    /**
     * Get avatar user
     * @param $id
     * @return mixed
     */
    public function getAvatar($id)
    {
        $userManager = new UserManager();
        $image = $userManager->getImage($id, 'avatar');
        return $image;
    }
    /**
     * Get face-image user
     * @param $id
     * @return mixed
     */
    public function getFaceImage($id)
    {
        $userManager = new UserManager();
        $image = $userManager->getImage($id, 'face_image');
        return $image;
    }

    /**
     * Get all user role BTACustomerManager
     *
     */
    public function ajaxUserBtaManager(Request $request){
        $userId = Auth::id();
        $members = [];
        $item = [];
        $stringHelper = new StringHelper();
        $keyword_search = $stringHelper->formatStringWhereLike($request->q);
        $members = User::select(['users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position'])
                ->leftjoin('positions', 'positions.id','users.position_id');

        // search by key word
        $members = $members->where(function($query) use($keyword_search) {
            $query->orwhere('users.id', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere('users.email', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%'.$keyword_search.'%');
        });

        $members = $members->whereHas(
            'roles', function($q){
                $q->where('name', Role::ROLE_BTA_CUSTOMER_MANAGER);
            }
        )
        ->orderByRaw('FIELD(users.id, '.$userId.',users.id), users.last_name ASC')->get();

        foreach ($members as $member) {
            $item[] = [
                'id' => $member->id,
                'name' => $member->first_name . " " . $member->last_name,
                'avatar' => route('user.avatar',['id' => $member->id]),
                'email' => $member->email,
                'position' => $member->position,
            ];
        }
        $result= [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($members)
        ];

        return response()->json($result);
    }
}
