<?php

namespace App\Exports\Report;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCharts;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Chart\Chart;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;
use PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues;
use PhpOffice\PhpSpreadsheet\Chart\Layout;
use PhpOffice\PhpSpreadsheet\Chart\Legend;
use PhpOffice\PhpSpreadsheet\Chart\PlotArea;
use PhpOffice\PhpSpreadsheet\Chart\Title;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class SixthSheetExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles, WithCharts
{
    protected $sheetName;
    protected $data;

    function __construct($sheetName, $data) {
        $this->sheetName = $sheetName;
        $this->data = $data;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return (collect($this->data));
    }

    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.severity'),
            trans('language.number_of_bugs'),
        ];
    }

    /**
     * @return string
     */
    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 25,
        ];
    }

    /**
     * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->setTitle($this->sheetName);
        $sheet->getStyle('A1:B1')->applyFromArray(array(
            'borders' => [
                'all' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ));
        $sheet->getStyle('A1:B'.(count($this->data)))->getAlignment()->setWrapText(true);
        $sheet->getStyle('A1:B'.(count($this->data)+1))->applyFromArray(array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ]
        ));
    }


    /**
     * @return Chart|Chart[]
     */
    public function charts()
    {
        $cnt = count($this->data) + 1;
        $sheetName = "'" . $this->sheetName . "'";
        $labels     = [new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, "$sheetName!A1", null)];
        $categories = [new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, "$sheetName!A2:A$cnt",null, 2)];
        $values     = [new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, "$sheetName!B2:B$cnt",null)];

        $layout = new Layout();
        // Show value on pie chart
        $layout->setShowPercent(true);

        $chart1 = new Chart(
            'chart',
            new Title($this->sheetName),
            new Legend(),
            new PlotArea($layout, [
                new DataSeries(DataSeries::TYPE_PIECHART, null, range(0, count($values) - 1), $labels, $categories, $values)
            ])
        );

        $chart1->setTopLeftPosition('E1');
        $cnt +=9;
        $chart1->setBottomRightPosition('L'. $cnt);

        return $chart1;
    }
}
