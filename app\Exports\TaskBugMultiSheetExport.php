<?php

namespace App\Exports;

use App\Exports\Report\FifthSheetExport;
use App\Exports\Report\FirstSheetExport;
use App\Exports\Report\FourthSheetExport;
use App\Exports\Report\SecondSheetExport;
use App\Exports\Report\SixthSheetExport;
use App\Exports\Report\ThirdSheetExport;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class TaskBugMultiSheetExport implements WithMultipleSheets
{
    use Exportable;

    protected $data;
    protected $invalidCharacters = array('*', ':', '/', '\\', '?', '[', ']');

    function __construct($data) {
        $this->data = $data;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new TaskBugSheetExport($this->data['data']);
        $sheets[] = new FirstSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.function_screen")), $this->data['dataSheet']['function_screen']);
        $sheets[] = new SecondSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.person_charge")), $this->data['dataSheet']['user_name']);
        $sheets[] = new ThirdSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.bug_classify")), $this->data['dataSheet']['bug_classify_name']);
        $sheets[] = new FourthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.range_classify")), $this->data['dataSheet']['bug_range_name']);
        $sheets[] = new FifthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.reason_classify")), $this->data['dataSheet']['bug_reason_name']);
        $sheets[] = new SixthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.severity")), $this->data['dataSheet']['bug_severity_name']);

        return $sheets;
    }
}
