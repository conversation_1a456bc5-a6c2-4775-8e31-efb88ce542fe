<?php

namespace App\Http\Requests;

use App\Models\Tenant\AppVersion;
use App\Rules\MaxNormalizedLength;
use App\Rules\UniqueVersionPerPlatform;
use Illuminate\Foundation\Http\FormRequest;

class VersionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'version' => ['required', 'string', 'regex:/^\d+(\.\d+)*$/', 'max:20'],
            'status' => ['required', 'in:' . AppVersion::DRAFT . ',' . AppVersion::PUBLISHED],
            'description' => ['nullable', 'string', 'max:500'],
            'release_notes' => ['nullable', 'string', 'max:10000'],
            'force_update' => ['nullable', 'boolean'],
        ];

        if ($this->isMethod('post')) {
            $rules['platforms'] = ['required', 'in:' . AppVersion::IOS . ',' . AppVersion::ANDROID . ',' . AppVersion::ANDROID_AND_IOS];
            $rules['version'][] = new UniqueVersionPerPlatform($this->getPlatformsArray());
        } elseif ($this->isMethod('put') || $this->isMethod('patch')) {
            $rules['platforms'] = ['required', 'in:' . AppVersion::IOS . ',' . AppVersion::ANDROID];
            $rules['version'][] = new UniqueVersionPerPlatform(
                $this->input('platforms'),
                $this->route('id')
            );
        }

        return $rules;
    }
    
    /**
     * Get the platforms array from the request.
     *
     * @return array
     */
    private function getPlatformsArray(): array
    {
        return $this->input('platforms') == AppVersion::ANDROID_AND_IOS
            ? [AppVersion::IOS, AppVersion::ANDROID]
            : [$this->input('platforms')];
    }


    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'version.required' => __('validation.required', ['attribute' => __('validation.attributes.version_name')]),
            'version.regex' => __('validation.regex', ['attribute' => __('validation.attributes.version_name')]),
            'version.max' => __('validation.max.string', ['attribute' => __('validation.attributes.version_name'), 'max' => 20]),
            'platforms.required' => __('validation.required', ['attribute' => __('validation.attributes.device_type')]),
            'platforms.*.in' => __('validation.in', ['attribute' => __('validation.attributes.device_type')]),
            'status.required' => __('validation.required', ['attribute' => __('validation.attributes.status')]),
            'status.in' => __('validation.in', ['attribute' => __('validation.attributes.status')]),
            'description.max' => __('validation.max.string', ['attribute' => __('validation.attributes.version_description'), 'max' => 500]),
            'release_notes.max' => __('validation.max.string', ['attribute' => __('validation.attributes.version_release_notes'), 'max' => 10000]),
        ];
    }
}
