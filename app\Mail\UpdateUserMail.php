<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\Language;

class UpdateUserMail extends BaseMail
{
    use Queueable, SerializesModels;

    protected $user;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $language_id = isset($user['language_id']) ? $user['language_id'] : null;
        parent::__construct($language_id);
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
        ->subject(trans('language.mail_update_user.subject'))
        ->view('mail.mail_update_user', ['user' => $this->user]);
    }
}
