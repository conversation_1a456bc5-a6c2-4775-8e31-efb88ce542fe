<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCustomerRequest;
use App\Logics\CustomerManager;
use App\Logics\UserManager;
use App\Models\Role;
use App\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class CustomerController extends Controller
{
    const PER_PAGE = 15;

    protected $customerManager;
    protected $userManager;

    /**
     * constructor.
     */
    public function __construct(
        CustomerManager $customerManager,
        UserManager $userManager
    ) {
        $this->customerManager = $customerManager;
        $this->userManager = $userManager;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return View|RedirectResponse
     */
    public function index(Request $request)
    {
        // Get all customers
        $customers = $this->customerManager->getAllCustomers($request);
        $btaSettings = $this->customerManager->getAllBTASettings();
        $prefectures = $this->customerManager->getAllPrefectures();

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $customers = $customers->paginate($perPage);

        // Redirect to last page if page parameter greater than last page
        if ($customers->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $customers->lastPage()]));
        }
        // Redirect to first page if page parameter less than 0
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        $is_filter = "";
        $fields = [
            'id',
            'name',
            'age',
            'gender',
            'job',
            'address',
            'level_of_inquiry',
            'feedback',
            'level_care',
            'target',
            'language_proficiency',
            'modes_study',
            'concerns',
            'data_channels',
            'creation_date',
            'note',
            'user_id'
        ];
        foreach ($fields as $field) {
            $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
            $tagSpanClose = '</span>';
            $value = '';
            if ($request->has($field) && $request->$field != null) {
                switch ($field) {
                    case 'id':
                        $idItems = array_filter(array_map("trim", explode(",", StringHelper::escapeHtml($request->id))));
                        if (!empty($idItems)) {
                            foreach ($idItems as $idItem) {
                                $value .= $tagSpanOpen . "#" . $idItem . $tagSpanClose;
                            }
                        }
                        break;
                    case 'name':
                    case 'creation_date':
                    case 'note':
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                    case 'address':
                        foreach ($request->address as $address) {
                            $value .= $tagSpanOpen . $prefectures->find(StringHelper::escapeHtml($address))->name . $tagSpanClose;
                        }
                        break;
                    case 'user_id':
                        $sales = User::whereIn('id', $request->$field)->select('first_name', 'last_name')->get();
                        foreach ($sales as $sale) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($sale->first_name . ' ' . $sale->last_name) . $tagSpanClose;
                        }
                        break;
                    default:
                        foreach ($request->$field as $item) {
                            $value .= $tagSpanOpen . $btaSettings->find(StringHelper::escapeHtml($item))->name . $tagSpanClose;
                        }
                        break;
                }
                $is_filter .= $value;
            }
        }
        return view('admin.customer.index', [
            'customers' => $customers,
            'is_filter' => $is_filter,
            'bta_settings' => $btaSettings,
            'prefectures' => $prefectures,
        ]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return View|RedirectResponse
     */
    public function create()
    {
        $sales = $this->userManager->getUsersByRole(Role::ROLE_BTA_CUSTOMER_MANAGER);
        $btaSettings = $this->customerManager->getAllBTASettingsDividedIntoType();
        $prefectures = $this->customerManager->getAllPrefectures();
        return view('admin.customer.create', [
            'sales' => $sales,
            'btaSettings' => $btaSettings,
            'prefectures' => $prefectures
        ]);
    }

    /**
     * Create customer
     * @param   StoreAssetRequest  $request
     */
    public function store(StoreCustomerRequest $request)
    {
        DB::beginTransaction();
        try {
            $param = $request->all();
            $this->customerManager->save(null, $param);

            DB::commit();
            return redirect()->route('admin.customer.create')->with([
                'status_succeed' => trans('message.create_customer_success'),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return back()->with([
                'status_failed' => trans('message.create_customer_failed')
            ]);
        }
    }

    /**
     * show customer
     * @param $id
     * @return View|RedirectResponse
     */
    public function show($id)
    {
        $customer = $this->customerManager->getDataById($id);
        if (!$customer) {
            abort(404);
        }
        return view('admin.customer.show', [
            'customer' => $customer,
        ]);
    }

    /**
     * edit customer
     * @param $id
     * @return View|RedirectResponse
     */
    public function edit($id)
    {
        $customer = $this->customerManager->getDataById($id);
        if (!$customer) {
            abort(404);
        }
        $sales = $this->userManager->getUsersByRole(Role::ROLE_BTA_CUSTOMER_MANAGER);
        $btaSettings = $this->customerManager->getAllBTASettingsDividedIntoType();
        $prefectures = $this->customerManager->getAllPrefectures();
        return view('admin.customer.edit', [
            'customer' => $customer,
            'sales' => $sales,
            'btaSettings' => $btaSettings,
            'prefectures' => $prefectures
        ]);
    }

    /**
     * Update customer
     * @param   StoreAssetRequest  $request
     */
    public function update($id, StoreCustomerRequest $request)
    {
        DB::beginTransaction();
        try {
            $param = $request->all();
            $this->customerManager->save($id, $param);

            DB::commit();
            return redirect()->route('admin.customer.show', ['id' => $id])->with([
                'status_succeed' => trans('message.update_customer_success'),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return back()->with([
                'status_failed' => trans('message.update_customer_failed')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return Customer|array
     */
    public function destroy($id)
    {
        $customer = $this->customerManager->getDataById($id);
        if (!$customer) {
            return [
                'status' => ResponseAlias::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.customer_not_exist'),
                ]
            ];
        }

        $customer->delete();

        return [
            'status' => ResponseAlias::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_customer_succeed'),
            ],
            'redirect' => route('admin.customer.index'),
        ];
    }
}
