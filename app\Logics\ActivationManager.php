<?php

namespace App\Logics;

use App\Models\Activation;
use Carbon\Carbon;
use Illuminate\Support\Str;

class ActivationManager
{
    /**
     * Check limit send code
     * @param int $userId
     * @return bool
     */
    public function checkLimitSendCode($userId)
    {
        $count = Activation::where('user_id', $userId)
            ->where('completed', Activation::ACTIVATION_NOT_COMPLETED)
            ->where('created_at', '>', Carbon::now()->subMinutes(Activation::RESET_TIME_SEND_CODE))
            ->count();

        return $count < Activation::LIMIT_SEND_CODE;
    }


    /**
     * Create activation
     * @param int $userId
     * @param string $code
     * @param string $token
     * @return bool
     */
    public function createActivation($userId, $code, $token)
    {
        Activation::create([
            'user_id' => $userId,
            'code' => $code,
            'token' => $token,
            'expired_time' => Carbon::now()->addMinutes(Activation::EXPIRED_TIME_CODE),
        ]);
    }

    /**
     * Find activation by user id
     * @param int $userId
     * @return Activation
     */
    public function findActivationByUserId($userId)
    {
        return Activation::select('id', 'user_id', 'code', 'token', 'expired_time', 'created_at')
            ->where('user_id', $userId)
            ->where('completed', Activation::ACTIVATION_NOT_COMPLETED)
            ->orderBy('created_at', 'desc')
            ->first();
    }

    /**
     * Find activation by token
     * @param string $token
     * @return Activation
     */
    public function findActivationByToken($token)
    {
        return Activation::where('token', $token)
            ->where('completed', Activation::ACTIVATION_NOT_COMPLETED)
            ->whereNull('completed_at')
            ->where('created_at', '>', Carbon::now()->subMinutes(Activation::EXPIRED_TIME_TOKEN))
            ->first();
    }

    /**
     * Generate token
     * @return string
     */
    public function generateToken()
    {
        return hash_hmac('sha256', Str::random(200), Activation::ACTIVATION_TOKEN_KEY);
    }

    /**
     * Generate code
     * @return string
     */
    public function generateCode()
    {
        return sprintf('%06d', rand(1, 999999));
    }
}
