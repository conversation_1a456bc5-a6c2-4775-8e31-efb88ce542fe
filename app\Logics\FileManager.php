<?php

namespace App\Logics;

use App\Helpers\FileHelper;
use App\Models\Message;
use App\Traits\StorageTrait;
use Exception;
use FFMpeg\Format\Audio\Mp3;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use FFMpeg\FFMpeg;
use FFMpeg\Coordinate\TimeCode;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;

class FileManager{
    use StorageTrait;
    /**
     * handle upload file
     * @param $request
     * @return array
     * @throws Exception
     */

    public function handleUploaded($request)
    {
        try {
            $data = [];
            $checkType = $this->checkTypeUpload($request);
 
            if (empty($checkType)) {
                return $data;
            }
            $uploadPath = FileHelper::handleUploadFile($checkType['uploadPath'], 'test_url', $request);
            $data['file_path'] = $uploadPath;
            if ($request->input('type') == 'message') {
                $extension = $request->file('test_url')->getClientOriginalExtension();
                $data['type'] = $this->checkExtensionUpload($extension);
                if ($data['type'] == Message::EXTENSION_IMAGE || $data['type'] == Message::EXTENSION_VIDEO) {
                    [$data['width'], $data['height'], $data['thumbnail']] = self::getWidthHeight($data['file_path'], $data['type']);
                }
                $data['name'] = $request->file('test_url')->getClientOriginalName();

                // If messages is record, convert to mp3 file
                if ($data['type'] == Message::EXTENSION_RADIO && $extension != 'mp3') {
                    //Init FFMpeg
                    $ffmpeg = FFMpeg::create([
                        'ffmpeg.binaries'  => env('FFMPEG_BINARIES', 'ffmpeg'), // the path to the FFMpeg binary
                        'ffprobe.binaries' => env('FFPROBE_BINARIES', 'ffprobe'), // the path to the FFProbe binary
                        'timeout'          => 3600, // the timeout for the underlying process
                        'ffmpeg.threads'   => 12,   // the number of threads that FFMpeg should use
                    ]);
                    //Open file audio
                    $audio = $ffmpeg->open(Storage::disk(FILESYSTEM)->path($uploadPath));
                    //Audio format Mp3
                    $audioFormat = new Mp3();
                    //File name
                    $fullName = date('YmdHis') . '_' . sha1(Str::uuid()) . '.mp3';
                    $outputFilePath = TEMP_DIR. '/' . $fullName;
                    //Save file audio after convert
                    $audio->save($audioFormat, Storage::disk(FILESYSTEM)->path($outputFilePath));
                    //Replace new file path
                    $data['file_path'] = $outputFilePath;
                    //Remove tmp file in Storage
                    $this->deleteFile($uploadPath);
                    $data['name'] = $fullName;
                }
            }
            $data['inputType'] = $checkType['name'];
            return $data;
        } catch (Exception $e) {
            Log::error("[UploadFileService][handleUploaded] error " . $e->getMessage());
            throw new Exception('[UploadFileService][handleUploaded] error ' . $e->getMessage());
        }
    }

    /**
     * check type upload file
     * @param $request
     * @return array
     * @throws Exception
     */

    private function checkTypeUpload($request)
    {
        $result = [];
        $path_tmp = TEMP_DIR . '/';

        switch ($request->input('type')) {
            case 'message_record':
            case 'message':
                $result = [
                    'uploadPath' => $path_tmp,
                    'name' => 'message',
                ];
                break;
            default:
                break;
        }
        return $result;
    }
    
    /**
    * check extension upload file
    * @param $extension
    * @return int
    * @throws Exception
    */

    public function checkExtensionUpload($extension)
    {
        switch (true) {
            case in_array(strtolower($extension), ['jpeg', 'png', 'jpg', 'gif', 'tiff']):
                $result = Message::EXTENSION_IMAGE;
                break;
            case in_array(strtolower($extension), ['mov', 'mp4']):
                $result = Message::EXTENSION_VIDEO;
                break;
            case in_array(strtolower($extension), ['mp3', 'm4a']):
                $result = Message::EXTENSION_RADIO;
                break;
            default:
                $result = Message::EXTENSION_FILE_OTHER;
                break;
        }
        return $result;
    }

    /**
     * get width height image video
     * @param $filePath
     * @param $type
     * @return array
     * @throws Exception
     */

    private function getWidthHeight($filePath, $type)
    {
        $filePath = config('constants.sftpPath') . Storage::disk(FILESYSTEM)->path($filePath);
        $filePathImage = TEMP_DIR . '/' . date('YmdHis') . '_' . sha1(Str::uuid()) . ".jpg";
        $thumbnail = config('constants.sftpPath') . Storage::disk(FILESYSTEM)->path($filePathImage);
        if ($type == Message::EXTENSION_IMAGE) {
            [$width, $height] = $this->getThumbnail($filePath, $thumbnail);
        } else {
            $ffmpeg = FFMpeg::create([
                'ffmpeg.binaries'  => env('FFMPEG_BINARIES', 'ffmpeg'), // the path to the FFMpeg binary
                'ffprobe.binaries' => env('FFPROBE_BINARIES', 'ffprobe'), // the path to the FFProbe binary
                'timeout'          => 3600, // the timeout for the underlying process
                'ffmpeg.threads'   => 12,   // the number of threads that FFMpeg should use
            ]);
            $timeVideo = $ffmpeg->getFFProbe()
                ->format($filePath)
                ->get('duration');
            $timeVideo = $timeVideo > env('TIME_THUMBNAIL_VIDEO', 1) ? env('TIME_THUMBNAIL_VIDEO', 1) : 0;
            $ffmpeg->open($filePath)->frame(TimeCode::fromSeconds($timeVideo))->save($thumbnail);
            [$width, $height] = $this->getThumbnail($thumbnail, $thumbnail);
        }
        return [$width, $height, $filePathImage];
    }

    /**
     * get thumbnail image
     * @param $filePath
     * @param $thumbnail
     * @return array
     */

    public static function getThumbnail($filePath, $thumbnail) {
        $image = Image::make($filePath);
        $image->orientate();
        $width = (int) env('WIDTH_IMAGE_APP', 300);
        $height = (int) ($width * $image->getHeight() / $image->getWidth());
        $image->resize($width, $height, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        })->save($thumbnail);
        return [$width, $height];
    }
}