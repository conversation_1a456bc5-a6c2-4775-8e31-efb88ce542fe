<?php

namespace App\Http\Requests;

use App\Enums\TimeFilterEnum;
use App\Logics\ConversationManager;
use App\Models\ConversationFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetFileConversationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $typeFilter = [
            TimeFilterEnum::FILTER_CUSTOM
            , TimeFilterEnum::FILTER_YESTERDAY
            , TimeFilterEnum::FILTER_LAST_WEEK
            , TimeFilterEnum::FILTER_LAST_MONTH
            , TimeFilterEnum::FILTER_7_DAYS_AGO
            , TimeFilterEnum::FILTER_30_DAYS_AGO
            , TimeFilterEnum::FILTER_90_DAYS_AGO
        ];
        return [
            'type' => ['required', function ($attribute, $value, $fail) {
                $type = json_decode($value, true);
                if (!is_array($type) || empty($type)) return $fail(trans('message.request.input_exists', ['attribute' => "Phân loại"]));
                foreach ($type as $value) {
                    if (!is_numeric($value) || !in_array($value, [
                        ConversationFile::TYPE_LINK,
                        ConversationFile::TYPE_IMAGE,
                        ConversationFile::TYPE_RADIO,
                        ConversationFile::TYPE_VIDEO,
                        ConversationFile::TYPE_FILE_OTHER,
                    ])) {
                        return $fail(trans('message.request.input_exists', ['attribute' => "Phân loại"]));
                    }
                }
            }],
            'conversation_id' => ['nullable', function ($attribute, $value, $fail) {
                $conversationManager = new ConversationManager;
                $userId = Auth::user()->id;
                $conversation = $conversationManager->checkPermissionConversation($value, $userId);
                if (empty($conversation)) $fail(trans('message.request.input_exists', ['attribute' => "Cuộc trò chuyện"]));
            }],
            'user_id' => ['nullable', 'exists:users,id'],
            'filter_from' => ['nullable', 'date_format:d-m-Y'],
            'filter_to' => ['nullable', 'date_format:d-m-Y', function ($attribute, $value, $fail) {
                $from = $this->input('filter_from');
                if (!empty($value) && !empty($from)) {
                    $to = date_create_from_format('d-m-Y', $value);
                    $from = date_create_from_format('d-m-Y', $from);
                    if ($to < $from) {
                        $fail(__('message.message.file.sort'));
                    }
                }
            }],
            'typeToFilter' => ['nullable', 'in:'.implode(',',$typeFilter)],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'user_id.exists' => trans('message.request.input_exists', ['attribute' => "Người dùng"]),
        ];
    }
}
