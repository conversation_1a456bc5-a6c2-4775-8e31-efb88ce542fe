<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Language;
use App\Models\UserDevice;
use App\User;
use App\Repositories\CachedTokenRepository;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/projects';

    protected $maxAttempts  = 3;  // Amount of bad attempts user can make
    protected $decayMinutes = 1; // Time for which user is going to be blocked in minutes

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }
    /**
     * Show the application's login form.
     *
     * @return \Illuminate\View\View
     */
    public function showLoginForm()
    {
        $languages = Language::select('id','name','display_name')->get();
        return view('auth.login', [
            'languages' => $languages
        ]);
    }
    /**
     * Get the login username to be used by the controller.
     * 
     * @return string : username field name (email or phone)
     */
    public function username()
    {
        return 'username';
    }

    /**
     * Determine if the given login is an email or phone number
     * and return the appropriate field name.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array Contains field type and login value
     */
    private function getLoginField(Request $request)
    {
        $login = $request->input('username');
        $field = filter_var($login, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';
        
        return [
            'field' => $field,
            'value' => $login
        ];
    }

    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        $loginData = $this->getLoginField($request);
        
        $request->merge([$loginData['field'] => $loginData['value']]);
        
        $messages = [
            'username.required' => __('validation.required', ['attribute' => 'email/phone'])
        ];
        
        $this->validate($request, [
            'username' => 'required|string',
            'password' => 'required|string'
        ], $messages);
    }

    /**
     * Get the needed authorization credentials from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    protected function credentials(Request $request)
    {
        $loginData = $this->getLoginField($request);
        
        return [
            $loginData['field'] => $loginData['value'],
            'password' => $request->input('password')
        ];
    }

    /**
     * Attempt to log the user into the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        $loginData = $this->getLoginField($request);
        
        if (User::onlyTrashed()->where($loginData['field'], $loginData['value'])->first()) {
            return false;
        }
        
        return $this->guard()->attempt(
            $this->credentials($request), $request->filled('remember')
        );
    }

    /**
     * Get the failed login response instance.
     *
     * @param Request $request
     * @return Response
     *
     * @throws ValidationException
     */
    protected function sendFailedLoginResponse(Request $request)
    {
        throw ValidationException::withMessages([
            'login_failed' => [trans('auth.failed')],
        ]);
    }

    /**
     * Redirect the user after determining they are locked out.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function sendLockoutResponse(Request $request)
    {
        $seconds = $this->limiter()->availableIn(
            $this->throttleKey($request)
        );

        $maxAttempts = $this->maxAttempts();

        throw ValidationException::withMessages([
            'login_failed' => [Lang::get('auth.throttle', ['attempts' => $maxAttempts, 'seconds' => $seconds])],
        ])->status(429);
    }
    /**
     *  change language
     * 
     * @param string $locale
     * 
     * @return Response
     */ 
    public function changeLanguage($locale)
    {
        App::setLocale($locale);
        session()->put('locale', $locale);
        return redirect()->back();

    } 

    public function logout(Request $request)
    {
        // Delete all access tokens of user in Q2A, BeeChat site
        $accessTokensQr = DB::table('oauth_access_tokens')
            ->where('user_id', Auth::id())
            ->where(function($query) {
                $query->where('client_id', env('CLIENT_ID_Q2A', null))
                    ->orWhere('client_id', env('CLIENT_ID_BEECHAT', 13));
            });
        
        $accessTokens = $accessTokensQr->pluck('id')->toArray();
        $accessTokensQr->delete();

        // Delete the associated refresh token
        DB::table('oauth_refresh_tokens')
            ->whereIn('access_token_id', $accessTokens)
            ->delete();

        // Handle delete device_token
        if (isset($request->device_token)) {
            UserDevice::query()
                ->where('user_id', Auth::id())
                ->where('device_token', $request->device_token)
                ->delete();
        }

        // Revoke the token that was used to authenticate the user
        foreach ($accessTokens as $tokenId) {
            app(CachedTokenRepository::class)->revokeAccessToken($tokenId);
        }

        $this->guard()->logout();

        $request->session()->flush();

        $request->session()->regenerate();
        if (isset($request->redirect_uri)){
            return redirect($request->redirect_uri);
        }
        return redirect('/');
    }
}