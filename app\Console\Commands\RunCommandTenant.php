<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RunCommandTenant extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'runtenancy {--command=} {--website_id=*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run command with multi tenant';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            if (env('USE_TENANT', false)) {
                $command = $this->option('command');
                $website_ids = $this->option('website_id');
                if (empty($website_ids)) {
                    $website_ids = app(\Hyn\Tenancy\Contracts\Repositories\WebsiteRepository::class)->query()->select('id')->pluck('id')->toArray();
                }
                foreach ($website_ids as $website_id) {
                    $this->call($command, ['--website_id' => $website_id]);
                }
            }
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return $e->getMessage();
        }
    }
}
