<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class EvaluationFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'evaluation_template' => 'required',
            'evaluation_form_name' => 'required',
            'considering_the_time_period' => 'required',
            'evaluation_deadline' => 'required',
            'apply_for' => 'required',
            'evaluator' => 'required',
        ];
    }
}
