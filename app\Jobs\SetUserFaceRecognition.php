<?php

namespace App\Jobs;

use App\Helpers\RequestHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Storage;


class SetUserFaceRecognition implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    protected $user;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        // Call API set_user_face
        $response = (new RequestHelper())->sendRequest(
            API_SET_USER_FACE_RECOGNITION,
            'POST',
            [
                [
                    'name' => 'id',
                    'contents' => $this->user->id,
                ],
                [
                    'name' => 'face_image',
                    'contents' => Storage::disk(FILESYSTEM)->get($this->user->face_image),
                    'filename' => basename($this->user->face_image)
                ],
            ]
        );
        if ($response == null) {
            throw new \Exception('Set user face recognition failed');
        }
    }
}
