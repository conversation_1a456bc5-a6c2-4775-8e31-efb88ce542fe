<?php

namespace App\Jobs\Notification;

use App\Helpers\CacheHelper;
use App\Helpers\NotificationHelper;
use App\Jobs\BaseQueue;
use App\Logics\MessageManager;
use App\Logics\UserConfigManager;
use App\Models\ConversationParticipant;
use App\Models\Conversation;
use App\Services\MessageToCounterService;
use App\UserConfig;
use Illuminate\Support\Facades\Log;
use Exception;

class sendNotificationMessage extends BaseQueue
{

    protected $dataNotification;
    protected $conversationParticipants;

    /**
     * Create a new job instance.
     * @param      $dataNotification
     * @param      $conversation
     * @param null $websiteId
     */
    public function __construct(
        $dataNotification,
        $conversationParticipants,
        $websiteId = null
    ) {
        parent::__construct($websiteId);
        $this->dataNotification = $dataNotification;
        $this->conversationParticipants = $conversationParticipants;
    }

    /**
     * Execute the job.
     *
     * @return int
     * @throws Exception
     */
    public function handle()
    {
        parent::handle();
        try {
            $userConfigManager = new UserConfigManager();
            //Query gets a list of users mentioned in the message (case TO in chat main)
            $listRemindUsers = null;
            if (!empty($this->dataNotification['remind_users'])) {
                $listRemindUsers = json_decode($this->dataNotification['remind_users'], true);
            }

            //Get list of members in thread
            if (isset($this->dataNotification["reply_id"])) {
                $messageManager = new MessageManager();
                $usersInThread = $messageManager->getUsersInThread($this->dataNotification["reply_id"]);
            }

            // Set sub domain name. 
            $subDomain = $this->getSubDomainFormHostname();
            $this->dataNotification['subdomain'] = $subDomain;
            
            // Get message to multiple users.
            $messageToCounterService = new MessageToCounterService();
            foreach ($this->conversationParticipants as $user) {
                $this->dataNotification['user_id'] = $user['user_id']; // id of receiver noti
                $this->dataNotification['type_account'] = $user['type']; // type of reciver noti
                
                //Check user enable notification
                $hasUserConfigPushNotification = $userConfigManager->checkPushNotification (
                    UserConfig::PUSH_FIREBASE_NOTIFICATION,
                    $user['user_id']
                );
                //Check if the user is mentioned in the message or not
                $isTo = !empty($listRemindUsers) && in_array($user['user_id'], $listRemindUsers);
                //Check if the user in thread or not
                $isInThread = isset($usersInThread) && in_array($user['user_id'], $usersInThread);
                /*
                Push notification to user if:
                - The user has enabled notifications
                - Chat doesn't turn off notifications
                - The user mentioned in the chat or user in thread
                - Direct chat
                */
                if ($hasUserConfigPushNotification 
                    && $user->is_mute === ConversationParticipant::NOT_MUTE
                    && ($isTo || $isInThread 
                        || $this->dataNotification["conversation_type"] == Conversation::TYPE_TWO_USER)
                ) {
                    // Add total message to for user.
                    if ($this->verifyCallCalculatorBadge($user['user_id'])) {
                        $badge = $messageToCounterService->countMessagesToUser($user['user_id']);
                        $this->dataNotification['badge'] = $badge;
                    }
                    
                    $deviceToken = NotificationHelper::getDeviceToken([$user['user_id']]);
                    NotificationHelper::sendNotifyUserDevice($user['user_id'], $deviceToken, $this->dataNotification);
                }
            }
            return 0;
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Check cache call calculator last message send. 
     * 
     * @param $userId
     * @return bool
     */
    private function verifyCallCalculatorBadge($userId)
    {
        if (CacheHelper::hasRecentlySentMessage($userId)) {
            return false;
        }

        CacheHelper::markMessageSent($userId);
        return true;
    }
}
