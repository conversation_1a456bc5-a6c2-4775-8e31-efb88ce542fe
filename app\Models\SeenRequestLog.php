<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class SeenRequestLog extends Model
{
    public $timestamps = false;
    protected $guarded = [];
    protected $table = 'seen_request_logs';

    protected $fillable = ['request_id','user_id','seen_at'];

    /**
     * Save id user created
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->created_by = auth()->id();
        });
    }
}
