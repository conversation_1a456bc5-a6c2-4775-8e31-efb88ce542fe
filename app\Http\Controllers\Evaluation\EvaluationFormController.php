<?php

namespace App\Http\Controllers\Evaluation;

use App\Models\EvaluationResult;
use App\Models\ProjectGroupRole;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Logics\EvaluationManager;
use App\Models\Evaluation;
use App\Models\ProjectMember;
use App\Models\Role;
use App\User;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\EvaluationFormRequest;
use App\Models\EvaluationTemplate;
use App\Jobs\CreateEvaluation;
use App\Logics\ProjectMemberManager;
use App\Logics\TaskManager;
use App\Models\EvaluationForm;
use App\Models\SiteSetting;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;

class EvaluationFormController extends Controller
{
    const PAGINATION = 15;
     /**
     * show list of  evaluation form
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Get list user
        $users = User::select('id', 'first_name', 'last_name', 'email')->orderby('id')->get();
        $evaluationManager =  new EvaluationManager;
        $filterHtml = $evaluationManager->getFilterHtml($request,['project','members','start_at','end_at']);

        if(!empty($request->members)){
            $members = $request->members;
            $evaluationForms = EvaluationForm::select('evaluation_forms.*')
                ->join('evaluation_results','evaluation_forms.id','evaluation_results.form_id')
                ->whereIn('evaluation_results.user_id',$members)
                ->groupBy('evaluation_forms.id')
                ->sortable(['created_at' => 'desc'])
                ->distinct();
        }else{
            $evaluationForms = EvaluationForm::sortable(['created_at' => 'desc']);
        }
        if (!empty($request->start_at)) {
            $start_at = $request->start_at;
            $start_at = Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');
            $evaluationForms = $evaluationForms->where(function ($query) use ($start_at) {
                $query->where('evaluation_forms.from', '>=', $start_at)
                    ->orwhere('evaluation_forms.to', '>=', $start_at);
            });
        }
        if (!empty($request->end_at)) {
            $end_at = $request->end_at;
            $end_at = Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
            $evaluationForms = $evaluationForms->where(function ($query) use ($end_at) {
                $query->where('evaluation_forms.from', '<=', $end_at)
                    ->orwhere('evaluation_forms.to', '<=', $end_at);
            });
        }
        $evaluationForms = $evaluationForms->paginate(self::PAGINATION);
        return view('evaluation.form.index', [
            'evaluationForms' =>$evaluationForms,
            'filterHtml' => $filterHtml,
            'users' => $users,
        ]);
    }
    public function getDataScore(Request $request)
    {
        if(!empty($request->members)){
            $members = $request->members;
            $evaluationForms = EvaluationResult::select([
                'evaluation_forms.name',
                'evaluation_forms.id',
                'evaluation_forms.created_at',
                'evaluation_forms.to',
            ])
                ->join('evaluation_forms','evaluation_forms.id','evaluation_results.form_id')
                ->whereIn('evaluation_results.user_id',$members)
                ->orderBy('evaluation_forms.to','DESC')
                ->distinct();
            if (empty($request->end_at) && empty($request->start_at)) {
                $evaluationForms = $evaluationForms->limit(10);
            } else {
                if (!empty($request->start_at)) {
                    $start_at = $request->start_at;
                    $start_at = Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');
                    $evaluationForms = $evaluationForms->where(function($query)  use ( $start_at){
                        $query->where('evaluation_forms.from','>=',  $start_at)
                            ->orwhere('evaluation_forms.to', '>=',  $start_at);
                    });
                }
                if (!empty($request->end_at)) {
                    $end_at = $request->end_at;
                    $end_at = Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
                    $evaluationForms = $evaluationForms->where(function($query)  use ($end_at){
                        $query->where('evaluation_forms.from','<=',  $end_at)
                            ->orwhere('evaluation_forms.to', '<=',  $end_at);
                    });
                }
            }
            $evaluationForms = $evaluationForms->get()->sortBy('to');
            $date = [];
            $name = [];
            $scoreUser = [];
            $form_id = [];
            foreach($evaluationForms as $evaluationForm){
                $date[] = Carbon::createFromFormat('Y-m-d H:i:s', $evaluationForm->to)->format('d-m-Y');
                $name[] = $evaluationForm->name;
                $urls[] = route('evaluation.form.show', ['id' => $evaluationForm->id]);
                $scoreUser[$evaluationForm->id] = null;
                $form_id[] = $evaluationForm->id;
            }

            $result = [];
            $evaluationResults = EvaluationResult::select('score','form_id','user_id')
                                ->whereIn('form_id',$form_id)
                                ->whereIn('user_id',$members)->get();

            $names = [];
            foreach($members as $member){
                $user = User::select('first_name','last_name')
                            ->where('id',$member)->first();
                $names[] = $user->first_name.' ' .  $user->last_name;
                $result[$member] = $scoreUser;
            }
            foreach( $evaluationResults as  $evaluationResult){
                $result[$evaluationResult->user_id][$evaluationResult->form_id] = empty($evaluationResult->score)?0:$evaluationResult->score;
            }
            foreach($members as $member){
                $result[$member] = array_values($result[$member]);
            }
            $result = array_values($result);
            $dataset = [];
            $rand = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f');
            foreach($result as $key=>$arrayScore){
                $borderColor='#' . $rand[rand(0,14)].$rand[rand(0,15)].$rand[rand(0,15)].$rand[rand(0,15)].$rand[rand(0,15)].$rand[rand(0,15)];
                $dataset[] = [
                    "data"=> $arrayScore,
                    "label" => $names[$key],
                    "borderColor" => $borderColor,
                    "fill" => false,
                    "lineTension" => 0,
                    "spanGaps" => true,
                    "pointBorderWidth" => 4
                ];
            }
            $data['date'] =  $date;
            $data['name'] =  $name;
            $data['score'] = $dataset;

            return response()->json($data);
        }
    }

    /**
     *  Display the create evaluation form interface
     */
    public function create()
    {
        $templateForms = EvaluationTemplate::select('id', 'name')->where('is_draft', EvaluationTemplate::IS_NOT_DRAFT)->get();
        $projectGroupRoles = ProjectGroupRole::all();
        return view('evaluation.form.create', [
            'templateForms' => $templateForms,
            'projectGroupRoles' => $projectGroupRoles
        ]);
    }

    /**
     *  Save the rating form, submit the form to selected users
     *
     * @param EvaluationFormRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(EvaluationFormRequest $request)
    {
        $consideringTheTimePeriod = explode(' - ', $request->considering_the_time_period);
        $evaluationDeadline = explode(' - ', $request->evaluation_deadline);
        $startedAt = Carbon::createFromFormat('d/m/Y', $consideringTheTimePeriod[0])->format('Y-m-d 00:00:00');
        $endedAt = Carbon::createFromFormat('d/m/Y', $consideringTheTimePeriod[1])->format('Y-m-d 23:59:59');
        $from = Carbon::createFromFormat('d/m/Y', $evaluationDeadline[0])->format('Y-m-d 00:00:00');
        $to = Carbon::createFromFormat('d/m/Y', $evaluationDeadline[1])->format('Y-m-d 23:59:59');
        if(strtotime($to) < strtotime($endedAt)){
            return Redirect::back()
                    ->withInput($request->input())
                    ->withErrors(['evaluation_deadline' => trans('message.evaluation_deadline_after_considering_the_time_period')]);
        }

        $evaluationTemplate = EvaluationTemplate::where('id', $request->evaluation_template)->select('content')->first()->content;

        if(in_array('all',$request->apply_for)){
            $applyFor = [ProjectGroupRole::PROJECT_MANAGER, ProjectGroupRole::LEADER, ProjectGroupRole::MEMBER];
        }else{
            $applyFor = $request->apply_for;
        }

        if(in_array('all',$request->evaluator)){
            $evaluator = [ProjectGroupRole::PROJECT_MANAGER, ProjectGroupRole::LEADER, ProjectGroupRole::MEMBER];
        }else{
            $evaluator = $request->evaluator;
        }

        // Create an evaluation form
        $evaluationForm = EvaluationForm::create(
            [
                'name' => $request->evaluation_form_name,
                'apply_for'=> implode(CONCAT_SEPARATOR,$applyFor),
                'evaluator'=> implode(CONCAT_SEPARATOR,$evaluator),
                'content' => $evaluationTemplate,
                'started_at' => $startedAt,
                'ended_at' => $endedAt,
                'from' => $from,
                'to' => $to,
            ]
        );

        // Create evaluations for involved users
        $websiteId = null;
        if (env('USE_TENANT', false)) {
            $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
        }
        dispatch(new CreateEvaluation($applyFor, $evaluationForm, $evaluator, $websiteId))->onQueue(QUEUE_CREATE_EVALUATION);

        return redirect()->route('evaluation.form.index')->with([
            'status_succeed' => trans('message.evaluation_form_success')
        ]);
    }

    /**
     * Show the result evaluation
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Get evaluation
        $evaluation = EvaluationForm::select('id', 'name', 'content')->find($id);

        if (empty($evaluation)) {
            return redirect()->route('evaluation.form.index');
        }

        // Get evaluation result
        $evaluationResults = EvaluationResult::select([
            'evaluation_results.score',
            'evaluation_results.form_id',
            'evaluation_results.user_id',
            'evaluation_forms.created_at',
            DB::raw('CONCAT_WS(" ",first_name,last_name) as full_name'),
            'users.email'

        ])
            ->join('evaluation_forms','evaluation_forms.id','evaluation_results.form_id')
            ->join('users','users.id','evaluation_results.user_id')
            ->where('evaluation_results.form_id',$id)
            ->orderBy('evaluation_results.score','DESC')
            ->paginate(self::PAGINATION);

        return view('evaluation.form.show')->with([
            'evaluation' => $evaluation,
            'evaluationResults' => $evaluationResults,
        ]);
    }

    /**
     * Get result evaluation of user
     * @param  int  $id
     * @param Illuminate\Http\Request $request
     */
    public function getEvaluationResult(Request $request, $id)
    {
        $minTimeProject = SiteSetting::minTimeProject();
        // Get Evaluation form
        $evaluationForm = EvaluationForm::select('content', 'ended_at', 'started_at','is_evaluation_week')->find($id);

        //get content of evaluation form
        $evaluationManager = new EvaluationManager();
        $formContent = $evaluationManager->getContent($evaluationForm->content);
        $userId = $userName = $userUrl = '';
        $evaluators = [];
        $project = [];
        if ($request->user_id != 'null') {
            // Get user
            $user = User::select('first_name', 'last_name', 'id')->withTrashed()->find($request->user_id);
            $userName = $user->first_name.' '.$user->last_name;
            $userId = $user->id;
            $userUrl = route('user.public', ['id' => $user->id]);
            // Get projects of user join in evaluation stage
            $project = ProjectMember::select(
            DB::raw("GROUP_CONCAT(DISTINCT CONCAT_WS('".CONCAT_SEPARATOR."','".route('project.show',['id' => 'idProject'])."',
             projects.id, projects.name) SEPARATOR '".GROUP_CONCAT_SEPARATOR."') AS projects"))
            ->join('projects', 'project_members.project_id', 'projects.id')
            ->where('project_members.user_id', $request->user_id)
            ->where('project_members.created_at', '<=', $evaluationForm->ended_at)
            ->groupBy('project_members.user_id')
            ->first();

            // Get Evaluators work in evaluation stage
            $evaluators = Evaluation::select(
                'users.id',
                'users.first_name',
                'users.last_name',
                'evaluations.content',
                DB::raw('min(project_roles.group_role_id) as min_group_role_id'),
                DB::raw('
                CASE
                    WHEN min(project_roles.group_role_id) = 1 THEN "'.trans('language.project_manager').'"
                    WHEN min(project_roles.group_role_id) = 2 THEN "'.trans('language.leader').'"
                    ELSE null
                END
                as roleName')
            )
            ->join('users', 'users.id', 'evaluations.evaluator_id')
            ->leftJoin('project_members', function ($join) use ($evaluationForm,$minTimeProject){
                $join->on('project_members.user_id','=', 'evaluations.evaluator_id')
                    ->whereRaw(
                    'project_id IN (
                        SELECT project_members.project_id FROM project_members
                        LEFT JOIN projects on projects.id = project_members.project_id
                        WHERE project_members.user_id = evaluations.user_id
                        AND DATEDIFF(?,project_members.created_at) + 1 >= ? AND (projects.ended_at IS NULL or DATEDIFF(projects.ended_at,project_members.created_at) + 1 >= ?)
                        GROUP BY project_id)',[$evaluationForm->ended_at, $minTimeProject, $minTimeProject]);
            })
            ->leftJoin('projects', 'projects.id', '=', 'project_members.project_id')
            ->join('project_roles','project_members.role_id','project_roles.id')
            ->where([
                ['evaluations.form_id', $id],
                ['evaluations.user_id', $request->user_id],
            ])
            ->where('project_members.created_at','<=', $evaluationForm->ended_at)
            ->whereRaw("DATEDIFF('" . $evaluationForm->ended_at . "', project_members.created_at) + 1  >= ". $minTimeProject)
            ->whereRaw("(projects.ended_at IS NULL OR DATEDIFF(projects.ended_at, project_members.created_at) + 1 >= ". $minTimeProject.")")
            ->groupBy('users.id', 'evaluations.content')
            ->orderBy('min_group_role_id', 'asc')
            ->get();

            //string concatenation of content with score
            foreach ($evaluators as $idx => $evaluator) {
                // If the evaluator hasn't evaluated yet then remove this evaluator in evaluators
                if (empty($evaluator->content)) {
                    unset($evaluators[$idx]);
                    continue;
                }
                $itemScore = $evaluationManager->getScoreComment(json_decode($evaluator->content, true));
                $arrScore = explode('</tr>', $itemScore);
                unset($arrScore[count($arrScore)-1]);
                for ($i = 0; $i < count($formContent['allRows']); $i++) {
                    $formContent['allRows'][$i] .= $arrScore[$i];
                }
            }
            $evaluators = array_values($evaluators->toArray());
        }
        $formHtml = implode('', $formContent['allRows']);
        return [
            'userId' => $userId,
            'userUrl' => $userUrl,
            'userName' => $userName,
            'project' => $project,
            'formHtml' => $formHtml,
            'maxDepth' => $formContent['maxDepth'],
            'evaluators' => $evaluators,
            'isEvaluationWeek' => $evaluationForm->is_evaluation_week,
        ];
    }
    /**
     * Get evaluators of evaluation form
     * @param $id
     */
    public function getEvaluatedUsers(Request $request)
    {

        $keyword_search = $request->q;
        $evaluationId = $request->evaluationId;
        $members = [];
        $item = [];

        $members = User::select([
            'users.id',
            'users.first_name',
            'users.last_name',
            'users.avatar',
            'users.email',
            'positions.name as position'
        ])
            ->leftjoin('positions', 'positions.id', 'users.position_id')
            ->join('evaluations', 'evaluations.user_id', 'users.id')
            ->where('evaluations.form_id', $evaluationId)
            ->groupBy('evaluations.user_id')
            ->where(function ($query) use ($keyword_search) {
                $query->orwhere('users.id', 'LIKE', '%' . $keyword_search . '%')
                    ->orwhere('users.email', 'LIKE', '%' . $keyword_search . '%')
                    ->orwhere(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%' . $keyword_search . '%');
            })
            ->get();

        foreach ($members as $member) {
            $item[] = [
                'id' => $member->id,
                'name' => $member->first_name . " " . $member->last_name,
                'avatar' => route('user.avatar', ['id'=>$member->id]),
                'email' => $member->email,
                'position' => $member->position,
            ];
        }
        $result = [
            'incomplete_results' => false,
            'items' => $item,
            'total_count' => count($members)
        ];

        return response()->json($result);
    }
}
