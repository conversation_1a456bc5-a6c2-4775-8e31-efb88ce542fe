<?php

namespace App;

use App\Models\Contacts;
use App\Models\Conversation;
use App\Models\Department;
use App\Models\Language;
use App\Notifications\ResetPassword as ResetPasswordNotification;
use App\Providers\CacheUserProvider;
use Hyn\Tenancy\Traits\UsesTenantConnection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Kyslik\ColumnSortable\Sortable;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

/**
 * Class User
 * @property integer $id
 * @property string $first_name
 * @property string $last_name
 * @property string $name
 * @property string $name_search
 * @property string $email
 * @property string $personal_email
 * @property string $password
 * @property string $identity_card
 * @property string $id_issued_place
 * @property date $id_issued_at
 * @property string $banking_account
 * @property string $prefecture_id
 * @property string $district_id
 * @property string $commune_id
 * @property string $address
 * @property date $birthday
 * @property integer $gender
 * @property string $phone
 * @property string $avatar
 * @property string $department_id
 * @property integer $working_type
 * @property integer $position_id
 * @property string $face_image
 * @property timestamp $started_at
 * @property timestamp $signed_at
 * @property timestamp $ended_at
 */
class User extends Authenticatable
{
    use Notifiable;
    use HasRoles;
    use SoftDeletes;
    use Sortable;
    use UsesTenantConnection;
    use HasApiTokens;

    const SORT_NAME_ALPHA_BET = "TRIM(CONCAT_WS(' ',SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', - 1 ),SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT( users.first_name, ' ', users.last_name ),
                    ' ',-(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )))),
                    ' ',(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )) - 1
                    )),
                    IF(1 < LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )),
                    SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', 1 ),
                    NULL))) AS sort_name";
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token', 'pivot', 'sort_name'
    ];

    protected static function booted()
    {
        static::updated(function ($user) {
            // Clear the cache for the user
            CacheUserProvider::revokeCacheUser($user);
        });

        static::deleted(function ($user) {
            // Clear the cache for the user
            CacheUserProvider::revokeCacheUser($user);
        });
    }

    /**
     * Get the attendances for the user
     * @return HasMany
     */
    public function attendances()
    {
        return $this->hasMany('App\Models\Attendance');
    }

    /**
     * Get the prefecture for the user
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function prefecture()
    {
        return $this->belongsTo('App\Models\Prefecture');
    }

    /**
     * Get the district for the user
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function district()
    {
        return $this->belongsTo('App\Models\District');
    }

    /**
     * Get the commune for the user
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function commune()
    {
        return $this->belongsTo('App\Models\Commune');
    }

    /**
     * Get the position for the user
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function position()
    {
        return $this->belongsTo('App\Models\Position')->withTrashed();
    }

    /**
     * Get the remaining leave day for the user
     * @return HasMany
     */
    public function userRemainingLeaveDay()
    {
        return $this->hasMany('App\Models\UserRemainingLeaveDay');
    }

    /**
     * Send the password reset notification.
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        // Your your own implementation.
        $domain = request()->getSchemeAndHttpHost();
        $this->notify(new ResetPasswordNotification($token, $domain));
    }

    /**
     * Get the projects which user is a creator
     * @return HasMany
     */
    public function projectCreateByUser()
    {
        return $this->hasMany('App\Models\Project','created_by','id');
    }

    /**
     * Get the projects which user is a member
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function projects()
    {
        return $this->belongsToMany('App\Models\Project','project_members','user_id','project_id');
    }

    /**
     * Get the role of the user
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function role()
    {
        return $this->belongsToMany('App\Models\ProjectRole','project_members','user_id','role_id');
    }

    /**
     * Get the user's full concatenated name.
     * -- Must postfix the word 'Attribute' to the function name
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function OauthAcessToken(){
        return $this->hasMany('\App\Models\OauthAccessToken');
    }


    /**
     * Get the contacts of the user
     * @return HasMany
     */
    public function contacts(): HasMany
    {
        return $this->hasMany(Contacts::class);
    }

    /**
     * The conversations that belong to the user.
     * @return BelongsToMany
     */
    public function conversations(): BelongsToMany
    {
        return $this->belongsToMany(Conversation::class, 'conversation_participants');
    }
    
    public function department()
    {
        return $this->belongsTo(Department::class)->withTrashed();
    }

    public function language() {
        return $this->belongsTo(Language::class);
    }
}
