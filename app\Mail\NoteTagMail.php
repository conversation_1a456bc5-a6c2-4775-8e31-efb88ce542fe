<?php

namespace App\Mail;

use App\Models\Language;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NoteTagMail extends BaseMail
{
    use Queueable, SerializesModels;

    protected $user;
    protected $task;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($task)
    {
        $language_id = isset($task['language_id']) ? $task['language_id'] : null;    
        parent::__construct($language_id);
        $this->task = $task;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
            ->subject(trans('language.mail_create_task.subject'))
            ->view('mail.mail_note_tag_user', ['task' => $this->task]);
    }
}
