<?php

namespace App\Jobs\Notification;

use App\Helpers\NotificationHelper;
use App\Jobs\BaseQueue;
use App\Logics\ConversationManager;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Logics\UserConfigManager;
use App\UserConfig;
use App\Models\ConversationParticipant;
class sendNotificationConversation extends BaseQueue
{

    protected $dataNotification;
    protected $conversation;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($dataNotification, $conversation, $websiteId = null)
    {
        parent::__construct($websiteId);
        $this->dataNotification = $dataNotification;
        $this->conversation = $conversation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        parent::handle();
        try {
            // Set sub domain name. 
            $subDomain = $this->getSubDomainFormHostname();
            $this->dataNotification['subdomain'] = $subDomain;
            
            foreach ($this->conversation as $value) {
                $userConfigManager = new UserConfigManager();
                $hasUserConfigPushNotification = $userConfigManager->checkPushNotification(
                    UserConfig::PUSH_FIREBASE_NOTIFICATION,
                    $value['user_id']
                );
                // if user not enable and create group not admim
                if($hasUserConfigPushNotification && ConversationParticipant::IS_MEMBER === $value['admin']) {
                    $this->dataNotification['user_id'] = $value['user_id']; // id of receiver noti
                    $deviceToken = NotificationHelper::getDeviceToken([$value['user_id']]);
                    NotificationHelper::sendNotifyUserDevice($value['user_id'], $deviceToken, $this->dataNotification, ConversationManager::NOTIFICATION_CONVERSATION);
                }
            }
            return 0;
        } catch(Exception $e) {
            Log::error("[sendNotificationConversation][handle] error " . $e->getMessage());
            throw new Exception('[sendNotificationConversation][handle] error ' . $e->getMessage());
        }
    }
}
