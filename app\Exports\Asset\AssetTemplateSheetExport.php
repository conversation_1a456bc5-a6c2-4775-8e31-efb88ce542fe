<?php

namespace App\Exports\Asset;

use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\DataValidation;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class AssetTemplateSheetExport implements WithEvents, WithStyles, WithColumnWidths
{

    protected $sheetName;
    protected $result;
    protected $dataTable;


    public function __construct($sheetName, $result, $dataTable)
    {
        $this->sheetName = $sheetName;
        $this->result = $result;
        $this->dataTable = $dataTable;
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return array(
            'A' => 10,
            'B' => 30,
            'C' => 25,
            'D' => 25,
            'E' => 20,
            'F' => 20,
            'G' => 20,
            'H' => 20,
            'I' => 20,
            'J' => 20,
            'K' => 30,
            'L' => 20,
            'M' => 20,
            'N' => 20,
            'O' => 25,
            'P' => 20,
            'Q' => 20,
            'R' => 20,
            'S' => 25,
            'T' => 25,
            'U' => 20,
            'V' => 20,
            'W' => 25,
            'X' => 30,
            'Y' => 30,
            'Z' => 30,
            'AA' => 30
        );
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $sheet = $event->sheet;
                $style = [
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => Border::BORDER_THIN,
                            'color' => array(
                                'rgb' => '000000'
                            )
                        ]
                    ],
                ];
                $sheet->getStyle('B2:AA100')->applyFromArray($style);
                $sheet->getStyle('B3:AA3')->applyFromArray([
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'D0CECE',
                        ],
                    ]
                ]);

                $sheet->getStyle('A')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('B')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('C')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('D')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('E')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('F')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('G')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('H')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('I')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('J')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('K')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('L')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('M')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('N')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('O')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('P')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('Q')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('R')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('S')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('T')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('U')->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle('V')->getNumberFormat()->setFormatCode('#,##0');
                $sheet->getStyle('W')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('X')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                $sheet->getStyle('Y')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('Z')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
                $sheet->getStyle('AA')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);


                $sheet->setCellValue('A3', trans('language.asset_example'));

                $headingRow = $this->columnWidths();
                unset($headingRow['A']);
                $headingRow = array_keys($headingRow);
                $data = array_values($this->result[0]);
                $configHeadingRow = 2;
                $row = 3;
                $heading = [
                    trans('language.asset_name').'*',
                    trans('language.name_in_contract'),
                    trans('language.name_short'),
                    trans('language.management_unit_id'),
                    trans('language.type_asset'),
                    trans('language.department_use_asset'),
                    trans('language.asset_code'),
                    trans('language.asset_handover_record_code_short'),
                    trans('language.seri_number'),
                    trans('language.supplier'),
                    trans('language.country_of_manufacture'),
                    trans('language.manufacturer'),
                    trans('language.manufacturing_date'),
                    trans('language.asset_category'),
                    trans('language.purchase_date'),
                    trans('language.usage_date'),
                    trans('language.condition'),
                    trans('language.source_of_origin'),
                    trans('language.premises'),
                    trans('language.original_price'),
                    trans('language.residual_value'),
                    trans('language.bidding_package_asset'),
                    trans('language.asset_user'),
                    trans('language.asset_location'),
                    trans('language.asset_description'),
                    trans('language.asset_note'),
                ];
                foreach ($headingRow as $key => $value){
                    $sheet->setCellValue(($value . $configHeadingRow), $heading[$key]);
                    $sheet->setCellValue(($value . $row), $data[$key]);
                }

                for ($i = 3; $i <= 500; ++$i) {
                    $objValidation = $sheet->getCell("D$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['tableShortName']) == 0 ? 3 : (count($this->dataTable['tableShortName'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$T$3:$T$' . $row);

                    $objValidation = $sheet->getCell("E$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table1']) == 0 ? 3 : (count($this->dataTable['table1'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$B$3:$B$' . $row);

                    $objValidation = $sheet->getCell("F$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table2']) == 0 ? 3 : (count($this->dataTable['table2'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$D$3:$D$' . $row);

                    $objValidation = $sheet->getCell("G$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table3']) == 0 ? 3 : (count($this->dataTable['table3'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$F$3:$F$' . $row);

                    $objValidation = $sheet->getCell("K$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['tableSupplier']) == 0 ? 3 : (count($this->dataTable['tableSupplier'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$N$3:$N$' . $row);

                    $objValidation = $sheet->getCell("R$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table4']) == 0 ? 3 : (count($this->dataTable['table4'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$H$3:$H$' . $row);

                    $objValidation = $sheet->getCell("S$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table5']) == 0 ? 3 : (count($this->dataTable['table5'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$J$3:$J$' . $row);

                    $objValidation = $sheet->getCell("T$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['table6']) == 0 ? 3 : (count($this->dataTable['table6'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$L$3:$L$' . $row);

                    //Sheet data - Asset Bidding Package
                    $objValidation = $sheet->getCell("W$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['tableBiddingPackages']) == 0 ? 3 : (count($this->dataTable['tableBiddingPackages'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$P$3:$P$' . $row);

                    //Sheet data - Asset User
                    $objValidation = $sheet->getCell("X$i")->getDataValidation();
                    $objValidation->setType(DataValidation::TYPE_LIST);
                    $objValidation->setErrorStyle(DataValidation::STYLE_INFORMATION);
                    $objValidation->setAllowBlank(false);
                    $objValidation->setShowInputMessage(true);
                    $objValidation->setShowErrorMessage(true);
                    $objValidation->setShowDropDown(true);
                    $objValidation->setErrorTitle('Input error');
                    $objValidation->setError('Value is not in list.');
                    $objValidation->setPromptTitle('Pick from list');
                    $objValidation->setPrompt('Please pick a value from the drop-down list.');
                    $row = count($this->dataTable['tableUsers']) == 0 ? 3 : (count($this->dataTable['tableUsers'])) + 2;
                    $objValidation->setFormula1("='" . trans("language.asset_list_options") . "'!" . '$R$3:$R$' . $row);
                }
            }
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return mixed
     * @throws Exception
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->setTitle($this->sheetName);
        $sheet->getStyle('B:AA')->applyFromArray([
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_LEFT,
            ],
             'font' => [
                'name' => 'Times New Roman',
                'size' => 11
            ]
        ]);

        $sheet->getStyle('B2:AA2')->applyFromArray([
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
            'font' => [
                'name' => 'Times New Roman',
                'bold' => true,
                'size' => 11
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true
            ],
        ]);
    }
}