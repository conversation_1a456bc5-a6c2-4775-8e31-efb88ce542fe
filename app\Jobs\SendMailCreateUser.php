<?php

namespace App\Jobs;

use App\Mail\CreateUserMail;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendMailCreateUser extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;

    /**
     * Create a new job instance.
     *
     * @param $user
     */
    public function __construct($user, $websiteId=null)
    {
        // Inherit from the parent
        parent::__construct($websiteId);

        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Inherit from the parent
        parent::handle();

        $user = $this->user;
        $email = new CreateUserMail($this->user);
        Mail::to($user['email'])->send($email);
    }
}
