<?php

namespace App\Http\Requests;

use App\Enums\TimeFilterEnum;
use Illuminate\Foundation\Http\FormRequest;

class SearchMessageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $timeRangeTypes = [
            TimeFilterEnum::FILTER_CUSTOM
            , TimeFilterEnum::FILTER_7_DAYS_AGO
            , TimeFilterEnum::FILTER_30_DAYS_AGO
            , TimeFilterEnum::FILTER_90_DAYS_AGO
        ];
        $rules = [
            'per_page' => ['nullable', 'integer'],
            'last_record_id' => ['nullable', 'integer'],
            'keyword' => ['nullable'],
            'from' => ['nullable', 'date_format:d/m/Y'],
            'to' => ['nullable', 'date_format:d/m/Y', isset($this->from) ? 'after_or_equal:from' : ''],
            'time_range_type' => ['nullable', 'in:' . implode(',', $timeRangeTypes)],
            'conversation_id' => ['nullable', 'integer'],
            'senders' => ['nullable', function ($attribute, $value, $fail) {
                $items = json_decode($value, true); 
                if (!is_array($items)) {
                    return $fail(trans('validation.array'));
                }
                foreach ($items as $item) {
                    if (!intval($item)) {
                        return $fail(trans('validation.integer', ['attribute' => $attribute . ' items']));
                    }
                }
                return true;
            }],
        ];

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        $messages = [

        ];
        return $messages;
    }
}
