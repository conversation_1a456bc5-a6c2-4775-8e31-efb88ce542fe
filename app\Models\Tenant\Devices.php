<?php

namespace App\Models\Tenant;

use Hyn\Tenancy\Abstracts\SystemModel;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kyslik\ColumnSortable\Sortable;

class Devices extends SystemModel
{
    use SoftDeletes;
    use Sortable;

    protected $connection= 'system';

    protected $table = 'devices';
    const STATUS_OFFLINE = 0;
    const STATUS_ONLINE = 1;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
            $data->updated_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_by = auth()->id();
        });
    }
}
