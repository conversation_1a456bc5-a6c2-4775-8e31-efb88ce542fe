<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use K<PERSON>lik\ColumnSortable\Sortable;

/**
 * Class Request
 * @property integer $id
 * @property string $name
 * @property integer $type
 * @property text $content
 * @property json $approvers
 * @property json $followers
 * @property text $note
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */
class Request extends Model
{
    use Sortable;
    use SoftDeletes;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'approvers' => 'array',
        'followers' => 'array',
        'content' => 'array',
    ];
    public const STATUS_WAITING = 0;
    public const STATUS_ACCEPT = 1;
    public const STATUS_REFUSE = 2;
    public const STATUS_CONFIRM = 3;
    public const STATUS = [
        Request::STATUS_WAITING => 'waiting',
        Request::STATUS_ACCEPT => 'accepted',
        Request::STATUS_REFUSE => 'refused',
        Request::STATUS_CONFIRM => 'confirmed',
    ];
    public const TYPE_CHECKIN = 1;
    public const TYPE_CHECKOUT = 2;
    public const TYPE_VACATION = 3;
    public const TYPE_FAMILY_BUSINESS= 4;
    public const TYPE_FREELANCER = 5;
    public const TYPE_OT = 6;
    public const TYPE_RECEIVE_ARREAR_CASH = 8;
    public const TYPE_COLLECT_ARREAR = 9;
    public const TYPE_RECEIVE_ARREAR = 10;
    public const TYPE_PERCENT_WORK = 11;
    public const TYPE_MENTOR = 12;
    public const TYPE_BTA = 13;
    public const TYPE_COLLABORATOR = 14;
    public const TYPE = [
        Request::TYPE_CHECKIN => 'checkin_request_type',
        Request::TYPE_CHECKOUT => 'checkout_request_type',
        Request::TYPE_VACATION => 'request_vacation',
        Request::TYPE_FAMILY_BUSINESS => 'request_family_business',
        Request::TYPE_FREELANCER => 'request_freelancer',
        Request::TYPE_OT => 'request_ot',
        Request::TYPE_MENTOR => 'request_mentor',
        Request::TYPE_RECEIVE_ARREAR_CASH => 'request_receive_arrear_cash',
        Request::TYPE_COLLECT_ARREAR => 'request_collect_arrear',
        Request::TYPE_RECEIVE_ARREAR => 'request_receive_arrear',
        Request::TYPE_PERCENT_WORK => 'request_percent_work',
        Request::TYPE_BTA => 'request_BTA',
        Request::TYPE_COLLABORATOR => 'request_collaborator'
    ];

    // Collaborator
    public const SHIFT = [
        'morning_shift' => 1,
        'afternoon_shift' => 2,
        'night_shift' => 3
    ];

    public const COLLABORATOR_WORK  = 1;
    public const COLLABORATOR_WORK_FULL  = 2;

    public const TYPE_REQUEST_COLLABORATOR = [
        'work_early' => self::COLLABORATOR_WORK,
        'work_full' => self::COLLABORATOR_WORK_FULL,
    ];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
            $data->created_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }

    /**
     * Get the user who create project
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function userCreate()
    {
        return $this->belongsTo('App\User','created_by','id');
    }

    /**
     * Scope check the user has permission to access the project
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeCheckUserPermission($query, $userId) {
        return $query->where(function($subquery)  use ($userId){
            $subquery->where('requests.created_by', $userId)
                ->orwhereRaw('JSON_CONTAINS(requests.approvers, JSON_QUOTE("'. $userId .'"))')
                ->orwhereRaw('JSON_CONTAINS(requests.followers, JSON_QUOTE("'. $userId .'"))');
        });
    }
}
