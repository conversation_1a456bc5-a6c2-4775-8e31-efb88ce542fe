<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class RequestLog extends Model
{
    public const TYPE_CREATE = 0;
    public const TYPE_UPDATE = 1;
    public const TYPE_ACCEPT = 2;
    public const TYPE_REFUSE = 3;
    public const TYPE_CANCEL = 4;
    public const TYPE_CONFIRM = 5;

    public $timestamps = false;
    protected $fillable = ['request_id','type','log'];

    /**
     * Save id user created
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->created_by = auth()->id();
        });
    }

    /**
     * Get the user who create project
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function userCreate()
    {
        return $this->belongsTo('App\User','created_by','id');
    }
}
