<?php

namespace App\Http\Controllers\Project;

use App\Http\Controllers\Controller;
use App\Logics\DateFormatManager;
use App\Logics\ProjectManager;
use App\Models\ProjectSprint;
use App\Models\ProjectTask;
use App\Models\TaskStatus;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BurnChartSprintController extends Controller
{
    public function index(Request $request, $projectId){
        $userId = Auth::id();
        $sprints = ProjectSprint::where('project_id',$projectId)->get();

        $data = ProjectSprint::where('project_id', $request->projectId)
        ->when(isset($request->sprint_id), function ($query) use ($request){
            $query->where('id', $request->sprint_id);
        })
        ->where('status', ProjectSprint::ACTIVE)
        ->first();
        
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }
        return view('task.burn_down_chart.index', [
            'project' => $project,
            'projectId' => $projectId,
            'sprints' => $sprints,
            'data' => $data
        ]);
    }

    public function GetBurnChartTask(Request $request){
        $data = [];
        $sprint = ProjectSprint::where('project_id', $request->projectId)
            ->when(isset($request->sprint_id), function ($query) use ($request){
                $query->orWhere('id', $request->sprint_id);
            })
            ->where('status', ProjectSprint::ACTIVE)
            ->first();
        $from = Carbon::createFromFormat('Y-m-d', $sprint->started_at)->startOfDay();
        $to = Carbon::createFromFormat('Y-m-d', $sprint->ended_at)->endOfDay();

        $startDate = Carbon::parse($from);
        $endDate = Carbon::parse($to);

        $totalDaysExpected = 0;
        $arrTotalDaysExpected = [];
        for ($date = $startDate->copy(); $date->lte($endDate); $date->addDay()) {
            $index = $date->format('Y-m-d');
            if (!$date->isWeekend()) {
                $totalDaysExpected++;
                $arrTotalDaysExpected[$index] = $totalDaysExpected;
            }
        }

        $estExpected = array();
        $estReality = array();
        
        //lấy ra thời gian dự kiến của các task
        $getTaskSprint = ProjectTask::select(
                'estimated_time',
                DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
            )
            ->where('sprint_id', $sprint->id)
            ->where('project_id', $request->projectId)
            ->whereNotNull('estimated_time')
            ->having('number_child', '=', 0)
            ->get();

        $getTaskSprint = $getTaskSprint->sum('estimated_time');
        $ratioPerDay = 0;
        $totalEstTaskSprint = $getTaskSprint;
        $totalEstTaskSprintReality = $getTaskSprint;
        $initialDate = '';

        // Lấy ra các task đang hoàn thành
        $completedTasks = ProjectTask::select(
                'estimated_time',
                DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child'),
                'updated_at',
            )
            ->where('sprint_id', $sprint->id)
            ->where('project_id', $request->projectId)
            ->where('status', '=', TaskStatus::TASK_STATUS_CLOSE)
            ->whereDate('updated_at', '>=', $from)
            ->having('number_child', '=', 0)
            ->get();

        // Tạo mảng chứa thời gian thực tế đã hoàn thành cho mỗi ngày
        $realTimeCompletedByDay = [];

        // Lặp qua các task đã hoàn thành để tính thời gian est thực tế cho mỗi ngày
        foreach ($completedTasks as $task) {
            $completedDate = Carbon::parse($task->updated_at)->format('Y-m-d');
            $completedTime = $task->estimated_time;

            if (!isset($realTimeCompletedByDay[$completedDate])) {
                $realTimeCompletedByDay[$completedDate] = 0;
            }
            
            $realTimeCompletedByDay[$completedDate] += $completedTime;
        }
        //soft ngày từ bé đến lớn
        ksort($realTimeCompletedByDay);

        $dateFormat = new DateFormatManager;
        //copy date
        $startDateReality = $startDate->copy();
        //Tính thời gian Dự kiến
        do {
            $indexExpected = $startDate->format('Y-m-d');
            $formatDateExpected = $dateFormat->dateFormatLanguage($startDate, 'd/m/Y');
            $arrDateExpected[] = $formatDateExpected;
            if(!$startDate->isWeekend()){
                $ratioPerDay  = ($getTaskSprint / $totalDaysExpected) * $arrTotalDaysExpected[$indexExpected];
                $totalEstTaskSprint = $getTaskSprint - $ratioPerDay;
                $estExpected[] = round($totalEstTaskSprint, 2);
            }else {
                $estExpected[] = round($totalEstTaskSprint, 2); // Giữ nguyên est vào các ngày nghỉ
            }
            $startDate->addDay();
        } while ($totalEstTaskSprint > 0);

        //Tính thời gian thực tế
        do {
            $indexReality = $startDateReality->format('Y-m-d');
            $formatDateReality = $dateFormat->dateFormatLanguage($startDateReality, 'd/m/Y');
            $arrDateReality[] = $formatDateReality;
            if(!$startDateReality->isWeekend()){
                if(isset($realTimeCompletedByDay[$indexReality])){
                    $totalEstTaskSprintReality -= $realTimeCompletedByDay[$indexReality];
                    $estReality[] = round($totalEstTaskSprintReality, 2);
                }else{
                    $estReality[] = round($totalEstTaskSprintReality, 2);
                }
            }else {
                $estReality[] = round($totalEstTaskSprintReality, 2); // Giữ nguyên est vào các ngày nghỉ
            }
        } while ($startDateReality->addDay()->lte(Carbon::now()));

        //khởi tạo thời gian est ban đầu và date ban đầu
        array_unshift($estExpected, round($getTaskSprint, 2));
        array_unshift($estReality, round($getTaskSprint, 2));
        array_unshift($arrDateExpected, $initialDate);
        array_unshift($arrDateReality, $initialDate);

        //lấy ra các ngày từ đầu sprint đến ngày hiện tại
        $ArrDate = array_merge($arrDateExpected, $arrDateReality);
        $datasetExpect = [
            "data"=> $estExpected,
            "label" =>  __(trans('language.estimate_expected')),
        ];
        $datasetReality = [
            "data"=> $estReality,
            "label" =>  __(trans('language.estimate_reality'))
        ];
        $data['expected'] = $datasetExpect;
        $data['reality'] = $datasetReality;
        //unique những phần tử bị lặp sau khi merge và đánh index cho nó
        $data['date'] = array_values(array_unique($ArrDate));
        return response()->json($data);
    }
}
