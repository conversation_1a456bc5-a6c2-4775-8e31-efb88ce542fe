<?php

namespace App\Http\Controllers\Api;

use App\Helpers\StringHelper;
use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Requests\ConversationRequest;
use App\Logics\ConversationManager;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\Message;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use  \Illuminate\Http\Response;

class ConversationController extends AbstractApiController
{
    /**
     * @var ConversationManager
     */
    
     protected $conversationManager;
     
    public function __construct(ConversationManager $conversationManager)
    {
        $this->conversationManager = $conversationManager;
    }
    
    /**
     * list conversation of user
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function listConversation(Request $request)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            $data = $this->conversationManager->listConversation($idUser, $request);
            $msg = __('message.conversation.listConversation.success');
            if (empty($data)) {
                $msg = __('message.conversation.listConversation.fail');
                return $this->respondForbidden($msg);
            }
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Create conversation
     * @param ConversationRequest $request
     * @return json
     * @throws Exception
     */

    public function store(ConversationRequest $request)
    {
        try {
            $data = $this->conversationManager->store(Auth::guard('api')->user()['id'], $request);
            $msg =  __('message.conversation.create.success');
            if (empty($data)) {
                $msg = __('message.conversation.create.fail');
                return $this->respondBadRequest($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationController][store] error " . $e->getMessage());
            throw new Exception('[ConversationController][store] error ' . $e->getMessage());
        }
    }
  
     /**
     * update conversation
     * @param ConversationRequest $request
     * @return json
     * @throws Exception
     */
     public function updateConversation($conversationId, ConversationRequest $request)
     {
        try {
            $data = $this->conversationManager->updateConversation($conversationId, $request);
            if (isset($data['status_error'])) {
                $statusError = $data['status_error'];
                if (ConversationParticipant::ERROR_PERMISSION === $statusError) {
                    $msg = __('message.conversation.update.error_permission');
                    return $this->respondForbidden($msg);
                }
                if (ConversationParticipant::ERROR_NOT_FOUND === $statusError) {
                    $msg = __('message.conversation.update.not_found');
                    return $this->respondNotFound($msg);
                }
            }
            $msg =  __('message.conversation.update.success');
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
             Log::error("[ConversationController][updateConversation] error " . $e->getMessage());
            throw new Exception('[ConversationController][updateConversation] error ' . $e->getMessage());
        }
     }
  
    /**
     * mute conversation
     * @param ConversationRequest $request
     * @return json
     * @throws Exception
     */
    public function muteConversation(Request $request)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            if(empty($request->input('conversation_id'))){
                return $this->respondForbidden(__('message.conversation.pin.fail', ['action' => 'Tắt thông báo']));
            }
            $conversationId = $request->input('conversation_id');
            [$data,$action] = $this->conversationManager->muteConversation($idUser, $conversationId);
            $msg =  __('message.conversation.pin.success', ['action' => $action]);
            if (isset($data['status_error'])) {
                $msg = __('message.conversation.pin.fail', ['action' => $action]);
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationController][muteConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationController][muteConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
    /**
     * delete conversation
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */

    public function leaveConversation($conversationId)
    {
        try {
            $data = $this->conversationManager->leaveConversation($conversationId, Auth::guard('api')->user()->id);
            $msg =  __('message.message.delete.success', ['attribute' => __('message.conversation.title')]);
            if (isset($data['status_error'])) {
                $msg = __('message.message.delete.fail', ['attribute' => __('message.conversation.title')]);
                if ($data['status_error'] == ConversationParticipant::ERROR_EMPTY_ADMIN) {
                    $msg = __('message.conversationParticipant.update.empty_admin');
                }
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse([] ,$msg);
        } catch (Exception $e) {
            Log::error("[ConversationController][store] error " . $e->getMessage());
            throw new Exception('[ConversationController][store] error ' . $e->getMessage());
        }
    }

    /**
     * get detail conversation
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */

     public function detailConversation($conversationId)
     {
         try {
             $data = $this->conversationManager->detailConversation($conversationId);
             $msg = __('message.conversation.detailConversation.success');
             if (empty($data)) {
                 $msg = __('message.conversation.detailConversation.fail');
                 return $this->respondForbidden($msg);
             }
             return $this->renderJsonResponse($data, $msg);
         } catch (Exception $e) {
             Log::error("[ConversationController][store] error " . $e->getMessage());
             throw new Exception('[ConversationController][store] error ' . $e->getMessage());
         }
     }

    /**
     * invitation link
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */

     public function invitationLink($encryptedConversationId)
     {
        return view('deeplink.index', [
            'encryptedConversationId' => $encryptedConversationId
        ]);
     }

    /**
     * get detail conversation by invitation link
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */

     public function getConversationInvitation($encryptedConversationId)
     {
        try {
            $data = $this->conversationManager->getConversationInvitation($encryptedConversationId);
            $msg = __('message.conversation.detailConversation.success');
            if (empty($data)) {
                $msg = __('message.conversation.detailConversation.fail');
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationController][getConversationInvitation] error " . $e->getMessage());
            throw new Exception('[ConversationController][getConversationInvitation] error ' . $e->getMessage());
        }
     }
    /**
     * delete conversation by conversation id
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */
    public function deleteConversation($conversationId)
    {
        $data = $this->conversationManager->deleteConversation($conversationId);
        if (isset($data['status_error'])) {
            $statusError = $data['status_error'];
            if ($statusError === ConversationParticipant::ERROR_PERMISSION) {
                $msg = __('message.conversation.delete.error_permission');
                return $this->respondForbidden($msg);
            }
            if ($statusError === ConversationParticipant::ERROR_KICKED) {
                $msg = __('message.conversation.delete.kicked');
                return $this->respondForbidden($msg);
            }
            if ($statusError === ConversationParticipant::ERROR_NOT_FOUND) {
                $msg = __('message.conversation.delete.not_found');
                return $this->respondNotFound($msg);
            }
        }
        $msg = __('message.conversation.delete.success');
        return $this->renderJsonResponse([], $msg);
    }
}
