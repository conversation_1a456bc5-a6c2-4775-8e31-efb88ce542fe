<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\QueryException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $exception
     * @return void
     *
     * @throws \Throwable
     */
    public function report(Throwable $exception)
    {
        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($request->is('api/*')) {
            if($exception instanceof \Illuminate\Auth\AuthenticationException ){
                $statusCode = Response::HTTP_UNAUTHORIZED;
                $response = [
                    'code' => $statusCode,
                    'message' => 'Unauthorized'
                ];
            } else if ($exception instanceof ValidationException) {
                $msg = '';
                foreach ($exception->errors() as $field => $errors) {
                    foreach ($errors as $key => $value) {
                        $msg = $value;
                        continue;
                    }
                }
                $statusCode = Response::HTTP_UNPROCESSABLE_ENTITY;
                $response = [
                    'code' => $statusCode,
                    'message' => $msg
                ];
            } else {
                $statusCode = Response::HTTP_INTERNAL_SERVER_ERROR;
                $response = [
                    'code' => $statusCode,
                    'message' => trans('message.server_error')
                ];
            }
            return response()->json($response, $statusCode);
        } else{
            if ($exception instanceof \Illuminate\Session\TokenMismatchException) {
                return redirect()->route('login');
            }
        }
        return parent::render($request, $exception);
    }
}
