<?php

namespace App\Logics;

use App\Models\Challenge;
use App\Traits\StorageTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class CodingContestManager
{
    use StorageTrait;
    /**
     * change image url description challenge
     */
    public function saveImage($challengeId, $imgs)
    {
        $challenge = Challenge::find($challengeId);
        $hostname = URL::to('/');
        foreach($imgs as $item){
            if(strpos($challenge->problem,  $item) !== false){
                $filePath = 'tmp/'. $item;
                $destinationPath = str_replace('{challenge_id}', $challenge->id, CONTEST_QUESTION_DIR).'/'.$item;
                $fileSize = $this->moveFile($filePath,$destinationPath);
                $imageUrlPrevious = $hostname. '/'.$filePath;
                $imageUrlCurrent = $hostname. "/coding/" . $challenge->id .'/'. $item;
                $challenge->problem = str_replace($imageUrlPrevious, $imageUrlCurrent, $challenge->problem);
            }
        }
        $challenge->save();
    }
}
