<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserConfigRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'value' => 'required|max:255'
        ];
    }

    /**
     * Validate message.
     * 
     * @return array
     */
    public function messages(): array
    {
        return [
            'value.required' => __('validation.required', ['attribute' => __('validation.attributes.value')]),
            'value.max' =>__('validation.max.string', ['attribute' => __('validation.attributes.value')])
        ];
    }

}
