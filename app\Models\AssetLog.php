<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class TaskLog
 * @property integer $id
 * @property integer $asset_id
 * @property string $log
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */

class AssetLog extends Model
{
    protected $table = 'asset_logs';
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    protected $fillable = [
        'asset_id',
        'log',
        'created_by',
        'updated_by',
    ];
}
