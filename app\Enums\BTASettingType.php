<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static AGE()
 * @method static static GENDER()
 * @method static static JOB()
 * @method static static LEVEL_OF_INQUIRY()
 * @method static static FEEDBACK()
 * @method static static LEVEL_CARE()
 * @method static static TARGET()
 * @method static static LANGUAGE_PROFICIENCY()
 * @method static static MODES_STUDY()
 * @method static static CONCERNS()
 * @method static static DATA_CHANNELS()
 */
final class BTASettingType extends Enum
{
    const AGE = 1;
    const GENDER = 2;
    const JOB = 3;
    const LEVEL_OF_INQUIRY = 4;
    const FEEDBACK = 5;
    const LEVEL_CARE = 6;
    const TARGET = 7;
    const LANGUAGE_PROFICIENCY = 8;
    const MODES_STUDY = 9;
    const CONCERNS = 10;
    const DATA_CHANNELS = 11;
}
