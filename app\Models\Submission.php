<?php

namespace App\Models;

use App\User;
use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kyslik\ColumnSortable\Sortable;


/**
 * Class Submission
 * @property integer $id
 * @property integer $user_id
 * @property integer $challenge_id
 * @property string $sourcecode
 * @property float $runtime
 */
class Submission extends Model
{
    use SoftDeletes;
    use Sortable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * Get user who submitted this challenge
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
