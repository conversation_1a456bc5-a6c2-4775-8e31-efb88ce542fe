<?php

namespace App\Logics;
use App\Models\FoodOrder;
use App\Models\FoodMenu;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Response;

class FoodOrderManager
{
    const PAGINATE = 10;
    /**
     * get food and note
     * @param string $order
     * @return mix
     */
    public static function getFoodAndNote($order){
        if ($order) {
            $order = explode(GROUP_CONCAT_SEPARATOR, $order);
            $result['food'] = trim($order[0]);
            $result['note'] = isset($order[1])?str_replace(CONCAT_SEPARATOR,"",$order[1]):null;
            return $result;
        } else {
            return null;
        }
    }

    /**
     * check order time
     * @return bool
     */
    public static function checkOrderTime($date) {
        $status = true;
        $foodMenu = FoodMenu::whereDate('date', $date)->first();
        
        if ($foodMenu) {
            $menu = json_decode($foodMenu->menu, true);
            $foodOrderTime = date($date . ' ' .$menu['time']);
        } else {
            $foodOrderTime = date($date . ' ' .FOOD_ORDER_TIME);
        }
        if (date('Y-m-d H:i') > $foodOrderTime) {
            $status = false;
        }
        return $status;
    }

     /**
     * get user order
     * @return bool
     */
    public function getUserOrder($date) {
        $userId = Auth::id();
        $userOrder = FoodOrder::join('food_menus', 'food_menus.id', 'food_orders.menu_id')
        ->where('user_id', $userId)
        ->whereDate('food_menus.date', $date)
        ->first();
        if ($userOrder) {
            $userOrder = $this->getFoodAndNote($userOrder->order);
        } else {
            $userOrder = null;
        }
        return $userOrder;
    }
    /**
     * list order
     * @param $menu
     * @return array
     */
    public function listOrder(Request $request = null, $paginate = null, $date = null) {
        $orders = FoodOrder::select(
            'food_orders.id',
            'food_orders.menu_id',
            'food_orders.user_id',
            'food_orders.order',
            'food_orders.updated_at',
            'food_orders.created_at',
            'food_menus.date',
            DB::raw("CONCAT_WS(' ', first_name, last_name) AS name")
        ) 
        ->join('users','users.id','food_orders.user_id')
        ->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
        ->whereDate('food_menus.date', $date);

        if ($request) {
            $sortable = ['users.last_name', 'food_orders.order', 'food_orders.created_at'];
            $direction = in_array($request->direction, ['asc', 'desc']) ? $request->direction : 'asc';
            if (in_array($request->sort, $sortable)) {
                $orders = $orders->orderBy($request->sort, $direction);
            } else {
                $orders->orderBy('food_orders.created_at', 'desc' );
            }
        } else {
            $orders->orderBy('food_orders.created_at', 'desc' );
        }
        
        if (!$paginate) {
            $orders = $orders->paginate(self::PAGINATE)
            ->withQueryString()
            ->withPath(route('employee.order.list'));
        } else{
            $orders = $orders->get();
        }
        
        return $orders;
    }
}
