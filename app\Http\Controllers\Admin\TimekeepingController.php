<?php

namespace App\Http\Controllers\Admin;

use App\Exports\Timekeeping\TimeKeepingMultiSheetExport;
use App\Helpers\CsvHelper;
use App\Helpers\RequestHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ExportTimekeepingFormRequest;
use App\Http\Requests\Admin\ImportTimekeepingFormRequest;
use App\Logics\TimekeepingManager;
use App\Logics\UserManager;
use App\Models\Attendance;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\EmployeeExport;
use App\Logics\TaskManager;
use App\Models\EvaluationResult;
use App\Models\UserLeaveDayLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class TimekeepingController extends Controller
{
    const PER_PAGE = 10;
    const BEETECH_EXPORT_USER_ID_SORT_ORDER = '1,3,10,2,12,13,16,22,15,8,5,30,9,18,68,33,69,40,43,45,48,49,50,51,67,52,59,57,56,55,61,63,62,64,65';

    public function __construct()
    {
        set_time_limit(120);
    }
    public function index(Request $request)
    {
        // Get all users
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['choose_month']);
        $users_db = User::where('id', '!=', '0')->select(['id', 'first_name', 'last_name']);
        if ($request->has('deleted')) {
            $users_db = $users_db->onlyTrashed();
            $deleted = true;
        } else $deleted = false;

        if(isset($request->user)){
            $users_db->where('id',$request->user);
        }
        $users = $users_db->paginate(self::PER_PAGE);

        // Get selected user
        $user_id = null;
        $selected_user = null;
        if (isset($request->user)) {
            $user_id = $request->user ?? $users_db->first()->id;
            $userManager = new UserManager();
            $selected_user = $userManager->getUserWithLeaveDay($user_id, $request->has('deleted'))->first();
        }

        $arrUsers = [];
        $listUserId = [];
        $month = isset($request->choose_month) ?Carbon::createFromFormat('d/m/Y','01/'.$request->choose_month) : Carbon::now();
        $start_date = $month->firstOfMonth()->format('Y-m-d');
        $end_date = $month->endOfMonth()->format('Y-m-d');
        foreach($users as $user){
            $arrUsers[$user->id]['id'] = $user->id;
            $arrUsers[$user->id]['name'] = $user->first_name. ' ' .$user->last_name;
            $arrUsers[$user->id]['leave_day'] = isset($user->userRemainingLeaveDay()->orderBy('month','DESC')->first()->hours)?$user->userRemainingLeaveDay()->orderBy('month','DESC')->first()->hours:0;
            $listUserId[] = $user->id;
        }

        $timekeepingManager = new TimekeepingManager();
        $user_attendances = $timekeepingManager->getUserAttendances($listUserId, $start_date, $end_date, $request->sort, $deleted, $request->direction)->get();
        $allDetailedCheckTimes = $timekeepingManager->getDetailedCheckTimesForMultipleUsers($listUserId, $start_date, $end_date);

        foreach($user_attendances as $item){
            $working = $timekeepingManager->calcWorkingDuration($item);
            $has_type_gps = $working['has_type_gps'];
            $check_in_location = $working['check_in_location'];
            $check_out_location = $working['check_out_location'];
            $late_join = $working['late_join'];
            $early_leave = $working['early_leave'];
            $data = [$working['checked_in'],$working['checked_out']];
            $other_checked_times = $allDetailedCheckTimes[$item->user_id][$item->date] ?? [];



            $otherCheckedTimes = $allDetailedCheckTimes[$item->user_id][$item->date] ?? [];
            $checkInLocation = reset($otherCheckedTimes);
            $checkOutLocation = end($otherCheckedTimes);

            $checkInLocationStr = $checkInLocation['location'] ?? null;
            if (empty($working['checked_in'] )) {
                $checkInLocationStr = null;
            }
            
            $checkOutLocationStr = $checkOutLocation['location'] ?? null;
            if (empty($working['checked_out'] )) {
                $checkOutLocationStr = null;
            }

            $arrUsers[$item->user_id][$item->date] = [
                $data,$late_join,$early_leave,
                    'checked_in_id' => $checkInLocation['id'] ?? null,
                    'checked_out_id' => $checkOutLocation['id'] ?? null,
                    'has_type_gps' => $has_type_gps,
                    'check_in_location' => $checkInLocationStr,
                    'check_out_location' => $checkOutLocationStr,
                    'other_checked_times' => array_slice($other_checked_times, 1, -1),
            ];
        }

        //Get the days of the month
        $dates = [];
        for ($i = 1; $i < $month->daysInMonth + 1; $i++) {
            $currentDate = \Carbon\Carbon::createFromDate($month->year, $month->month, $i);
            $weeked = $timekeepingManager->getWeekedDay($currentDate->format('l'));
            $isFutureDate  = $timekeepingManager->getFutureDay($currentDate);
            $isToday  = $timekeepingManager->getToday($currentDate);
            $dates[] = [
                $currentDate->format(trans("language.time_keeping_format_date")),
                $weeked,
                'isFutureDate' => $isFutureDate,
                'isToday' => $isToday,
            ];
        }

        // Redirect to last page if page parameter greater than last page
        if ($request->page > $users->lastPage()) {
            return redirect($request->fullUrlWithQuery(["page" => $users->lastPage()]));
        }
        return view('admin.timekeeping.index', [
            'users' => $users,
            'selected_user' => $selected_user,
            'attendances' => $user_attendances,
            'filterHtml' => $filterHtml,
            'arrUsers' => $arrUsers,
            'dates' => $dates,
            'day' => $month
        ]);
    }

    /**
     * Add new attendance for user
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function addUserAttendance(Request $request) {
        try {
            DB::beginTransaction();
            $preUrl = empty(URL::previous())?route('admin.timekeeping.index'):(URL::previous());

            $chooseTimeCheckin = $request->choose_time_checkin . ':00';
            $chooseTimeCheckOut = $request->choose_time_checkout . ':00';
            $checkInOld = $request->check_in_old ;
            $checkOutOld = $request->check_out_old ;

            if($request->choose_time_checkin != null && $request->check_in_old == null){
                Attendance::create([
                    'user_id'=>$request->user_id,
                    'checked_at' => Carbon::createFromFormat('Y-m-d H:i:s', $request->date_timekeeping." ".$chooseTimeCheckin),
                    'checked_type' => Attendance::MANUAL_CHECK
                ]);
            }elseif($request->choose_time_checkin != null && $request->check_in_old != null){
                $attendance = Attendance::query()->select('id','checked_at')
                    ->where('id', $request->check_in_id)
                    ->first();
                $attendance->checked_at = Carbon::createFromFormat('Y-m-d H:i:s', $request->date_timekeeping." ".$checkInOld)->setTimeFromTimeString($request->date_timekeeping." ".$chooseTimeCheckin)->toDateTimeString();
                $attendance->save();
            }elseif($request->choose_time_checkin == null && $request->check_in_old != null){
                $attendance = Attendance::select('id','checked_at')
                    ->where('id', $request->check_in_id)
                    ->first();
                $attendance->delete();

            }
            if($request->choose_time_checkout != null && $request->check_out_old == null){
                Attendance::create([
                    'user_id'=>$request->user_id,
                    'checked_at' => Carbon::createFromFormat('Y-m-d H:i:s', $request->date_timekeeping." ".$chooseTimeCheckOut),
                    'checked_type' => Attendance::MANUAL_CHECK
                ]);
            }elseif($request->choose_time_checkout != null && $request->check_out_old != null){
                $attendance = Attendance::query()->select('id','checked_at')
                    ->where('id', $request->check_out_id)
                    ->first();
                $attendance->checked_at = Carbon::createFromFormat('Y-m-d H:i:s', $request->date_timekeeping." ".$checkOutOld)->setTimeFromTimeString($request->date_timekeeping." ".$chooseTimeCheckOut)->toDateTimeString();
                $attendance->save();
            }elseif($request->choose_time_checkout == null && $request->check_out_old != null ){
                $attendance = Attendance::query()->select('id','checked_at')
                    ->where('id', $request->check_out_id)
                    ->first();
                $attendance->delete();
            }

            if ($request->has('check_ids') && $request->has('check_duration_times_old')) {
                $checkIds = $request->check_ids;
                $checkTimesOld = $request->check_duration_times_old;
                $checkTimesNew = $request->check_times_new ?? [];
                $date = $request->date_timekeeping;
            
                foreach ($checkIds as $index => $checkId) {
                    $oldTime = $checkTimesOld[$index] ?? null;
                    $newTime = $checkTimesNew[$index] ?? null;
            
                    if ($oldTime) {
                        $query = Attendance::query()->where('id', $checkId);
            
                        if ($newTime) {
                            $newCheckedAt = Carbon::parse("$date $newTime")->toDateTimeString();
                            $query->update(['checked_at' => $newCheckedAt]);
                        } else {
                            $query->delete();
                        }
                    }
                }
            }
            DB::commit();
            return redirect($preUrl);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e);
            $preUrl = empty(URL::previous())?route('admin.timekeeping.index'):(URL::previous());
            return redirect($preUrl);
        }
    }

    /**
     * Export timekeeping and return excel file
     *
     * @param ExportTimekeepingFormRequest $request
     * @return \Illuminate\Http\Response
     */
    public function exportTimekeeping(ExportTimekeepingFormRequest $request)
    {
        $holidays = [];
        if ($request->choose_holidays) {
            $chooseHolidays = explode(', ', $request->choose_holidays);
            foreach ($chooseHolidays as $holiday) {
                $holidays[Carbon::createFromFormat('d/m/Y',$holiday)->format('Y-m-d')] = $holiday;
            }
        }
        $month = Carbon::createFromFormat('d/m/Y','01/'.$request->choose_month)->month;
        $year = Carbon::createFromFormat('d/m/Y','01/'.$request->choose_month)->year;
        $endDayMonth = Carbon::createFromFormat('d/m/Y','01/'.$request->choose_month)->endOfMonth()->format('Y-m-d');
        $date = $request->choose_month;

        $isBeeTechCompany = env('USE_TENANT', false) && app(\Hyn\Tenancy\Environment::class)->website()->id == env('BEETECH_COMPANY_ID', '');

        $allStaffs = User::withTrashed()->selectRaw(
            'users.id,
            users.email,
            users.first_name,
            users.last_name,
            users.working_type,
            users.banking_account,
            users.ended_at,
            users.deleted_at,
            users.number_dependents,
            users.work_place_id,
            users.company_insurance,
            users.company_contract,
            users.personnel_status,
            users.monthly_allowance,
            users.salary_calculation_method,
            users.hour_register,
            users.percent_finish,
            users.outbound_parking,
            users.bta_allowance,
            users.position_allowance,
            users.social_insurance_fee,
            users.vehicle_type,
            users.started_at_phase2,
            work_places.name as work_place_name,
            CASE WHEN user_remaining_leave_days.hours IS NOT NULL
                THEN user_remaining_leave_days.hours
                ELSE 0
            END remaining_leave_hours,
            CASE WHEN DATE_FORMAT(users.signed_at, "%y-%m") = DATE_FORMAT(?, "%y-%m")
                THEN ROUND((DATEDIFF(LAST_DAY(users.signed_at),users.signed_at)+1) / DAY(LAST_DAY(users.signed_at)) * 8, 2)
                ELSE 8
            END plus_leave_hours,
            CASE WHEN users.signed_at IS NOT NULL AND users.signed_at <= ?
                THEN users.signed_at
                ELSE NULL
            END signed_at
            ', [$endDayMonth, $endDayMonth]
        )
        ->leftJoin('user_remaining_leave_days', function ($q) use ($month, $year){
                $q->on('users.id', 'user_remaining_leave_days.user_id')
                    ->whereRaw('MONTH(user_remaining_leave_days.month) = ?', $month)
                    ->whereRaw('DAY(user_remaining_leave_days.month) = ?', 1)
                    ->whereRaw('YEAR(user_remaining_leave_days.month) = ?', $year);
            })
            ->leftJoin('work_places', 'work_places.id', 'users.work_place_id')
        ->where(function($query) use ($year) {
                $query->whereNull('users.deleted_at')
            ->orWhere(function($q) use ($year){
                        $q->whereNotNull('users.ended_at')
                            ->whereYear('users.ended_at', $year);
                    });
            });

        if ($isBeeTechCompany) {
            $allStaffs = $allStaffs->whereNotIn('users.id', [0])
            ->orderByRaw('IF(FIELD(users.id, '.self::BEETECH_EXPORT_USER_ID_SORT_ORDER.')=0,1,0), FIELD(users.id, '.self::BEETECH_EXPORT_USER_ID_SORT_ORDER.'), users.id');
        } else {
            $allStaffs = $allStaffs->orderBy('users.id');
        }

        $allStaffs = $allStaffs->get();
        $timekeepingManager = new TimekeepingManager();
        $timekeepingManager->updateLeaveDays($allStaffs, $year, $month, $holidays);

        return Excel::download(new TimeKeepingMultiSheetExport($allStaffs, $date, $holidays, $isBeeTechCompany), 'BeeID_Salary_'.$year.'-'.$month.'.xlsx');
    }
    /**
     * Import timekeeping
     *
     * @param ImportTimekeepingFormRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function importTimekeeping(ImportTimekeepingFormRequest $request) {
        try {
            if (!$request->hasFile('file')) {
                return back()->with([
                    'status_failed' => trans('message.import_timekeeping_failed')
                ]);
            }
            // Open file from request
            $file = $request->file('file');
            $file = fopen($file->getRealPath(), 'r');

            // Import csv file
            $model = Attendance::class;
            $columns = ['user_id', 'checked_at', 'checked_type', 'checked_image'];
            $batchSize = config('import.batch_size.timekeeping');
            $csvHelper = new CsvHelper();
            $csvHelper->import($file, $model, $columns, $batchSize);

            return back()->with([
                'status_succeed' => trans('message.import_timekeeping_succeed')
            ]);
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.import_timekeeping_failed')
            ]);
        }
    }

    /**
     * Update check_in,check_out data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeTimekeeping(Request $request)
    {
        $attendance = Attendance::select('id','checked_at')
            ->where('user_id', $request->user_id)
            ->where('checked_at', date('Y-m-d H:i:s', strtotime($request->old_time)))
            ->first();

        $attendance->checked_at = Carbon::createFromFormat('Y-m-d H:i:s', $request->old_time)->setTimeFromTimeString($request->new_time)->toDateTimeString();
        $attendance->save();

        $timekeepingManager = new TimekeepingManager();
        $user_attendance = $timekeepingManager->getUserAttendances($request->user_id, $request->old_time, $request->old_time, null, null)->first();
        $duration = $user_attendance == null ? 0 : $timekeepingManager->calcWorkingDuration($user_attendance)['duration'];
        return $duration;
    }

    /**
     * Delete check_in,check_out data
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteTimekeeping(Request $request)
    {
        Attendance::select('id','checked_at')
            ->where('user_id', $request->user_id)
            ->where('checked_at', date('Y-m-d H:i:s', strtotime($request->old_time)))
            ->delete();

        $timekeepingManager = new TimekeepingManager();
        $user_attendance = $timekeepingManager->getUserAttendances($request->user_id, $request->old_time, $request->old_time, null, null)->first();
        $duration = $user_attendance == null ? 0 : $timekeepingManager->calcWorkingDuration($user_attendance)['duration'];
        return $duration;
    }
    /**
     * Export listemployee
     * @param Request $request
     */
    public function exportEmployee(Request $request){
        // Get user
        $users = User::select(
            'id',
            'first_name',
            'last_name',
            'deleted_at',
        )
            ->where('users.id', '!=', 0);
        if(isset($request->user)) {
            $users->where('users.id', $request->user);
        }
        if($request->has('deleted')) {
            $users = $users->onlyTrashed();
        }
        $users = $users->get();

        $users_id = [];
        foreach($users as $user){
            $users_id[] = $user->id;
        }

        // Get evaluation KPI
        $evaluationForms = EvaluationResult::select([
            'evaluation_results.user_id',
            DB::raw("GROUP_CONCAT(evaluation_results.score SEPARATOR ' | ') AS scores")
        ])
            ->join('evaluation_forms','evaluation_forms.id','evaluation_results.form_id')
            ->whereIn('evaluation_results.user_id',$users_id)
            ->where('evaluation_forms.ended_at', '<=', Carbon::now())
            ->where('evaluation_results.score', '!=',null)
            ->groupBy('evaluation_results.user_id');

        // Get time work
        $attendances = Attendance::selectRaw('
            user_id,
            DATE(checked_at) date,
            TIME(MIN(checked_at)) checked_in,
            TIME(MAX(checked_at)) checked_out',
        )->whereIn('user_id',$users_id);

        $start_date = empty($request->start_date)?$request->start_date:Carbon::createFromFormat('d/m/Y',$request->start_date)->format('Y-m-d');
        $end_date = empty($request->end_date)?$request->end_date:Carbon::createFromFormat('d/m/Y',$request->end_date)->format('Y-m-d');

        if (isset($start_date)) {
            $attendances->whereDate('checked_at', '>=', $start_date);
            $evaluationForms = $evaluationForms->where(function($query)  use ( $start_date){
                $query->where('evaluation_forms.from','>=',  $start_date)
                    ->orwhere('evaluation_forms.to', '>=',  $start_date);
            });
        } else {
            $attendances->whereDate('checked_at', '>=', date('Y-m-1', strtotime('this month')));
        }

        if (isset($end_date)) {
            $attendances->whereDate('checked_at', '<=', $end_date);
            $evaluationForms = $evaluationForms->where(function($query)  use ($end_date){
                $query->where('evaluation_forms.from','<=',  $end_date)
                    ->orwhere('evaluation_forms.to', '<=',  $end_date);
            });
        }

        $attendances = $attendances->groupBy(DB::raw('DATE(checked_at)'),'user_id')->get();
        $evaluationForms = $evaluationForms->get()->sortBy('to')->keyBy('user_id');
        $times = [];

        $timekeepingManager = new TimekeepingManager();
        foreach($attendances as $attendance){
            if(!isset($times[$attendance->user_id])){
                $times[$attendance->user_id]['working_time_user'] = 0;
                $times[$attendance->user_id]['late_join'] = 0;
            }
            $calcWorkingDuration =  $timekeepingManager->calcWorkingDuration($attendance);
            $times[$attendance->user_id]['working_time_user'] += $calcWorkingDuration['duration'];
            if($calcWorkingDuration['late_join']){
                $times[$attendance->user_id]['late_join'] += 1;
            }
        }

        // create array data export
        $index = 1;
        $result = [];
        foreach ($users as $user){
            $item['number_order'] = $index;
            $item['name_employee'] = $user->first_name.' '.$user->last_name;
            $item['working_time'] = isset($times[$user->id]['working_time_user']) ? $times[$user->id]['working_time_user'] : '';
            $item['day_off'] = "";
            $item['late_work'] = isset($times[$user->id]['late_join']) ? $times[$user->id]['late_join'] : '';
            $item['KPI_score'] = isset($evaluationForms[$user->id]) ? $evaluationForms[$user->id]->scores : '' ;
            $index += 1;
            $result[] = $item;
        }
        $pathPrefix = env('USE_TENANT', false) ? app(\Hyn\Tenancy\Website\Directory::class)->path() : '';
        $path = TEMP_DIR . '/'. Str::random(25).'.xlsx';
        $nameFile = trans('language.information_employee').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
        Excel::store(new EmployeeExport($result), $pathPrefix.$path);
        return Storage::disk(FILESYSTEM)->download($path, $nameFile);
    }

    public function calWorkingHoursInYear(Request $request){
        $toMonth = $request->month ? Carbon::createFromFormat('m/Y', $request->month) : Carbon::now();
        $users = User::withTrashed()->selectRaw("users.id,
        users.first_name,
        users.last_name,
        users.started_at,
        users.signed_at,
        users.ended_at,
        users.deleted_at")->whereRaw("( users.deleted_at IS NULL OR users.ended_at IS NOT NULL and (Year(users.ended_at) = '. $toMonth->year .')) ")->orderByRaw("IF
        ( FIND_IN_SET( users.id, '1,3,10,2,12,13,16,22,15,8,5,30,9,18,68,33,69,40,43,45,48,49,50,51,67,52,59,57,56,55,61,63,62,64,65' )= 0, 1, 0 ),
        FIND_IN_SET( users.id, '1,3,10,2,12,13,16,22,15,8,5,30,9,18,68,33,69,40,43,45,48,49,50,51,67,52,59,57,56,55,61,63,62,64,65' ),
        users.id")->get();

        $hours = UserLeaveDayLog::select(DB::raw('SUM(hours) as hours, user_leave_days_logs.user_id'))
            ->join('users', 'users.id', 'user_leave_days_logs.user_id')
        ->whereRaw('leave_day>="'. $toMonth->year .'-01-01"')
        ->whereRaw('leave_day<="'. $toMonth->endOfMonth() .'"')
            ->whereRaw('leave_day >= DATE(users.signed_at)')
            ->whereRaw('DAYOFWEEK(Date(leave_day)) NOT IN (1, 7)')
            ->groupBy('user_leave_days_logs.user_id')
            ->get()->keyBy('user_id')->toArray();

        $timeKeepingManager = new TimekeepingManager();
        foreach($users as &$user){
            if($user->signed_at){
                $attendances = Attendance::select(
                    'attendances.user_id',
                    DB::raw('DATE(checked_at) as date'),
                    DB::raw('TIME(MIN(checked_at)) as checked_in'),
                    DB::raw('TIME(MAX(checked_at)) as checked_out'),
                )

                    ->whereRaw('YEAR(checked_at) = ' . $toMonth->year)
                    ->whereRaw('MONTH(checked_at) <= ' . $toMonth->month)
                ->whereRaw("Date(checked_at)>='".$user->signed_at."'")
                    ->where('attendances.user_id', $user->id)
                    ->whereRaw('DAYOFWEEK(Date(checked_at)) NOT IN (1, 7)')
                    ->groupBy(DB::raw('attendances.user_id, DATE(checked_at)'))
                    ->orderBy(DB::raw('attendances.user_id, DATE(checked_at)'))
                    ->get();
            }else{
                $attendances = [];
            }

            $user->time = 0;

            $user->time += isset($hours[$user->id])? $hours[$user->id]['hours'] : 0;

            foreach($attendances as $attendance){
                $duration = $timeKeepingManager->calcWorkingDuration($attendance, true);
                $user->time += $duration['duration'];
            }
        }
        return view('admin.timekeeping.hour_work', ['users' => $users]);
    }

    public function calWorkingHoursInYearEachMonth(Request $request){
        $toMonth = $request->month ? Carbon::createFromFormat('m/Y', $request->month) : Carbon::now();
        $users = User::withTrashed()->selectRaw("users.id,
        users.first_name,
        users.last_name,
        users.started_at,
        users.signed_at,
        users.ended_at,
        users.deleted_at")->whereRaw("( users.deleted_at IS NULL OR users.ended_at IS NOT NULL and (Year(users.ended_at) = '. $toMonth->year .')) ")->orderByRaw("IF
        ( FIND_IN_SET( users.id, '1,3,10,2,12,13,16,22,15,8,5,30,9,18,68,33,69,40,43,45,48,49,50,51,67,52,59,57,56,55,61,63,62,64,65' )= 0, 1, 0 ),
        FIND_IN_SET( users.id, '1,3,10,2,12,13,16,22,15,8,5,30,9,18,68,33,69,40,43,45,48,49,50,51,67,52,59,57,56,55,61,63,62,64,65' ),
        users.id")->get();

        $hours = UserLeaveDayLog::select(DB::raw('
            SUM(hours) as hours,
            user_leave_days_logs.user_id,
            MONTH(user_leave_days_logs.leave_day) AS month
            '))
            ->join('users', 'users.id', 'user_leave_days_logs.user_id')
        ->whereRaw('leave_day>="'. $toMonth->year .'-01-01"')
        ->whereRaw('leave_day<="'. $toMonth->endOfMonth() .'"')
            ->whereRaw('leave_day >= DATE(users.signed_at)')
            ->whereRaw('DAYOFWEEK(Date(leave_day)) NOT IN (1, 7)')
            ->groupBy('user_leave_days_logs.user_id')
            // ->groupByRaw('YEAR(user_leave_days_logs.leave_day)')
            ->groupByRaw('MONTH(user_leave_days_logs.leave_day)')
            ->get()->toArray();
        // dd($hours);

        $leaveDays = [];
        foreach ($hours as $leaveDay) {
            $leaveDays[$leaveDay['user_id']][$leaveDay['month']] = $leaveDay['hours'];
            if (!isset($leaveDays[$leaveDay['user_id']]['total'])) {
                $leaveDays[$leaveDay['user_id']]['total'] = $leaveDay['hours'];
            } else {
                $leaveDays[$leaveDay['user_id']]['total'] += $leaveDay['hours'];
            }
        }
        // dd($leaveDays);

        $timeKeepingManager = new TimekeepingManager();
        $workingTimeInMonths = [];
        foreach($users as &$user){
            $workingTimeInMonths[$user->id] = [];
            if($user->signed_at){
                $attendances = Attendance::select(
                    'attendances.user_id',
                    DB::raw('MAX(YEAR(checked_at)) as year'),
                    DB::raw('MAX(MONTH(checked_at)) as month'),
                    DB::raw('DATE(checked_at) as date'),
                    DB::raw('TIME(MIN(checked_at)) as checked_in'),
                    DB::raw('TIME(MAX(checked_at)) as checked_out'),
                )

                    ->whereRaw('YEAR(checked_at) = ' . $toMonth->year)
                    ->whereRaw('MONTH(checked_at) <= ' . $toMonth->month)
                ->whereRaw("Date(checked_at)>='".$user->signed_at."'")
                    ->where('attendances.user_id', $user->id)
                    ->whereRaw('DAYOFWEEK(Date(checked_at)) NOT IN (1, 7)')
                    ->groupBy(DB::raw('attendances.user_id, DATE(checked_at)'))
                    ->orderBy(DB::raw('attendances.user_id, DATE(checked_at)'))
                    ->get();
            }else{
                $attendances = [];
            }
            if ($user->id == 2) {
                // dd($attendances[100]);
            }

            $user->time = 0;

            // $user->time += isset($hours[$user->id])? $hours[$user->id]['hours'] : 0;

            foreach($attendances as $attendance){
                $duration = $timeKeepingManager->calcWorkingDuration($attendance, true);
                $user->time += $duration['duration'];
                if (!isset($workingTimeInMonths[$user->id][$attendance->month])) {
                    $workingTimeInMonths[$user->id][$attendance->month] = $duration['duration'];
                } else {
                    $workingTimeInMonths[$user->id][$attendance->month] += $duration['duration'];
                }
            }
            // if ($user->id == 2) {
            //     dd($workingTimeInMonths[$user->id]);
            // }
            $total = 0;
            foreach ($workingTimeInMonths[$user->id] as $month => $duration) {
                if (isset($leaveDays[$user->id][$month])) {
                    $workingTimeInMonths[$user->id][$month] = $duration + $leaveDays[$user->id][$month];
                }
                $total += $workingTimeInMonths[$user->id][$month];
            }
            $workingTimeInMonths[$user->id]['total'] = $total;
            
        }
        dd($workingTimeInMonths);
        return view('admin.timekeeping.hour_work', ['users' => $users]);
    }
}