<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Author: BaoDV
 * Author Email: <EMAIL>
 *
 * Created on March, 3/31/2025, by vanba
 */
class UpdateLanguageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            "language" => "required|exists:languages,id",
        ];
    }

    public function messages(): array
    {
        return [
            "language.required" => __('message.request.input_required', ['attribute' => __('validation.attributes.language')]),
            "language.exists" => __('message.request.input_exists', ['attribute' => __('validation.attributes.language')]),
        ];
    }
}