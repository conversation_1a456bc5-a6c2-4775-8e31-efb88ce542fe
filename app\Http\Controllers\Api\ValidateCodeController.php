<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\ValidateCodeRequest;
use App\Models\Tenant\Hostname;
use Illuminate\Http\Response;

class ValidateCodeController extends Controller
{
    /**
     * Get avatar user
     * @param $id
     * @return mixed
     */
    public function validateCode(ValidateCodeRequest $request)
    {
        $hostname = Hostname::query()
            ->select([
                'hostnames.website_id',
                'companyprofiles.name',
            ])
            ->join('companyprofiles', 'hostnames.website_id', '=', 'companyprofiles.website_id')
            ->where('fqdn',$request->code.'.'.env('APP_URL_BASE'))
            ->first();
        if(!empty($hostname)){
            return response()->json([
                'code' => Response::HTTP_OK,
                'message' => trans('message.success_api'),
                'data' => [
                    'website_id' => $hostname->website_id,
                    'company_name' => $hostname->name
                ]
            ], Response::HTTP_OK);
        }
        
        return response()->json([
            'code' => Response::HTTP_NOT_FOUND,
            'message' => trans('message.code_not_exist')
        ],Response::HTTP_BAD_REQUEST);
    }
}
