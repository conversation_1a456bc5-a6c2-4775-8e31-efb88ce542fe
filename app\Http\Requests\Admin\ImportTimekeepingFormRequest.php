<?php
namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ImportTimekeepingFormRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @param Illuminate\Http\Request $request
     * @return array
     */
    public function rules() {
        $rules = [
            'file' => 'required|file|mimes:csv,txt',
        ];
        return $rules;
    }


}
