<?php

namespace App\Http\Requests;

use App\Models\Project;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Resource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class EventRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $projects = Project::select('projects.id')->checkUserPermission(Auth::id())->groupBy('projects.id')->get()->pluck('id')->toArray();
        $rules = [
            'name' => 'required|max:1000',
            'started_at' =>['required','date_format:d/m/Y H:i'],
            'ended_at' => 'nullable|date_format:d/m/Y H:i|after_or_equal:started_at',
            'date_limit' => 'nullable|date_format:d/m/Y',
            'project_id' =>'nullable|in:'.implode(',',$projects),
            'location' => ['nullable',
                            Rule::exists('resources', 'id')
                            ->where('type', Resource::TYPE_MEETING_ROOM),
                        ],
            'repeat' => 'required|numeric|max:255|in:0,1,2,3,4',
            'type' => 'nullable|numeric|max:255|in:0',
        ];
        if(Route::is('event.store')){
            $rules['started_at'][] = 'after_or_equal:'.now()->format('d/m/Y H:i');
        }

        return $rules;
    }
    public function attributes()
    {
        return [
            'name' => trans('validation.attributes.name_meeting'),
        ];
    }
}
