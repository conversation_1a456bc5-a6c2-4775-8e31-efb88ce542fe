<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;

class MaxNormalizedLength implements Rule
{
    /**
     * Maximum allowed length after normalization
     *
     * @var int
     */
    protected $maxLength;

    /**
     * Create a new rule instance.
     *
     * @param int $maxLength
     * @return void
     */
    public function __construct(int $maxLength)
    {
        $this->maxLength = $maxLength;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value) : bool
    {
        if ($value === null) {
            return true;
        }

        // Normalize line breaks: convert \r\n or \r to a single \n character
        $normalizedText = str_replace("\r\n", "\n", $value);
        $normalizedText = str_replace("\r", "\n", $normalizedText);

        // Count the length after normalization
        $normalizedLength = mb_strlen($normalizedText);

        // Check if the normalized length is within the limit
        return $normalizedLength <= $this->maxLength;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message() : string
    {
        return __('validation.max.string', ['max' => $this->maxLength]);
    }
}
