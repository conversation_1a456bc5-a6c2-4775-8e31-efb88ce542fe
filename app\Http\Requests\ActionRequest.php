<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Request as ModelsRequest;

class ActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rule = [];
        if($this->status == ModelsRequest::STATUS_REFUSE){
            $rule['log'] = ['required'];
        }
        return $rule;
    }


    /**
     * @return array|string[]
     */
    public function messages(): array
    {
        return [
            'log.required' => trans('language.reason_placeholder'),
        ];
    }
}
