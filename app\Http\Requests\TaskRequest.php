<?php

namespace App\Http\Requests;

use App\Logics\DateFormatManager;
use App\Logics\ProjectManager;
use App\Models\TaskStatus;
use App\Logics\TaskManager;
use App\Models\ProjectTask;
use Illuminate\Http\Request;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use App\Models\TaskType;

class TaskRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(Request $request)
    {
        $taskChild = 0;
        if (isset($request->id)){
            $taskChild = (new TaskManager())->getNumberChild($request->id);
        }
        $rules = [
            'project_id' => 'required|exists:projects,id',
            'name' => 'required|max:1000',
            'description' => 'nullable',
            'status' => 'required|exists:task_status,id',
            'parent_task' => [
                'nullable',
                Rule::exists('project_tasks','id')->where('project_id',$request->project_id)
            ] ,
            'priority' => 'required|exists:task_priorities,id',
            'started_at' => 'nullable|date_format:d/m/Y',
            'ended_at' => 'nullable|date_format:d/m/Y|after_or_equal:started_at',
            'user_id' => 'nullable|exists:users,id',
            'estimated_time' => [
                'nullable',
                'numeric',
                'min:0',
                Rule::requiredIf(function () use ($request,$taskChild){
                    return ($request->progress >0 || $request->status == TaskStatus::TASK_STATUS_CLOSE || $request->started_at != null || $request->ended_at != null) && $taskChild == 0;
                })
            ],
            'progress' => 'nullable',
            'description.*' => 'max:500',
            'score' => 'nullable|integer|between:0,10',
            'sprint_id' => 'nullable|exists:project_sprints,id'
        ];

        if((new ProjectManager())->UserHasGroupRolePMLeader(Auth::id(), $request->project_id)){
            $rules['type'] = 'required|exists:task_types,id';
        }else{
            $rules['type'] = [
                'required',
                Rule::exists('task_types','id')->whereNot('id',TaskType::PROBLEM)
            ];
        }
        if(isset($request->parent_task)){
            $projectTask = (new TaskManager())->checkLimitParent($request->parent_task);
            $dateFormat = new DateFormatManager();
            if(isset($projectTask) && $projectTask->limit){
                if(isset($request->started_at)){
                    if(isset($projectTask->started_at)){
                        $rules['started_at'] .= '|after_or_equal:'. $dateFormat->dateFormatLanguage($projectTask->started_at,'d/m/Y');
                    }
                    if(isset($projectTask->ended_at)){
                        $rules['started_at'] .= '|before_or_equal:'.$dateFormat->dateFormatLanguage($projectTask->ended_at,'d/m/Y');
                    }
                }
                if(isset($request->ended_at)){
                    if(isset($projectTask->started_at)){
                        $rules['ended_at'] .= '|after_or_equal:'. $dateFormat->dateFormatLanguage($projectTask->started_at,'d/m/Y');
                    }
                    if(isset($projectTask->ended_at)){
                        $rules['ended_at'] .= '|before_or_equal:'.$dateFormat->dateFormatLanguage($projectTask->ended_at,'d/m/Y');
                    }
                }
            }
        }
        if(isset($request->limit)&&$request->id){
            $taskChild = ProjectTask::selectRaw('min(started_at) as min_time,max(ended_at) as max_time')
                ->where('parent_task',$request->id)
                ->first();
            if (!empty($taskChild->min_time)){
                $rules['started_at'] .= '|before_or_equal:'.date('d/m/Y', strtotime($taskChild->min_time));
            }
            if (!empty($taskChild->max_time)){
                $rules['ended_at'] .= '|after_or_equal:'.date('d/m/Y', strtotime($taskChild->max_time));
            }
        }
            
        if($request->type == TaskType::BUG){
            $rules['function_screen'] = 'required';
            $rules['bug_classify_id'] = 'required';
            $rules['bug_range_id'] = 'required';
            $rules['bug_severity_id'] = 'required';
            $rules['bug_tag_id'] = 'max:191';
            
            if($request->progress == TaskType::PROGRESS_BUG || $request->status == TaskStatus::TASK_STATUS_RESOLVED || $request->status == TaskStatus::TASK_STATUS_CLOSE){
                $rules['reasons'] = 'required';
                $rules['fix'] = 'required';
                $rules['range'] = 'required';
            }
        }
        return $rules;
    }
}
