<?php

namespace App\Logics;

use App\Models\SiteSetting;

class ModuleManager
{
    public static function getUsedModules() {
        $usedModulesSetting = SiteSetting::select('value')->find(SiteSetting::USED_MODULES);
        if (!isset($usedModulesSetting) || empty($usedModulesSetting->value)) {
            return [];
        }
        $usedModules = json_decode($usedModulesSetting->value, true);

        return $usedModules;
    }
}
