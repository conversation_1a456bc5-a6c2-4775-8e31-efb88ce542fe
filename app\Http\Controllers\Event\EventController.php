<?php

namespace App\Http\Controllers\Event;

use App\Http\Controllers\Controller;
use App\Http\Requests\EventRequest;
use App\Logics\EventManager;
use App\Models\Event;
use App\Models\EventParticipant;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EventController extends Controller
{
    public function store(EventRequest $request){
        DB::beginTransaction();
        $urlPrev = url()->previous();
        try {
            $eventManager = new EventManager();
            $event = $eventManager->createEvent($request);
            DB::commit();
            return redirect($urlPrev)->with('status_succeed', trans('message.create_event_succeed'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect($urlPrev)->with('status_failed', trans('message.server_error'));
        }
    }

    /**
     * Update the specified resource.
     *
     * @param \App\Http\Requests\Admin\EventRequest $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(EventRequest $request,$id){
        DB::beginTransaction();
        $urlPrev = url()->previous();
        $event = Event::select('events.*')->CheckUserPermission(Auth::id())->find($id);
        if($event == null){
            return redirect($urlPrev)->with('status_failed', trans('message.event_not_exist'));
        }
        try {
            $eventManager = new EventManager();
            $event = $eventManager->updateEvent($event,$request);
            DB::commit();
            return redirect($urlPrev)->with('status_succeed', trans('message.update_event_succeed'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect($urlPrev)->with('status_failed', trans('message.server_error'));
        }
    }

    public function destroy($id){
        DB::beginTransaction();
        try {
            $event = Event::select('events.*')->CheckUserPermission(Auth::id())->find($id);
            if($event == null){
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    'msg' => [
                        'title' => trans('message.event_not_exist') ,
                    ],
                ];
            }
            $eventManager = new EventManager();
            $event = $eventManager->deleteEvent($event);

            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_event_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }
}
