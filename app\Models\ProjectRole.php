<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class ProjectRole
 * @property integer $id
 * @property string $name
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */

class ProjectRole extends Model
{
    use SoftDeletes;

    protected $table = 'project_roles';
    public const ProjectManager = 1;
    public const Developer = 2;
    public const Reporter = 3;
    public const QA = 4;
    public const Designer = 5;
    public const SaleExecutive = 6;

    /**
     * Get the user who has the role
     * @return \Illuminate\Database\Eloquent\Relations\belongsToMany
     */
    public function users()
    {
        $this->belongsToMany('App\User','project_members','role_id','user_id');
    }

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
        self::saving (function ($data) {
            $data->updated_by = auth()->id();
        });
    }
}
