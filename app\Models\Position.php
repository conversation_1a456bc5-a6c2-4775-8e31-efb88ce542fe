<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class District
 * @property string $id
 * @property string $name
 * @property string $type
 * @property string $prefecture_id
 */
class Position extends Model
{
    use SoftDeletes;
    protected $table = 'positions';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [

    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [

    ];

    /**
     * Get department for the position
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function department()
    {
    	return $this->belongsTo('App\Models\Department');
    }

    /**
     * Get users for the position
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function users()
    {
        return $this->hasMany('App\User');
    }
}
