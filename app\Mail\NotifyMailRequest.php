<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class NotifyMailRequest extends BaseMail
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */

    protected $number;
    protected $user;
    public function __construct($number, $user)
    {
        $language_id = isset($user->language_id) ? $user->language_id : null;  
        parent::__construct($language_id);
       $this->number = $number;
       $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
        ->subject(trans('language.mail_get_request.subject'))
        ->view('mail.mail_notify_request', ['number' => $this->number, 'role' => isset($this->user->roles->first()->name)?$this->user->roles->first()->name : '<PERSON><PERSON><PERSON> Viên']);
    }
}
