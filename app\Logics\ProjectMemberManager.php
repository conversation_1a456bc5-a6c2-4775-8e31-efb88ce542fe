<?php

namespace App\Logics;

use App\User;
use Illuminate\Support\Facades\DB;

class ProjectMemberManager
{

    const PAGE_SIZE_MEMBER = 12;
    /**
     * Get list
     * @param array $params
     */
    public function getList($projectId,$params,$pageSize=self::PAGE_SIZE_MEMBER) {
        // Get list members
        $columns = [
            'project_members.user_id',
            'users.first_name',
            'users.last_name',
            'users.email',
            'users.avatar',
            'phone',
            DB::raw('group_concat(project_roles.name SEPARATOR "'.GROUP_CONCAT_SEPARATOR.'") as role_name'),
            DB::raw('group_concat(project_roles.id) as role_id'),
            DB::raw('min(project_roles.group_role_id) as role'),
            DB::raw('min(project_members.created_at) as created_at'),
        ];
        $members = User::select($columns)
            ->distinct()
            ->leftJoin('project_members','users.id','project_members.user_id')
            ->leftJoin('project_roles','project_roles.id','project_members.role_id')
            ->where('project_members.project_id',$projectId)
            ->groupby('project_members.user_id');
        if(isset($params['roleId']) && is_array($params['roleId'])){
            $members = $members->whereHas('role', function ($query) use ($params,$projectId) {
                $query->whereIn('role_id', $params['roleId'])
                    ->where('project_id', $projectId);
            });
        }
        if ( isset($params['member_id']) && is_array($params['member_id'])){
            $members = $members->whereIn('users.id', $params['member_id']);
        }
        // Pagination
        $members = $members->orderBy('role', 'asc')
            ->orderBy('users.id', 'asc')
            ->paginate($pageSize);
        return $members;
    }

}
