<?php

namespace App\Http\Middleware;

use \Illuminate\Http\Response as Res;
use App\Http\Requests\CheckVersionRequest;
use App\Services\AppVersionService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response;

class AppVersionMiddleware
{
    protected $appVersionService;

    public function __construct(AppVersionService $appVersionService)
    {
        $this->appVersionService = $appVersionService;
    }

    public function handle(Request $request, Closure $next): Response
    {
        // Only operate when USE_TENANT=true
        if (!env('USE_TENANT', false)) {
            return $next($request);
        }

        // Get device and version from headers
        $device = $request->headers->get('device');
        $version = $request->headers->get('version');

        // If no headers provided → pass through
        if (!$device || !$version) {
            return $next($request);
        }

        // If headers invalid → pass through
        $checkVersionRequest = new CheckVersionRequest();
        $validator = Validator::make([
            'device' => $device,
            'version' => $version
        ], $checkVersionRequest->rules(), $checkVersionRequest->messages());

        if ($validator->fails()) {
            return $next($request);
        }

        // Call AppVersionService::checkVersion() to get full information
        $versionData = $this->appVersionService->checkVersion((int)$device, $version);

        // If force update required → return HTTP 426 with headers and response data
        if ($versionData['force_update']) {
            $data = [
                'mode' => $versionData['mode'],
                'force_update' => $versionData['force_update'],
                'description' => $versionData['description']
            ];

            $response = response()->json([
                'code' => Res::HTTP_UPGRADE_REQUIRED,
                'message' => __('message.version_update_required'),
                'data' => $data
            ], Res::HTTP_UPGRADE_REQUIRED);
            return $response;
        }

        // If no force update needed → pass through
        return $next($request);
    }
}
