<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\MessageReactionRequest;
use App\Logics\MessageReactionManager;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use \Illuminate\Http\Response;

class MessageReactionController extends AbstractApiController
{
    /**
     * @var MessageReactionManager
     */
    protected $messageReactionManager;

    public function __construct(MessageReactionManager $messageReactionManager)
    {
        $this->messageReactionManager = $messageReactionManager;
    }
    
    /**
     * Create message in conversation by User
     * @param $conversationId
     * @param MessageStoreRequest $request
     * @return json
     * @throws Exception
     */

    public function store(MessageReactionRequest $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $data = $this->messageReactionManager->store($userId, $request);
            if (isset($data['status_error'])) {
                $msg = __('message.message.reaction.fail');
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
                return $this->respondBadRequest($msg);
            }
            $msg =  __('message.message.reaction.success');
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[MessageController][storeMessageOfUser] error " . $e->getMessage());
            throw new Exception('[MessageController][storeMessageOfUser] error ' . $e->getMessage());
        }
    }
}
