<?php

namespace App\Console\Commands;

use App\Helpers\StringHelper;
use App\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RefactorNameSearchUser extends Command
{
    protected $signature = 'db:replace-users-name-search-column';
    protected $description = 'Replace value column name_search on users table.';

    public function handle()
    {
        $this->info('Processing...');
        try {
            $helper = new StringHelper;
            $users = User::withTrashed()->get();
            DB::beginTransaction();
            $users->each(function ($item) use ($helper) {
                $fullname = "{$item->first_name} {$item->last_name}";
                try {
                    $item->name_search = $helper->transformSearchFullname($fullname);
                    $item->save();
                } catch (Exception $e) {
                    $item->name_search = $helper->transformSearchFullname(normalizer_normalize($fullname));
                    $item->save();
                }
                $this->info('-- Modified user: ' . $fullname);
            });
            DB::commit();

            $this->info("\n\n Done!");
        } catch (Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
        }
    }
}
