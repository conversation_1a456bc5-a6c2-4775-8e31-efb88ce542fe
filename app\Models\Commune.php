<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class Commune
 * @property string $id
 * @property string $name
 * @property string $type
 */
class Commune extends Model
{
    public $timestamps = false;

    protected $table = 'communes';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [

    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'string',
    ];

    /**
     * Get users for the prefecture
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function users()
    {
    	return $this->hasMany('App\User');
    }
}
