<?php

namespace App\Http\Middleware;

use App\Models\Tenant\Devices;
use Closure;
use Illuminate\Http\Response;

class ApiDevices
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $key = getallheaders()['key'];
        //check header key with device id
        $domain = $request->getHost();
        $deviceId = Devices::join('hostnames','hostnames.website_id','devices.website_id')->where('hostnames.fqdn',$domain)->where('devices.device_id', $key)->first();
        if(empty($deviceId)){
            return response()->json([
                'code' => Response::HTTP_FORBIDDEN,
                'message' => "Forbidden!"
            ], Response::HTTP_FORBIDDEN);
        }
        return $next($request);
    }
}
