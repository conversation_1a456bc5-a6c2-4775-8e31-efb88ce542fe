<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;


class CreateUserMail extends BaseMail
{
    use Queueable, SerializesModels;

    protected $user;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user)
    {
        $language_id = isset($user['language_id']) ? $user['language_id'] : null;
        parent::__construct($language_id);
        $this->user = $user;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
            ->subject(trans('language.mail_create_user_subject'))
            ->view('mail.mail_create_user', ['user' => $this->user]);
    }
}
