<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ZipHelper;
use App\Traits\StorageTrait;
use App\Models\Tenant\Devices;
use Exception;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\AttendanceRequest;
use App\Http\Requests\TimeKeepingRequest;
use App\Logics\TimekeepingManager;
use App\Logics\UserManager;
use App\Models\Attendance;
use App\Models\Role;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TimekeepingController extends AbstractApiController
{
    use StorageTrait;

    protected $timekeepingManager;
    protected $userManager;

    public function __construct(
        TimekeepingManager $timekeepingManager,
        UserManager $userManager
    ) {
        $this->timekeepingManager = $timekeepingManager;
        $this->userManager = $userManager;
    }

    /**
     * Api Import timekeeping
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function uploadAttendance(AttendanceRequest $request)
    {
        DB::beginTransaction();
        try{
            // save file to storage
            if ($request->hasFile('checked_image_file')) {
                $file = $request->file('checked_image_file');
                $checkedAt = Carbon::createFromFormat('d/m/Y H:i:s', $request->checked_at);
                $folder = CHECKED_IMAGE_DIR. '/' . $checkedAt->format('Y_m_d') . '/' . $request->user_id;
                $imagePath = $this->uploadFileByStream($file, $folder);
            }

            // insert new attendance
            $attendance = new Attendance();
            $attendance->user_id = $request->user_id;
            $attendance->checked_at = Carbon::createFromFormat('d/m/Y H:i:s', $request->checked_at);
            $attendance->checked_type = $request->checked_type;
            $attendance->checked_image = $imagePath ?? '';
            $attendance->save();

            DB::commit();
            return response()->json([
                'code' => Response::HTTP_OK,
                'message' => "Success!"
            ], Response::HTTP_OK);

        } catch (\Exception $e){
            Log::error($e);
            DB::rollBack();
            return response()->json([
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Api download all attendances on yesterday
     * @return JsonResponse|BinaryFileResponse
     */
    public function downloadAttendance()
    {
        try{
            $yesterday = Carbon::now()->subDay()->format('Y_m_d');
            $inputFolder = CHECKED_IMAGE_DIR . '/' . $yesterday;
            $outputFilePath = TEMP_DIR . '/checked_image_logs.zip';

            // Check folder save file zip exists
            if (!Storage::disk(FILESYSTEM)->exists(TEMP_DIR)) {
                $this->makeDirectory(TEMP_DIR);
            }

            $zipHelper = new ZipHelper();
            $result = $zipHelper->zip($inputFolder, $outputFilePath);

            if (!$result) {
                return response()->json([
                    'code' => Response::HTTP_NOT_FOUND,
                    'message' => 'Has not checked images on yesterday'
                ], Response::HTTP_NOT_FOUND);
            }

            return Storage::disk(FILESYSTEM)->download($outputFilePath);

        } catch (\Exception $e){
            Log::error($e);
            return response()->json([
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ]);
        }
    }

    /**
     * update device by device_id
     * @return JsonResponse
     */
    public function deviceHealthCheck()
    {
        try {
            $key = getallheaders();
            if (!empty($key['key'])) {
                $deviceId = $key['key'];
                $device = Devices::where('device_id', $deviceId)->first();
                if (!empty($device)) {
                    DB::beginTransaction();
                    $device->update([
                        'status' => Devices::STATUS_ONLINE,
                        'last_connection' => Carbon::now()
                    ]);
                    DB::commit();
                    return response()->json([
                        'code' => Response::HTTP_OK,
                        'message' => 'Success!'
                    ], Response::HTTP_OK);
                }
            }
            return response()->json([
                'code' => Response::HTTP_NOT_FOUND,
                'message' => 'Device not found'
            ], Response::HTTP_NOT_FOUND);
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get user time sheet
     */
    public function getUserTimeSheet(Request $request) {
        // If current user id is admin then can get time sheet of another user, 
        // else default get time sheet of current user
        $userId = $request->user_id;
        if (!($userId && Auth::user()->hasRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]))) {
            $userId = Auth::user()->id;
        }

        $timeSheetData = $this->timekeepingManager->getUserTimeSheet($request->month, $userId);
        $data = [];
        // Get timekeeping in month
        foreach($timeSheetData[0] as $timekeepingDay) {
            $data[$timekeepingDay['date']] =  $timekeepingDay;
        }
        // Get holiday in month
        foreach($timeSheetData[1] as $holiday) {
            $days = explode(', ', $holiday['choose_holidays']);
            foreach($days as $day) {
                $data[$day]['date'] =  $day;
                $data[$day]['holiday'] =  $holiday['holiday_name'];
            }
        }
        // Get vacation request in month
        foreach($timeSheetData[2] as $vacationRequest) {
            foreach ($vacationRequest as $day => $requestHours) {
                if ($requestHours == 8) {
                    $data[$day]['date'] =  $day;
                    $data[$day]['vacation_request'] =  true;
                }
            }
        }
        // Sort by day desc
        krsort($data);

        // Convert map to array
        $data = array_values($data);

        // Get remaining leave hours of user
        $user = $this->userManager->getUserWithLeaveDay($userId, false)->first();
        $userRemainingLeaveHours = isset($user->userRemainingLeaveDay[0]->hours) ? $user->userRemainingLeaveDay[0]->hours : 0;

        $result = [
            "timesheet" => $data,
            "remaining_leave_hours" => $userRemainingLeaveHours
        ];

        return $this->renderJsonResponse($result, __('language.success'));
    }

    /**
     * Add a record of user attendance via GPS
     *
     * This API is used by mobile app to add a record of user attendance via GPS.
     * It will check if the user has already checked in or out today before adding a new record.
     * If the user has already checked in or out today, it will update the record.
     * If the user has not checked in or out today, it will add a new record.
     *
     * @param TimeKeepingRequest $request Request object containing attendance data.
     * @return JsonResponse JSON response with the attendance check-in time and location, or an error message if the process fails.
     */
    public function createTimekeeping(TimeKeepingRequest $request)
    {
        $userId = Auth::user()->id;
        $result = $this->timekeepingManager->createTimekeeping($userId, $request);
        return $this->renderJsonResponse($result, __('language.success'));
    }

    /**
     * Retrieve the list of timekeeping locations.
     *
     * @param Request $request The incoming HTTP request.
     * @return \Illuminate\Http\JsonResponse JSON response containing the list of locations.
     */
    public function getListLocation(Request $request)
    {
        $result = $this->timekeepingManager->getListLocation($request);
        return $this->renderJsonResponse($result, __('language.success'));
    }

    /**
     * api update timekeeping info from device.
     *
     * @param Request $request
     * @return json response
     * @throws Exception
     */
    public function updateTimekeepingByDevice(Request $request)
    {
        $userId = $request['info']['CustomizeID'] ?? '';
        $result = $this->timekeepingManager->createTimekeeping($userId, $request);
        return $this->renderJsonResponse($result, __('language.success'));
    }
}
