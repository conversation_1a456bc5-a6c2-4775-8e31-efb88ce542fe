<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\RequireRequest;
use App\Logics\RequestManager;
use Illuminate\Http\Response;

class RequestController extends Controller
{
    protected $requestManager;
     
    public function __construct(RequestManager $requestManager)
    {
        $this->requestManager = $requestManager;
    }

    /**
     * Store request
     */
    public function store(RequireRequest $request)
    {
        $this->requestManager->store($request);
        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('message.success')
        ], Response::HTTP_OK);
    }
}
