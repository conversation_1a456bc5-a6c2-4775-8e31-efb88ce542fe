<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Contacts extends Model
{
    //Status Sended Contact
    const STATUS_SENDED = 0;
    //Status Accept contact
    const STATUS_ACCEPTED = 1;
    //Status Reject contact
    const STATUS_REJECTED = 2;
    const TYPE_EMAIL = 1;
    const TYPE_PHONE = 2;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'contacts';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id', 'receiver_user_id', 'status'];

    /**
     * Get the user that owns the contact.
     * 
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo('\App\Models\Contacts');
    }
}
