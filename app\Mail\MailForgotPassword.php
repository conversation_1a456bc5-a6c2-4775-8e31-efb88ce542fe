<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;

class MailForgotPassword extends BaseMail
{
    use Queueable, SerializesModels;

    protected $code;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($code)
    {
        $this->code = $code;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
            ->subject(__('language.mail_forgot_password.subject'))
            ->view('mail.mail_forgot_password', ['code' => $this->code]);
    }
}
