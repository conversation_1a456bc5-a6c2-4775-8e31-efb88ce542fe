<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class ApiLanguageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $excludedRouteNames = $this->excludedRouteNames();
        if (in_array(optional($request->route())->getName(), $excludedRouteNames)) {
            $user = Auth::guard('api')->user();

            $languageId  = optional($user)->language_id
                ? $user->language_id
                : $request->header('language', config('app.locale'));
            $locale = Language::where('id', $languageId)->value('code');
            if ($locale) {
                App::setLocale($locale);
                session()->put('locale', $locale);
            }
        } else {
            App::setLocale('en');
        }
        
        return $next($request);
    }


    /**
     * List route excluded.
     * 
     * @return string[]
     */
    private function excludedRouteNames(): array
    {
        return [
            'api.v1.login',
            'api.v1.logout',
            'api.v1.timesheet',
            'api.v1.timekeeping',
            'api.v1.request',
            'api.v1.language',
            'api.v1.users',
            'api.v1.user.show',
            'api.v1.user.update',
            'api.v1.user.config.list',
            'api.v1.user.config.update',
            'api.v1.user.change.password',
            'api.v1.user.password.forgot',
            'api.v1.user.password.verify',
            'api.v1.user.password.reset'
        ];
    }
}
