<?php

namespace App\Exports\Asset;

use App\Models\Department;
use App\Models\PropertyType;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class AssetReportExport
{
    public function exportInventoryReport($dataAsset, $assetTypeId, $departmentId)
    {
        $template = storage_path('template') . '/' . TEMPLATE_EXPORT_ASSET;
        $key = 9;
        $rowLast = $key + $dataAsset->count() - 1;
        $department = Department::where('id', $departmentId)->first();
        $now = Carbon::now();

        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
        $spreadsheet = $reader->load($template);
        $spreadsheet->setActiveSheetIndex(0);
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('C2', isset($department) ? $department->name : "");

        $cellVal = $sheet->getCell('O11')->getValue();
        $text = str_replace('dd', $now->format('d'), $cellVal);
        $text = str_replace('mm', $now->format('m'), $text);
        $text = str_replace('yyyy', $now->format('Y'), $text);
        $sheet->setCellValue('O11', $text);

        if (isset($assetTypeId)) {
            $assetType = PropertyType::where('id', $assetTypeId)->first();
            $assetTypeName = isset($assetType) ? $assetType->name : "";
            $sheet->setCellValue('A3', trans('language.title_template_export_asset') . ' ' . mb_strtoupper($assetTypeName));
        }
        if (env('USE_TENANT', false)) {
            $webId = app(\Hyn\Tenancy\Environment::class)->website()->id;
            $companyName = \App\Models\Tenant\CompanyProfile::where('website_id', $webId)->first()->name;
            $sheet->setCellValue('C1', $companyName);
        } else {
            $sheet->setCellValue('C1', env('COMPANY_NAME', ''));
        }

        $sheet->insertNewRowBefore(10, $dataAsset->count() - 1);
        foreach ($dataAsset as $i => $value) {
            $sheet->setCellValue('A' . ($key + $i), $i + 1);
            $sheet->setCellValue('B' . ($key + $i), $value->asset_code);
            $sheet->setCellValue('C' . ($key + $i), $value->name);
            $sheet->setCellValue('D' . ($key + $i), $value->country_of_manufacture);
            $sheet->setCellValue('E' . ($key + $i), Carbon::parse($value->usage_date)->format('Y'));
            $sheet->setCellValue('F' . ($key + $i), $value->description);
            $sheet->setCellValue('G' . ($key + $i), $value->seri_number);
            $sheet->setCellValue('H' . ($key + $i), $value->asset_category);
            $sheet->setCellValue('I' . ($key + $i), $value->manufacturer);
            $sheet->setCellValue('J' . ($key + $i), $value->source_of_origin);
            $sheet->setCellValue('K' . ($key + $i), 1);
            $sheet->setCellValue('L' . ($key + $i), $value->original_price);
            $sheet->setCellValue('M' . ($key + $i), $value->residual_value);
        }
        $sheet->setCellValue('K8', '=SUM(K9:K'. $rowLast .')');
        $sheet->setCellValue('L8', '=SUM(L9:L'. $rowLast .')');
        $sheet->setCellValue('M8', '=SUM(M9:M'. $rowLast .')');

        $writer = new Xlsx($spreadsheet);
        $date = $now->format('dmY');
        $fileName = trans('language.file_name_export_asset', ['date' => $date]);
        $filePath = storage_path('template') . '/' . $fileName;
        $writer->save($filePath);
        return [$filePath, $fileName];
    }
}
