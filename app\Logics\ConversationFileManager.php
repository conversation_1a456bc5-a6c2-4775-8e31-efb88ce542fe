<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use App\Models\ConversationFile;
use App\Models\Message;
use Embed\Exceptions\InvalidUrlException;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Embed\Embed;

class ConversationFileManager
{
    public static function store($files = [], $messageId)
    {
        if (empty($files)) {
            return false;
        }
        try {
            $data = [];
            $arrInsertConversationFileIds = [];
            foreach ($files as $file) {
                Log::info($file);
                $dataInsert = [
                    'message_id' => $messageId,
                    'name' => isset($file['name']) ? $file['name'] : null,
                    'thumbnail' => isset($file['thumbnail']) ? $file['thumbnail'] : null,
                    'path' => isset($file['file_path']) ? $file['file_path'] : null,
                    'size' => isset($file['size']) ? $file['size'] : null,
                    'type' => isset($file['type']) ? $file['type'] : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
                //Add the value about to be added to the conversation_files table to $data to save the log
                $data[] = $dataInsert;
                //Add the record to the conversation_files table and get back the id of the newly added record
                 $arrInsertConversationFileIds[] = ConversationFile::query()->insertGetId($dataInsert);
            }
            Log::info($data);
            Log::info($messageId);
            //Handle updates to content_files of message
            $message = Message::query()->find($messageId);
            if (!empty($message->content_file)) {
                $contentFiles = json_decode($message->content_file, true);
                //In the content_file of the message, set the id of the file and set is_delete = false
                foreach ($arrInsertConversationFileIds as $key => $fileId) {
                    if (isset($contentFiles[$key]) && empty($contentFiles[$key]['id'])) {
                        $contentFiles[$key]['id'] = $fileId;
                        $contentFiles[$key]['is_delete'] = false;
                    } else {
                        $contentFiles[] = [
                            'file_path' => $data[$key]['path'],
                            'name' => $data[$key]['name'],
                            'type' => $data[$key]['type'],
                            'size' =>  $data[$key]['size'],
                            'id' => $fileId,
                            'is_delete' => false
                        ];
                    }
                }
                //Update content_file of the message
                DB::table('messages')->where('id', $messageId)->update([
                    'content_file' => json_encode($contentFiles)
                ]);
            }
            return true;
        } catch (Exception $e) {
            Log::error('[ConversationFileManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
            throw new Exception('[ConversationFileManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * get link from message text
     * @param string $message
     * @param int    $messageId
     * @throws Exception
     */
    public static function getLinkInMessage($message, $messageId){
        try {
            $listLink = StringHelper::extractUrls($message);
            $data = [];
            foreach ($listLink as $value) {
                try {
                    $info = Embed::create($value);
                    $title = $info->title;
                    if($title == "file_get_contents returns 403 forbidden"){
                        $title = parse_url($value)['host'];
                    }
                    $data[] = [
                        "name"=> $title,
                        "thumbnail" => $info->image,
                        "file_path" => $value,
                        "type"=> ConversationFile::TYPE_LINK
                    ];
                } catch (InvalidUrlException $e) {
                    Log::debug('[ConversationFileManager][getLinkInMessage] message ID: ' .$messageId . ' - ' . $e->getMessage());
                    $data[] = [
                        "name"=> $value,
                        "thumbnail" => null,
                        "file_path" => $value,
                        "type"=> ConversationFile::TYPE_LINK
                    ];
                }
            };
            self::store($data, $messageId);
        } catch (Exception $e) {
            Log::error('[ConversationFileManager][getLinkInMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
            throw new Exception('[ConversationFileManager][getLinkInMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }
    /**
     * delete ConversationFile by messageIds
     * @param array $messageIds
     * @return void
     */
    public static function deleteFileByMessageIds($messageIds)
    {
        ConversationFile::query()->whereIn('message_id', $messageIds)->delete();
    }
}