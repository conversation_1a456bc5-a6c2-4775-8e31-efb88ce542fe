<?php

namespace App\Models;

use App\Models\Model;
use <PERSON><PERSON><PERSON>\ColumnSortable\Sortable;

/**
 * Class Attendance
 * @property integer $id
 * @property integer $user_id
 * @property timestamp $checked_at
 * @property integer $checked_type
 */
class Attendance extends Model
{
    public const CHECK_IN = 0;
    public const CHECK_OUT = 1;
    public const OT_IN = 2;
    public const OT_OUT = 3;
    public const MANUAL_CHECK = 4;
    public const GPS_CHECK = 5;

    use Sortable;

    public $timestamps = false;

    protected $table = 'attendances';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id', 'user_id', 'checked_at', 'checked_type', 'metadata'
    ];

    public function user()
    {
        return $this->belongsTo('App\User');
    }

    /**
     * Sort by date of the attendance
     * @param $query
     * @param $direction
     * @return mixed
     */
    public function dateSortable($query, $direction)
    {
        return $query->orderBy('date', $direction);
    }
}
