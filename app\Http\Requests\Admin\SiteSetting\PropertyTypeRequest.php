<?php

namespace App\Http\Requests\Admin\SiteSetting;

use Illuminate\Foundation\Http\FormRequest;

class PropertyTypeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'property_name' => 'required|max:200',
            'property_code' => 'nullable|max:50'
        ];
        
        return $rules;
    }

    /**
     * attributes of validation rules
     *
     * @return array
     */

    public function attributes()
    {
        return [
            'property_name' => trans('language.site_setting_property_type.name'),
            'property_code' => trans('language.site_setting_property_type.code')
        ];
    }
}
