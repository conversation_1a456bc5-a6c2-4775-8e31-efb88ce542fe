<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Helpers\NotificationHelper;
use App\Helpers\StringHelper;
use App\Jobs\Notification\acceptNotificationContact;
use App\Jobs\Notification\sendNotificationContact;
use App\Models\Contacts;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Traits\ImageTrait;
use App\Traits\StorageTrait;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ContactManager
{
    use ImageTrait;
    use StorageTrait;

    const NOTIFICATION_CONTACT = 'send_contact';
    const IS_SCREEN_CREATE_NEW_CHAT = 1;
    protected $stringHelpers;

    public function __construct(StringHelper $stringHelpers)
    {
        $this->stringHelpers = $stringHelpers;
    }

    /**
     * List users without in contacts
     * @param Request $request
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function listNoContact(Request $request)
    {
        try {
            $typeSearch = (int)$request->input('type_search');
            $keyword = $request->input('keyword');
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $userId = Auth::guard('api')->user()->id;
            $data = User::query()
                // ->leftJoin('contacts', function(JoinClause $join) {
                //     $join->on('users.id', '=', 'contacts.user_id')
                //         ->orOn('users.id', '=', 'contacts.receiver_user_id');
                // })
                // ->whereDoesntHave('conversations', function(EloquentBuilder $query) {
                //     $query->where('type', '=', Conversation::TYPE_TWO_USER);
                // })
                ->where('users.id', '!=', $userId)
                // ->where(function(EloquentBuilder $query) {
                //     $query->whereNull('contacts.status')
                //     ->orWhereNotIn('contacts.status', [Contacts::STATUS_ACCEPTED]);
                // })
                ->leftJoin('positions', 'positions.id', 'users.position_id')
                ->select([
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                    'users.id AS user_id',
                    'users.avatar',
                    'users.company_contract',
                    DB::raw("TRIM(CONCAT_WS(' ',SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', - 1 ),SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT( users.first_name, ' ', users.last_name ),
                    ' ',-(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )))),
                    ' ',(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )) - 1
                    )),
                    IF(1 < LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )),
                    SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', 1 ),
                    NULL))) AS sort_name"),
                    'positions.name as position',
                ]);

            $data->when((!empty($typeSearch) && isset($keyword)), function(EloquentBuilder $query) use ($typeSearch, $keyword) {
                if ($typeSearch === Contacts::TYPE_EMAIL){
                    $query->where('users.email', '=', $keyword);
                }
                if ($typeSearch === Contacts::TYPE_PHONE){
                    $query->where('users.phone','=', $keyword);
                }
            });
            
            $data->orderBy('sort_name');

            return $data->paginate($perPage);
        } catch (Exception $e) {
            Log::error("[ContactManager][listNoContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][listNoContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }


    /**
     * Handle adding new contact
     * @param     $request
     * @return array|true|null
     * @throws Exception
     */
    public function addNewContact($request)
    {
        $type = (int)$request->input('type_request');
        $value = $request->input('value');
        $result = null;
        try {
            DB::beginTransaction();
            
            $currentUser = Auth::guard('api')->user();
            $currentUserId = $currentUser->id;
            //Check xem có gửi đến chính mình không
            if($type == 0 && $value == $currentUserId){
                return null;
            }
            $dataUser = User::query();

            if ($type === 0){
                $receiverUserId = [(int)$value];
            }else{
                if ($type === Contacts::TYPE_EMAIL){
                    $dataUser->where('email', '=', $value)->where('users.id', '!=', $currentUserId);
                }

                if ($type === Contacts::TYPE_PHONE) {
                    $dataUser->where('phone', '=', $value)->where('users.id', '!=', $currentUserId);
                }
                $receiverUserId = $dataUser->pluck('id')->toArray();

            }

            if (empty($receiverUserId)){
                return null;
            }
            // Check đã gửi lời mời đến user chưa
            if (count($receiverUserId) === 1){
                $checkStatus = Contacts::query()
                    ->where('receiver_user_id', $receiverUserId[0])
                    ->where('user_id', $currentUserId)
                    ->where('status', Contacts::STATUS_SENDED)
                    ->lockForUpdate()
                    ->first();
                if (!empty($checkStatus)){
                    $result['hasSendRequest'] = true;
                    return $result;
                }
            }
            // check user đó đã gửi lời mời kết bạn đến user đăng nhập chưa
            $checkContactStatus = Contacts::query()
                ->join('users', 'contacts.user_id', '=', 'users.id')
                ->where('contacts.receiver_user_id', $currentUserId)
                ->where('contacts.status', Contacts::STATUS_SENDED)
                ->whereIn('contacts.user_id', $receiverUserId)
                ->select([
                    'users.id AS user_id',
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name')
                    ])
                ->get();

            //Kiểm tra lời mời từ đối phương
            if ($checkContactStatus->isNotEmpty() && count($receiverUserId) === 1){
                $result['received_invitation'] = true;
                $result['name'] = $checkContactStatus[0]['name'];
                return $result;
            }

            // kiểm tra xem giữa user nhận và user đang đăng nhập có cuộc trò chuyện type = 2 chưa
            $listUserNotSender = array_diff($receiverUserId, $checkContactStatus->pluck('user_id')->values()->all());

            //Lasy list user đã là bạn bè trong $listUserNotSender
            $conversationOfCurrentUser = Conversation::join('conversation_participants', 'conversations.id', '=', 'conversation_participants.conversation_id')
                                        ->where('conversations.type', Conversation::TYPE_TWO_USER)
                                        ->where('conversation_participants.user_id',$currentUserId)
                                        ->select('conversations.id');
            $listUserHasConversation = ConversationParticipant::joinSub($conversationOfCurrentUser,'listConversation',function($join){
                                            $join->on('listConversation.id','=','conversation_participants.conversation_id');
                                        })
                                        ->where('conversation_participants.user_id','!=', $currentUserId)
                                        ->whereIn('conversation_participants.user_id', $listUserNotSender)
                                        // ->group_by('conversation_participants.user_id')
                                        ->pluck('user_id')->toArray();

            if (!empty($listUserHasConversation) && count($receiverUserId) === 1){
                $result['were_friend'] = true;
                return $result;
            }

            $listID = array_diff($listUserNotSender, $listUserHasConversation);

            // $listID = $listUserNotSender->diff($conversation->pluck('user_id'))->values();

            if (!empty($listID)) {
                $insertedIds = array();
                foreach ($listID as $id) {
                    $dataUpdateContact = [
                        'user_id' => $currentUserId,
                        'receiver_user_id' => $id
                    ];
                    $dataInsertContact = [
                        'user_id' => $currentUserId,
                        'receiver_user_id' => $id,
                        'status' => Contacts::STATUS_SENDED,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ];
                    $insertedId = Contacts::query()->updateOrCreate($dataUpdateContact, $dataInsertContact);
                    $insertedIds[] = $insertedId->id;
                }
                DB::commit();
                $receiverUser = User::query()
                    ->whereIn('id', $listID)
                    ->get(['first_name', 'last_name', 'avatar']);
                //Send notification to Socket
                foreach ($insertedIds as $key => $contactID) {
                    $dataNotiSocket = [
                        'contact_id' => $contactID,
                        'contact_sender' => [
                            'full_name' => $currentUser->full_name,
                            'id' => $currentUserId,
                            'avatar' => $currentUser->avatar
                        ],
                        'contact_receiver' => [
                            'full_name' => $receiverUser[$key]->full_name,
                            'id' => $listID[$key],
                            'avatar' => $receiverUser[$key]->avatar
                        ],
                        'receiver' => [
                            $listID[$key],
                            $currentUserId
                        ],
                    ];
                    // Send socket add new contact.
                    app(SocketManager::class)->emit(SocketEvent::NEW_CONTACT, $dataNotiSocket);

                    //Send notification via Firebase
                    $dataNotification = [
                        'title' => $currentUser->full_name,
                        'content' => trans('message.contact.addContact.contentNotification'),
                        'avatar' => $currentUser->avatar,
                        'notification_type' => NotificationHelper::NOTIFICATION_TYPE_CONTACT,
                        'contact_id' => $contactID,
                        'user_id' => $currentUserId,
                        'receiver_user_id' => $listID[$key],

                    ];
                    $websiteId = null;
                    if (env('USE_TENANT', false)) {
                        $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                    }
                    dispatch(new sendNotificationContact($dataNotification, $websiteId))->onQueue(config('queue.queueType.contact'));
                }
            }
            $result = true;
            return $result;
        } catch (Exception$e) {
            Log::error("[ContactManager][addNewContact] line " . $e->getLine() . " error " . $e->getMessage());
            DB::rollBack();
            throw new Exception("[ContactManager][addNewContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get a list of conversations of the currently logged-in user
     * @param int      $userId
     * @param bool     $checkUser
     * @param int|null $checkUserId
     * @return EloquentBuilder|mixed
     */
    public function getListConversationsOfUser(int $userId, bool $checkUser = false, int $checkUserId = null)
    {
        return User::query()
            ->join('conversation_participants', 'users.id', '=', 'conversation_participants.user_id')
            ->join('conversations', 'conversation_participants.conversation_id', '=', 'conversations.id')
            ->where('conversations.type', Conversation::TYPE_TWO_USER)
            ->where('users.id', '!=', $userId)
            ->when($checkUser, function(EloquentBuilder $query) use ($checkUserId) {
                $query->whereIn('users.id', [$checkUserId]);
            });
    }


    /**
     * Get contact of user by status
     * @param int      $status
     * @param int|null $senderUserId
     * @param int|null $receiverUserId
     * @param bool     $checkContact
     * @return EloquentBuilder|mixed
     */
    public function getContact(int $status, int $senderUserId, int $receiverUserId = null, bool $checkContact)
    {
        return Contacts::query()
            ->when($checkContact, function(EloquentBuilder $query) use ($senderUserId, $receiverUserId) {
                $query->where(function($query) use ($senderUserId, $receiverUserId) {
                    $query->where(function(EloquentBuilder $subQuery) use ($senderUserId, $receiverUserId) {
                        $subQuery->where('user_id', $senderUserId)
                            ->where('receiver_user_id', $receiverUserId);
                    })->orWhere(function(EloquentBuilder $subQuery) use ($senderUserId, $receiverUserId) {
                        $subQuery->where('user_id', $receiverUserId)
                            ->where('receiver_user_id', $senderUserId);
                    });
                });
            }, function(EloquentBuilder $query) use ($senderUserId) {
                $query->where(function(EloquentBuilder $q) use ($senderUserId) {
                    $q->where('user_id', $senderUserId)
                        ->orWhere('receiver_user_id', $senderUserId);
                });
            })
            ->where('status', $status);
    }

    /**
     * Handle get list contact
     * @param Request $request
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getListContact(Request $request): LengthAwarePaginator
    {
        try {
            $keyword = $request->get('keyword');
            $isScreenCreateNewChat = (int)$request->get('is_screen_create_new_chat');
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $currentUserId = Auth::guard('api')->user()->id;

            $listConversations = Conversation::query()
                ->leftJoin('conversation_participants', function(JoinClause $join) use ($currentUserId){
                    $join->on('conversations.id', '=', 'conversation_participants.conversation_id')
                        ->where('conversation_participants.user_id', '!=', $currentUserId);
                })
                ->leftJoin('users', 'conversation_participants.user_id', '=', 'users.id')
                ->leftJoin('positions', 'positions.id', 'users.position_id')
                ->where('conversations.type', '=', Conversation::TYPE_TWO_USER)
                ->whereHas('conversationParticipant', function (EloquentBuilder $query) use ($currentUserId) {
                    $query->where('conversation_participants.user_id', '=', $currentUserId);
                })
                ->whereNull('users.deleted_at')
                ->select([
                    'conversations.id AS conversation_id',
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                    'users.id AS user_id',
                    'users.avatar',
                    'users.company_contract',
                    DB::raw("TRIM(CONCAT_WS(' ',SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', - 1 ),SUBSTRING_INDEX(SUBSTRING_INDEX(CONCAT( users.first_name, ' ', users.last_name ),
                    ' ',-(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )))),
                    ' ',(LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )) - 1
                    )),
                    IF(1 < LENGTH(
                    CONCAT( users.first_name, ' ', users.last_name )) - LENGTH(
                    REPLACE ( CONCAT( users.first_name, ' ', users.last_name ), ' ', '' )),
                    SUBSTRING_INDEX( CONCAT( users.first_name, ' ', users.last_name ), ' ', 1 ),
                    NULL))) AS sort_name"),
                    'positions.name as position',
                ]);

            $listConversations->when(($request->has('is_screen_create_new_chat') && $isScreenCreateNewChat === ContactManager::IS_SCREEN_CREATE_NEW_CHAT), function(EloquentBuilder $query) {
                    $query->where('conversations.is_hide', '=', Conversation::HIDE);
            });

            $listConversations->when(isset($keyword), function(EloquentBuilder $query) use ($keyword) {
                $keyword = $this->stringHelpers->formatStringWhereLike($keyword);
                $keyword = $this->stringHelpers->transformSearchFullname($keyword);
                $query->where('users.name_search', 'LIKE', "%$keyword%");
            });

            $listConversations = $listConversations
                ->orderBy('sort_name')
                ->groupBy('users.id');

            return $listConversations->paginate($perPage);

        } catch (Exception $e) {
            Log::error("[ContactManager][getListContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][getListContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Handle get list received request
     * @param Request $request
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getListReceivedRequest(Request $request)
    {
        try {
            $keyword = $request->get('keyword');
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $currentUserId = Auth::guard('api')->user()->id;

            $listIdContact = $this->getContact(Contacts::STATUS_SENDED, $currentUserId, null, false)
                ->where('receiver_user_id', '=', $currentUserId)
                ->select('id','user_id', 'created_at');

            $result = User::query()
                ->joinSub($listIdContact, 'contacts', function (JoinClause $join) {
                    $join->on('users.id', '=', 'contacts.user_id');
                })
                ->leftJoin('positions', 'positions.id', 'users.position_id')
                ->select([
                    'contacts.id AS contact_id',
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                    'users.id AS user_id',
                    'users.avatar',
                    'users.company_contract',
                    DB::raw(User::SORT_NAME_ALPHA_BET),
                    'positions.name as position'
                ])
                ->when($request->has('keyword'), function(EloquentBuilder $query) use ($keyword) {
                    $keyword = $this->stringHelpers->formatStringWhereLike($keyword);
                    $nameSearch = $this->stringHelpers->transformSearchFullname($keyword);
                    $query->where('users.name_search', 'LIKE', "%$nameSearch%");
                })
                ->orderBy('sort_name')
                ->paginate($perPage);

            return $result;
        } catch (Exception $e) {
            Log::error("[ContactManager][getListReceivedRequest] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][getListReceivedRequest] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Handle get list sent request
     * @param Request $request
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getListSentRequest(Request $request)
    {
        try {
            $keyword = $request->get('keyword');
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $currentUserId = Auth::guard('api')->user()->id;

            $listIdContact = $this->getContact(Contacts::STATUS_SENDED, $currentUserId, null, false)->select(['id', 'receiver_user_id', 'created_at']);

            $result = User::query()
            ->joinSub($listIdContact, 'contacts', function (JoinClause $join) use ($currentUserId) {
                $join->on('users.id', '=', 'contacts.receiver_user_id')
                ->where('contacts.receiver_user_id', '!=', $currentUserId);
            })
            ->leftJoin('positions', 'positions.id', 'users.position_id')
            ->select([
                'contacts.id AS contact_id',
                DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                'users.id AS user_id',
                'users.avatar',
                'users.company_contract',
                DB::raw(User::SORT_NAME_ALPHA_BET),
                'positions.name as position'
            ])
            ->when($request->has('keyword'), function(EloquentBuilder $query) use ($keyword) {
                $keyword = $this->stringHelpers->formatStringWhereLike($keyword);
                $nameSearch = $this->stringHelpers->transformSearchFullname($keyword);
                $query->where('users.name_search', 'LIKE', "%$nameSearch%");
            })
            ->orderBy('sort_name')
            ->paginate($perPage);
            return $result;

        } catch (Exception $e) {
            Log::error("[ContactManager][getListSentRequest] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][getListSentRequest] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }


    /**
     * Handle get detail information of user
     * @param         $userId
     * @param Request $request
     * @return EloquentBuilder|EloquentBuilder[]|Collection|Model|object
     * @throws Exception
     */
    public function getDetailInformationUser($userId, Request $request)
    {
        $userId = (int) $userId;
        $currentUserId = Auth::guard('api')->user()->id;
        try {
            $conversationOfCurrentUser = Conversation::join('conversation_participants', 'conversations.id', '=', 'conversation_participants.conversation_id')
                                        ->where('conversations.type', Conversation::TYPE_TWO_USER)
                                        ->where('conversation_participants.user_id',$currentUserId)
                                        ->whereNull('conversation_participants.deleted_at')
                                        ->select('conversations.id');
            $dataUser = User::query()
                ->leftJoin('conversation_participants', 'users.id', '=', 'conversation_participants.user_id')
                ->leftJoinSub($conversationOfCurrentUser, 'list_conversation', function($join){
                    $join->on('list_conversation.id', 'conversation_participants.conversation_id');
                })
                ->leftJoin('positions', 'users.position_id', '=', 'positions.id')
                ->leftJoin('languages', 'users.language_id', 'languages.id')
                ->leftJoin('work_places', 'users.work_place_id', 'work_places.id')
                ->leftJoin('departments', 'users.department_id', 'departments.id')
                ->leftJoin('contacts', function(JoinClause $join) use ($userId, $currentUserId) {
                    $join->on('users.id', '=', 'contacts.user_id')
                        ->orOn('users.id', '=', 'contacts.receiver_user_id')
                        ->where(function($qr) use ($userId, $currentUserId){
                            $qr->where("contacts.user_id", "=", $userId)
                                ->where("contacts.receiver_user_id", "=", $currentUserId);
                        })->orWhere(function($qr) use ($userId, $currentUserId){
                            $qr->where("contacts.user_id", "=", $currentUserId)
                                ->where("contacts.receiver_user_id", "=", $userId);
                        });
                })
                ->where('users.id', $userId)
                ->select([
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS name'),
                    'users.id AS user_id',
                    'users.avatar',
                    'positions.name AS position',
                    'users.email',
                    'users.first_name',
                    'users.last_name',
                    'users.address',
                    'contacts.id as contact_id',
                    DB::raw('
                    CASE
                        WHEN list_conversation.id is null THEN 1
                        WHEN contacts.status = '.Contacts::STATUS_ACCEPTED.' THEN 1
                        WHEN contacts.status = '.Contacts::STATUS_SENDED.' AND contacts.receiver_user_id = '.$currentUserId.' THEN 2
                        WHEN contacts.status IS NULL AND contacts.user_id IS NULL THEN 3
                        WHEN contacts.status = '.Contacts::STATUS_SENDED.' AND contacts.user_id = '.$currentUserId.' THEN 4
                        ELSE null
                    END AS contact_status
                    '),
                    'list_conversation.id as conversation_id',
                    'languages.display_name AS language',
                    'work_places.name AS work_location',
                    'departments.name AS department',
                ])
                ->orderBy('list_conversation.id', 'DESC')
                ->first();
            return $dataUser;
        } catch (Exception $e) {
            Log::error("[ContactManager][getDetailInformationUser] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][getDetailInformationUser] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Handle accept or reject request
     * @param $contactId
     * @param $type
     * @return array
     * @throws Exception
     */
    public function acceptOrRejectContact($contactId, $type)
    {
        $currentUser = Auth::guard('api')->user();
        $result = [];
        $dataNotiSocket = [];

        try {
            DB::beginTransaction();
            $contact = Contacts::query()
                ->join('users', function(JoinClause $join) {
                    $join->on('contacts.user_id', 'users.id')
                        ->orOn('contacts.receiver_user_id', 'users.id');
                })
                ->select([
                    'users.id AS user_id',
                    DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS full_name'),
                    'users.avatar',
                    'users.company_contract',
                    'contacts.status',
                    'contacts.id AS contact_id',
                    'contacts.user_id AS user_id_send_contact',
                    'contacts.receiver_user_id'
                ])
                ->where(function($query) use ($currentUser){
                    $query->where('contacts.user_id',$currentUser->id)
                    ->orWhere('contacts.receiver_user_id',$currentUser->id);
                })
                ->where('contacts.id', $contactId)
                ->first();
            if(empty($contact)){
                return $result;
            }
            if($contact->status != Contacts::STATUS_SENDED){
                if($type === Contacts::STATUS_REJECTED){
                    if ($contact->receiver_user_id === $currentUser->id) {
                        //Người nhận từ chối
                        $result['error_code'] = Response::HTTP_NOT_FOUND;
                        return $result;
                    }else{
                        //Người gửi hủy
                        $result['error_code'] = Contacts::STATUS_REJECTED;
                        return $result;
                    }
                } else if ($type === Contacts::STATUS_ACCEPTED) {
                    //In case the sender cancels the request -> the recipient accepts the request
                    if ($contact->receiver_user_id === $currentUser->id) {
                        $result['error_code'] = Response::HTTP_NOT_FOUND;
                        return $result;
                    }
                } else {
                    $result['error_code'] = Contacts::STATUS_ACCEPTED;
                    return $result;
                }
            }
            //Reject request
            if ($type === Contacts::STATUS_REJECTED){
                Contacts::query()
                    ->whereId($contactId)
                    ->update([
                        'status' => Contacts::STATUS_REJECTED
                    ]);

                $dataUser = $this->getDataUserByContact($contact->user_id_send_contact, $contactId);
                $contactSender = [
                    'id' => $dataUser->user_id,
                    'full_name' => $dataUser->full_name,
                    'avatar' => $dataUser->avatar,
                    'company_contract' => $dataUser->company_contract
                ];

                $contactReceiver = [
                    'id' => $currentUser->id,
                    'full_name' => $currentUser->full_name,
                    'avatar' => $currentUser->avatar,
                    'company_contract' => $currentUser->company_contract
                ];

                //Current user is sender contact
                if ($contact->user_id_send_contact === $currentUser->id){
                    $contactSender = [
                        'id' => $currentUser->id,
                        'full_name' => $currentUser->full_name,
                        'avatar' => $currentUser->avatar,
                        'company_contract' => $currentUser->company_contract
                    ];

                    $contactReceiver = [
                        'id' => $contact->user_id,
                        'full_name' => $contact->full_name,
                        'avatar' => $contact->avatar,
                        'company_contract' => $contact->company_contract
                    ];
                }

                $actionByUserId = $contact->user_id_send_contact === $currentUser->id ? $currentUser->id : $contact->user_id;
                $receiverUserId = $contact->only('user_id_send_contact', 'receiver_user_id');

                //Push notification to Socket
                $dataNotiSocket = [
                    'contact_id' => $contact->contact_id,
                    'contact_sender' => $contactSender,
                    'contact_receiver' => $contactReceiver,
                    'receiver' => array_values($receiverUserId),
                    'action_by_user_id' => $actionByUserId,
                    'event' => SocketEvent::REJECT_CONTACT
                ];

                //Case of successfully refusing to make friends
                $result['type'] = Contacts::STATUS_REJECTED;
            }
            //Accept request
            if ($type === Contacts::STATUS_ACCEPTED){

                Contacts::query()
                    ->whereId($contactId)
                    ->update([
                        'status' => Contacts::STATUS_ACCEPTED
                    ]);
                $dataUserSendContact = $this->getDataUserByContact($contact->user_id_send_contact, $contact->contact_id);
                $avatarPath = null;
                if (!empty($dataUserSendContact->avatar) && Storage::exists($dataUserSendContact->avatar)){
                    $avatar = $this->resizeImage(storage_path('app/' . $dataUserSendContact->avatar), CONVERSATION_AVATAR_WIDTH);
                    $avatarPath = $this->uploadFileByStream($avatar, CONVERSATION_DIR . time() . Str::random(5) . '.' . $avatar->extension);
                }

                $conversation = Conversation::query()
                    ->create([
                        'name' => $dataUserSendContact->full_name,
                        'type' => Conversation::TYPE_TWO_USER,
                        'avatar' => $avatarPath
                    ]);

                if (!empty($conversation['id'])){
                    $dataInsertParticipant = [
                        [
                            'conversation_id' => $conversation['id'],
                            'user_id' => $currentUser->id,
                            'admin' => ConversationParticipant::IS_ADMIN,
                            'status' => ConversationParticipant::STATUS_NOT_DELETE,
                            'created_at' => Carbon::now()
                        ],
                        [
                            'conversation_id' => $conversation['id'],
                            'user_id' => $contact->user_id_send_contact,
                            'admin' => ConversationParticipant::IS_ADMIN,
                            'status' => ConversationParticipant::STATUS_NOT_DELETE,
                            'created_at' => Carbon::now()
                        ]
                    ];
                    ConversationParticipant::query()->insert($dataInsertParticipant);

                    //Push notification to Socket
                    $dataNotiSocket = [
                        'contact_id' => $contact->contact_id,
                        'conversation_id' => $conversation['id'],
                        'contact_sender' => [
                            'id' => $currentUser->id,
                            'full_name' => $currentUser->full_name,
                            'avatar' => $currentUser->avatar
                        ],
                        'contact_receiver' => [
                            'id' => $dataUserSendContact->user_id,
                            'full_name' => $dataUserSendContact->full_name,
                            'avatar' => $dataUserSendContact->avatar
                        ],
                        'receiver' => [
                            $currentUser->id,
                            $contact->user_id_send_contact
                        ],
                        'event' => SocketEvent::ACCEPT_CONTACT
                    ];

                    //Push notification accept contact via Firebase
                    $dataNotification = [
                        'title' => trans('message.contact.acceptContact.titleNotification'),
                        'content' => trans('message.contact.acceptContact.contentNotification', [ 'name' => $currentUser->full_name ]),
                        'avatar' => $currentUser->avatar,
                        'notification_type' => NotificationHelper::NOTIFICATION_TYPE_CONTACT,
                        'conversation_id' => $conversation['id'],
                        'user_id' => $currentUser->id,
                        'receiver_user_id' => $dataUserSendContact->user_id,
                    ];
                    $websiteId = null;
                    if (env('USE_TENANT', false)) {
                        $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                    }
                    dispatch(new acceptNotificationContact($dataNotification, $websiteId))->onQueue(config('queue.queueType.contact'));

                    $result['data'] = $conversation;
                    $result['type'] = Contacts::STATUS_ACCEPTED;
                }
            }
            DB::commit();
            if($dataNotiSocket) {
                $event = $dataNotiSocket['event'];
                app(SocketManager::class)->emit($event, $dataNotiSocket);
            }
            return $result;
        } catch (Exception$e) {
            Log::error("[ContactManager][acceptOrRejectContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactManager][acceptOrRejectContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    private function getDataUserByContact($userId, $contactId)
    {
        $contact = Contacts::query()
            ->join('users', function(JoinClause $join) {
                $join->on('contacts.user_id', 'users.id')
                    ->orOn('contacts.receiver_user_id', 'users.id');
            })
            ->select([
                'users.id AS user_id',
                DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS full_name'),
                'users.avatar',
                'users.company_contract',
                'contacts.status',
                'contacts.id AS contact_id',
                'contacts.user_id AS user_id_send_contact',
                'contacts.receiver_user_id'
            ])
            ->where('users.id', $userId)
            ->where('contacts.id', $contactId)
            ->first();
        return $contact;
    }
}