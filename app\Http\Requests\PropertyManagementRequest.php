<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PropertyManagementRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'property_agency_name' => 'required|max:200',
        ];
        
        return $rules;
    }

    /**
     * attributes of validation rules
     *
     * @return array
     */

     public function attributes()
     {
         return [
            'property_agency_name' => trans('language.site_setting_property_management_agency.name'),
         ];
     }
}
