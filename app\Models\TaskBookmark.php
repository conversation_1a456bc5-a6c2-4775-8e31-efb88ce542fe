<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class TaskBookmark
 * @property int $task_id
 * @property int $user_id
 * @property int $created_by
 * @property timestamp $created_at
 */

class TaskBookmark extends Model
{
     /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'task_bookmarks';
    
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['task_id', 'user_id', 'created_by', 'created_at'];
}
