<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kyslik\ColumnSortable\Sortable;

/**
 * Class SpeccialRequest
 * @property integer $id
 * @property date $month
 * @property integer $user_id
 * @property float $freelancer
 * @property float $percent_work
 * @property float $ot
 * @property float $mentor
 * @property integer $cash_collect
 * @property integer $cash_chase
 * @property integer $arrears
 * @property integer $back_pay
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */
class SpecialRequest extends Model
{
    use Sortable;
    use SoftDeletes;

    protected $guarded = [];
        /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
            $data->created_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }
}
