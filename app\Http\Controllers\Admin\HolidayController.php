<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\SiteSetting\HolidayRequest;
use App\Logics\TaskManager;
use App\Models\Holiday;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;

class HolidayController extends Controller
{
    const PAGINATION = 12;

    public function holidays(Request $request){
        $year = isset($request->choose_year) ? $request->choose_year : today()->format('Y');
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['choose_year']);
        $allHolidays = Holiday::query()
            ->where('choose_holidays','like',"%$year%")
            ->sortable(['created_at' => 'DESC'])
            ->paginate(self::PAGINATION);
        return view('admin.site_setting.holiday',[
            'allHolidays'=>$allHolidays,
            'filterHtml' =>$filterHtml
        ]);
    }
    public function createHoliday(HolidayRequest $request){
        DB::beginTransaction();
        try {
            $holiday = new Holiday();
            $holiday->name = $request->name;
            $holiday->choose_holidays = $request->choose_holidays;
            $holiday->save();
            DB::commit();
            return redirect()
                ->route('admin.holiday.index')
                ->with([
                    'status_succeed' => trans('message.holiday_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.holiday.index')
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }
    
    public function editHoliday(HolidayRequest $request,$id){
        DB::beginTransaction();
        try {
            $holiday = Holiday::query()->findOrFail($id);
            if ($holiday) {
                $holiday->name = $request->name;
                $holiday->choose_holidays = $request->choose_holidays;
                $holiday->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.holiday.index')
                ->with([
                    'status_succeed' => trans('message.holiday_edit_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.holiday.index')
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }
    
    public function deleteHoliday($id){
        DB::beginTransaction();
        try {
            $holiday = Holiday::query()->findOrFail($id);
            if ($holiday){
                $holiday->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.holiday_delete_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }
}
