<?php

namespace App\Jobs;

use App\Mail\NoteTagMail;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Mail\RequestMail;

class SendMailRequest extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $arrRequestInfor;
    private $approverTag;

    /**
     * Create a new job instance.
     *
     * @param $user
     */
    public function __construct($approverTag, $websiteId=null, $arrRequestInfor)
    {
        parent::__construct($websiteId);

        $this->approverTag = $approverTag;
        $this->arrRequestInfor = $arrRequestInfor;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        parent::handle();
        $email = new RequestMail($this->arrRequestInfor,$this->approverTag);
        Mail::to($this->approverTag->email)->send($email);
    }
}
