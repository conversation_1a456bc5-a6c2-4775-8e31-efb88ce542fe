<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Model;

/**
 * Class ProjectMember
 * @property integer $project_id
 * @property integer $user_id
 * @property integer $role_id
 */

class ProjectMember extends Model
{
    protected $table = 'project_members';

    public $timestamps = false;

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
    }
    
}
