<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Helpers\RedisHelper;

class SocketManager
{
    public function emit($event, $data, $channel='chat') {
        $data['event'] = $data['event'] ?? $event;
        
        if (env('USE_TENANT', false)) {
            $websiteId = app(\Hyn\Tenancy\Environment::class)->tenant()->id;
            $data['website_id'] = $websiteId;
        }

        // Format data
        switch ($event) {
            case SocketEvent::NEW_CONVERSATION:
            case SocketEvent::UPDATE_CONVERSATION:
                if (isset($data['most_recent_message'])) {
                    $messageManager = new MessageManager();
                    $data['most_recent_message'] = $messageManager->handleLastMessageContentText($data['most_recent_message']);
                }
                break;
        }
        RedisHelper::sendToSocket($channel, $data);
    }
}
