<?php

namespace App\Http\Controllers\Project;

use App\Jobs\SendMailTagNote;
use App\Logics\TaskLogManager;
use App\Models\ProjectTask;
use App\Models\TaskLog;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Logics\AttachmentManager;
use App\Models\TaskAttachment;
use App\Traits\StorageTrait;
use App\User;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use App\Helpers\StringHelper;

class TaskLogController extends Controller
{
    use StorageTrait;
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,$id)
    {
        $userId = Auth::id();
        DB::beginTransaction();
        // Check task exist
        $task = ProjectTask::select('project_tasks.*')->checkUserPermission($userId)->find($id);
        if ($task == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'msg' => trans('message.task_not_exist'),
                ],
            ];
        }

        if (empty($request->note)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'msg' => trans('message.content_note_not_null')
                ],
            ];
        }
        try{
            // Save note
            $taskNote = new TaskLog();
            $taskNote->task_id = $id;
            $taskNote->log = StringHelper::escapeHtmlForSummernote($request->note);
            $taskNote->type = TaskLog::NOTE;
            $taskNote->created_by = $userId;
            $taskNote->updated_by = $userId;
            $taskNote->save();

            $data = TaskLog::find($taskNote->id);
            $task = ProjectTask::find($data->task_id);
            $destinationPath = str_replace(['{project_id}', '{task_id}'], [$task->project_id, $data->task_id], TASK_ATTACHMENT_DIR).'/';
            $files = (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_TASK_LOG,$request->documents, $request->descriptionDocument, $destinationPath, 'log');

            $user = Auth::user();
            foreach($files as $file){
                $file->first_name = $user->first_name;
                $file->last_name = $user->last_name;
            }
            //get note
            $taskLogManager = new TaskLogManager();
            $note = $taskLogManager->getLog($taskNote->id);

            if($request->user_id != null){
                $taskInfor = [];
                $websiteId = null;
                if (env('USE_TENANT', false)) {
                    $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                }
                $taskInfor['userEmail'] = $user->email;
                $taskInfor['checkMethod'] = "store";
                $taskInfor['url'] = route("task.show", ['id' => $task->id]);
                $taskInfor['language_id'] = $user->language_id;
                $taskInfor['title'] =  '#'.$task->id.' - '.$task->name;
                $idTag = explode(',' , $request->user_id);
                $userTags = User::select('id', 'email')->whereIn('id', $idTag)->get();
                foreach($userTags as $userTag){
                    dispatch(new SendMailTagNote($userTag, $websiteId, $taskInfor))->onQueue(QUEUE_MAIL);
                }
            }

            DB::commit();
            //get html
            $html = view('task.partials.item-task-history', [
                'data' => $note,
                'userId' => $userId,
                'task' => $task,
                'taskFiles' => $files,
                'type' => 'note'
            ])->render();
            $htmlFile = view('task.partials.list-file-task', [
                'taskFiles' => $files,
                'task' => $task
            ])->render();
            $htmlFilePreview = view('task.partials.list-preview-file-task', [
                'taskFiles' =>$files,
                'task' => $task
            ])->render();

            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success'),
                    'msg' => trans('message.create_succeed')
                ],
                'html' => $html,
                'htmlFile' => $htmlFile,
                'htmlFilePreview' => $htmlFilePreview
            ];
        } catch (\Exception $e){
            DB::rollBack();
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.add_documents_failed'),
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @param  int  $taskId
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $taskId, $id)
    {
        $urlBase = URL::to('/');
        $userId = Auth::id();
        // Check task exist
        $task = ProjectTask::select('project_tasks.*')->checkUserPermission($userId)->find($taskId);
        if ($task == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => trans('message.task_not_exist'),
            ];
        }

        // Check note exist
        $taskNote = TaskLog::select('*')->where('created_by',$userId)->where('id',$id)->where('task_id',$taskId)->first();
        if ($taskNote == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => trans('message.note_not_exist'),
            ];
        }

        if (empty($request->note)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => trans('message.content_note_not_null'),
            ];
        }

        // Update note
        $taskNote->log = StringHelper::escapeHtmlForSummernote($request->note);
        $taskNote->updated_by = $userId;
        $taskNote->save();

        //get note
        $taskLogManager = new TaskLogManager();
        $note = $taskLogManager->getLog($taskNote->id);

        if ($request->user_id != null) {
            $taskInfor = [];
            $user = Auth::user();
            $websiteId = null;
            if (env('USE_TENANT', false)) {
                $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
            }
            $taskInfor['userEmail'] = $user->email;
            $taskInfor['checkMethod'] = "update";
            $taskInfor['url'] = route("task.show", ['id' => $task->id]);
            $taskInfor['language_id'] = $user->language_id;
            $taskInfor['title'] =  '#' . $task->id . ' - ' . $task->name;
            $idTag = explode(',', $request->user_id);
            $userTags = User::select('id', 'email')->whereIn('id', $idTag)->get();
            foreach ($userTags as $userTag) {
                dispatch(new SendMailTagNote($userTag, $websiteId, $taskInfor))->onQueue(QUEUE_MAIL);
            }
        }

        $data = TaskLog::find($taskNote->id);
        $task = ProjectTask::find($data->task_id);
        $destinationPath = str_replace(['{project_id}', '{task_id}'], [$task->project_id, $data->task_id], TASK_ATTACHMENT_DIR).'/';
        $files = (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_TASK_LOG,$request->documents, $request->descriptionDocument, $destinationPath, 'log');
        
        //get html
        $html = view('task.partials.item-task-history', [
            'data' => $note,
            'userId' => $userId,
            'task' => $task,
            'type' => 'note'
        ])->render();
        return [
            'status' => Response::HTTP_OK,
            'msg' => trans('message.update_succeed'),
            'html' => $html
        ];
    }
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @param  int  $taskId
     * @return \Illuminate\Http\Response
     */
    public function destroy($taskId,$id)
    {
        $userId = Auth::id();
        // Check task exist
        $task = ProjectTask::select('project_tasks.*')->checkUserPermission($userId)->find($taskId);
        if ($task == null) {
            return redirect()->route('projects.tasks');
        }

        // Return error message if task not exist
        if ($task == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.task_not_exist'),
                    'msg' => ''
                ]
            ];
        }

        // Check note exist
        $taskNote = TaskLog::find($id);
        if ($taskNote == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.note_not_exist'),
                    'msg' => ''
                ]
            ];
        }
        $taskNote->delete();

        // Delete attachment
        $attachment = [];
        $taskAttachments = TaskAttachment::where('related_id','=',$id)
                ->where('type','=',TaskAttachment::TYPE_TASK_LOG)
                ->get();
        foreach($taskAttachments as $taskAttachment){
            $attachment[] = $taskAttachment->id;
            $taskAttachment->delete();
            $this->deleteFile($taskAttachment->file_path);
        }

        return [
            'status' => Response::HTTP_OK,
            'attachment'=>  $attachment,
            'msg' => [
                'title' => trans('message.delete_note_succeed') ,
                'msg' => ''
            ]
        ];
    }
}
