<?php

namespace App\Helpers;

class CsvHelper {

    /**
     * Import csv file
     *
     * @param $file
     * @param $model
     * @param $columns
     * @param int $batchSize
     * @param bool $header
     * @param string $delimiter
     */
    public function import($file, $model, $columns, $batchSize=100, $header=true, $delimiter = ',') {
        $data = [];
        while ($csvRow = fgetcsv($file, 0, $delimiter)) {
            // Skip the header is the first row
            if($header) {
                $header = false;
                continue;
            }
            // Combine the columns is keys and the csvRow is values to: [columnX => csvRowX]
            $row = array_combine($columns, $csvRow);

            $data[] = $row;
        }
        fclose($file);

        // Batch import
        $batches = array_chunk($data, $batchSize);

        foreach($batches as $batch) {
            $model::insert($batch);
        }
    }
}