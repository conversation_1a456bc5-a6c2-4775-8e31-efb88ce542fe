<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsThreadIdMessageReadTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('message_read', function (Blueprint $table) {
            $table->unsignedBigInteger('thread_id')->nullable()->after('conversation_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('message_read', function (Blueprint $table) {
            $table->dropColumn([
                'thread_id'
            ]);
        });
    }
}
