<?php
namespace App\Helpers;

use Caxy\HtmlDiff\HtmlDiff;

class Helpers {
	/*
	* Check exit email
	*/
	public static function checkUsernameOrEmail($str) {
		$check_is_email = strpos($str,'@');
		if (isset($check_is_email)) return true;
		return false;
	}

	/**
     * check route is admin
     */
	public function checkHeadquarterSubdomain()
    {
        return preg_match('/^http[s]?:\/\/'
        . env('HEADQUARTER_SUBDOMAIN')
        . '.'
        . env('APP_URL_BASE', 'localhost')
        . '/', url()->current());
}

    /**
     * compare 2 string and render highlight difference
     * @param $old
     * @param $new
     * @return string[]
     */
	public static function diff($old, $new)
    {
        $htmlDiff = new HtmlDiff($old, $new);
        $htmlDiff->getConfig()
            ->setMatchThreshold(80)
            ->setInsertSpaceInReplace(true)
        ;

        // Calculate the differences using the configuration and get the html diff.
        $content = $htmlDiff->build();
        // dd($content);
        return [
            'old' => $content,
        ];
    }

    /**
     * @param $str
     * @return string|string[]|null
     */
    public static function trimSpaces($str)
    {
        if (is_string($str) && $str) {
            $chars = '\s　';
            $str = preg_replace("/^[$chars]+/u", '', $str);
            $str = preg_replace("/[$chars]+$/u", '', $str);
        }
        return $str;
    }
}
