<?php

namespace App\Console\Commands;

use Exception;
use App\Services\EncryptService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class EncryptMessagesContent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'messages:encrypt-content 
                            {--dry-run : Show what would be encrypted without doing it}
                            {--batch=1000 : Records per batch}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Encrypt existing messages content_text and quote_text';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int|void
     */
    public function handle()
    {
        $encryptService = new EncryptService();
        $encryptKey = $encryptService->getEncryptedKey();
        $isDryRun = $this->option('dry-run');
        $batchSize = (int) $this->option('batch');

        if ($isDryRun) {
            $this->info('DRY RUN MODE - No actual changes will be made');
            $this->showWhatWouldBeEncrypted();
            return;
        }

        $this->info('Starting encryption process...');

        $stats = $this->getEncryptionStats();
        $this->displayStats($stats);

        if (!$this->confirm('Continue with encryption?')) {
            $this->info('Encryption cancelled');
            return;
        }

        // Start transaction for entire process
        DB::beginTransaction();
        
        try {
            if ($stats['content_text_count'] > 0) {
                $this->encryptField('content_text', $encryptKey, $batchSize, $stats['content_text_count']);
            }

            if ($stats['quote_text_count'] > 0) {
                $this->encryptField('quote_text', $encryptKey, $batchSize, $stats['quote_text_count']);
            }

            // Commit only if no errors
            DB::commit();
            $this->info('Encryption completed successfully!');
            $this->showFinalStats();
            
        } catch (Exception $e) {
            DB::rollBack();
            $this->error("Fatal error occurred. All changes rolled back: " . $e->getMessage());
            return 1;
        }
    }

    private function getEncryptionStats(): array
    {
        return [
            'content_text_count' => DB::table('messages')->whereNotNull('content_text')->count(),
            'quote_text_count' => DB::table('messages')->whereNotNull('quote_text')->count(),
            'total_messages' => DB::table('messages')->count()
        ];
    }

    private function displayStats($stats)
    {
        $this->info("Encryption Statistics:");
        $this->table(
            ['Field', 'Records to Encrypt'],
            [
                ['content_text', number_format($stats['content_text_count'])],
                ['quote_text', number_format($stats['quote_text_count'])],
                ['Total Messages', number_format($stats['total_messages'])]
            ]
        );
    }

    private function showWhatWouldBeEncrypted()
    {
        $stats = $this->getEncryptionStats();
        $this->displayStats($stats);

        $samples = DB::table('messages')
            ->select('id', 'content_text', 'quote_text')
            ->whereNotNull('content_text')
            ->limit(3)
            ->get();

        $this->info("\nSample data that would be encrypted:");
        foreach ($samples as $sample) {
            $this->line("ID: " . $sample->id);
            $this->line("Content: " . substr($sample->content_text, 0, 100) . "...");
            $this->line("---");
        }
    }

    private function encryptField($field, $encryptKey, $batchSize, $totalCount)
    {
        $this->info("Encrypting " . $field . "...");

        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->setFormat('verbose');

        $processed = 0;

        DB::table('messages')
            ->whereNotNull($field)
            ->orderBy('id')
            ->chunk($batchSize, function ($messages) use ($field, $encryptKey, $progressBar, &$processed) {

                foreach ($messages as $message) {
                    $content = $message->{$field};
                    
                    // Validate field name to prevent SQL injection
                    if (!in_array($field, ['content_text', 'quote_text'])) {
                        throw new Exception("Invalid field name: $field");
                    }
                    
                    // Use prepared statement with field name validated
                    $sql = "UPDATE messages SET `$field` = TO_BASE64(AES_ENCRYPT(?, ?)) WHERE id = ?";
                    DB::update($sql, [$content, $encryptKey, $message->id]);

                    $processed++;
                    $progressBar->advance();
                }

                // Sleep 10ms between batches to avoid DB overload
                usleep(10000);
            });

        $progressBar->finish();
        $this->line('');

        $this->info($field . " encryption completed - Processed: " . number_format($processed) . " records");
        $this->line('');
    }

    private function showFinalStats()
    {
        $this->info("Final Statistics:");

        $encryptedContent = DB::table('messages')
            ->whereNotNull('content_text')
            ->whereRaw("content_text REGEXP '^[A-Za-z0-9+/]+=*$'")
            ->count();

        $encryptedQuote = DB::table('messages')
            ->whereNotNull('quote_text')
            ->whereRaw("quote_text REGEXP '^[A-Za-z0-9+/]+=*$'")
            ->count();

        $this->table(
            ['Field', 'Encrypted Records'],
            [
                ['content_text', number_format($encryptedContent)],
                ['quote_text', number_format($encryptedQuote)]
            ]
        );

        $this->info("All done! No manual cleanup required.");
    }
}