<?php

namespace App\Http\Controllers\Asset;

use App\Events\UpdateAsset;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreAssetRequest;
use App\Logics\AssetManager;
use App\Logics\AttachmentManager;
use App\Logics\DateFormatManager;
use App\Models\Asset;
use App\Models\AssetSetting;
use App\Models\Department;
use App\Models\Language;
use App\Models\PropertyManagementAgency;
use App\Models\PropertyStatus;
use App\Models\PropertyType;
use App\Models\Role;
use App\Models\TaskAttachment;
use App\Traits\StorageTrait;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use App\Exceptions\ImportException;
use App\Exports\Asset\AssetMultiSheetExport;
use App\Exports\Asset\AssetReportExport;
use App\Helpers\Helpers;
use App\Http\Requests\ImportAssetRequest;
use App\Rules\NoEmojiRule;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Throwable;

class AssetController extends Controller
{
    use StorageTrait;
    const PAGE_SIZE = 50;
    const FIRST_SHEET = 0;
    protected $dateFormatManager;
        /**
     * $assetManager
     *
     * @var AssetManager
     */
    private $assetManager;

            /**
     * $assetManager
     *
     * @var AssetManager
     */
    private $attachmentManager;

    public function __construct(DateFormatManager $dateFormatManager, AttachmentManager $attachmentManager, AssetManager $assetManager)
    {
        $this->attachmentManager = $attachmentManager;
        $this->assetManager = $assetManager;
        $this->dateFormatManager = $dateFormatManager;
    }

        /**
     * Delete asset
     * @param $id
     * @return array
     */
    public function delete($id)
    {
        $asset = Asset::find($id);

        if ($asset == null) {
            return [
                'status' => ResponseAlias::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.asset_not_exist'),
                ]
            ];
        }

        $asset->delete();

        return [
            'status' => ResponseAlias::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_asset_succeed') ,
            ],
            'redirect' => route('asset.index'),
        ];
    }

    /**
     * Download excel template
     * @return BinaryFileResponse|void
     */
    public function exportExcelTemplate()
    {
        $user = Auth::user();
        try {
            //List management agency by role
            $listManagementAgencyByUser = $this->assetManager->getListAssetManagementAgencyByUser();
            $listManagementAgency = PropertyManagementAgency::query()
                ->select('name')
                ->orderby('sort_order');
            // User has Asset Manager role manage some agency then only show their manage agency
            if ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && !in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $listManagementAgencyByUser)) {
                $listManagementAgency = $listManagementAgency->whereIn('id', $listManagementAgencyByUser);
            }
            $listManagementAgency = $listManagementAgency->get()
                ->toArray();
                
            $listAssetType = PropertyType::select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();
            $listDepartment = Department::select('name')
                ->orderByDesc('id')
                ->get()
                ->toArray();
            $listAssetStatus = PropertyStatus::select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();
            $listSourceOfOrigin = AssetSetting::query()
                ->select('name')
                ->orderby('sort_order')
                ->where('type', '=',AssetSetting::ASSET_FORMATION_SOURCE)
                ->get()
                ->toArray();
            $listPremises = AssetSetting::query()
                ->select('name')
                ->orderby('sort_order')
                ->where('type', '=',AssetSetting::ASSET_BRANCH)
                ->get()
                ->toArray();

            $listAssetSupplier = AssetSetting::suppliers()
                ->select("name")
                ->get()
                ->toArray();

            $listBiddingPackages = AssetSetting::query()
                ->where('type', '=',AssetSetting::ASSET_BIDDING_PACKAGE)
                ->select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();

            $listUsers = User::query()
                ->select([DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as name')])
                ->orderByDesc('id')
                ->get()
                ->toArray();

            $shortNames = AssetSetting::shortName()
                ->select('name')
                ->get()
                ->toArray();

            $dataTable = [
                'table1' => $listManagementAgency,
                'table2' => $listAssetType,
                'table3' => $listDepartment,
                'table4' => $listAssetStatus,
                'table5' => $listSourceOfOrigin,
                'table6' => $listPremises,
                'tableSupplier' => $listAssetSupplier,
                'tableBiddingPackages' => $listBiddingPackages,
                'tableUsers' => $listUsers,
                'tableShortName' => $shortNames
            ];

            $item['name'] = 'Máy chấm công BeeID';
            $item['name_in_contract'] = 'Máy chấm công BeeID';
            $item['asset_short_name_id'] = 'Máy chấm công BeeID';
            $item['management_unit_id'] = $listManagementAgency[0]['name'] ?? '';
            $item['type_id'] = $listAssetType[0]['name'] ?? '';
            $item['department_id'] = $listDepartment[0]['name'] ?? '';
            $item['asset_code'] =  'ABC123';
            $item['handover_record_code'] = 'ABC123';
            $item['seri_number'] = '123456';
            $item['asset_supplier_id'] = 'Công ty Beetech';
            $item['country_of_manufacture'] = 'Việt Nam';
            $item['manufacturer'] = 'BeeTech';
            $item['manufacturing_date'] = '31/10/2019';
            $item['asset_category'] = 'Máy chấm công';
            $item['purchase_date'] = '31/12/2022';
            $item['usage_date'] = '31/01/2023';
            $item['condition_id'] = $listAssetStatus[0]['name'] ?? '';
            $item['source_of_origin'] = $listSourceOfOrigin[0]['name'] ?? '';
            $item['premises'] = $listPremises[0]['name'] ?? '';
            $item['original_price'] = '123456789';
            $item['residual_value'] = '123456789';
            $item['bidding_package'] = $listBiddingPackages[0]['name'] ?? '';
            $item['asset_user'] = $listUsers[0]['name'] ?? '';
            $item['location'] = 'Sảnh chung';
            $item['description'] = 'Máy chấm công BeeID';
            $item['note'] = 'Máy chấm công BeeID';

            $result[] = $item;
            $nameFile = trans('language.asset_title_export_template').'.xlsx';
            return Excel::download(new AssetMultiSheetExport($result, $dataTable), $nameFile);
        } catch (Exception $e) {
            Log::error("[AssetController][exportExcelTemplate]: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            abort(500);
        }
    }

    private function mapData($row, $propertyManagerAgencies, $types, $departments, $conditions, $sourceOfOrigin, $premises, $supplier, $biddingPackages, $users, $shortNames)
    {
        return [
            'name' => $row['1'] ?? null,
            'name_in_contract' => $row['2'] ?? null,
            'asset_short_name_id' => isset($row['3']) ? array_search($row['3'], $shortNames) : null,
            'management_unit_id' => isset($row['4']) ? array_search($row['4'], $propertyManagerAgencies) : null,
            'type_id' => isset($row['5']) ? array_search($row['5'], $types) : null,
            'department_id' => isset($row['6']) ? array_search($row['6'], $departments) : null,
            'asset_code' => $row['7'] ?? null,
            'handover_record_code' => $row['8'] ?? null,
            'seri_number' => $row['9'] ?? null,
            'asset_supplier_id' => isset($row['10']) ? array_search($row['10'], $supplier) : null,
            'country_of_manufacture' => $row['11'] ?? null,
            'manufacturer' => $row['12'] ?? null,
            'manufacturing_date' => $row['13'] ?? null,
            'asset_category' => $row['14'] ?? null,
            'purchase_date' => $row['15'] ?? null,
            'usage_date' => $row['16'] ?? null,
            'condition_id' => isset($row['17']) ? array_search($row['17'] , $conditions) : null,
            'source_of_origin' => isset($row['18']) ? array_search($row['18'] , $sourceOfOrigin) : null,
            'premises' => isset($row['19']) ? array_search($row['19'] , $premises) : null,
            'original_price' => $row['20'] ?? null,
            'residual_value' => $row['21'] ?? null,
            'bidding_package' => isset($row['22']) ? array_search($row['22'] , $biddingPackages) : null,
            'user_id' => isset($row['23']) ? array_search($row['23'] , $users) : null,
            'location' => $row['24'] ?? null,
            'description' => $row['25'] ?? null,
            'note' => $row['26'] ?? null,
        ];
    }

    private function readFile($file)
    {
        $time = time();
        Log::info('[AssetController][readFile] begin read file import at: ' . (time()-$time));
        $spreadsheet = IOFactory::load($file);
        $spreadsheet->setActiveSheetIndex(self::FIRST_SHEET);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestDataRow();
        $rows = [];
        $fetchData = [];
        $i = 0;
        $columnDate = ['N', 'P', 'Q'];
        // Header Master Define
        $headerMaster = $this->headerMaster();
        // Header Excel Format
        $headerExcels = [];


        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(FALSE);

            $cells = [];
            foreach ($cellIterator as $cell) {
                $valueCell = Helpers::trimSpaces($cell->getValue());
                $cells[] = $valueCell;

                // Check header from excels
                if ($i == 1) {
                    if(Helpers::trimSpaces($cell->getValue())) {
                        $headerExcels[] = Helpers::trimSpaces($cell->getValue());
                    }
                }
            }
            // Check matching position header excel with header master
            if ( $i == 1) {
                $checkHeaders = $this->checkHeaderMatching($headerMaster, $headerExcels);
                if (!$checkHeaders) {
                    throw new Exception(trans('message.import_asset_valid_header'), Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }

            // Break when exel > heightRows
            if ($i >= $highestRow) {
                break;
            }

            $i++;



            $filteredRowData = array_filter( array_map('trim', $cells));
            if(empty($filteredRowData)) {
                continue;
            }

            if($i > 3) {
                $rows[$i] = $cells;
            }


            if (count($rows) > config('app.max_import_asset')) {
                throw new Exception(trans('message.import_asset_max') , Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }
        $propertyManagerAgencies = $this->propertyManagerAgencies();
        $types = $this->types();
        $departments = $this->departments();
        $conditions = $this->conditions();
        $sourceOfOrigin = $this->sourceOfOrigin();
        $premises = $this->premises();
        $suppliers = $this->suppliers();
        $biddingPackages = $this->biddingPackages();
        $users = $this->users();
        $shortNames = $this->shortNames();

        foreach ($rows as $k => $row) {
            $fetchData[$k] = $this->mapData($row, $propertyManagerAgencies, $types, $departments, $conditions, $sourceOfOrigin, $premises, $suppliers, $biddingPackages, $users, $shortNames);
        }
        Log::info('[AssetController][readFile] end read file import at: ' . (time()-$time));
        return $fetchData;
    }

    /**
     * Matter Header Import
     * @return string[]
     */
    private function headerMaster() : array
    {
        $array = [
            trans('language.asset_name').'*',
            trans('language.name_in_contract'),
            trans('language.name_short'),
            trans('language.management_unit_id'),
            trans('language.type_asset'),
            trans('language.department_use_asset'),
            trans('language.asset_code'),
            trans('language.handover_record_code'),
            trans('language.seri_number'),
            trans('language.supplier'),
            trans('language.country_of_manufacture'),
            trans('language.manufacturer'),
            trans('language.manufacturing_date'),
            trans('language.asset_category'),
            trans('language.purchase_date'),
            trans('language.usage_date'),
            trans('language.condition'),
            trans('language.source_of_origin'),
            trans('language.premises'),
            trans('language.original_price'),
            trans('language.residual_value'),
            trans('language.bidding_package_asset'),
            trans('language.asset_user'),
            trans('language.asset_location'),
            trans('language.asset_description'),
            trans('language.asset_note')
        ];
        return $array;
    }

    /**
     * Check matching position header
     * @param $headerOrigial
     * @param $headerExcels
     * @return bool
     */
    protected function checkHeaderMatching($headerOrigial, $headerExcels)
    {
        if(count($headerOrigial) != count($headerOrigial)) {
            return false;
        }

        $arrDiff = array_diff($headerOrigial, $headerExcels);
        if (count($arrDiff) > 0) {
            return false;
        }
        return true;
    }

    /**
     * Import asset.
     *
     * @param ImportAssetRequest $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|true
     * @throws Throwable
     */
    public function import(ImportAssetRequest $request)
    {
        try {
            $file = $request->file('file_import');
            $rows = $this->readFile($file);
            list($flagError, $failures, $results) = $this->validateRows($rows);
            if ($flagError) {
                throw new ImportException(trans('message.import_asset_failed'), $failures, 422);
            }
            $chunkSize = 1000;
            $time = time();
            Log::info('[FirstSheetImport][collection] start import at: ' . $time);
            DB::beginTransaction();
            $rows = collect($results);
            $rows->chunk($chunkSize)->each(function ($chunk) {
                $data = $chunk->map(function($asset){
                    if (!empty($asset['manufacturing_date'])) {
                        $asset['manufacturing_date'] =  Carbon::createFromFormat('d/m/Y', $asset['manufacturing_date'])->format('Y-m-d');
                    } else {
                        $asset['manufacturing_date'] = null;
                    }
                    if (!empty($asset['purchase_date'])) {
                        $asset['purchase_date'] = Carbon::createFromFormat('d/m/Y', $asset['purchase_date'])->format('Y-m-d');
                    } else {
                        $asset['purchase_date'] = null;
                    }
                    if (!empty($asset['usage_date'])) {
                        $asset['usage_date'] = Carbon::createFromFormat('d/m/Y', $asset['usage_date'])->format('Y-m-d');
                    }  else {
                        $asset['usage_date'] = null;
                    }
                    return $asset;
                });
                Asset::insert($data->toArray());
            });
            DB::commit();
            Log::info('[FirstSheetImport][collection] end import at: ' . (time() - $time));
            session()->flash('status_succeed', trans('message.import_asset_success'));
            return true;
        } catch (ImportException $e) {
            $failures = $e->getErrors();
            $html = view('asset.component.model-import-error', compact('failures'))->render();
            return response()->json([
                'status' => true,
                'message' => trans('message.import_asset_failed'),
                'html' => $html,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }  catch (\Exception $e) {
            DB::rollBack();
            if ($e->getCode() === Response::HTTP_UNPROCESSABLE_ENTITY) {
                return response()->json([
                    'errors' => [
                        'max_row_import' => $e->getMessage()
                    ]
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            Log::error($e->getMessage() . $e->getLine() . $e->getFile());
            return back()->with([
                'status_succeed' => trans('message.import_asset_failed')
            ]);
        }
    }

    private function mapColumnByKey($key)
    {
        $columns = [
            'name' => 'B',
            'name_in_contract' => 'C',
            'asset_short_name_id' => 'D',
            'management_unit_id' => 'E',
            'type_id' => 'F',
            'department_id' => 'G',
            'asset_code' => 'H',
            'handover_record_code' => 'I',
            'seri_number' => 'J',
            'asset_supplier_id' => 'K',
            'country_of_manufacture' => 'L',
            'manufacturer' => 'M',
            'manufacturing_date' => 'N',
            'asset_category' => 'O',
            'purchase_date' => 'P',
            'usage_date' => 'Q',
            'condition_id' => 'R',
            'source_of_origin' => 'S',
            'premises' => 'T',
            'original_price' => 'U',
            'residual_value' => 'V',
            'bidding_package' => 'W',
            'user_id' => 'X',
            'location' => 'Y',
            'description' => 'Z',
            'note' => 'AA'
        ];
        return $columns[$key] ?? '';
    }

    private function validateRows($rows)
    {
        $failures = [];
        $rules = $this->rules();
        $flagError = false;
        $customAttributes = $this->customValidationAttributes();
        $results = [];
        foreach ( $rows as $key => $row) {
            $validator = Validator::make($row, $rules, [], $customAttributes);
            if ($validator->fails()) {
                $flagError = true;
                $errors = $validator->errors();
                foreach ($errors->getMessages() as $k => $v) {
                    $failures[$key][] = [
                        'column' => $this->mapColumnByKey($k),
                        'attribute' => $k,
                        'errors' => $v
                    ];
                }
            } else {
                $results[] = $row;
            }
        }
        return [$flagError, $failures, $results];
    }

    private function customValidationAttributes()
    {
        $array = [
            'name' => trans('language.asset_name'),
            'name_in_contract' => trans('language.name_in_contract'),
            'asset_short_name_id' => trans('language.name_short'),
            'management_unit_id' => trans('language.management_unit_id'),
            'type_id' => trans('language.type_asset'),
            'department_id' => trans('language.department_use_asset'),
            'asset_code' => trans('language.asset_code'),
            'handover_record_code' => trans('language.handover_record_code'),
            'seri_number' => trans('language.seri_number'),
            'asset_supplier_id' => trans('language.supplier'),
            'country_of_manufacture' => trans('language.country_of_manufacture'),
            'manufacturer' => trans('language.manufacturer'),
            'manufacturing_date' => trans('language.manufacturing_date'),
            'asset_category' => trans('language.asset_category'),
            'purchase_date' => trans('language.purchase_date'),
            'usage_date' => trans('language.usage_date'),
            'condition_id' => trans('language.condition'),
            'source_of_origin' => trans('language.source_of_origin'),
            'premises' => trans('language.premises'),
            'original_price' => trans('language.original_price'),
            'residual_value' => trans('language.residual_value'),
            'bidding_package' => trans('language.bidding_package_asset'),
            'user_id' => trans('language.asset_user'),
            'location' => trans('language.asset_location'),
            'description' => trans('language.asset_description'),
            'note' => trans('language.asset_note')
        ];
        return $array;
    }

    /**
     * Get propertyManagerAgencies.
     *
     * @return array
     */
    private function propertyManagerAgencies()
    {
        return  PropertyManagementAgency::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get types.
     *
     * @return array
     */
    private function types()
    {
        return PropertyType::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get departments.
     *
     * @return array
     */
    private function departments()
    {
        return Department::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get PropertyStatus.
     *
     * @return array
     */
    private function conditions()
    {
        return PropertyStatus::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get SourceOfOrigin.
     *
     * @return array
     */
    private function sourceOfOrigin(): array
    {
        return AssetSetting::query()->where('type', '=', AssetSetting::ASSET_FORMATION_SOURCE)->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get Premises
     *
     * @return array
     */
    private function premises(): array
    {
        return AssetSetting::query()->where('type', '=', AssetSetting::ASSET_BRANCH)->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get Suppliers
     *
     * @return array
     */
    private function suppliers(): array
    {
        return AssetSetting::suppliers()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get Bidding Packages
     *
     * @return array
     */
    private function biddingPackages(): array
    {
        return AssetSetting::query()->where('type', '=', AssetSetting::ASSET_BIDDING_PACKAGE)->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get list users
     *
     * @return array
     */
    private function users(): array
    {
        return User::query()
            ->select([
                DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as name'),
                'id'
            ])
            ->get()
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Get short name
     *
     * @return array
     */
    private function shortNames(): array
    {
        return AssetSetting::shortName()->get()->pluck('name', 'id')->toArray();
    }

    private function rules(): array
    {
        $today = \Carbon\Carbon::today()->format("d/m/Y");
        $user = Auth::user();
        $rules = [
            'name' => ['required', 'max:200', new NoEmojiRule()],
            'name_in_contract' => ['nullable', 'max:200', new NoEmojiRule()],
            'asset_short_name_id' => ['nullable',
                new NoEmojiRule(),
                Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_SHORT_NAME)],
            "type_id" => ['nullable', new NoEmojiRule(), 'exists:property_types,id'],
            "department_id" => ['nullable', new NoEmojiRule(), 'exists:departments,id'],
            "user_id" => ['nullable', new NoEmojiRule(), 'exists:users,id'],
            "asset_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "handover_record_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "seri_number" => ['nullable', 'max:50', new NoEmojiRule()],
            "asset_supplier_id" => ['nullable',
                new NoEmojiRule(),
                Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_SUPPLIER)],
            "country_of_manufacture" => ['nullable', 'max:200', new NoEmojiRule()],
            "manufacturer" => ['nullable', 'max:200', new NoEmojiRule()],
            "manufacturing_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            'asset_category' => ['nullable', 'max:200', new NoEmojiRule()],
            "purchase_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            "usage_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            "condition_id" => ['nullable', 'exists:property_status,id'],
            "source_of_origin" => [
                'nullable',
                new NoEmojiRule(),
                Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_FORMATION_SOURCE)
            ],
            "premises" => [
                'nullable',
                new NoEmojiRule(),
                Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_BRANCH)
            ],
            "location" => ['nullable', 'max:1000', new NoEmojiRule()],
            "description" => ['nullable', 'max:10000', new NoEmojiRule()],
            "note" => ['nullable', 'max:10000', new NoEmojiRule()],
            'original_price' => ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,3})?$/'],
            'residual_value'=> ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,3})?$/'],
            "bidding_package" => ['nullable', new NoEmojiRule(), Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)],
        ];

        $listManagementAgencyByUser = $this->assetManager->getListAssetManagementAgencyByUser();
        if ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && !in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $listManagementAgencyByUser)) {
            $rules['management_unit_id'] = ['nullable', new NoEmojiRule(), Rule::in($listManagementAgencyByUser)];
        } else {
            $rules['management_unit_id'] = ['nullable', new NoEmojiRule(), 'exists:property_management_agency,id'];
        }

        return $rules;
    }
        /**
     * @throws Throwable
     */
    public function index(Request $request)
    {
        // Get assets list
        $assetManager = new AssetManager();
        $orderBy = isset($request->sort) ? [$request->sort => $request->direction, 'id' => 'DESC'] : ['id' => 'DESC'];
        [$assets, $totalOriginPrice, $totalResidualValue, $existManagementAgency] = $assetManager->getAssetList($request->all(), self::PAGE_SIZE, $orderBy);
        $filterHtml = $assetManager->getFilterHtml($request, ['type', 'name', 'short_name',  'code', 'department', 'user', 'seri_number', 'supplier', 'status', 'from', 'to', 'bidding_package']);

        //Get list management agency
        $managementAgency = PropertyManagementAgency::select(['id', 'name'])->orderby('sort_order')->get();

        //Get list asset types
        $assetTypes = PropertyType::select(['id', 'name'])->orderby('sort_order')->get();

        //Get list department
        $listDepartment = Department::select(['id', 'name'])->orderByDesc('id')->get();

        //Get list status
        $listPropertyStatus = PropertyStatus::select(['id', 'name'])->orderby('sort_order')->get();

        //Get list asset suppliers
        $assetSuppliers = AssetSetting::suppliers()->select(['id', 'name'])->get();

        //Get list bidding package
        $listBiddingPackage = AssetSetting::query()
            ->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)
            ->select(['id', 'name'])
            ->orderby('sort_order')
            ->get();

        $assetShortNames = AssetSetting::shortName()->select(['id', 'name'])->get();

        $data = [
            'assets' => $assets,
            'listManagementAgency' => $managementAgency,
            'assetTypes' => $assetTypes,
            'listDepartment' => $listDepartment,
            'listPropertyStatus' => $listPropertyStatus,
            'listBiddingPackage' => $listBiddingPackage,
            'filterHtml' => $filterHtml,
            'totalOriginPrice' => $totalOriginPrice,
            'totalResidualValue' => $totalResidualValue,
            'existManagementAgency' => $existManagementAgency,
            'assetSuppliers' => $assetSuppliers,
            'assetShortNames' => $assetShortNames
        ];

        return view('asset.index', $data);
    }

    /**
     * Display a listing of the resource.
     * @param Request $request
     * @return array
     * @throws Throwable
     */
    public function listAssets(Request $request)
    {
        $assetManager = new AssetManager();
        $orderBy = isset($request->sort) ? [$request->sort => $request->direction, 'id' => 'DESC'] : ['id' => 'DESC'];
        [$assets, $totalOriginPrice, $totalResidualValue] = $assetManager->getAssetList($request->all(), self::PAGE_SIZE, $orderBy);
        $currentRouteName = Route::currentRouteName();

        $html = view('partials.list-assets', [
            'assets' => $assets,
            'currentRouteName' => $currentRouteName,
            'request' => $request,
            'totalOriginPrice' => $totalOriginPrice,
            'totalResidualValue' => $totalResidualValue
        ])->render();
        return [
            'status' => ResponseAlias::HTTP_OK,
            'html' => $html,
        ];
    }

    public function create()
    {
        $user = auth()->user();

        $departments = Department::select(['id', 'name'])->get();
        $managementAgency = PropertyManagementAgency::select(['id', 'name'])->orderby('sort_order')->get();
        $propertyTypes = PropertyType::select(['id', 'name'])->orderby('sort_order')->get();
        $propertyStatus = PropertyStatus::select(['id', 'name'])->orderby('sort_order')->get();
        $formationSource = AssetSetting::where('type', AssetSetting::ASSET_FORMATION_SOURCE)->orderby('sort_order')->get();
        $listPremises = AssetSetting::where('type', AssetSetting::ASSET_BRANCH)->orderby('sort_order')->get();
        $assetSuppliers = AssetSetting::suppliers()->select(['id', 'name'])->get();
        $listBiddingPackages = AssetSetting::where('type', AssetSetting::ASSET_BIDDING_PACKAGE)->orderby('sort_order')->get();
        $assetShortNames = AssetSetting::shortName()->select(['id', 'name'])->get();
        return view('asset.create', [
            'user' => $user,
            'departments' => $departments,
            'managementAgency' => $managementAgency,
            'propertyTypes' => $propertyTypes,
            'propertyStatus' => $propertyStatus,
            'formationSource' => $formationSource,
            'listPremises' => $listPremises,
            'assetSuppliers' => $assetSuppliers,
            'assetShortNames' => $assetShortNames,
            'listBiddingPackages' => $listBiddingPackages
        ]);
    }

    /**
     * Create asset
     * @param   StoreAssetRequest  $request
     */
    public function store(StoreAssetRequest $request)
    {
        DB::beginTransaction();
        try {
        $param = $request->all();
        $data = $this->assetManager->getDataInParam($param);
        $asset = Asset::create($data);
        $fileImage = !empty($param['asset_img']) ? $param['asset_img'] : [];
        $fileOther = !empty($param['asset_file']) ? $param['asset_file'] : [];
        [$fileImg, $removeFileImg] = $this->assetManager->filterFileValid($fileImage, 10, true);
        [$fileFilter, $removeFile] = $this->assetManager->filterFileValid($fileOther, 100);
        $fileRemove = array_merge($removeFileImg, $removeFile);
        foreach ($fileRemove as $value) {
            $this->deleteFile($value);
        }
        $listFile = array_merge($fileFilter, $fileImg);
        // Save attachments
        if (!empty($listFile)) {
            $destinationPath = str_replace(['{asset_id}'], [$asset->id], ASSET_ATTACHMENT_DIR) . '/';
            $this->attachmentManager->saveAttachments($asset, TaskAttachment::TYPE_ASSET, $listFile, $param["descriptionDocument"], $destinationPath);
        }

        // save log
        $fieldsChange = $this->assetManager->getFieldsChange($asset, true);
        event(new UpdateAsset(json_encode($fieldsChange),$asset->id));
        DB::commit();
        return redirect()->route('asset.create')->with([
            'status_succeed' => trans('message.create_asset_success'),
        ]);
    } catch (\Exception $e) {
        DB::rollBack();
        Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
        return back()->with([
            'status_failed' => trans('message.create_asset_failed')
        ]);
    }
    }


    /**
     * update asset
     */
    public function edit($id)
    {
        try {
            $asset = Asset::select([
                'assets.id',
                'assets.name as asset_name',
                'assets.name_in_contract',
                'assets.type_id',
                'assets.asset_short_name_id',
                'assets.management_unit_id',
                'assets.department_id',
                'assets.asset_code',
                'assets.handover_record_code',
                'assets.seri_number',
                'assets.asset_supplier_id',
                'assets.user_id as user_id',
                'assets.country_of_manufacture',
                'assets.manufacturer',
                'assets.asset_category',
                'assets.condition_id',
                'assets.source_of_origin',
                'assets.premises',
                'assets.original_price',
                'assets.residual_value',
                'assets.location as asset_location',
                'assets.description as asset_description',
                'assets.note as asset_note',
                'assets.bidding_package',
                DB::raw('DATE_FORMAT(assets.manufacturing_date, "%d/%m/%Y") as manufacturing_date'),
                DB::raw('DATE_FORMAT(assets.purchase_date, "%d/%m/%Y") as purchase_date'),
                DB::raw('DATE_FORMAT(assets.usage_date, "%d/%m/%Y") as usage_date')
            ])
            ->findOrFail($id);
            $departments = Department::select(['id', 'name'])->get();
            $managementAgency = PropertyManagementAgency::select(['id', 'name'])->orderby('sort_order')->get();
            $propertyTypes = PropertyType::select(['id', 'name'])->orderby('sort_order')->get();
            $propertyStatus = PropertyStatus::select(['id', 'name'])->orderby('sort_order')->get();
            $formationSource = AssetSetting::where('type', AssetSetting::ASSET_FORMATION_SOURCE)->orderby('sort_order')->get();
            $listPremises = AssetSetting::where('type', AssetSetting::ASSET_BRANCH)->orderby('sort_order')->get();
            $listBiddingPackages = AssetSetting::where('type', AssetSetting::ASSET_BIDDING_PACKAGE)->orderby('sort_order')->get();
            $listFileImg = [];
            $listFile = [];
            $taskFiles = TaskAttachment::leftJoin('users', 'task_attachments.created_by', 'users.id')
            ->where(function ($query) use ($id) {
                $query->where([
                    ['task_attachments.related_id', $id],
                    ['task_attachments.type', TaskAttachment::TYPE_ASSET]
                ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();
            $stringHelper = new StringHelper;
            foreach ($taskFiles as $file) {
                if($stringHelper->isImageFileByExtensionAll($file->file_name)){
                    $listFileImg[] = $file;
                }else{
                    $listFile[] = $file;
                }
            }
            $assetSuppliers = AssetSetting::suppliers()->select("id", "name")->get();
            $assetShortNames = AssetSetting::shortName()->select(['id', 'name'])->get();
            return view('asset.edit', [
                'assets' => $asset,
                'departments' => $departments,
                'managementAgency' => $managementAgency,
                'propertyTypes' => $propertyTypes,
                'propertyStatus' => $propertyStatus,
                'listFileImg' => $listFileImg,
                'listFile' => $listFile,
                'formationSource' => $formationSource,
                'listPremises' => $listPremises,
                'assetSuppliers' => $assetSuppliers,
                'listBiddingPackages' => $listBiddingPackages,
                'assetShortNames' => $assetShortNames
            ]);
        } catch (Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            abort (404);
        }
    }
    /**
     * details asset
     */
    public function detail($id)
    {
        try {
            $user = Auth::user();
            $isAdmin = $user->hasRole([Role::ROLE_ASSET_MANAGER, Role::ROLE_SYSTEM_MANAGER]);
            $asset = Asset::select([
                'assets.id',
                'assets.name as asset_name',
                'assets.name_in_contract',
                'property_types.name as type_name',
                'asset_short_names.name as name_short',
                'assets.management_unit_id',
                'assets.user_id',
                'property_management_agency.name as management_unit_name',
                'departments.name as department_name',
                'assets.asset_code',
                'assets.handover_record_code',
                'assets.seri_number',
                'asset_suppliers.name as supplier',
                DB::raw("CONCAT(users.first_name, ' ', users.last_name, ' | ', users.email) as full_name_email"),
                DB::raw('CASE WHEN premises_settings.type = '.AssetSetting::ASSET_BRANCH.' THEN premises_settings.name ELSE NULL END AS premises'),
                DB::raw('CASE WHEN formation_source_settings.type = '.AssetSetting::ASSET_FORMATION_SOURCE.' THEN formation_source_settings.name ELSE NULL END AS source_of_origin'),
                'assets.country_of_manufacture',
                'assets.manufacturer',
                'assets.asset_category',
                'property_status.name as property_name',
                'assets.original_price',
                'assets.residual_value',
                'assets.location as asset_location',
                'assets.description as asset_description',
                'assets.note as asset_note',
                'asset_bidding_packages.name as bidding_package_name',
                DB::raw('DATE_FORMAT(assets.manufacturing_date, "%d/%m/%Y") as manufacturing_date'),
                DB::raw('DATE_FORMAT(assets.purchase_date, "%d/%m/%Y") as purchase_date'),
                DB::raw('DATE_FORMAT(assets.usage_date, "%d/%m/%Y") as usage_date')
            ])
            ->leftJoin('property_types', 'assets.type_id', 'property_types.id')
            ->leftJoin('property_management_agency', 'assets.management_unit_id', 'property_management_agency.id')
            ->leftJoin('asset_settings as premises_settings', 'assets.premises', '=', 'premises_settings.id')
            ->leftJoin('asset_settings as formation_source_settings', 'assets.source_of_origin', '=', 'formation_source_settings.id')
            ->leftJoin('property_status', 'assets.condition_id', 'property_status.id')
            ->leftJoin('users', 'assets.user_id', 'users.id')
            ->leftJoin('asset_settings as asset_suppliers', 'assets.asset_supplier_id', 'asset_suppliers.id')
            ->leftJoin('asset_settings as asset_bidding_packages', 'assets.bidding_package', '=', 'asset_bidding_packages.id')
            ->leftJoin('asset_settings as asset_short_names', 'assets.asset_short_name_id', '=', 'asset_short_names.id')
            ->leftJoin('departments', 'assets.department_id', 'departments.id')
            ->getAssetByRole();


            $asset = $asset->findOrFail($id);
            if (empty($asset)) {
                abort(403);
            }
            $permissionEditAsset = $this->assetManager->checkRoleUpdate($asset);

            $listFileImg = [];
            $listFile = [];
            $taskFiles = TaskAttachment::leftJoin('users', 'task_attachments.created_by', 'users.id')
            ->where(function ($query) use ($id) {
                $query->where([
                    ['task_attachments.related_id', $id],
                    ['task_attachments.type', TaskAttachment::TYPE_ASSET]
                ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();
            $histories = $this->assetManager->getListLogs($id);
            $stringHelper = new StringHelper;
            foreach ($taskFiles as $file) {
                if($stringHelper->isImageFileByExtensionAll($file->file_name)){
                    $listFileImg[] = $file;
                }else{
                    $listFile[] = $file;
                }
            }
            $asset->asset_description = nl2br($asset->asset_description);
            $asset->asset_note = nl2br($asset->asset_note);
            return view('asset.detail', [
                'assets' => $asset,
                'listFileImg' => $listFileImg,
                'listFile' => $listFile,
                'histories' => $histories,
                'isAdmin'=> $isAdmin,
                'permissionEditAsset' => $permissionEditAsset
            ]);
        } catch (Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            abort (404);
        }
    }
    /**
     * Update asset
     * @param   StoreAssetRequest  $request
     */
    public function update(StoreAssetRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $param = $request->all();
            $data = $this->assetManager->getDataInParam($param);
            $listFileRemove = !empty($request->input('file_remove')) ? $request->input('file_remove') : [];
            $asset = Asset::find($id);
            foreach ($data as $key => $value) {
                $asset->$key = $value;
            }
            // save log
            $fieldsChange = $this->assetManager->getFieldsChange($asset);
            $fileImage = !empty($param['asset_img']) ? $param['asset_img'] : [];
            $fileOther = !empty($param['asset_file']) ? $param['asset_file'] : [];
            [$fileImg, $removeFileImg] = $this->assetManager->filterFileValid($fileImage, 10, true);
            [$fileFilter, $removeFile] = $this->assetManager->filterFileValid($fileOther, 100);
            $fileRemove = array_merge($removeFileImg, $removeFile);
            foreach ($fileRemove as $value) {
                $this->deleteFile($value);
            }
            $listFile = array_merge($fileFilter, $fileImg);
            // Save attachments
            if (!empty($listFile)) {
                $destinationPath = str_replace(['{asset_id}'], [$asset->id], ASSET_ATTACHMENT_DIR) . '/';
                $this->attachmentManager->saveAttachments($asset, TaskAttachment::TYPE_ASSET, $listFile, $param["descriptionDocument"], $destinationPath);
            }
            $asset->save();
            $listIdRemove = [];
            foreach ($listFileRemove as $value) {
                $file = json_decode($value, true);
                $this->deleteFile($file['path']);
                $listIdRemove[] = [$file['id']];
            }
            if(!empty($fieldsChange)){
                event(new UpdateAsset(json_encode($fieldsChange), $asset->id));
            }
            if(!empty($listIdRemove)) {
                TaskAttachment::whereIn('id', $listIdRemove)
                                ->where('type', TaskAttachment::TYPE_ASSET)
                                ->where('related_id', $id)
                                ->delete();
            }
            DB::commit();
            return redirect()->route('asset.detail', ['id' => $id])->with([
                'status_succeed' => trans('message.update_asset_success'),
            ]);
        } catch (Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.update_asset_failed')
            ]);
        }
    }
    /**
     * Export asset
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        try {
            $assetManager = new AssetManager();
            $orderBy = isset($request->sort) ? [$request->sort => $request->direction, 'id' => 'DESC'] : ['id' => 'DESC'];
            [$assets] = $assetManager->getAssetList($request->all(), null, $orderBy, true);
            $assetExport = new AssetReportExport();
            [$filePath, $fileName] = $assetExport->exportInventoryReport($assets, $request->type, $request->department);
            $headers = [
                'Content-Type' => 'application/vnd.ms-excel',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"'
            ];
            return response()->download($filePath, $fileName, $headers)->deleteFileAfterSend(true);;
        } catch (Exception $e) {
            Log::error($e->getMessage() . $e->getLine() . $e->getFile());
            return back()->with([
                'status_failed' => trans('message.export_asset_failed')
            ]);
        }
    }
}
