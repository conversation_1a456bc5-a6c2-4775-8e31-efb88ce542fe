<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


/**
 * Class Challenge
 * @property integer $id
 * @property string $name
 * @property string $problem
 * @property integer $type
 */
class Challenge extends Model
{
    use SoftDeletes;

    public const PRACTICE_TYPE = 0;
    public const CHALLENGE_TYPE = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = ['id'];

    /**
     * Get the submissions for the challenge
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function submissions()
    {
        return $this->hasMany(Submission::class);
    }
}
