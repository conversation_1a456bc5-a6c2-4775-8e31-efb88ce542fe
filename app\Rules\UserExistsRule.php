<?php

namespace App\Rules;

use App\Logics\UserManager;
use App\User;
use Illuminate\Contracts\Validation\Rule;

class UserExistsRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $userManager = new UserManager();
        $user = $userManager->getUserByEmail($value);
        return !empty($user);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('validation.exists', ['attribute' => __('validation.attributes.contact.email_or_phone')]);
    }
}
