<?php

namespace App\Exports\Timekeeping;

use App\Enums\PersonnelEnum;
use App\Enums\WorkingTypeEnum;
use App\Logics\TimekeepingManager;
use App\Logics\WorkingTimeManager;
use App\Models\SpecialRequest;
use App\Models\UserLeaveDayLog;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use App\Models\Request as ModelsRequest;
use App\Models\SiteSetting;

class TimekeepingSheetExport implements WithHeadings, WithColumnWidths, WithStyles
{
    protected $sheetName;
    protected $attendance;
    protected $date;
    protected $staff;
    protected $idx;
    protected $holidays;
    protected $cashLunchLocations;
    protected $lunch_benefit;
    protected $notTimekeepingStillEatings;

    const LUNCH_BENEFIT = 30000;
    const LUNCH_BENEFIT_HN = 30000;
    const MIN_FIRST_MONTH_WORKING_HOURS = 112;
    const USERS_NOT_LUNCH = [16, 136, 138, 164, 166];
    const AMOUNT_OF_DEDUCTION_DEPENDS = 4400000;
    const USER_SALARY_NOT_DIFFERENT=[27, 68, 73];
    const OUTBOUND_PARKING = 6000;
    const MAX_SALARY_TTS_FULL_TIME_2 = 4000000;
    const MAX_SALARY_TTS_PART_TIME_2 = 2500000;
    const SOCIAL_INSURANCE_PAYMENT_LEVEL = 10.5;
    const SOCIAL_INSURANCE_FOREIGN_PAYMENT_LEVEL = 9.5;

    function __construct($sheetName, $attendance, $date, $staff, $idx, $holidays, $isBeeTechCompany, $cashLunchLocations, $notTimekeepingStillEatings) {
        $this->sheetName = $sheetName;
        $this->attendance = $attendance;
        $this->date = $date;
        $this->staff = $staff;
        $this->idx = $idx;
        $this->holidays = $holidays;
        $this->isBeeTechCompany = $isBeeTechCompany;
        $this->cashLunchLocations = $cashLunchLocations;
        $this->notTimekeepingStillEatings = $notTimekeepingStillEatings;
        // Check the employee's work location to calculate the lunch fee
        if($staff->work_place_id == 1){
            $this->lunch_benefit = self::LUNCH_BENEFIT_HN;
        }else{
            $this->lunch_benefit = self::LUNCH_BENEFIT;
        }
    }
     /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            "Ngày",
            "Check in",
            "Check out",
            "Thời gian",
            "Giờ phép đã sử dụng",
            "Đi muộn",
            "Về sớm",
            "Trợ cấp ăn trưa",
            "Thời gian OT tháng trước",
            "Trợ cấp ăn trưa tăng ca",
            "Ghi chú"
        ];
    }
    /**
     * @return string
     */
    public function columnWidths(): array
    {
        return [
            'A' => 25,
            'B' => 15,
            'C' => 15,
            'D' => 15,
            'E' => 20,
            'F' => 15,
            'G' => 15,
            'H' => 15,
            'I' => 25,
            'J' => 25,
            'K' => 35
        ];
    }
    /**
     * @return string
     */
    public function styles(Worksheet $sheet)
    {
        $sheet->setTitle($this->sheetName);

        $numDays = $this->date['numDays'];
        $month = $this->date['month'];
        $year = $this->date['year'];
        $lateJoinNumber = 0;

        $sheet->getStyle('A1:K1')->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'argb' => '3366FF',
                ],
            ],
            'font' => [
                'color' => [
                    'argb' => 'FFFFFF',
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));

        $sheet->getStyle('A1:K'.($numDays+8))->applyFromArray(array(
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));

        $sheet->getStyle('A1:J'.($numDays+24))->applyFromArray(array(
            'font' => [
                'size'  => 10,
                'name'  => 'Arial'
            ],
        ));

        $sheet->getStyle('K2:K' . $numDays)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $user_leave_days_logs = UserLeaveDayLog::select('user_id','leave_day','hours')
        ->where('user_id', $this->staff->id)
        ->whereRaw('MONTH(leave_day) ='. $month)
        ->whereRaw('YEAR(leave_day) =' .$year)
        ->orderBy('leave_day','ASC')
        ->get();
        $row_it = 0;
        $rowIt = 0;
        $attendance = $this->attendance;
        $timeKeepingManager = new TimekeepingManager();

        $checkUserWork = false;

        // special request salary
        $specialRequest = SpecialRequest::select(
            'id', 
            'month', 
            'user_id', 
            'freelancer', 
            'mentor',
            'bta_bonus')
        ->where('user_id', $this->staff->id)
        ->whereRaw('MONTH(month) ='. $month)
        ->whereRaw('YEAR(month) =' .$year)
        ->first();

        // join late but take leave
        $firstOfMonth = Carbon::createFromFormat('d/m/Y','01/'.$month.'/'.$year)->firstOfMonth();
        $endOfMonth = Carbon::createFromFormat('d/m/Y','01/'.$month.'/'.$year)->endOfMonth();
        $leave_days = ModelsRequest::select(
                'requests.id',
                'requests.content',
                'requests.created_by',
                'users.id as user_id'
        )->leftjoin('users', 'users.id', 'requests.created_by')
        ->where('users.id', $this->staff->id)
        ->where('requests.type', ModelsRequest::TYPE_VACATION)
        ->where('requests.status', ModelsRequest::STATUS_CONFIRM)
        ->where(function($query)  use ($firstOfMonth){
            $query->whereRaw('JSON_EXTRACT(content, "$.time_start")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"')
                ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"');
        })
        ->where(function($query)  use ($endOfMonth){
            $query->whereRaw('JSON_EXTRACT(content, "$.time_start")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"')
                ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"');
        })->get();

        // get all request collaborator if staff is collaborator
        $collaboratorRequest = [];
        if ($this->staff->personnel_status == COLLABORATOR) {
            $formatDateQuery = "{$year}-" . str_pad($month, 2, '0', STR_PAD_LEFT). '-%';
            $collaboratorRequest = ModelsRequest::select('requests.content')
            ->where('requests.type', ModelsRequest::TYPE_COLLABORATOR)
            ->where('requests.status', ModelsRequest::STATUS_CONFIRM)
            ->where('requests.created_by', $this->staff->id)
            ->whereRaw("JSON_SEARCH(JSON_EXTRACT(content, '$[*].collaborator_date'), 'one', ?) IS NOT NULL", [$formatDateQuery])
            ->get()->toArray();
        }

        $arrLeave = [];
        $arrEarly = [];
        foreach($leave_days as $leave_day){
            $time_end = Carbon::parse($leave_day->content['time_end']);
            $time_start = Carbon::parse($leave_day->content['time_start']);
            if($time_start->format('H:i:s') == MORNING_START || $time_start->format('H:i:s') == AFTERNOON_START){
                if($time_end->format('Y-m-d') <= $endOfMonth){
                    $arrLeave[$time_end->format('Y-m-d')] = $time_end->format('H:i:s');
                }
            }
            if($time_end->format('H:i:s') == MORNING_END || $time_end->format('H:i:s') == AFTERNOON_END){
                $arrEarly[$time_end->format('Y-m-d')] = $time_start->format('H:i:s');
            }
        }

        // Write all days in month to sheet
        for ($i = 1; $i <= $numDays; $i++) {
            $dateFomat = $year.'-'.$month.'-'.$i;
            $dateFomat = Carbon::createFromFormat('Y-m-d', $dateFomat);
            $newDate = $dateFomat->format('Y-m-d');
            $sheet->setCellValue('A'.($i+1), $newDate);
            $isLeaveDay = false;

            if ($dateFomat->dayOfWeek == 0) {
                $sheet->getStyle('A'.($i+1).':K'.($i+1).'')->applyFromArray(array(
                    'fill' => [
                        'fillType' => Fill::FILL_SOLID,
                        'startColor' => [
                            'argb' => 'C0C0C0',
                        ],
                    ],
                ));
            }
            if ($row_it < count($user_leave_days_logs)) {
                $row = $user_leave_days_logs[$row_it];
                if ($row['leave_day'] == $newDate) {
                    $row_it += 1;
                    $sheet->setCellValue('E'.($i+1), $row['hours']);
                    $isLeaveDay = true;
                }
            }
            if (isset($this->holidays[$newDate])) {
                $sheet->setCellValue('J'.($i+1), '=IF(I'.($i+1).' <= 4, "", IF(I'.($i+1).' < 8, '.($this->lunch_benefit/2).', '.($this->lunch_benefit).'))');
            }
            // Write Check in, Check out, Duration, Late join, Early leave to sheet
            if ($rowIt < count($attendance)) {
                $row = $attendance[$rowIt];
                if ($row['date'] == $newDate && $row['checked_in'] != null && $row['checked_out'] != null) {
                    $rowIt += 1;
                    if (isset($this->holidays[$newDate]) && $this->staff->personnel_status != COLLABORATOR) {
                        continue;
                    }
                    $duration = $timeKeepingManager->calcWorkingDuration($row, true);
                    if(isset($arrLeave[$newDate])){
                        $lateLunch = $timeKeepingManager->checkLateJoinLunch($duration['checked_in'], isset($arrLeave[$newDate]) ? strtotime($arrLeave[$newDate])+ PADDING_TIME : null);
                    }else{
                        $lateLunch = $duration['late_join'];
                    }

                    // Check if late join and not leave day set lateJoinNumber
                    if ($lateLunch && !$isLeaveDay) $lateJoinNumber++;

                    if(isset($arrEarly[$newDate])){
                        $avv = $timeKeepingManager->checkEarly($duration['checked_out'], isset($arrEarly[$newDate]) ? strtotime($arrEarly[$newDate]) : null);
                    }else{
                        $avv = $duration['early_leave'];
                    }
                    
                    $duration = [
                        'checked_in' => ($duration['checked_in'] == 0)?'':date('H:i:s', $duration['checked_in']),
                        'checked_out' => ($duration['checked_out'] == 0)?'':date('H:i:s', $duration['checked_out']),
                        'duration' => $duration['duration'],
                        'late_join' => $duration['late_join'],
                        'early_leave' => $duration['early_leave']
                    ];

                    if($duration['duration'] > 0){
                        $checkUserWork = true;
                    }

                    // Check if lateJoinNumber > 2 or leave day do caculate duration working max is afternoon end (17:30:00)
                    if ($lateJoinNumber > LATE_JOIN_EXCEED || $isLeaveDay) {
                        $timeCheckout = min(strtotime($duration['checked_out']), strtotime(AFTERNOON_END));
                        $duration['duration'] = (new WorkingTimeManager())->getWorkTimeInOneDay(
                            strtotime($duration['checked_in']),
                            $timeCheckout
                        );
                    }
                    $collaboratorAttendences = [];
                    // if staff is Collaborator do calculate duration
                    if ($this->staff->personnel_status == COLLABORATOR) {
                        // Check request checkout early in month
                        $fullAttendance = $timeKeepingManager->getTimeCheckedInAndCheckedOut($row);
                        $isHoliday = isset($this->holidays[$newDate]) || $dateFomat->isWeekend();
                        $collaboratorWorkingTime = (new WorkingTimeManager())->getCollaboratorWorkTimeInOneDay(
                            $year,
                            $month,
                            $newDate,
                            $this->staff->id,
                            $fullAttendance,
                            $collaboratorRequest,
                            $isHoliday
                        );
                        $duration['duration'] = $collaboratorWorkingTime['duration'];
                        $collaboratorAttendences = $collaboratorWorkingTime['attendences'] ?? [];
                    }

                    // Write to sheet
                    $sheet->setCellValue('B'.($i+1), $duration['checked_in']);
                    $sheet->setCellValue('C'.($i+1), $duration['checked_out']);
                    $sheet->setCellValue('D'.($i+1), $duration['duration']);
                    $sheet->setCellValue('F'.($i+1), ($lateLunch == true && $this->staff->personnel_status != COLLABORATOR)?'x':'');
                    $sheet->setCellValue('G'.($i+1), ($avv == true && $this->staff->personnel_status != COLLABORATOR)?'x':'');
                    $sheet->setCellValue('K'.($i+1), count($collaboratorAttendences) ? implode(', ', $collaboratorAttendences) : '');
                    if ($row['lunch'] == 1 || in_array($this->staff->id,self::USERS_NOT_LUNCH) || (in_array($this->staff->work_place_id, $this->cashLunchLocations))) {
                        // If total late join is > 4 or (has late join and total late join is < 3) set lunch_benefit_day = 0
                        // If total late join is > 2 or (duration < 8 and > 4), then lunch benefit is half, otherwise is full
                        $lunch_benefit_day = '=IF(F'.($numDays+2).'>4, 0, IF(AND(F'.($i+1).'="x", F'.($numDays+2).'<3), 0, IF(OR(G'.($i+1).'="x", F'.($numDays+2)
                                               .'>2, AND(D'.($i+1).' < 8, D'.($i+1).' > 4)), '.($this->lunch_benefit/2).', '.($this->lunch_benefit).')))';
                        if (
                            (
                                $this->staff->personnel_status != OFFICIAL &&
                                $this->staff->personnel_status != PROBATION &&
                                $this->staff->personnel_status != INTERN &&
                                $this->staff->personnel_status != FOREIGNER
                            ) ||
                            (
                                ($this->staff->salary_calculation_method == TTS_2_FULL_TIME ||
                                $this->staff->salary_calculation_method == TTS_2_PART_TIME) &&
                                $duration['duration'] < 8
                            ) ||
                            (
                                $duration['duration'] <= 4
                            )
                        ) {
                            $lunch_benefit_day = 0;
                        }
                        if (
                            $this->staff->salary_calculation_method == TTS_1 ||
                            (
                                ($this->staff->salary_calculation_method == TTS_2_FULL_TIME ||
                                $this->staff->salary_calculation_method == TTS_2_PART_TIME) &&
                                strtotime($this->staff->started_at_phase2) > strtotime($year . '-' . $month . '-' . $i)
                            )
                        ) {          
                            if(in_array($this->staff->work_place_id, $this->cashLunchLocations)){
                                $lunch_benefit_day = '';
                            }else{
                                $lunch_benefit_day = 0;
                            }
                        }
                        $sheet->setCellValue('H'.($i+1), $lunch_benefit_day);
                    }
                }else{
                    if(in_array($newDate, $this->notTimekeepingStillEatings)){
                        $lunch_benefit_day = 0;
                        $sheet->setCellValue('H'.($i+1), $lunch_benefit_day);
                    }
                }
            }else{
                if(in_array($newDate, $this->notTimekeepingStillEatings)){
                    $lunch_benefit_day = 0;
                    $sheet->setCellValue('H'.($i+1), $lunch_benefit_day);
                }
            }
        }

        // Write total to sheet
        $sheet->setCellValue('A'.($numDays+2), 'Tổng');
        $sheet->setCellValue('D'.($numDays+2), '=SUM(D2:D'.($numDays+1).')');
        $sheet->setCellValue('E'.($numDays+2), '=SUM(E2:E'.($numDays+1).')');
        $sheet->setCellValue('F'.($numDays+2), '=COUNTIF(F2:F'.($numDays+1).',"*")');
        $sheet->setCellValue('G'.($numDays+2), '=COUNTIF(G2:G'.($numDays+1).',"*")');
        $sheet->setCellValue('H'.($numDays+2), '=SUM(H2:H'.($numDays+1).')');
//        $sheet->getCell('I'.($numDays+2))->setFormulaAttributes(['t'=>'array']);
//        $sheet->getCell('I'.($numDays+2))->setValueExplicit('=SUM(IF(WEEKDAY(A2:A'.($numDays+1).',3)=5,I2:I'.($numDays+1).'*2,IF(WEEKDAY(A2:A'.($numDays+1).',3)=6,I2:I'.($numDays+1).'*2,I2:I'.($numDays+1).'*1.5)))',DataType::TYPE_FORMULA);
        $sheet->setCellValue('I'.($numDays+2), '=Lương!M'.($this->idx+2));
        $sheet->setCellValue('J'.($numDays+2), '=SUM(J2:J'.($numDays+1).')');
        // $pitFormula = '=SUM(IF(WEEKDAY(A{f}:A{l})=7,I{f}:I{l}*2,IF(WEEKDAY(A{f}:A{l})=1,I{f}:I{l}*2,I{f}:I{l}*1.5)))';
        // $pitFormula = str_replace(["{f}","{l}"], ["2",$num], $pitFormula);
        // $sheet->setCellValue('I'.($numDays+1), $pitFormula);
        $sheet->setCellValue('D'.($numDays+3), '=D'.($numDays+2).'+E'.($numDays+2));

        $sheet->setCellValue('I'.($numDays+3), 'Lương cơ bản tháng trước');
        $sheet->setCellValue('J'.($numDays+3), '=Lương!K'.($this->idx+2));
        $sheet->setCellValue('I'.($numDays+4), 'Số giờ công tháng trước');
        $sheet->setCellValue('J'.($numDays+4), '=Lương!L'.($this->idx+2));

        $sheet->getStyle('A'.($numDays+2).':I'.($numDays+8))->applyFromArray(array(
            'font' => [
                'bold' => true,
            ]
        ));
        $sheet->getStyle('D'.($numDays+3))->applyFromArray(array(
            'font' => [
                'color' => [
                    'argb' => 'FF0000',
                ],
            ]
        ));
        $sheet->getStyle('E'.($numDays+7))->applyFromArray(array(
            'font' => [
                'color' => [
                    'argb' => 'FF0000',
                ],
            ]
        ));
        $sheet->getStyle('A'.($numDays+31))->applyFromArray(array(
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));
        $sheet->getStyle('B'.($numDays+10).':B'.($numDays+27))->applyFromArray(array(
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ]
        ));
        $sheet->getStyle('I'.($numDays+3).':I'.($numDays+4))->applyFromArray(array(
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));
        $sheet->getStyle('J'.($numDays+3).':J'.($numDays+4))->applyFromArray(array(
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));
        $sheet->getStyle('B'.($numDays+10).':B'.($numDays+14))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('B'.($numDays+16).':B'.($numDays+26))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('B'.($numDays+28).':B'.($numDays+28))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('B'.($numDays+29).':B'.($numDays+29))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('B'.($numDays+30).':B'.($numDays+30))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('J'.($numDays+3).':J'.($numDays+3))
        ->getNumberFormat()
        ->setFormatCode('#,##0');

        $sheet->setCellValue('A'.($numDays+8), 'Số buổi của mentor');
        $sheet->setCellValue('D'.($numDays+8), isset($specialRequest) && $specialRequest->mentor != null ? $specialRequest->mentor : "0");
        
        if ($this->staff->salary_calculation_method == FULL_TIME_SALARY || $this->staff->salary_calculation_method == TTS_2_FULL_TIME) {
            $sheet->setCellValue('A'.($numDays+4), 'Số giờ công trong tháng');
            $sheet->setCellValue('D'.($numDays+4), ($numDays-count($this->holidays))*8);
        }
        elseif($this->staff->salary_calculation_method == TTS_2_PART_TIME){
            $sheet->setCellValue('A'.($numDays+4), 'Số giờ đăng ký');
            $sheet->setCellValue('D'.($numDays+4), ($numDays-count($this->holidays))*4) ;
        }
        elseif(isset($specialRequest) && $specialRequest->freelancer != null && $this->staff->salary_calculation_method == FREELANCER){
            $sheet->setCellValue('A'.($numDays+4), 'Số giờ công trong tháng');
            $sheet->setCellValue('D'.($numDays+4), $specialRequest->freelancer);
        }
        else {
            $sheet->setCellValue('A'.($numDays+4), 'Số giờ đăng ký');
            $sheet->setCellValue('D'.($numDays+4), round((float)$this->staff->hour_register, 2));
        }


        if ($this->staff->signed_at) {
            $sheet->setCellValue('A'.($numDays+5), 'Số giờ phép còn lại');
            $sheet->setCellValue('A'.($numDays+6), 'Tháng trước');
            $sheet->setCellValue('A'.($numDays+7), 'Tháng hiện tại');
            $remainingLeaveHours = round((float) $this->staff->remaining_leave_hours + (float) $this->staff->plus_leave_hours, 2);
            $sheet->setCellValue('E'.($numDays+6), $remainingLeaveHours);
            if ($month == RESET_LEAVE_DAY_YEARLY_MONTH) {
                $sheet->setCellValue('E'.($numDays+7), '=ROUND(MIN(E'.($numDays+6).'-E'.($numDays+2).','.RESET_LEAVE_DAY_YEARLY_HOURS.'),2)');
                $sheet->setCellValue('E'.($numDays+8), '=ROUND(E'.($numDays+6).'-E'.($numDays+2).'-E'.($numDays+7).',2)');
                $sheet->setCellValue('F'.($numDays+8), '(quy đổi)');
            } else {
                $sheet->setCellValue('E'.($numDays+7), '=E'.($numDays+6).'-E'.($numDays+2));
            }
        }

        if ($this->staff->salary_calculation_method == PART_TIME || $this->staff->salary_calculation_method == TTS_2_PART_TIME || $this->staff->salary_calculation_method == TTS_2_FULL_TIME ) {
            $sheet->setCellValue('A'.($numDays + 9), 'Phần trăm hoàn thành:');
            $sheet->setCellValue('B'.($numDays + 9), '=Lương!N'.($this->idx+2));
        }
        $sheet->setCellValue('A'.($numDays+10), 'Lương:');
        $sheet->setCellValue('B'.($numDays+10), '=Lương!F'.($this->idx+2));
        $sheet->setCellValue('A'.($numDays+11), 'Lương mentor theo buổi:');
        $sheet->setCellValue('B'.($numDays+11), '=Lương!P'.($this->idx+2));
        
        $sheet->setCellValue('A'.($numDays+12), "Lương tháng $month:");
        if(in_array($this->staff->id, self::USER_SALARY_NOT_DIFFERENT)){
            $sheet->setCellValue('B'.($numDays+12),'=B'.($numDays+10));
        }else{
            switch($this->staff->salary_calculation_method){
                case FULL_TIME_SALARY:
                    $sheet->setCellValue('B'.($numDays+12), '=D'.($numDays+3).'/D'.($numDays+4).'*B'.($numDays+10).'+IF('.'J'.($numDays+4).'<>0,'.'B'.($numDays+15).'*('.'J'.($numDays+3).'/J'.($numDays+4).'),0)');
                    break;
                case FREELANCER:
                    $sheet->setCellValue('B'.($numDays+12), '=D'.($numDays+4).'*B'.($numDays+10));
                    break;
                case MENTOR:
                    $sheet->setCellValue('B'.($numDays+12), '=D'.($numDays+4).'*B'.($numDays+10));
                    break;
                case TTS_2_FULL_TIME:
                    $sheet->setCellValue('B'.($numDays+12), '=MIN(IF(D'.($numDays+4).'=0,0,D'.($numDays+3).'/D'.($numDays+4).'*B'.($numDays+10).'),'. self::MAX_SALARY_TTS_FULL_TIME_2.')'.'*B'.($numDays+9));
                    break;
                case TTS_2_PART_TIME:
                    $sheet->setCellValue('B'.($numDays+12), '=MIN(IF(D'.($numDays+4).'=0,0,D'.($numDays+3).'/D'.($numDays+4).'*B'.($numDays+10).'),'. self::MAX_SALARY_TTS_PART_TIME_2.')'.'*B'.($numDays+9));
                    break;
                default: 
                    $sheet->setCellValue('B'.($numDays+12), '=IF(D'.($numDays+4).'=0,0,D'.($numDays+3).'/D'.($numDays+4).'*B'.($numDays+10).'*B'.($numDays+9).')'.'+IF('.'J'.($numDays+4).'<>0,'.'B'.($numDays+15).'*('.'J'.($numDays+3).'/J'.($numDays+4).'),0)');
                    break;
            }
        }
        if ($this->staff->signed_at) {
            $sheet->setCellValue('C'.($numDays+12), '="(Trừ "&E'.($numDays+2).'&" giờ phép)"');
        }
        $sheet->setCellValue('A'.($numDays+13), 'Trợ cấp ăn trưa:');
        if($this->staff->salary_calculation_method == "TTS giai đoạn 1"){
            $sheet->setCellValue('B'.($numDays+13), 0);
        }else{
            $sheet->setCellValue('B'.($numDays+13), '=H'.($numDays+2).'+J'.($numDays+2));
        }
        $sheet->setCellValue('A'.($numDays+14), 'Ăn trưa:');
        if(in_array($this->staff->id,self::USERS_NOT_LUNCH) || (in_array($this->staff->work_place_id, $this->cashLunchLocations))){
            $sheet->setCellValue('B'.($numDays+14), 0);
        }else{
            $sheet->setCellValue('B'.($numDays+14), '=COUNTIF(H2:H'.($numDays+1).',">="&0)*'.$this->lunch_benefit);
        }

        $sheet->setCellValue('A'.($numDays+15), 'Thời gian OT tháng trước:');
        $sheet->setCellValue('B'.($numDays+15), '=Lương!M'.($this->idx+2));
        $sheet->setCellValue('C'.($numDays+15), 'giờ');

        $sheet->setCellValue('A'.($numDays+16), 'Trợ cấp BTA:');
        $sheet->setCellValue('B'.($numDays+16), ('=B'.($numDays+11) .'*D'.($numDays+8)) . (isset($specialRequest) && $specialRequest->bta_bonus != null ? ('+'.$specialRequest->bta_bonus) : ""));
        if ($this->staff->signed_at && ($this->staff->ended_at == null || 
            strtotime($this->staff->ended_at) >= strtotime($year . '-' . $month . '-' . '01'))) {
            $sheet->setCellValue('A'.($numDays+17), 'Đóng bảo hiểm:');
            // If user is foreiner SOCIAL_INSURANCE is 9.5%, else 10.5%
            $isForeigner = $this->staff->personnel_status == FOREIGNER;
            $userInsuranceFee = (
                $this->staff->social_insurance_fee *
                (
                    $isForeigner
                        ? self::SOCIAL_INSURANCE_FOREIGN_PAYMENT_LEVEL
                        : self::SOCIAL_INSURANCE_PAYMENT_LEVEL
                )
            ) / 100;
            $userSignedAt = Carbon::parse($this->staff->signed_at);
            if ($userSignedAt->year == $year && $userSignedAt->month == $month) {
                $userInsuranceFee = '=IF(D'.($numDays+3).' > '.self::MIN_FIRST_MONTH_WORKING_HOURS.', '.$userInsuranceFee.', 0)';
            }
            $sheet->setCellValue('B'.($numDays+17), $userInsuranceFee);
            $sheet->setCellValue('C'.($numDays+17), '('.($isForeigner ? '9.5' : '10.5').'%, Công ty đóng 21.5%)');
        }
        $sheet->setCellValue('A'.($numDays+18), 'Giảm trừ gia cảnh bản thân:');
        $sheet->setCellValue('B'.($numDays+18), '11000000');
        $sheet->setCellValue('A'.($numDays+19), 'Giảm trừ phụ thuộc:');
        $sheet->setCellValue('B'.($numDays+19), self::AMOUNT_OF_DEDUCTION_DEPENDS*$this->staff->number_dependents);
        $sheet->setCellValue('A'.($numDays+20), 'Thuế TNCN phải nộp:');
        $pitFormular = '=IF(AND(B:i<=5000000,B:i>0), B:i*5%, IF(AND(B:i>5000000, B:i<=10000000), 250000+(B:i-5000000)*10%, '
        .'IF(AND(B:i>10000000, B:i<=18000000), 750000+(B:i-10000000)*15%,  IF(AND(B:i>18000000, B:i<=32000000), '
        .'1950000+(B:i-18000000)*20%,  IF(AND(B:i>32000000, B:i<=52000000), 4750000+(B:i-32000000)*25%,  '
        .'IF(AND(B:i>52000000, B:i<=80000000), 9750000+(B:i-52000000)*30%, '
        .'IF(B:i>80000000, 18150000+(B:i-80000000)*35%, 0)))))))';
        if ($this->staff->personnel_status == COLLABORATOR) {
            $pitFormular = '=IF(B'.($numDays+12).'>5000000, (B'.($numDays+12).'-5000000)*10%, 0)';
        }
        $sheet->setCellValue('B'.($numDays+20), str_replace(':i', (string)($numDays+23), $pitFormular));

        $sheet->setCellValue('A'.($numDays+21), 'Truy thu:');
        $sheet->setCellValue('B'.($numDays+21), '=Lương!AE'.($this->idx+2));
        $sheet->setCellValue('A'.($numDays+22), 'Truy lĩnh:');
        $sheet->setCellValue('B'.($numDays+22), '=Lương!Y'.($this->idx+2));

        $sheet->setCellValue('A'.($numDays+23), 'Thu nhập tính thuế:');
        $sheet->setCellValue('B'.($numDays+23), '=MAX(0, B'.($numDays+12) . '+B'.($numDays+16).'-IF(J'.($numDays+4).'<>0,J'.($numDays+3).'/J'.($numDays+4).'*B'.($numDays+15).'/2' .',0)' .'-B'.($numDays+17).'-B'.($numDays+18) .'-B'.($numDays+19).')');
            
        $parking_fee = SiteSetting::getPriceVehicleType($this->staff->vehicle_type);
        $sheet->setCellValue('A'.($numDays+24), 'Trợ cấp gửi xe:');
        $sheet->setCellValue('B'.($numDays+24), $this->staff->vehicle_type=='car'?$parking_fee/2:$parking_fee);
        $sheet->setCellValue('A'.($numDays+25), 'Phí gửi xe:');
        $sheet->setCellValue('B'.($numDays+25), $parking_fee);
        $sheet->setCellValue('A'.($numDays+26), 'Chuyển khoản:');
        if($this->staff->salary_calculation_method == TTS_1){
            $sheet->setCellValue('B'.($numDays+26), '=IF(B'.($numDays+12).'+B'.($numDays+13).'-B'.($numDays+14).'+B'.($numDays+16)
            .'-B'.($numDays+17).'-B'.($numDays+20).'+B'.($numDays+24).'-B'.($numDays+25).'+B'.($numDays+22).'-B'.($numDays+21).'<0,0,B'.($numDays+12).'+B'.($numDays+13).'-B'.($numDays+14).'+B'.($numDays+16)
            .'-B'.($numDays+17).'-B'.($numDays+20).'+B'.($numDays+24).'-B'.($numDays+25).'+B'.($numDays+22).'-B'.($numDays+21).')');
        }else{
            $sheet->setCellValue('B'.($numDays+26), '=B'.($numDays+12).'+B'.($numDays+13).'-B'.($numDays+14).'+B'.($numDays+16)
            .'-B'.($numDays+17).'-B'.($numDays+20).'+B'.($numDays+24).'-B'.($numDays+25).'+B'.($numDays+22).'-B'.($numDays+21));
        }
        if ($this->staff->signed_at) {
            $sheet->setCellValue('A'.($numDays+27), 'Số giờ phép được sử dụng:');
            $sheet->setCellValue('B'.($numDays+27), '=E'.($numDays+7));
            $sheet->setCellValue('C'.($numDays+27), 'giờ');
        }
        $sheet->setCellValue('A'.($numDays+28), 'Phụ cấp chức vụ:');
        $sheet->setCellValue('B'.($numDays+28), '=Lương!V'.($this->idx+2));

        $sheet->setCellValue('A'.($numDays+29), 'Truy thu tiền mặt:');
        if($this->staff->salary_calculation_method == TTS_1){
            $sheet->setCellValue('B'.($numDays+29), '=IF(B'.($numDays+12).'+B'.($numDays+13).'-B'.($numDays+14).'+B'.($numDays+16)
            .'-B'.($numDays+17).'-B'.($numDays+20).'+B'.($numDays+21).'-B'.($numDays+25).'+B'.($numDays+22).'-B'.($numDays+21).'<0,0-(B'.($numDays+12).'+B'.($numDays+13).'-B'.($numDays+14).'+B'.($numDays+16)
            .'-B'.($numDays+17).'-B'.($numDays+20).'+B'.($numDays+24).'-B'.($numDays+25).'+B'.($numDays+22).'-B'.($numDays+21).'),0)');
        }else{
            $sheet->setCellValue('B'.($numDays+29), 0);
        }
        
        $sheet->setCellValue('A'.($numDays+30), 'Truy lĩnh tiền mặt:');
        $sheet->setCellValue('B'.($numDays+30), '=Lương!Z'.($this->idx+2));

        $sheet->setCellValue('A'.($numDays+31), $this->staff->first_name.' '.$this->staff->last_name);
        $sheet->getCell("A".($numDays+31))->getHyperlink()->setUrl("sheet://'Lương'!A". ($this->idx + 2));

        if((($this->staff->ended_at != null && $checkUserWork == false) || $this->staff->personnel_status == PersonnelEnum::GUEST) && Carbon::parse($dateFomat)->format('Y-m') > Carbon::parse($this->staff['ended_at'])->format('Y-m'))
        {
            $sheet->setCellValue('B'.($numDays+31), '');
            if($this->staff->ended_at != null && $checkUserWork == false){
                $sheet->setSheetState(Worksheet::SHEETSTATE_HIDDEN);
            }
        }
        else
            $sheet->setCellValue('B'.($numDays+31), $this->staff->email);
    }
}