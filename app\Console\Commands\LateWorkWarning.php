<?php

namespace App\Console\Commands;

use App\Jobs\SendEmailLateWorkWarning;
use App\Logics\TimekeepingManager;
use App\Models\Attendance;
use App\Models\Request;
use App\Models\SiteSetting;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LateWorkWarning extends BaseCommand
{
    const NUMBER_WARNING_LATE_JOIN = 2;

    protected $morning_start, $morning_end, $afternoon_start, $afternoon_end;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:late_work_warning {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'late work warning';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle()
    {
        $listGoLateWarning = $this->getListGoLateWarning();
        $email = SiteSetting::where('id', SiteSetting::ADMIN_EMAIL_ID)->first();
        if($email && $listGoLateWarning != 0){
            $email = $email->value;

            $infoMail = [
                'list_go_late_warning' => $listGoLateWarning,
                'admin_email' => $email,
                'late_day' => Carbon::yesterday()->format('d/m/Y'),
            ];
            dispatch(new SendEmailLateWorkWarning($infoMail))->onQueue(QUEUE_MAIL);
        }
        return 0;
    }

    /**
     * get users arrived late yesterday and arrived later than twice a month
     * @return $users
     */
    private function getListGoLateWarning() {
        $timekeepingManager = new TimekeepingManager();
        $this->morning_start = date("H:i:s", $timekeepingManager->morning_start);
        $this->morning_end = date("H:i:s", $timekeepingManager->morning_end);
        $this->afternoon_start = date("H:i:s", $timekeepingManager->afternoon_start);
        $this->afternoon_end = date("H:i:s", $timekeepingManager->afternoon_end);

        $firstDayMonth = Carbon::now()->firstOfMonth()->format('Y-m-d');
        $today = Carbon::now()->format('Y-m-d');
        if($firstDayMonth == $today){
            $firstDayMonth = Carbon::now()->subMonths()->format('Y-m-d');
        }

        $attendances = Attendance::select(DB::raw('
                user_id,
                DATE( checked_at ) AS date,
                TIME( MIN( checked_at ) ) AS checked_in,
                TIME( MAX( checked_at ) ) AS checked_out
            '))
            ->groupBy(DB::raw('DATE(checked_at)'), 'user_id')
            ->orderBy(DB::raw('DATE(checked_at)'), 'DESC');

        // get users arrived late yesterday
        $usersLateYesterday = User::select('users.id')
            ->leftJoinSub(
                (clone $attendances)->whereDate(DB::raw('DATE(checked_at)'), Carbon::yesterday()->format('Y-m-d')),
                'attendances',
                function($leftJoin){
                    $leftJoin->on("attendances.user_id", "=", "users.id");
                }
            )
            ->whereNull('deleted_at')
            ->where(function($q){
                $q->where(function($q1){
                    $q1->where('attendances.checked_in', '>=', $this->morning_start)
                    ->where('attendances.checked_in', '<=', $this->morning_end);
                })
                ->orWhere(function($q2){
                    $q2->where('attendances.checked_in', '>=', $this->afternoon_start)
                    ->where('attendances.checked_in', '<=', $this->afternoon_end);
                });
            })
            ->pluck('users.id');

        // get users arrived late yesterday and arrived later than twice a month
        $users = User::select(
                'users.id',
                'users.email',
                'users.first_name',
                'users.last_name',
                DB::raw('COUNT(*) as total'),
                DB::raw("GROUP_CONCAT(CONCAT(DATE_FORMAT(attendances.date,'%d/%m/%Y'), ' ', attendances.checked_in ) SEPARATOR ', ') as dateTime")
            )
            ->leftJoinSub(
                (clone $attendances)->whereDate(DB::raw('DATE(checked_at)'), '>=', $firstDayMonth),
                'attendances',
                function($leftJoin){
                    $leftJoin->on("attendances.user_id", "=", "users.id");
                }
            )
            ->whereNull('deleted_at')
            ->whereIn('users.id', $usersLateYesterday)
            ->where(function($q){
                $q->where(function($q1){
                    $q1->where('attendances.checked_in', '>=', $this->morning_start)
                    ->where('attendances.checked_in', '<=', $this->morning_end);
                })
                ->orWhere(function($q2){
                    $q2->where('attendances.checked_in', '>=', $this->afternoon_start)
                    ->where('attendances.checked_in', '<=', $this->afternoon_end);
                });
            })
            ->groupBy('users.id')
            ->having('total', '>=', self::NUMBER_WARNING_LATE_JOIN)
            ->get();

        $checkUserHaveRequest = null;
        $arrUserLate = [];
        if($users->isNotEmpty()){
            foreach($users as $item) {
                $checkUserHaveRequest = Request::query()
                    ->join('users', 'users.id', 'requests.created_by')
                    ->select(['users.id','users.last_name','users.first_name','requests.name','requests.content', 'users.email'])
                    ->where('requests.created_by',$item->id)
                    ->where('requests.type',Request::TYPE_VACATION)
                    ->where('requests.status',Request::STATUS_CONFIRM)
                    ->get();
                $arrDateLate = explode(", ", $item->dateTime);
                $newArr[$item->id]['date'] = [];
                foreach ($arrDateLate as $date) {
                    $formatDate2 = Carbon::createFromFormat('d/m/Y H:i:s', $date)->format('d/m/Y');
                    $formatTime2 = Carbon::createFromFormat('d/m/Y H:i:s', $date)->format('H:i:s');
                    $formatDate3 = Carbon::createFromFormat('d/m/Y H:i:s', $date)->format('Y-m-d');
                    $newArr[$item->id]['date'][$formatDate2] = $formatDate2 . " " . $formatTime2;
                    if(!empty($checkUserHaveRequest)){
                        foreach ($checkUserHaveRequest as $item) {
                            if (Carbon::parse($item->content['time_start'])->format('Y-m-d') == $formatDate3) {
                                $time_end = Carbon::parse($item->content['time_end'])->format('H:i:s');
                                if($time_end >= $formatTime2){
                                    unset($newArr[$item->id]['date'][$formatDate2]);
                                }
                            }
                        }
                    }
                }
                if (count($newArr[$item->id]['date']) >= self::NUMBER_WARNING_LATE_JOIN && isset($newArr[$item->id]['date'][Carbon::yesterday()->format('d/m/Y')])) {
                    $newArr[$item->id] = [
                        'id' => $item->id,
                        'full_name' => $item->first_name . " " . $item->last_name,
                        'email' => $item->email,
                        'time_late' => count($newArr[$item->id]['date']),
                        'day_late' => implode(", ", array_values($newArr[$item->id]['date'])),
                    ];
                    $arrUserLate[] = $newArr[$item->id];
                }
            }
            return $arrUserLate;
        }
        return 0;
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->dailyAt('07:00');
    }

}
