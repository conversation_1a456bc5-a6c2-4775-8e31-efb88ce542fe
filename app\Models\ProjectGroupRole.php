<?php

namespace App\Models;

use App\Models\Model;
use App\Models\ProjectRole;

class ProjectGroupRole extends Model
{
    public const PROJECT_MANAGER = 1;
    public const LEADER = 2;
    public const MEMBER = 3;
    public const TOTAL_GROUP_ROLE = 3;

    public const PROJECT_MANAGER_COEFFICIENT = 5;
    public const LEADER_COEFFICIENT = 3;
    public const STAFF_COEFFICIENT = 1;

     /**
     * Get project role belong to project group role
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function projectRoles()
    {
    	return $this->hasMany('App\Models\ProjectRole', 'group_role_id');
    }
}
