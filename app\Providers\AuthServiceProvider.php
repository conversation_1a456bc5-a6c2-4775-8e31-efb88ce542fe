<?php

namespace App\Providers;

use App\Repositories\CachedClientRepository;
use App\Repositories\CachedTokenRepository;
use Laravel\Passport\Passport;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Laravel\Passport\TokenRepository;
use Laravel\Passport\ClientRepository;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();
        Passport::routes(
            function ($routeRegistrar) {
                $routeRegistrar->all();
                Route::post('/token', [
                    'uses' => 'AccessTokenController@issueToken',
                    'middleware' => 'throttle:10000,1',
                ]);
            }
        );
        Route::get('oauth/authorize', [
            'uses' => '\App\Http\Controllers\Auth\Oauth\AuthorizationController@authorize',
        ])->middleware(['web', 'auth']);
        Passport::tokensExpireIn(Carbon::now()->addDays(config('passport.access_token_ttl', 7)));

        // Bind the custom token repository
        $this->app->bind(TokenRepository::class, CachedTokenRepository::class);
        $this->app->bind(ClientRepository::class, CachedClientRepository::class);

        // Bind cache user provider
        Auth::provider('cache-user', function ($app, array $config) {
            return new CacheUserProvider($app['hash'], $config['model']);
        });
    }
}
