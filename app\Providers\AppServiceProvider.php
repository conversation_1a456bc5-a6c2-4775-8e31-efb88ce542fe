<?php

namespace App\Providers;

use App\Helpers\RequestHelper;
use App\Logics\SocketManager;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;


class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        if(config('app.env') !== 'local') {
            \URL::forceScheme('https');
        }

        \Laravel\Passport\Passport::ignoreMigrations();

        // Select multi date validator
        Validator::extend('multi_date_format', function($attribute, $value, $parameters, $validator) {

            $validator->addReplacer('multi_date_format', function($message, $attribute, $rule, $parameters){
                return str_replace([':format', ':separator'], $parameters, $message);
            });

            $separator = "/$parameters[1]/";
            $values = preg_split($separator, $value);
            foreach($values as $val) {
                $val = trim($val);
                // parse date with current format
                $parsed = date_parse_from_format($parameters[0], $val);

                // if value don't matches given format return false
                if ($parsed['error_count'] > 0 || $parsed['warning_count'] > 0) {
                    return false;
                }
            }

            // value did not match any of the provided formats, so return false=validation failed
            return true;
        });

        // Face image validator
        Validator::extend('image_only_one_face', function($attribute, $value, $parameters, $validator) {
            $response = (new RequestHelper())->sendRequest(
                API_VALIDATE_USER_FACE,
                'POST',
                [
                    [
                        'name' => 'face_image',
                        'contents' => file_get_contents(request()->face_image->getRealPath()),
                        'filename' => request()->face_image->getClientOriginalName()
                    ],
                ]
            );
            if ($response == null) {
                return false;
            }
            return true;
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(SocketManager::class, function ($app) {
            return new SocketManager();
        });
    }
}
