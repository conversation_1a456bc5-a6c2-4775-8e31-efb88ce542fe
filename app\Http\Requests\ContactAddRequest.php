<?php

namespace App\Http\Requests;

use App\Models\Contacts;
use App\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContactAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $typeRequest = (int)request()->input('type_request');
        $inputValue = request()->input('value');
        $rules = [
            'type_request' => [
                Rule::in([Contacts::TYPE_EMAIL, Contacts::TYPE_PHONE]),
                function($attribute, $value, $fail) use ($inputValue) {
                    $value = (int)$value;
                    if (empty($inputValue)) {
                        return $fail(trans('validation.required', ['attribute' => trans('validation.attributes.contact.email_or_phone')]));
                    }
                    if (!empty($value)) {
                        $user = User::query();
                        if ($value === Contacts::TYPE_EMAIL) {
                            $email_validation_regex = "/^[a-z0-9!#$%&'*+\\/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+\\/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/";
                            if (!preg_match($email_validation_regex, $inputValue)) {
                                return $fail(trans('validation.email'));
                            }
                            $checkEmailUser = $user->where('email', $inputValue)->first('id');
                            if (empty($checkEmailUser['id'])){
                                return $fail(trans('validation.exists', ['attribute' => 'Email']));
                            }
                            return true;
                        }
                        if ($value === Contacts::TYPE_PHONE) {
                            $phoneRegex = '/^0[0-9]{9}$/';
                            if (!preg_match($phoneRegex, $inputValue)) {
                                return $fail(trans('validation.regex', ['attribute' => trans('validation.attributes.contact.phone')]));
                            }
                            $checkPhoneUser = $user->where('phone', $inputValue)->first('id');
                            if (empty($checkPhoneUser['id'])){
                                return $fail(trans('validation.exists', ['attribute' => trans('validation.attributes.contact.phone')]));
                            }
                            return true;
                        }
                    }
                    return true;
                }
            ]
        ];

        if (empty($typeRequest)){
            $rules['value'] = 'required|exists:users,id';
        }

        return $rules;
    }
}
