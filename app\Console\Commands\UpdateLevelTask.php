<?php

namespace App\Console\Commands;

use App\Logics\TaskManager;
use App\Models\ProjectTask;
use Illuminate\Support\Facades\DB;

class UpdateLevelTask extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_level_task {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update column level task parent,task child';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }
    // Implement the command here
    protected function _handle() {
        try {
            DB::beginTransaction();
            // get list task
            $tasks = ProjectTask::select('id')
                ->whereNull('parent_task')
                ->get()->pluck('id')->toArray();
            ProjectTask::whereIn('id', $tasks)->update(['level' => 1]);
            (new TaskManager())->updateLevelTaskChild( $tasks, 1);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }
    }
}
