<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IpTimekeepingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'iptimekeeping' => 'array',
            'iptimekeeping.*' => 'required|ip|distinct',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'iptimekeeping.*.ip' => trans("validation.attributes.ip_add_format"),
            'iptimekeeping.*.distinct' => trans("validation.attributes.ip_distinct"),
        ];
    }
}
