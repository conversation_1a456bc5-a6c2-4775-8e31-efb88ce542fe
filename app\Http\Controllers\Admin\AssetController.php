<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\ImportException;
use App\Exports\Asset\AssetMultiSheetExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\ImportAssetRequest;
use App\Logics\AssetManager;
use App\Logics\DateFormatManager;
use App\Models\Asset;
use App\Models\Department;
use App\Models\PropertyManagementAgency;
use App\Models\PropertyStatus;
use App\Models\PropertyType;
use App\Rules\NoEmojiRule;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Throwable;

class AssetController extends Controller
{
    const FIRST_SHEET = 0;
    protected $dateFormatManager;

    public function __construct(DateFormatManager $dateFormatManager)
    {
        $this->dateFormatManager = $dateFormatManager;
    }

    /**
     * Delete asset
     * @param $id
     * @return array
     */
    public function delete($id)
    {
        $asset = Asset::find($id);

        if ($asset == null) {
            return [
                'status' => ResponseAlias::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.asset_not_exist'),
                ]
            ];
        }

        $asset->delete();

        return [
            'status' => ResponseAlias::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_asset_succeed') ,
            ],
            'redirect' => route('asset.index'),
        ];
    }

    /**
     * Download excel template
     * @return BinaryFileResponse|void
     */
    public function exportExcelTemplate()
    {
        try {
            $listManagementAgency = PropertyManagementAgency::select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();
            $listAssetType = PropertyType::select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();
            $listDepartment = Department::select('name')
                ->orderByDesc('id')
                ->get()
                ->toArray();
            $listAssetStatus = PropertyStatus::select('name')
                ->orderby('sort_order')
                ->get()
                ->toArray();
            $dataTable = ['table1' => $listManagementAgency, 'table2' => $listAssetType, 'table3' => $listDepartment, 'table4' => $listAssetStatus];

            $item['name'] = 'Máy chấm công BeeID';
            $item['name_in_contract'] = 'Máy chấm công BeeID';
            $item['name_short'] = 'Máy chấm công BeeID';
            $item['management_unit_id'] = $listManagementAgency[0]['name'] ?? '';
            $item['type_id'] = $listAssetType[0]['name'] ?? '';
            $item['department_id'] = $listDepartment[0]['name'] ?? '';
            $item['asset_code'] =  'ABC123';
            $item['handover_record_code'] = 'ABC123';
            $item['seri_number'] = '123456';
            $item['supplier'] = 'Công ty Beetech';
            $item['country_of_manufacture'] = 'Việt Nam';
            $item['manufacturer'] = 'BeeTech';
            $item['manufacturing_date'] = '31/10/2019';
            $item['asset_category'] = 'Máy chấm công';
            $item['purchase_date'] = '31/12/2022';
            $item['usage_date'] = '31/01/2023';
            $item['condition_id'] = $listAssetStatus[0]['name'] ?? '';
            $item['source_of_origin'] = 'Ngân sách công ty';
            $item['premises'] = 'BT';
            $item['original_price'] = '123456789';
            $item['residual_value'] = '123456789';
            $item['location'] = 'Sảnh chung';
            $item['description'] = 'Máy chấm công BeeID';
            $item['note'] = 'Máy chấm công BeeID';

            $result[] = $item;
            $nameFile = trans('language.asset_title_export_template').'.xlsx';
            return Excel::download(new AssetMultiSheetExport($result, $dataTable), $nameFile);
        } catch (Exception $e) {
            Log::error("[AssetController][exportExcelTemplate]: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            abort(500);
        }
    }

    private function mapData($row, $propertyManagerAgencies, $types, $departments, $conditions)
    {
        return [
            'name' => $row['1'] ?? null,
            'name_in_contract' => $row['2'] ?? null,
            'name_short' => $row['3'] ?? null,
            'management_unit_id' => isset($row['4']) ? array_search($row['4'], $propertyManagerAgencies) : null,
            'type_id' => isset($row['5']) ? array_search($row['5'], $types) : null,
            'department_id' => isset($row['6']) ? array_search($row['6'], $departments) : null,
            'asset_code' => $row['7'] ?? null,
            'handover_record_code' => $row['8'] ?? null,
            'seri_number' => $row['9'] ?? null,
            'supplier' => $row['10'] ?? null,
            'country_of_manufacture' => $row['11'] ?? null,
            'manufacturer' => $row['12'] ?? null,
            'manufacturing_date' => $row['13'] ?? null,
            'asset_category' => $row['14'] ?? null,
            'purchase_date' => $row['15'] ?? null,
            'usage_date' => $row['16'] ?? null,
            'condition_id' => isset($row['17']) ? array_search($row['17'] , $conditions) : null,
            'source_of_origin' => $row['18'] ?? null,
            'premises' => $row['19'] ?? null,
            'original_price' => $row['20'] ?? null,
            'residual_value' => $row['21'] ?? null,
            'location' => $row['22'] ?? null,
            'description' => $row['23'] ?? null,
            'note' => $row['24'] ?? null,
        ];
    }
    
    private function readFile($file)
    {
        $time = time();
        Log::info('[AssetController][readFile] begin read file import at: ' . (time()-$time));
        $spreadsheet = IOFactory::load($file);
        $spreadsheet->setActiveSheetIndex(self::FIRST_SHEET);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestRow = $worksheet->getHighestDataRow();
        $rows = [];
        $fetchData = [];
        $i = 0;
        $columnDate = ['N', 'P', 'Q'];
        // Header Master Define
        $headerMaster = $this->headerMaster();
        // Header Excel Format
        $headerExcels = [];


        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(FALSE);

            $cells = [];
            foreach ($cellIterator as $cell) {
                $valueCell = Helpers::trimSpaces($cell->getValue());
                $cells[] = $valueCell;

                // Check header from excels
                if ($i == 1) {
                    if(Helpers::trimSpaces($cell->getValue())) {
                        $headerExcels[] = Helpers::trimSpaces($cell->getValue());
                    }
                }
            }
            // Check matching position header excel with header master
            if ( $i == 1) {
                $checkHeaders = $this->checkHeaderMatching($headerMaster, $headerExcels);
                if (!$checkHeaders) {
                    throw new Exception(trans('message.import_asset_valid_header'), Response::HTTP_UNPROCESSABLE_ENTITY);
                }
            }
            
            // Break when exel > heightRows
            if ($i >= $highestRow) {
                break;
            }

            $i++;



            $filteredRowData = array_filter( array_map('trim', $cells));
            if(empty($filteredRowData)) {
                continue;
            }

            if($i > 3) {
                $rows[$i] = $cells;
            }
            

            if (count($rows) > config('app.max_import_asset')) {
                throw new Exception(trans('message.import_asset_max') , Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }
        $propertyManagerAgencies = $this->propertyManagerAgencies();
        $types = $this->types();
        $departments = $this->departments();
        $conditions = $this->conditions();
        
        foreach ($rows as $k => $row) {
            $fetchData[$k] = $this->mapData($row, $propertyManagerAgencies, $types, $departments, $conditions);
        }
        Log::info('[AssetController][readFile] end read file import at: ' . (time()-$time));
        return $fetchData;
    }

    /**
     * Matter Header Import
     * @return string[]
     */
    private function headerMaster() : array
    {
        $array = [
            trans('language.asset_name').'*',
            trans('language.name_in_contract'),
            trans('language.name_short'),
            trans('language.management_unit_id'),
            trans('language.type_asset'),
            trans('language.department_use_asset'),
            trans('language.asset_code'),
            trans('language.handover_record_code'),
            trans('language.seri_number'),
            trans('language.supplier'),
            trans('language.country_of_manufacture'),
            trans('language.manufacturer'),
            trans('language.manufacturing_date'),
            trans('language.asset_category'),
            trans('language.purchase_date'),
            trans('language.usage_date'),
            trans('language.condition'),
            trans('language.source_of_origin'),
            trans('language.premises'),
            trans('language.original_price'),
            trans('language.residual_value'),
            trans('language.asset_location'),
            trans('language.asset_description'),
            trans('language.asset_note')
        ];
        return $array;
    }

    /**
     * Check matching position header
     * @param $headerOrigial
     * @param $headerExcels
     * @return bool
     */
    protected function checkHeaderMatching($headerOrigial, $headerExcels)
    {
        if(count($headerOrigial) != count($headerOrigial)) {
            return false;
        }
        
        $arrDiff = array_diff($headerOrigial, $headerExcels);
        if (count($arrDiff) > 0) {
            return false;
        }
        return true;
    }

    /**
     * Import asset.
     * 
     * @param ImportAssetRequest $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|true
     * @throws Throwable
     */
    public function import(ImportAssetRequest $request)
    {
        try {
            $file = $request->file('file_import');
            $rows = $this->readFile($file);
            list($flagError, $failures, $results) = $this->validateRows($rows);
            if ($flagError) {
                throw new ImportException(trans('message.import_asset_failed'), $failures, 422);
            }
            $chunkSize = 1000;
            $time = time();
            Log::info('[FirstSheetImport][collection] start import at: ' . $time);
            DB::beginTransaction();
            $rows = collect($results);
            $rows->chunk($chunkSize)->each(function ($chunk) {
                $data = $chunk->map(function($asset){
                    if (!empty($asset['manufacturing_date'])) {
                        $asset['manufacturing_date'] =  Carbon::createFromFormat('d/m/Y', $asset['manufacturing_date'])->format('Y-m-d');
                    } else {
                        $asset['manufacturing_date'] = null;
                    }
                    if (!empty($asset['purchase_date'])) {
                        $asset['purchase_date'] = Carbon::createFromFormat('d/m/Y', $asset['purchase_date'])->format('Y-m-d');
                    } else {
                        $asset['purchase_date'] = null;
                    }
                    if (!empty($asset['usage_date'])) {
                        $asset['usage_date'] = Carbon::createFromFormat('d/m/Y', $asset['usage_date'])->format('Y-m-d');
                    }  else {
                        $asset['usage_date'] = null;
                    }
                    return $asset;
                });
                Asset::insert($data->toArray());
            });
            DB::commit();
            Log::info('[FirstSheetImport][collection] end import at: ' . (time() - $time));
            session()->flash('status_succeed', trans('message.import_asset_success'));
            return true;
        } catch (ImportException $e) {
            $failures = $e->getErrors();
            $html = view('asset.component.model-import-error', compact('failures'))->render();
            return response()->json([
                'status' => true,
                'message' => trans('message.import_asset_failed'),
                'html' => $html,
            ], Response::HTTP_UNPROCESSABLE_ENTITY);
        }  catch (\Exception $e) {
            DB::rollBack();
            if ($e->getCode() === Response::HTTP_UNPROCESSABLE_ENTITY) {
                return response()->json([
                    'errors' => [
                        'max_row_import' => $e->getMessage()
                    ]
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            Log::error($e->getMessage() . $e->getLine() . $e->getFile());
            return back()->with([
                'status_succeed' => trans('message.import_asset_failed')
            ]);
        }
    }

    private function mapColumnByKey($key)
    {
        $columns = [
            'name' => 'B',
            'name_in_contract' => 'C',
            'name_short' => 'D',
            'management_unit_id' => 'E',
            'type_id' => 'F',
            'department_id' => 'G',
            'asset_code' => 'H',
            'handover_record_code' => 'I',
            'seri_number' => 'J',
            'supplier' => 'K',
            'country_of_manufacture' => 'L',
            'manufacturer' => 'M',
            'manufacturing_date' => 'N',
            'asset_category' => 'O',
            'purchase_date' => 'P',
            'usage_date' => 'Q',
            'condition_id' => 'R',
            'source_of_origin' => 'S',
            'premises' => 'T',
            'original_price' => 'U',
            'residual_value' => 'V',
            'location' => 'W',
            'description' => 'X',
            'note' => 'Y'
        ];
        return $columns[$key] ?? '';
    }
    
    private function validateRows($rows)
    {
        $failures = [];
        $rules = $this->rules();
        $flagError = false;
        $customAttributes = $this->customValidationAttributes();
        $results = [];
        foreach ( $rows as $key => $row) {
            $validator = Validator::make($row, $rules, [], $customAttributes);
            if ($validator->fails()) {
                $flagError = true;
                $errors = $validator->errors();
                foreach ($errors->getMessages() as $k => $v) {
                    $failures[$key][] = [
                        'column' => $this->mapColumnByKey($k),
                        'attribute' => $k,
                        'errors' => $v
                    ];
                }
            } else {
                $results[] = $row;
            }
        }
        return [$flagError, $failures, $results];
    }

    private function customValidationAttributes()
    {
        $array = [
            'name' => trans('language.asset_name'),
            'name_in_contract' => trans('language.name_in_contract'),
            'name_short' => trans('language.name_short'),
            'management_unit_id' => trans('language.management_unit_id'),
            'type_id' => trans('language.type_asset'),
            'department_id' => trans('language.department_use_asset'),
            'asset_code' => trans('language.asset_code'),
            'handover_record_code' => trans('language.handover_record_code'),
            'seri_number' => trans('language.seri_number'),
            'supplier' => trans('language.supplier'),
            'country_of_manufacture' => trans('language.country_of_manufacture'),
            'manufacturer' => trans('language.manufacturer'),
            'manufacturing_date' => trans('language.manufacturing_date'),
            'asset_category' => trans('language.asset_category'),
            'purchase_date' => trans('language.purchase_date'),
            'usage_date' => trans('language.usage_date'),
            'condition_id' => trans('language.condition'),
            'source_of_origin' => trans('language.source_of_origin'),
            'premises' => trans('language.premises'),
            'original_price' => trans('language.original_price'),
            'residual_value' => trans('language.residual_value'),
            'location' => trans('language.asset_location'),
            'description' => trans('language.asset_description'),
            'note' => trans('language.asset_note')
        ];
        return $array;
    }

    /**
     * Get propertyManagerAgencies.
     *
     * @return array
     */
    private function propertyManagerAgencies()
    {
        return  PropertyManagementAgency::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get types.
     *
     * @return array
     */
    private function types()
    {
        return PropertyType::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get departments.
     *
     * @return array
     */
    private function departments()
    {
        return Department::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get PropertyStatus.
     *
     * @return array
     */
    private function conditions()
    {
        return PropertyStatus::query()->get()->pluck('name', 'id')->toArray();
    }

    private function rules(): array
    {
        $today = \Carbon\Carbon::today()->format("d/m/Y");
        return [
            'name' => ['required', 'max:200', new NoEmojiRule()],
            'name_in_contract' => ['nullable', 'max:200', new NoEmojiRule()],
            'name_short' => ['nullable', 'max:200', new NoEmojiRule()],
            "management_unit_id" => ['nullable', new NoEmojiRule(), 'exists:property_management_agency,id'],
            "type_id" => ['nullable', new NoEmojiRule(), 'exists:property_types,id'],
            "department_id" => ['nullable', new NoEmojiRule(), 'exists:departments,id'],
            "user_use_asset" => ['nullable', new NoEmojiRule(), 'exists:users,id'],
            "asset_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "handover_record_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "seri_number" => ['nullable', 'max:50', new NoEmojiRule()],
            "supplier" => ['nullable', 'max:1000', new NoEmojiRule()],
            "country_of_manufacture" => ['nullable', 'max:200', new NoEmojiRule()],
            "manufacturer" => ['nullable', 'max:200', new NoEmojiRule()],
            "manufacturing_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            'asset_category' => ['nullable', 'max:200', new NoEmojiRule()],
            "purchase_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            "usage_date" => ['nullable','date_format:d/m/Y', new NoEmojiRule(), 'before_or_equal:' .$today],
            "condition_id" => ['nullable', 'exists:property_status,id'],
            "source_of_origin" => ['nullable', 'max:1000', new NoEmojiRule()],
            "premises" => ['nullable', 'max:200', new NoEmojiRule()],
            "location" => ['nullable', 'max:1000', new NoEmojiRule()],
            "description" => ['nullable', 'max:10000', new NoEmojiRule()],
            "note" => ['nullable', 'max:10000', new NoEmojiRule()],
            'original_price' => ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,3})?$/'],
            'residual_value'=> ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,3})?$/'],
        ];
    }
    
}