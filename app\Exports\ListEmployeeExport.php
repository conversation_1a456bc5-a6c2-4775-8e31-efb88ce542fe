<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ListEmployeeExport implements FromCollection, WithHeadings, WithStyles, WithColumnFormatting, WithColumnWidths
{
    function __construct($result) {
        $this->result = $result;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    { 
        return (collect($this->result));
    }
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.number_order'),
            trans('language.full_name'),
            trans('language.check_name'),
            trans('language.email'),
            trans('language.personal_email'),
            trans('language.phone'),
            trans('language.gender'),
            trans('language.birthday'),
            trans('language.work_place'),
            trans('language.department'),
            trans('language.position'),
            trans('language.working_type'),
            trans('language.hometown'),
            trans('language.address'),
            trans('language.identity_card'),
            trans('language.id_issued_place'),
            trans('language.id_issued_at'),
            trans('language.banking_account'),
            trans('language.number_social_insurance'),
            trans('language.family_allowances'),
            trans('language.signed_at'),
            trans('language.started_at'),
            trans('language.ended_at')
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 40,
            'C' => 20,
            'D' => 30,
            'E' => 30,
            'F' => 15,
            'G' => 10,
            'H' => 15,
            'I' => 25,
            'J' => 30,
            'K' => 25,
            'L' => 20,
            'M' => 50,
            'N' => 50,
            'O' => 25,
            'P' => 30,
            'Q' => 15,
            'R' => 25,
            'S' => 20,
            'T' => 20,
            'U' => 15,
            'V' => 15,
            'W' => 15,
        ];
    }

    public function columnFormats(): array
    {
        return [
            'F' => NumberFormat::FORMAT_TEXT,
            'R' => '0',
            'O' => NumberFormat::FORMAT_TEXT,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:W1')->applyFromArray(array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));
        
        $num = count($this->result) + 1;
        $sheet->getStyle("A2:W$num")->applyFromArray(array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
        ));
        $alignRight = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT,
            ]  
        ];
        $sheet->getStyle("F2:F$num")->applyFromArray($alignRight);
        $sheet->getStyle("R2:R$num")->applyFromArray($alignRight);
        $sheet->getStyle("O2:O$num")->applyFromArray($alignRight);

    } 
}
