<?php

namespace App\Console\Commands;

use App\Enums\SocketEvent;
use App\Logics\ConversationManager;
use App\Logics\SocketManager;
use App\Models\Message;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;

class EmitEventSocketWhenReadMessage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:emit-event-socket-when-read-message';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command emit event socket when read message for all users';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('EmitEventSocketWhenReadMessage command started.');
        $tenantId = '';
        if (env('USE_TENANT', false)) {
            $tenantId = app(\Hyn\Tenancy\Environment::class)->tenant()->id;
            $tenantId = "tenant_{$tenantId}_";
        }
        $queueKey = "{$tenantId}read_message_queue";

        $entries = Redis::smembers($queueKey);
        Redis::del($queueKey);
        $conversationIdByUser = [];
        foreach ($entries as $entry) {
            [$userId, $conversationId] = explode(GROUP_CONCAT_SEPARATOR, $entry);
            if (!isset($conversationIdByUser[$userId])) {
                $conversationIdByUser[$userId] = [];
            }
            $conversationIdByUser[$userId][] = $conversationId;
        }
        $conversationManager = new ConversationManager();
        foreach ($conversationIdByUser as $userId => $conversationIds) {
            $conversation = $conversationManager->queryGetListConversationOfUser($userId, null, $conversationIds);
            $countMessagesUnread = $conversationManager->queryGetLastMessageInConversation($userId, $conversation)
                ->get();

            foreach ($countMessagesUnread as $conversation) {
                $socketManager = new SocketManager();
                $dataSocketUpdateConversation = [
                    'conversation_id' => (int)$conversation->conversation_id,
                    'message_count_unread' => (int)$conversation->message_count_unread,
                    'message_to_unread' => (int)$conversation->message_to_unread,
                    'receiver' => [(int)$userId],
                    'is_edit' => Message::NOT_EDITED,
                    'last_message_id_to' => (int)$conversation->last_message_id_to,
                ];
                $socketManager->emit(SocketEvent::UPDATE_CONVERSATION, $dataSocketUpdateConversation);
            }
        }
        Log::info('EmitEventSocketWhenReadMessage command completed.');
    }
}
