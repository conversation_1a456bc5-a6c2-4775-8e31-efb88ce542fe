<?php

namespace App\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use Kyslik\ColumnSortable\Sortable;
use Illuminate\Database\Eloquent\Model;
/**
 * Class ProjectSprint
 * @property integer $id
 * @property string $name
 * @property integer $project_id
 * @property integer $created_by
 * @property date $started_at
 * @property date $ended_at
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */
class ProjectSprint extends Model
{
  use Sortable, SoftDeletes;
  protected $table = 'project_sprints';
  protected $guarded = [];
    
  const ACTIVE = 1;
  const INITIALIZE = 0;
  const COMPLETE = 2;
}
