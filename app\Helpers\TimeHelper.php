<?php

namespace App\Helpers;

use Carbon\Carbon;

class TimeHelper
{
    public static function hoursToMinutes($hours)
    {
        $exploded = explode('.', $hours);
        $minutes = (int) $exploded[0] * 60 + round((float)('0.' . $exploded[1]) * 60);
        return $minutes;
    }

    public static function decimalHoursToHoursAndMinutes($decimalHours): array
    {
        if (empty($decimalHours)) {
            return [0, 0, 'default'];
        }
        
        $hours = floor($decimalHours);
        $minutes = ($decimalHours - $hours) * 100;
        $minutes = round($minutes);
        $progress = static::progressLabelOverTimes($hours);
        return [$hours, $minutes, $progress];
    }
    
    public static function progressLabelOverTimes($hours): string
    {
        if ($hours >= 45) {
            $progress = 'danger';
        } elseif ($hours >= 30) {
            $progress = 'warning';
        } elseif ($hours >= 15) {
            $progress = 'success';
        } else {
            $progress = 'default';
        }
        return $progress;
    }
    
    public static function progressPercentageLabel($decimalHours): array
    {
        if (empty($decimalHours)) {
            return [
                'progress' => 'default',
                'maxPercentage' => 0,
            ];
        }
        
        $hours = floor($decimalHours);
        $progress = static::progressLabelOverTimes($hours);
        $maxPercentage = round(($decimalHours / 45) * 100);
        if ($maxPercentage > 100) {
            $maxPercentage = 100;
        }
        
        return [
            'progress' => $progress,
            'maxPercentage' => $maxPercentage,
        ];
    }
    
    public static function workTimes($minutes)
    {
        if (empty($minutes)) {
            return [0, 0, 'default'];
        }

        $time = Carbon::now()->startOfDay()->addMinutes($minutes);
        $hours = $time->hour;
        $minutes = $time->minute;
        $progress = static::progressLabelOverTimes($hours);
        return [$hours, $minutes, $progress];
    }
}