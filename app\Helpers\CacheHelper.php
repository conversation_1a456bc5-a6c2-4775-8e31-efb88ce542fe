<?php
namespace App\Helpers;
use Closure;
use Illuminate\Config\Repository;
use Illuminate\Support\Facades\Cache;

class CacheHelper {
    const LAST_MESSAGE_SENT_AT = "last_message_sent_at:";

    /**
     * Get value prefix cache.
     * 
     * @return Repository|\Illuminate\Contracts\Foundation\Application|mixed
     */
    public static function getPrefixCache()
    {
        $prefix = config('cache.prefix');
        
        // If prefix is Closure, call to get real value
        if ($prefix instanceof Closure) {
            $prefix = $prefix();
        }
        
        return $prefix;
    }

    /**
     * Make cache key.
     * 
     * @param $parts
     * @return string
     */
    public static function makeCacheKey(...$parts)
    {
        return implode('', $parts);
    }

    /**
     * Marks message as sent, persists in Redis for 60 seconds.
     * 
     * @param $userId
     * @return void
     */
    public static function markMessageSent($userId)
    {
        $key = self::makeCacheKey(self::LAST_MESSAGE_SENT_AT, $userId);
        Cache::put($key, true, now()->addSeconds(60));
    }

    /**
     * Check if the user has sent a message within 60 seconds.
     * 
     * @param $userId
     * @return mixed
     */
    public static function hasRecentlySentMessage($userId)
    {
        $key = self::makeCacheKey(self::LAST_MESSAGE_SENT_AT, $userId);
        return Cache::has($key);
    }
}