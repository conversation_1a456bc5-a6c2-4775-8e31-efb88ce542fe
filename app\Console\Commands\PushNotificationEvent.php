<?php

namespace App\Console\Commands;

use App\Helpers\NotificationHelper;
use App\Models\Event;
use App\Models\Project;
use App\Models\UserDevice;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\DB;

class PushNotificationEvent extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:push_notification_before_the_meeting  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Push notification to firebase before the meeting';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        $events = Event::select(
            'events.name',
            'events.started_at',
            'events.ended_at',
            'events.location',
            'group_resources.name as group_resource',
            'resources.name as resource',
            'events.project_id',
            'projects.name as project_name',
            DB::raw('GROUP_CONCAT(user_devices.device_token SEPARATOR "'.GROUP_CONCAT_SEPARATOR.'") as tokens')
        )
            ->leftjoin('projects','projects.id','events.project_id')
            ->leftjoin('resources','resources.id','events.location')
            ->leftjoin('group_resources','group_resources.id','resources.group_id')
            ->rightJoin('event_participants','event_participants.event_id','events.id')
            ->rightJoin('user_devices','event_participants.user_id','user_devices.user_id')
            ->whereNull('events.deleted_at')
            ->where('user_devices.status', UserDevice::STATUS_ONLINE)
            ->where('events.type', Event::MEETING_TYPE)
            ->where(function ($query){
                $query->where(function ($subQuery){
                        $subQuery->where('events.repeat',Event::NO_REPEAT)
                            ->whereRaw('DATE_FORMAT(NOW(), "%Y-%m-%d %H:%i") = DATE_FORMAT(DATE_SUB(events.started_at, INTERVAL ' . TIME_PUSH_NOTIFICATION_BEFORE_MEETING . ' MINUTE), "%Y-%m-%d %H:%i")');
                    })
                    ->orwhere(function ($subQuery){
                        $subQuery->where('events.repeat',Event::REPEAT_DAILY)
                            ->where(function($q1){
                               $q1->whereDate('events.date_limit', '>=', DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'))
                                    ->orWhereNull('events.date_limit');
                            })
                            ->whereDate('events.started_at','<=',DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d %H:%i")'))
                            ->whereRaw('DATE_FORMAT(NOW(), "%H:%i") = DATE_FORMAT(DATE_SUB(events.started_at, INTERVAL ' . TIME_PUSH_NOTIFICATION_BEFORE_MEETING . ' MINUTE), "%H:%i")');
                    })
                    ->orwhere(function ($subQuery){
                            $subQuery->where('events.repeat',Event::REPEAT_EVERYDAY_OF_THE_WEEK)
                            ->where(function($q1){
                                $q1->whereDate('events.date_limit', '>=', DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'))
                                    ->orWhereNull('events.date_limit');
                            })
                            ->whereDate('events.started_at','<=',DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d %H:%i")'))
                            ->whereRaw('DATE_FORMAT(NOW(), "%H:%i") = DATE_FORMAT(DATE_SUB(events.started_at, INTERVAL ' . TIME_PUSH_NOTIFICATION_BEFORE_MEETING . ' MINUTE), "%H:%i")
                                and DAYOFWEEK(NOW()) != 1 and DAYOFWEEK(NOW()) != 7');
                    })
                    ->orwhere(function ($subQuery){
                            $subQuery->where('events.repeat',Event::REPEAT_EVERY_WEEKEND)
                            ->where(function($q1){
                                $q1->whereDate('events.date_limit', '>=', DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'))
                                    ->orWhereNull('events.date_limit');
                            })
                            ->whereDate('events.started_at','<=',DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d %H:%i")'))
                            ->whereRaw('DATE_FORMAT(NOW(), "%H:%i") = DATE_FORMAT(DATE_SUB(events.started_at, INTERVAL ' . TIME_PUSH_NOTIFICATION_BEFORE_MEETING . ' MINUTE), "%H:%i")
                                and (DAYOFWEEK(NOW()) = 1 or DAYOFWEEK(NOW()) = 7)');
                    })
                    ->orwhere(function ($subQuery){
                            $subQuery->where('events.repeat',Event::REPEAT_WEEKLY)
                            ->where(function($q1){
                                $q1->whereDate('events.date_limit', '>=', DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d")'))
                                    ->orWhereNull('events.date_limit');
                            })
                            ->whereDate('events.started_at','<=',DB::raw('DATE_FORMAT(NOW(), "%Y-%m-%d %H:%i")'))
                            ->whereRaw('DATE_FORMAT(NOW(), "%H:%i") = DATE_FORMAT(DATE_SUB(events.started_at, INTERVAL ' . TIME_PUSH_NOTIFICATION_BEFORE_MEETING . ' MINUTE), "%H:%i")
                                and DAYOFWEEK(events.started_at) = DAYOFWEEK(NOW())');
                    });
            })
            ->groupBy('events.id')
            ->get();
        if (count($events) > 0){
            foreach ($events as $event){
                $titleNotification = trans("language.notification.meeting_reminder");
                $notification = trans('language.notification.you_have_a_meeting') . ' ' . $event->name;
                if(!empty($event->project_id)){
                    $notification .= "\n" . trans('language.project') . ": " . $event->project_name;
                }
                if(!empty($event->started_at)){
                    $notification .= "\n" . trans('language.notification.from') . ' ' . date('d/m/Y'). ' '.date('H:i',strtotime($event->started_at));
                }
                if(!empty($event->ended_at)){
                    $time = strtotime($event->ended_at) - strtotime($event->started_at);
                    $end = date('d/m/Y H:i',strtotime(date('Y-m-d'). ' '.date('H:i',strtotime($event->started_at))) + $time);
                    $notification .= " " . trans('language.notification.to') . " " . $end;
                }
                if(!empty($event->location)){
                    $locationName  = $event->group_resource. ((isset($event->group_resource) && isset($event->resource) ) ? '-' : "").$event->resource;
                    $notification .= "\n" . trans('language.notification.location') . ": " . $locationName;
                }

                $notificationHelper = new NotificationHelper();
                if(!empty($event->tokens)){
                    $listToken = explode(GROUP_CONCAT_SEPARATOR,$event->tokens);
                    $listToken = array_chunk($listToken, 100);
                    // foreach ($listToken as $tokens){
                    //     $notificationHelper->sendNotificationDevice($tokens,
                    //         [
                    //             'title' => $titleNotification,
                    //             'content' => $notification,
                    //             'click_action' => route('projects.calendar', ['time'=>date('m/Y')]),
                    //         ]
                    //     );
                    // }
                }
            }
        }
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->everyMinute();
    }
}
