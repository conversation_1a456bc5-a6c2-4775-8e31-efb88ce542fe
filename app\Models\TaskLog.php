<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class TaskLog
 * @property integer $id
 * @property integer $task_id
 * @property string $log
 * @property integer $type
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */

class TaskLog extends Model
{
    public const NOTE = 1;
    public const ATTRIBUTE = 2;
    public const MEMBER = 3;
    public const DOCUMENT = 4;
    public const FIRST_ASSIGNED_USER = 5;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Scope check the user has permission to access the taskLog
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeCheckUserPermission($query, $userId) {
        return $query->leftJoin('project_tasks', 'task_logs.task_id', 'project_tasks.id')
            ->leftJoin('projects','projects.id','project_tasks.project_id')
            ->where(function($query) use ($userId){
                $query->whereIn('project_tasks.project_id',function($subQuery) use ($userId){
                    $subQuery->select('project_id')
                        ->distinct()
                        ->from('project_members')
                        ->where('user_id', $userId);
                })
                    ->orwhere('projects.public','=',1);
            });
    }

    public function logAttachments()
    {
        return $this->hasMany(TaskAttachment::class, 'related_id', 'id')
            ->leftJoin('users','task_attachments.created_by','users.id')
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->where('task_attachments.type', TaskAttachment::TYPE_TASK_LOG);
    }
}
