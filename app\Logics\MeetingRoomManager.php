<?php

namespace App\Logics;

use App\Models\Event;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MeetingRoomManager
{
    public function getListEventCalendar($params){
        if(isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y',$params['from']) > Carbon::createFromFormat('d/m/Y',$params['to'])){
            return [];
        }
        $eventCalendar = [];
        $events = Event::select(
                'events.started_at',
                'events.ended_at',
                'events.date_limit',
                'events.repeat',
                'group_resources.name as group_resource',
                'resources.name as resource',
                DB::raw('CONCAT_WS(" ",users.first_name,users.last_name) as full_name'),
            )
            ->leftjoin('users', 'users.id', 'events.created_by')
            ->leftjoin('resources','resources.id','events.location')
            ->leftjoin('group_resources','group_resources.id','resources.group_id')
            ->where(function ($query) use ($params){
                $query->where(function ($subquery) use ($params){
                        $subquery->where('repeat','!=',Event::NO_REPEAT);
                        if(isset($params['to'])){
                            $subquery->where('events.started_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                        }
                    })
                    ->orwhere(function ($subquery) use ($params){
                        $subquery->where('repeat',Event::NO_REPEAT);
                        if(isset($params['from'])){
                            $subquery->where(function($subquery)  use ($params){
                                $subquery->where('events.started_at','>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'))
                                    ->orwhere('events.ended_at', '>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'));
                            });
                        }
                        if(isset($params['to'])){
                            $subquery->where(function($subquery)  use ($params){
                                $subquery->where('events.started_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'))
                                    ->orwhere('events.ended_at', '<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                            });
                        }
                    });
            })
            ->where('events.type', Event::MEETING_TYPE)
            ->whereNotNull('group_resources.id')
            ->distinct();

        if(isset($params['group_resource'])){
            $events = $events->where('group_resources.id', $params['group_resource']);
            if(isset($params['resource'])){
                $events = $events->whereIn('resources.id', $params['resource']);
            }
        }

        $events = $events->get();
        foreach ($events as $event){
            $dataEvent = [
                'is_event' => 1,
                'start' => empty($event->started_at)?'':date('Y-m-d H:i:s',strtotime($event->started_at)),
                'end' => empty($event->ended_at)?'':date('Y-m-d H:i:s',strtotime($event->ended_at)),
                'startTime' => empty($event->started_at)?'':date('H:i:s',strtotime($event->started_at)),
                'endTime' => empty($event->ended_at)?'':date('H:i:s',strtotime($event->ended_at)),
                'startRecur' => empty($event->started_at)?'':date('Y-m-d',strtotime($event->started_at)),
                'location' => $event->group_resource. ((isset($event->group_resource) && isset($event->resource) ) ? '-' : "").$event->resource,
                'repeat' => $event->repeat,
                'started_at' => empty($event->started_at)?'':date('Y-m-d H:i:s',strtotime($event->started_at)),
                'ended_at' => empty($event->ended_at)?'':date('Y-m-d H:i:s',strtotime($event->ended_at)),
                'date_limit' => empty($event->date_limit)?'':date('Y-m-d',strtotime($event->date_limit)),
                'endRecur' => empty($event->date_limit)?'':date('Y-m-d',strtotime($event->date_limit)),
                'color' => '#8DC63F',
                'created_by' => $event->full_name,
            ];

            if($event->repeat == Event::NO_REPEAT){
                $dataEvent['endRecur'] = empty($event->started_at)?'':date('Y-m-d',strtotime($event->started_at.' + 1 days'));
            }else if($event->repeat == Event::REPEAT_EVERYDAY_OF_THE_WEEK){
                $dataEvent['daysOfWeek'] = ["1","2","3","4","5"];
            }else if($event->repeat == Event::REPEAT_EVERY_WEEKEND){
                $dataEvent['daysOfWeek'] = ["0","6"];
            }else if($event->repeat == Event::REPEAT_WEEKLY){
                $day = date("w", strtotime($event->started_at));
                $dataEvent['daysOfWeek'] = [$day];
            }
            if(!empty($event->started_at) && !empty($event->ended_at) && date('Y-m-d',strtotime($event->started_at))!=date('Y-m-d',strtotime($event->ended_at))&&$event->repeat == Event::NO_REPEAT) {
                $dataEvent['startTime'] = '';
                $dataEvent['endTime'] = '';
                $dataEvent['startRecur'] = '';
                $dataEvent['endRecur'] = '';
            }
            $eventCalendar[] = $dataEvent;
        }
        return $eventCalendar;
    }

}