<?php

namespace App\Jobs;

use Hyn\Tenancy\Models\Hostname;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Hyn\Tenancy\Contracts\Repositories\WebsiteRepository;
use Hyn\Tenancy\Environment;
use Illuminate\Support\Facades\App;
use App\Models\Language;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BaseQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $websiteId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($websiteId=null)
    {
        $this->websiteId = $websiteId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (env('USE_TENANT', false) && $this->websiteId != null) {
            // Switch to tenant
            $website = app(WebsiteRepository::class)->query()->where('id', $this->websiteId)->firstOrFail();
            app(Environment::class)->tenant($website);

            // Set cache prefix by tenant
            $prefix = 'tenant_' . $this->websiteId . '_cache_';
            config(['cache.prefix' => $prefix]);
            Log::info("Prefix cache websiteID: {$this->websiteId} -- {$prefix}");
        }
    }

    public function setLocaleByLanguageId($languageId)
    {
        $language = Language::find($languageId);
        $name = isset($language) ? $language->name : null;
        if ($name) {
            App::setLocale($name);
        }
    }

    /**
     * Get the current hostname
     *
     * @return void
     */
    protected function getHostname(): ?Hostname
    {
        if (empty($this->websiteId)) {
            return null;
        }

        return Hostname::where('website_id', $this->websiteId)->first();
    }

    /**
     * Get subdomain from fully qualified domain name.
     *
     * @param $fqdn
     * @return string|null
     */
    protected function getSubDomainFormHostname(): ?string
    {
        $hostname = $this->getHostname();
        if (empty($hostname)) {
            return null;
        }

        $fqdn = $hostname->fqdn;
        $baseDomain = config('app.url_base');
        return Str::before($fqdn, '.' . $baseDomain) ?: null;
    }
}
