<?php

namespace App\Helpers;

use App\Models\UserDevice;
use Exception;
use Illuminate\Support\Facades\Log;
use Google\Client;
use Google\Service\FirebaseCloudMessaging;
use GuzzleHttp\Client as GuzzleHttpClient;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Session\SessionManager;
use Illuminate\Session\Store;
use Carbon\Carbon;


class NotificationHelper
{
    const DEVICE_IOS = 'ios';
    const DEVICE_ANDROID = 'android';
    const DEVICE_WEB = 'web';
    const NOTIFICATION_TYPE_MESSAGE = 2;
    const NOTIFICATION_TYPE_CONTACT = 3;
    const NOTIFICATION_TYPE_CONVERSATION = 4;

    /*
     * Push Notification App by device_id
     */
    public static function sendNotificationDevice($token, $data = array())
    {
        try {
            $fcmUrl = 'https://fcm.googleapis.com/fcm/send';
            // For Android
            $data['sound'] = 'default';

            // For iOS
            $notification = [];
            $notification['title'] = $data['title'];
            $notification['body'] = $data['content'];
            $notification['avatar'] = $data['avatar'];
            $notification['click_action'] = $data['click_action'];
            $notification['sound'] = 'default';

            $apns['payload']['aps']['sound'] = 'default';
            $apn = json_decode(json_encode($apns), FALSE);
            $extraNotificationData = json_decode(json_encode($data), FALSE);
            $fcmNotification = [
                'registration_ids' => $token,
                'data' => $extraNotificationData,
                'notification' => json_decode(json_encode($notification), FALSE),
                'sound' => 'default',
                'apns' => $apn,
                "content_available"=> true
            ];

            $fcmNotification = json_decode(json_encode($fcmNotification), FALSE);

            $headers = array(
                'Authorization: key=' . config('constants.keyPushNotification'),
                'Content-Type: application/json',
            );
            Log::info('[NotificationHelper][sendNotificationDevice] start: data='. json_encode($fcmNotification));
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fcmNotification));
            $result = curl_exec($ch);
            curl_close($ch);
        } catch (Exception $e) {
            Log::error($e);
        }
    }
    
    /**
     * @param $arrUserID
     * @param $type
     * @return array
     */
    public static function getDeviceToken($arrUserID)
    {
        $devices = UserDevice::query()
            ->whereIn('user_devices.user_id', $arrUserID)
            ->select([
                'user_devices.user_id',
                'user_devices.device_information',
                'user_devices.device_token',
                'user_devices.status',
            ])
            ->orderBy('user_devices.user_id')
            ->get();
        $deviceToken = [];
        $deviceCheck = '';
        foreach ($devices as $key => $device) {
            $key = $device->user_id . '_' . $device->device_information;
            if ($deviceCheck !== $key) {
                $deviceCheck = $key;
            }
            $deviceToken[$key]['device_token'][] = $device->device_token;
        }
        return $deviceToken;
    }
    
    /**
     * send message for all device
     * @param $userID
     * @param $deviceToken
     * @param $data
     * @param $notification
     */
    public static function sendNotifyUserDevice($userID, $deviceToken, $data)
    {
        $deviceAndroid = $userID . '_' . self::DEVICE_ANDROID;
        $deviceIos = $userID . '_' . self::DEVICE_IOS;
        $deviceWeb = $userID . '_' . self::DEVICE_WEB;

        $deviceTokenAndroid = $deviceToken[$deviceAndroid]['device_token'] ?? [];
        $deviceTokenIos = $deviceToken[$deviceIos]['device_token'] ?? [];
        $deviceTokenWeb = $deviceToken[$deviceWeb]['device_token'] ?? [];

        // send notification android
        if(!empty($deviceTokenAndroid)){
            self::sendNotificationByDevice(
                $deviceTokenAndroid,
                $data,
                self::DEVICE_ANDROID,
                null
            );
        }

        // send notification ios
        if(!empty($deviceTokenIos)){
            self::sendNotificationByDevice(
                $deviceTokenIos,
                $data,
                self::DEVICE_IOS,
                null
            );
        }
            
        // send notification web
        if(!empty($deviceTokenWeb)){
            self::sendNotificationByDevice(
                $deviceTokenWeb,
                $data,
                self::DEVICE_WEB,
                null
            );
        }
    }
    
    /*
     * Push Notification App by device_id
    */
    public static function sendNotificationByDevice($token, $data = [], $device = self::DEVICE_ANDROID)
    {
        try {
            $fcmHelper = new NotificationHelper();
            $accessToken = $fcmHelper->getAccessToken();
            $time = time();


            Log::info('[NotificationHelper][sendNotificationDevice] start at: '. $time);
            if (!is_array($token)) {
                $token = [$token];
            }
            foreach ($token as $tk) {
                $fcmNotification = $fcmHelper->prepareDataPushNotification($tk, $data, $device);
                $fcmHelper->handlePushNotification($accessToken, $fcmNotification);
            }
            Log::info('[NotificationHelper][sendNotificationDevice] end at: '. (time() - $time));
            return true;
        } catch (Exception $e) {
            Log::error('[NotificationHelper][sendNotificationDevice] cause: ' . $e->getMessage());
        }
    }

    /**
     * Get OAuth 2 access token. Get from session or recall when session expires.
     * Token expires_in: 3599s
     *
     * @return Application|SessionManager|Store|mixed
     * @throws Exception
     */
    private function getAccessToken()
    {
        $accessToken = session('firebase_access_token');
        if (empty($accessToken)) {
            return $this->generateAccessToken();
        }

        $tokenExpiresIn = session('firebase_access_token_expires_in');
        if (empty($tokenExpiresIn)) {
            return $this->generateAccessToken();
        }

        $currentTime = Carbon::now()->format('Y-m-d H:i:s');
        if (strtotime($currentTime) > strtotime($tokenExpiresIn)) {
            return $this->generateAccessToken();
        }
        return $accessToken;
    }

    /**
     * Generate OAuth 2 access token.
     *
     * @return mixed
     * @throws Exception
     */
    private function generateAccessToken()
    {
        $serviceAccount = json_decode(file_get_contents(public_path(FIREBASE_CREDENTIALS_PATH)), true);
        $client = new Client();
        $client->setAuthConfig($serviceAccount);
        $client->addScope(FirebaseCloudMessaging::CLOUD_PLATFORM);
        $client->fetchAccessTokenWithAssertion();
        $token = $client->getAccessToken();
        $accessToken = $token['access_token'];
        $tokenExpiresIn = $token['expires_in'];
        session([
            'firebase_access_token' => $accessToken,
            'firebase_access_token_expires_in' => Carbon::now()->addSeconds($tokenExpiresIn)
        ]);
        return $accessToken;
    }

    /**
     * Add new extend key payload.
     *
     * @param $data
     * @param $payload
     * @return array
     */
    private function changeKeyDataExtendPayload($data, $payload): array
    {
        foreach ($data as $key => $value) {
            if ($key == 'title' || $key == 'body') {
                continue;
            }
            $newKey = 'gcm.notification.'.$key;
            if (is_array($value)) {
                $payload[$newKey] = json_encode($value);
            } else {
                $payload[$newKey] = (string) $value;
            }
        }
        return $payload;
    }

    /**
     * Convert init to string
     *
     * @param $data
     * @return array
     */
    private function convertDataIntToString($data): array
    {
        $result = [];
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = json_encode($value);
            } else {
                $result[$key] = (string) $value;
            }
        }
        return $result;
    }

     /**
     * Prepare data push notification.
     *
     * @param $token
     * @param $data
     * @param $device
     * @return array[]
     */
    private function prepareDataPushNotification($token, $data, $device): array
    {
        // Change key from "content" to "body"
        $data['body'] = $data['content'];
        $data['sound'] = 'default';

        $payload = [
            'aps' => [
                'content-available' => 1,
                'sound' => 'default'
            ],
        ];
        
        if (!empty($data['badge'])) {
            $data['badge'] = (int)$data['badge'];
            $payload['aps']['badge'] = $data['badge'];
        }
        
        $result = [
            'message' => [
                'token' => $token,
                'android' =>  [
                    'priority' => 'high'
                ],
                'apns' => [
                    'headers' => [
                        'apns-push-type' => 'alert',
                        'apns-priority' => '10'
                    ],
                    'payload' => $payload
                ]
            ]
        ];

        if ($device !== self::DEVICE_WEB) {
            $result['message']['notification'] = [
                'title' => $data['title'],
                'body' => $data['body']
            ];
        }
        
        switch ($device) {
            case self::DEVICE_IOS:
                $result['message']['apns']['payload'] = $this->changeKeyDataExtendPayload($data, $payload);
                break;
            default:
                $result['message']['data'] = $this->convertDataIntToString($data);
                break;
        }

        return $result;
    }

    /**
     * Send notification use GuzzleHttp;
     *
     * @param $accessToken
     * @param $data
     * @return void
     */
    private function handlePushNotification($accessToken, $data): void
    {
        $headers = [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' =>  'application/json',
        ];

        $client = new GuzzleHttpClient;
        try {
            $fcmUrl = FCM;
            $request = $client->post($fcmUrl, [
                'headers' => $headers,
                "body" => json_encode($data),
            ]);

            Log::info("[FcmService] SENT", [$data]);

            $request->getBody();
            return;
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            if ($e->getResponse() && $e->getResponse()->getStatusCode() === 404) {
                Log::warning("[FcmService][handlePushNotification] 404 NOT FOUND", [$e->getMessage()]);
                return;
            }
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
