<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\AssetBiddingPackageRequest;
use App\Logics\AttachmentManager;
use App\Models\TaskAttachment;
use App\Traits\StorageTrait;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use App\Http\Requests\Admin\SiteSettingRequest;
use App\Models\Department;
use App\Models\Position;
use App\Models\ProjectRole;
use App\Models\TaskPriority;
use App\Models\TaskStatus;
use App\Models\TaskType;
use App\Models\ProjectGroupRole;
use App\Http\Requests\Admin\SiteSetting\ProjectRequest;
use App\Http\Requests\Admin\SiteSetting\CalendarRequest;
use App\Http\Requests\Admin\SiteSetting\GroupResourceRequest;
use App\Http\Requests\Admin\SiteSetting\PositionRequest;
use App\Http\Requests\Admin\SiteSetting\ProjectRoleRequest;
use App\Http\Requests\Admin\SiteSetting\TaskPriorityRequest;
use App\Http\Requests\Admin\SiteSetting\TaskStatusRequest;
use App\Http\Requests\Admin\SiteSetting\TaskTypeRequest;
use App\Http\Requests\Admin\SiteSetting\MinTimeProjectRequest;
use App\Http\Requests\Admin\SiteSetting\PropertyStatusRequest;
use App\Http\Requests\Admin\SiteSetting\PropertyTypeRequest;
use App\Http\Requests\Admin\SiteSetting\ResourceRequest;
use App\Http\Requests\AssetBaseRequest;
use App\Http\Requests\AssetResourceRequest;
use App\Http\Requests\DepartmentRequest;
use App\Http\Requests\IpTimekeepingRequest;
use App\Http\Requests\PropertyManagementRequest;
use App\Logics\AssetManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Response;
use App\Logics\SortableManager;
use App\Models\AssetSetting;
use App\Models\GroupResource;
use App\Models\PropertyType;
use App\Models\PropertyManagementAgency;
use App\Models\PropertyStatus;
use App\Models\Resource;
use App\Http\Requests\Admin\SiteSetting\AssetSupplierRequest;
use App\Http\Requests\Admin\SiteSetting\AssetShortNameRequest;
use Illuminate\View\View;

class SiteSettingController extends Controller
{
    use StorageTrait;
    const PAGINATION = 15;

    /**
     * $assetManager
     *
     * @var AssetManager
     */
    private $assetManager;

    /**
     * @var AttachmentManager
     */
    private $attachmentManager;


    public function __construct(
        AssetManager $assetManager,
        AttachmentManager $attachmentManager
    )
    {
        $this->assetManager = $assetManager;
        $this->attachmentManager = $attachmentManager;
    }
    /**
     * Get site setting view.
     *
     * @return view
     */
    public function index(){
        $siteSettings = SiteSetting::all();
        return view('admin.site_setting.rule', ['siteSettings'=> $siteSettings]);
    }

     /**
     * Update site setting.
     *
     * @param SiteSettingRequest $request, int $id
     *
     * @return redrect
     */
    public function update(CalendarRequest $request){
        if ($request->start_morning_time){
            $siteSetting = SiteSetting::find(SiteSetting::MORNING_START_ID);
            $siteSetting->value = $request->start_morning_time;
            $siteSetting->save();
        }
        if ($request->end_morning_time){
            $siteSetting = SiteSetting::find(SiteSetting::MORNING_END_ID);
            $siteSetting->value = $request->end_morning_time;
            $siteSetting->save();
        }
        if ($request->start_afternoon_time){
            $siteSetting = SiteSetting::find(SiteSetting::AFTERNOON_START_ID);
            $siteSetting->value = $request->start_afternoon_time;
            $siteSetting->save();
        }
        if ($request->end_afternoon_time){
            $siteSetting = SiteSetting::find(SiteSetting::AFTERNOON_END_ID);
            $siteSetting->value = $request->end_afternoon_time;
            $siteSetting->save();
        }

        $siteSetting = SiteSetting::find(SiteSetting::DAY_OFF_ID);
        if ($request->dayoff){
            $siteSetting->value = json_encode($request->dayoff);
        } else {
            $siteSetting->value = json_encode([]);
        }
        $siteSetting->save();
        return back()->with(['success'=> trans('language.success')]);
    }
    /**
     * Update site setting.
     *
     * @param Request $request, int $id
     *
     * @return redrect
     */
    public function updateMeeting(Request $request){
        if (isset($request->meeting)){
            $siteSetting = SiteSetting::find(SiteSetting::MEETING_ID);
            $siteSetting->value = $request->meeting;
            $siteSetting->save();
        }
        return back()->with(['success-meeting'=> trans('language.success')]);
    }

    public function updateMinTimeProject(MinTimeProjectRequest $request){
        if ($request->min_time_project){
            $siteSetting = SiteSetting::find(SiteSetting::MIN_TIME_PROJECT_ID);
            $siteSetting->value = $request->min_time_project;
            $siteSetting->save();
        }
        $createEvaluationWeek = SiteSetting::find(SiteSetting::CREATE_EVALUATION_WEEK_ID);
        $createEvaluationWeek->value = $request->display_task_id?(SiteSetting::CREATE_EVALUATION_WEEK):(SiteSetting::NOT_CREATE_EVALUATION_WEEK);
        $createEvaluationWeek->save();

        return back()->with(['success-evaluation'=> trans('language.success')]);
    }
    /**
     * Get list of departments
     *
     * @return View
     */
    public function department(){
        $allDepartments = Department::all();
        $departments = Department::orderBy('id', 'desc')->paginate(self::PAGINATION);
        return view('admin.site_setting.department', ['departments'=>$departments, 'allDepartments'=>$allDepartments]);
    }

    /**
     * Delete  department
     *
     * @param int $id
     */
    public function deleteDepartment($id){
        DB::beginTransaction();
        try {
            $department = Department::find($id);
            if ($department){
                $department->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_department_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * Edit  departments
     *
     * @param int $id, ProjectRequest $request
     */
    public function editDepartment($id, Request $request){
        DB::beginTransaction();
        try {
            $department = Department::find($id);
            if ($department){
                $department->name = $request->department_name;
                $department->department_code = $request->department_code;
                $department->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.department.index')
                ->with([
                    'status_succeed' => trans('message.department_edit_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

     /**
     * Create  departments
     *
     * @param ProjectRequest $request
     */
    public function createDepartment(DepartmentRequest $request){
        DB::beginTransaction();
        try {
            $department = new Department();
            $department->name = $request->department_name;
            $department->department_code = $request->department_code;
            $department->save();
            DB::commit();
            return redirect()
                ->route('admin.department.index')
                ->with([
                    'status_succeed' => trans('message.department_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Get list of resources
     *
     * @return View
     */
    public function resources(){
        $allGroupResources = GroupResource::all();
        $groupResources = GroupResource::paginate(self::PAGINATION);
        return view('admin.site_setting.resource', ['groupResources'=>$groupResources, 'allGroupResources'=>$allGroupResources]);
    }
    /**
     * Create group resource
     *
     * @param ProjectRequest $request
     */
    public function createGroupResources(GroupResourceRequest $request){
        DB::beginTransaction();
        try {
            $groupResources = new GroupResource();
            $groupResources->name = $request->name;
            $groupResources->save();
            DB::commit();
            return redirect()
                ->route('admin.resources.index')
                ->with([
                    'status_succeed' => trans('message.group_resource_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.resources.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Edit group resource
     *
     * @param int $id, ProjectRequest $request
     */
    public function editGroupResources($id, GroupResourceRequest $request){
        DB::beginTransaction();
        try {
            $groupResources = GroupResource::find($id);
            if ($groupResources){
                $groupResources->name = $request->name;
                $groupResources->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.resources.index')
                ->with([
                    'status_succeed' => trans('message.group_resource_edit_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.resources.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Delete group resource
     *
     * @param int $id
     */
    public function deleteGroupResources($id){
        DB::beginTransaction();
        try {
            $groupResources = GroupResource::find($id);
            if ($groupResources){
                $groupResources->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_group_resource_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }
    /**
     * create resources
     *
     * @param ProjectRequest $request, int $departmentId
     */
    public function createResource(ResourceRequest $request, $groupId){
        DB::beginTransaction();
        try {
            $resource = new Resource();
            $resource->name = $request->name;
            $resource->type = $request->type;
            $resource->group_id = $groupId;
            $resource->save();
            DB::commit();
            return redirect()
                ->route('admin.resources.index')
                ->with([
                    'status_succeed' => trans('message.resource_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.resources.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Edit resource
     *
     * @param int $id, PositionRequest $request
     */
    public function editResource($id, ResourceRequest $request){
        DB::beginTransaction();
        try {
            $resource = Resource::find($id);
            if ($resource){
                $resource->name = $request->name;
                $resource->group_id = $request->groupId;
                $resource->type = $request->type;
                $resource->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.resources.index')
                ->with([
                    'status_succeed' => trans('message.resource_edit_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.resources.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Delete resource
     *
     * @param int $id
     */
    public function deleteResource($id){
        DB::beginTransaction();
        try {
            $resource = Resource::find($id);
            if ($resource){
                $resource->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_resource_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }
    /**
     * Delete  position
     *
     * @param int $id
     */
    public function deletePosition($id){
        DB::beginTransaction();
        try {
            $position = Position::find($id);
            if ($position){
                $position->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_position_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * Edit position
     *
     * @param int $id, PositionRequest $request
     */
    public function editPosition($id, PositionRequest $request){
        DB::beginTransaction();
        try {
            $position = Position::find($id);
            if ($position){
                $position->name = $request->position_name;
                $position->department_id = $request->department;
                $position->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.department.index')
                ->with([
                    'status_succeed' => trans('message.position_edit_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

     /**
     * create position
     *
     * @param ProjectRequest $request, int $departmentId
     */
    public function createPosition(PositionRequest $request, $departmentId){
        DB::beginTransaction();
        try {
            $position = new Position();
            $position->name = $request->position_name;
            $position->department_id = $departmentId;
            $position->save();
            DB::commit();
            return redirect()
                ->route('admin.department.index')
                ->with([
                    'status_succeed' => trans('message.position_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }


    /**
     * Get list of project roles
     *
     * @return View
     */
    public function listProjectRole(){
        $projectRoles = ProjectRole::all();
        $projectGroupRoles = ProjectGroupRole::paginate(self::PAGINATION);
        $allProjectGroupRoles = ProjectGroupRole::all();
        return view('admin.site_setting.project_role', ['projectRoles'=>$projectRoles, 'projectGroupRoles'=>$projectGroupRoles, 'allProjectGroupRoles'=>$allProjectGroupRoles]);
    }

    /**
     * Delete project role
     *
     * @param int $id
     */
    public function deleteProjectRole($id){
       DB::beginTransaction();
        try {
            $projectRole = ProjectRole::find($id);
            if ($projectRole){
                $projectRole->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_position_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

     /**
     * Create project role
     *
     * @param ProjectRequest $request, int $groupId
     */
    public function createProjectRole(ProjectRoleRequest $request, $project_group_role){
        DB::beginTransaction();
        try {
            $projectRole = new ProjectRole();
            $projectRole->name = $request->name;
            $projectRole->group_role_id = $project_group_role;
            $projectRole->save();
            DB::commit();
            return redirect()
                ->route('admin.project.projectRole.list')
                ->with([
                    'status_succeed' => trans('message.position_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Create project role
     *
     * @param int $id, ProjectRoleRequest $request
     */
    public function editProjectRole($id, ProjectRoleRequest $request){
        DB::beginTransaction();
        try {
            $projectRole = ProjectRole::find($id);
            if ($projectRole){
                $projectRole->name = $request->name;
                $projectRole->group_role_id = $request->project_group_role;
                $projectRole->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.department.index')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }


     /**
     * Get list of task priorities
     *
     * @return View
     */
    public function listTaskPriority(){
        $taskPriorities = TaskPriority::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.task_priority', ['taskPriorities'=>$taskPriorities]);
    }

    /**
     * Delete task priority
     *
     * @param int $id
     */
    public function deleteTaskPriority($id){
        DB::beginTransaction();
        try {
            $taskPriority = TaskPriority::find($id);
            if ($taskPriority){
                $taskPriority->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_task_priority_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

      /**
      * Create task priority
      *
      * @param TaskPriorityRequest $request
      */
     public function createTaskPriority(TaskPriorityRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = TaskPriority::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $taskPriority = new TaskPriority();
            $taskPriority->name = $request->name;
            $taskPriority->color = $request->color;
            $taskPriority->sort_order = $sortOrder + 1 ;
            $taskPriority->save();
            DB::commit();
            return redirect()
                ->route('admin.project.taskPriority.list')
                ->with([
                    'status_succeed' => trans('message.task_priority_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskPriority.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

     /**
      * Update task priority
      *
      * @param int $id, TaskPriorityRequest $request
      */
     public function editTaskPriority($id, TaskPriorityRequest $request){
        DB::beginTransaction();
        try {
            $taskPriority =  TaskPriority::find($id);
            if ($taskPriority){
                $taskPriority->name = $request->name;
                $taskPriority->color = $request->color;
                $taskPriority->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.project.taskPriority.list')
                ->with([
                    'status_succeed' => trans('message.task_priority_edit_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskPriority.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

    /**
     * Sort task priority
     *
     * @param Request $request
     * @return mix
     */
    public function sortTaskPriority(Request $request) {
        $sort = (new SortableManager())->sort(TaskPriority::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }


    /**
     * Get list task statuses
     *
     * @return View
     */
    public function listTaskStatus(){
        $taskStatuses = TaskStatus::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.task_status', ['taskStatuses'=>$taskStatuses]);
    }

     /**
     * Delete task status
     *
     * @param int $id;
     */
    public function deleteTaskStatus($id){
        DB::beginTransaction();
        try {
            $taskStatus= TaskStatus::find($id);
            if ($taskStatus){
                $taskStatus->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_task_status_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

      /**
      * Create task status
      *
      * @param TaskStatusRequest $request
      */
     public function createTaskStatus(TaskStatusRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = TaskStatus::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $taskStatus = new TaskStatus();
            $taskStatus->name = $request->name;
            $taskStatus->color = $request->color;
            $taskStatus->sort_order = $sortOrder + 1;
            $taskStatus->save();
            DB::commit();
            return redirect()
                ->route('admin.project.taskStatus.list')
                ->with([
                    'status_succeed' => trans('message.task_status_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskStatus.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

     /**
      * Create task status
      *
      * @param int $id, TaskStatusRequest $request
      */
     public function editTaskStatus($id, TaskStatusRequest $request){
        DB::beginTransaction();
        try {
            $taskStatus =  TaskStatus::find($id);
            if ($taskStatus){
                $taskStatus->name = $request->name;
                $taskStatus->color = $request->color;
                $taskStatus->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.project.taskStatus.list')
                ->with([
                    'status_succeed' => trans('message.task_status_edit_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskStatus.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

     /**
     * Sort task status
     *
     * @param Request $request
     * @return mix
     */
    public function sortTaskStatus(Request $request) {
        $sort = (new SortableManager())->sort(TaskStatus::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * Get list task types
     *
     * @return View
     */
    public function listTaskType(){
        $taskTypes = TaskType::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.task_type', ['taskTypes'=>$taskTypes]);
    }


     /**
     * Delete task type
     *
     * @param int $id
     */
    public function deleteTaskType($id){
        DB::beginTransaction();
        try {
            $taskType= TaskType::whereNotIn('id',[TaskType::PROBLEM,TaskType::CHANGE])->find($id);
            if ($taskType){
                $taskType->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_task_type_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

      /**
      * Create task type
      *
      * @param TaskTypeRequest $request
      */
    public function createTaskType(TaskTypeRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = TaskType::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $taskType = new TaskType();
            $taskType->name = $request->name;
            $taskType->sort_order = $sortOrder + 1;
            $taskType->save();
            DB::commit();
            return redirect()
                ->route('admin.project.taskType.list')
                ->with([
                    'status_succeed' => trans('message.task_type_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskType.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

     /**
      * Create task type
      *
      * @param int $id, TaskTypeRequest $request
      */
    public function editTaskType($id, TaskTypeRequest $request){
        DB::beginTransaction();
        try {
            $taskType =  TaskType::whereNotIn('id',[TaskType::PROBLEM,TaskType::CHANGE])->find($id);
            if ($taskType){
                $taskType->name = $request->name;
                $taskType->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.project.taskType.list')
                ->with([
                    'status_succeed' => trans('message.task_type_edit_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.project.taskType.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

    /**
     * Sort task type
     *
     * @param Request $request
     * @return mix
     */
    public function sortTaskType(Request $request) {
        $sort = (new SortableManager())->sort(TaskType::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * show general rules
     *
     * @return view
     */
    public function generalRules() {
        $displayTaskProjectId = SiteSetting::find(SiteSetting::DISPLAY_TASK_PROJECT_ID);
        $ganttTaskColorMode = SiteSetting::find(SiteSetting::GANTT_TASK_COLOR_MODE);
        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $columnTaskViewValue = json_decode($columnTaskView->value);
        $result = [];
        foreach ($columnTaskViewValue as $item){
            $result[] = $item->attribute;
        }
        return view('admin.site_setting.general_rules', [
            'displayTaskProjectId' => $displayTaskProjectId,
            'ganttTaskColorMode' => $ganttTaskColorMode,
            'columnTaskView' => $result
        ]);
    }

    /**
     * update general rules
     *
     * @param Request $request
     * @return mix
     */
    public function generalRulesTaskUpdate(Request $request) {
        $displayTaskProjectId = SiteSetting::find(SiteSetting::DISPLAY_TASK_PROJECT_ID);
        if ($displayTaskProjectId) {
            $displayTaskProjectId->value = $request->display_task_id?(SiteSetting::SHOW_TASK_PROJECT_ID):(SiteSetting::HIDE_TASK_PROJECT_ID);
            $displayTaskProjectId->save();
        }
        return back()->with('success-task', trans('language.save_success'));
    }

    /**
     * update gantt task color
     *
     * @param Request $request
     * @return mix
     */
    public function generalRulesGanttUpdate(Request $request) {
        $ganttTaskColorMode = SiteSetting::find(SiteSetting::GANTT_TASK_COLOR_MODE);
        if ($ganttTaskColorMode) {
            $ganttTaskColorMode->value = $request->gantt_task_color_mode;
            $ganttTaskColorMode->save();
        }
        return back()->with('success-gantt', trans('language.save_success'));
    }
    /**
     * update column task view
     *
     * @param Request $request
     * @return mix
     */
    public function generalRulesColumnTaskUpdate(Request $request) {
        $columnTaskView = SiteSetting::find(SiteSetting::DISPLAY_COLUMN_TASK);
        $data = $request->columnTask ;
        $sum = 0;
        $result = [];
        if(!empty($data)){
            foreach ($data as $item){
                $column = json_decode($item);
                $sum = $sum + (int)$column->width;
            }
            foreach ($data as $item){
                $columnUpdate = [];
                $column = json_decode($item);
                $columnUpdate['attribute'] = $column->attribute;
                $columnUpdate['width'] = (int)$column->width*WIDTH_PERCENT_TABLE/$sum .'%';
                $columnUpdate['sortablelink'] = $column->sortablelink;
                $result[] = $columnUpdate;
            }
        }
        if ($columnTaskView) {
            $columnTaskView->value = json_encode($result);
            $columnTaskView->save();
        }

        return back()->with('success-column-task', trans('language.save_success'));
    }

    /**
     * Update site setting.
     *
     * @param IpTimekeepingRequest $request, int $id
     *
     * @return redrect
     */

    public function updateIpTimekeeping(IpTimekeepingRequest $request){
        $siteSetting = SiteSetting::find(SiteSetting::IP_TIMEKEEPING);
        if ($request->iptimekeeping){
            $siteSetting->value = json_encode($request->iptimekeeping);
        } else {
            $siteSetting->value = json_encode([]);
        }
        $siteSetting->save();
        return back()->with(['success-iptimekeeping'=> trans('language.success')]);
    }

    /**
     * get list property type.
     *
     * @param IpTimekeepingRequest $request, int $id
     *
     * @return redrect
     */

     public function listPropertyType(){
        $propertyTypes = PropertyType::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.property_type', ['propertyTypes'=>$propertyTypes]);
    }

     /**
     * add property type
     *
     * @param ProjectRequest $request
     */
    public function addPropertyType(PropertyTypeRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = PropertyType::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $property_type = new PropertyType();
            $property_type->name = $request->property_name;
            $property_type->property_code = $request->property_code;
            $property_type->sort_order = $sortOrder + 1 ;
            $property_type->save();
            DB::commit();
            return redirect()
                ->route('admin.propertyType.list')
                ->with([
                    'status_succeed' => trans('message.property_type_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyType.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Sort property types
     *
     * @param Request $request
     * @return mix
     */
    public function sortPropertyType(Request $request) {
        $sort = (new SortableManager())->sort(PropertyType::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
      * Update property types
      *
      * @param int $id, TaskPriorityRequest $request
      */
      public function editPropertyType($id, PropertyTypeRequest $request){
        DB::beginTransaction();
        try {
            $propertyType =  PropertyType::find($id);
            if ($propertyType){
                $propertyType->name = $request->property_name;
                $propertyType->property_code = $request->property_code;
                $propertyType->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.propertyType.list')
                ->with([
                    'status_succeed' => trans('message.property_type_edit_succeed'),
                ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyType.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

    /**
     * Delete task priority
     *
     * @param int $id
     */
    public function deletePropertyType($id){
        DB::beginTransaction();
        try {
            $propertyType = PropertyType::find($id);
            if ($propertyType){
                $propertyType->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_property_type_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * get list property management.
     *
     *
     * @return redrect
     */
    public function listPropertyManagement(){
        $propertyManagements = PropertyManagementAgency::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.property_management', ['propertyManagements'=>$propertyManagements]);
    }

    /**
     * add property management agency
     *
     * @param ProjectRequest $request
     */
    public function addPropertyManagement(PropertyManagementRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = PropertyManagementAgency::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $propertyManagement = new PropertyManagementAgency();
            $propertyManagement->name = $request->property_agency_name;
            $propertyManagement->sort_order = $sortOrder + 1 ;
            $propertyManagement->save();
            DB::commit();
            return redirect()
                ->route('admin.propertyManagementAgency.list')
                ->with([
                    'status_succeed' => trans('message.add_property_management_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyManagementAgency.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * edit property management agency
     *
     * @param ProjectRequest $request
     */
    public function editPropertyManagement($id, PropertyManagementRequest $request){
        DB::beginTransaction();
        try {
            $propertyManagement =  PropertyManagementAgency::find($id);
            if($propertyManagement){
                $propertyManagement->name = $request->property_agency_name;
                $propertyManagement->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.propertyManagementAgency.list')
                ->with([
                    'status_succeed' => trans('message.edit_property_management_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyManagementAgency.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Sort property management
     *
     * @param Request $request
     * @return mix
     */
    public function sortPropertyManagement(Request $request) {
        $sort = (new SortableManager())->sort(PropertyManagementAgency::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * deletePropertyManagement
     *
     * @param int $id
     */
    public function deletePropertyManagement($id){
        DB::beginTransaction();
        try {
            $propertyManagement = PropertyManagementAgency::find($id);
            if ($propertyManagement){
                $propertyManagement->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_property_management_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * get list property status.
     *
     * @return redrect
     */
     public function listPropertyStatus() {
        $propertyStatus = PropertyStatus::orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.property_status', ['propertyStatus' => $propertyStatus]);
    }

    /**
     * Delete property status
     *
     * @param int $id
     */
    public function deletePropertyStatus($id) {
        DB::beginTransaction();
        try {
            $propertyStatus = PropertyStatus::find($id);
            if ($propertyStatus) {
                $propertyStatus->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_property_status_success') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

    /**
     * Create property status
     *
     * @param PropertyStatusRequest $request
     */
     public function createPropertyStatus(PropertyStatusRequest $request) {
        DB::beginTransaction();
        try {
            $sortOrder = PropertyStatus::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $propertyStatus = new PropertyStatus();
            $propertyStatus->name = $request->name;
            $propertyStatus->color = $request->color;
            $propertyStatus->sort_order = $sortOrder + 1 ;
            $propertyStatus->save();
            DB::commit();
            return redirect()
                ->route('admin.propertyStatus.list')
                ->with([
                    'status_succeed' => trans('message.create_property_status_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyStatus.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
     }

    /**
     * Update property status
     *
     * @param int $id, PropertyStatusRequest $request
     */
    public function editPropertyStatus($id, PropertyStatusRequest $request){
        DB::beginTransaction();
        try {
            $propertyStatus = PropertyStatus::find($id);
            if ($propertyStatus) {
                $propertyStatus->name = $request->name;
                $propertyStatus->color = $request->color;
                $propertyStatus->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.propertyStatus.list')
                ->with([
                    'status_succeed' => trans('message.edit_property_status_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.propertyStatus.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Sort property status
     *
     * @param Request $request
     * @return mix
     */
    public function sortPropertyStatus(Request $request) {
        $sort = (new SortableManager())->sort(PropertyStatus::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }
    /**
     * add asset base
     *
     * @param ProjectRequest $request
     */
    public function addAssetBranch(AssetBaseRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = AssetSetting::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $assetBase = new AssetSetting();
            $assetBase->name = $request->asset_branch_name;
            $assetBase->sort_order = $sortOrder + 1 ;
            $assetBase->type = AssetSetting::ASSET_BRANCH;
            $assetBase->save();
            DB::commit();
            return redirect()
                ->route('admin.AssetBranch.list')
                ->with([
                    'status_succeed' => trans('message.add_asset_branch_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetBranch.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * edit asset base
     *
     * @param ProjectRequest $request
     */
    public function editAssetBranch($id, AssetBaseRequest $request){
        DB::beginTransaction();
        try {
            $assetBase =  AssetSetting::find($id);
            if($assetBase){
                $assetBase->name = $request->asset_branch_name;
                $assetBase->type = AssetSetting::ASSET_BRANCH;
                $assetBase->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.AssetBranch.list')
                ->with([
                    'status_succeed' => trans('message.edit_asset_branch_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetBranch.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Delete asset base
     *
     * @param int $id
     */
    public function deleteAssetBranch($id) {
        DB::beginTransaction();
        try {
            $assetBase = AssetSetting::find($id);
            if ($assetBase) {
                $assetBase->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_asset_branch_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

    /**
     * Sort property status
     *
     * @param Request $request
     * @return mix
     */
    public function sortAssetBranch(Request $request) {
        $sort = $this->assetManager->sortRecord(AssetSetting::class, $request, AssetSetting::ASSET_BRANCH);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * get list asset base.
     *
     *
     * @return redrect
     */
    public function listAssetBranches(){
        $assetBases = AssetSetting::where('type', AssetSetting::ASSET_BRANCH)->orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.asset_branch', ['assetBases'=>$assetBases]);
    }

    /**
     * get list asset resource.
     *
     *
     * @return redrect
     */
    public function listAssetResources(){
        $assetResources = AssetSetting::where('type', AssetSetting::ASSET_FORMATION_SOURCE)->orderby('sort_order')->paginate(self::PAGINATION);
        return view('admin.site_setting.asset_resource', ['assetResources'=>$assetResources]);
    }

    /**
     * add resource
     *
     * @param ProjectRequest $request
     */
    public function addAssetResources(AssetResourceRequest $request){
        DB::beginTransaction();
        try {
            $sortOrder = AssetSetting::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $assetResource = new AssetSetting();
            $assetResource->name = $request->asset_resource_name;
            $assetResource->sort_order = $sortOrder + 1 ;
            $assetResource->type = AssetSetting::ASSET_FORMATION_SOURCE;
            $assetResource->save();
            DB::commit();
            return redirect()
                ->route('admin.AssetResource.list')
                ->with([
                    'status_succeed' => trans('message.add_asset_resource_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetResource.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * edit resource
     *
     * @param ProjectRequest $request
     */
    public function editAssetResources($id, AssetResourceRequest $request){
        DB::beginTransaction();
        try {
            $assetResource =  AssetSetting::find($id);
            if($assetResource){
                $assetResource->name = $request->asset_resource_name;
                $assetResource->type = AssetSetting::ASSET_FORMATION_SOURCE;
                $assetResource->save();
            }
            DB::commit();
            return redirect()
                ->route('admin.AssetResource.list')
                ->with([
                    'status_succeed' => trans('message.edit_asset_resource_succeed'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetResource.list')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Delete resource base
     *
     * @param int $id
     */
    public function deleteAssetResources($id) {
        DB::beginTransaction();
        try {
            $resource = AssetSetting::find($id);
            if ($resource) {
                $resource->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_asset_resource_succeed') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
     }

    /**
     * Sort resource
     *
     * @param Request $request
     * @return mix
     */
    public function sortAssetResources(Request $request) {
        $sort = $this->assetManager->sortRecord(AssetSetting::class, $request, AssetSetting::ASSET_FORMATION_SOURCE);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * get list asset suppliers.
     *
     *
     * @return redirect
     */
    public function listAssetSuppliers(){
        $assetSuppliers = AssetSetting::suppliers()->paginate(self::PAGINATION);
        return view('admin.site_setting.asset_supplier', ['assetSuppliers' => $assetSuppliers]);
    }

    /**
     * add asset supplier
     *
     * @param AssetSupplierRequest $request
     */
    public function addAssetSuppliers(AssetSupplierRequest $request){
         DB::beginTransaction();
        try {
            $sortOrder = AssetSetting::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $assetSupplier = new AssetSetting();
            $assetSupplier->name = $request->name;
            $assetSupplier->note = $request->note;
            $assetSupplier->sort_order = $sortOrder + 1 ;
            $assetSupplier->type = AssetSetting::ASSET_SUPPLIER;
            $assetSupplier->save();
            DB::commit();
            return redirect()
                ->back()
                ->with([
                    'status_succeed' => trans('language.site_setting_asset_supplier.create_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->back()
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * edit asset supplier
     *
     * @param AssetSupplierRequest $request
     */
    public function editAssetSupplier($id, AssetSupplierRequest $request){
        DB::beginTransaction();
        try {
            $assetSupplier =  AssetSetting::find($id);
            if($assetSupplier){
                $assetSupplier->name = $request->name;
                $assetSupplier->note = $request->note;
                $assetSupplier->save();
            }
            DB::commit();
            return redirect()
                ->back()
                ->with([
                    'status_succeed' => trans('language.site_setting_asset_supplier.edit_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->back()
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

     /**
     * Delete asset supplier
     *
     * @param int $id
     */
    public function deleteAssetSupplier($id) {
        DB::beginTransaction();
        try {
            $assetSupplier = AssetSetting::find($id);
            if ($assetSupplier) {
                $assetSupplier->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('language.site_setting_asset_supplier.delete_success') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * Sort asset supplier
     *
     * @param Request $request
     * @return mix
     */
    public function sortAssetSupplier(Request $request) {
        $sort = (new SortableManager())->sort(AssetSetting::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }


    /**
     * Get list asset bidding package
     *
     * @return Application|Factory|View
     */
    public function listAssetBiddingPackages()
    {
        $assetBiddingPackages = AssetSetting::query()
            ->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)
            ->orderBy('sort_order')
            ->paginate(self::PAGINATION);

        $listFileAttachments = TaskAttachment::query()
            ->leftJoin('users', 'task_attachments.created_by', '=','users.id')
            ->where(function ($query) {
                $query->where([
                    ['task_attachments.type', TaskAttachment::TYPE_ASSET_BIDDING_PACKAGE]
                ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();

        foreach ($assetBiddingPackages as $assetBiddingPackageItem) {
            $fileAttachments = [];
            $fileAttachments = $listFileAttachments->filter(function($fileAttachment) use ($assetBiddingPackageItem) {
                return $fileAttachment->related_id === $assetBiddingPackageItem->id;
            });
            $assetBiddingPackageItem->listFile = $fileAttachments;
        }

        return view('admin.site_setting.asset_bidding_package', [
            'assetBiddingPackages' => $assetBiddingPackages
        ]);
    }

    /**
     * Add asset bidding package
     * @param AssetBiddingPackageRequest $request
     * @return RedirectResponse
     */
    public function addAssetBiddingPackages(AssetBiddingPackageRequest $request)
    {
        DB::beginTransaction();
        try {
            $param = $request->all();
            $fileAttachment = !empty($param['asset_bidding_package_file_attachments']) ? $param['asset_bidding_package_file_attachments'] : [];
            $sortOrder = AssetSetting::query()
                ->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)
                ->withTrashed()
                ->selectRaw('max(sort_order) as sort_order')
                ->first()
                ->sort_order;
            $assetBiddingPackage = new AssetSetting();
            $assetBiddingPackage->name = $param['asset_bidding_package_name'];
            $assetBiddingPackage->code = $param['asset_bidding_package_code'];
            $assetBiddingPackage->note = $param['asset_bidding_package_note'];
            $assetBiddingPackage->sort_order = $sortOrder + 1;
            $assetBiddingPackage->type = AssetSetting::ASSET_BIDDING_PACKAGE;
            $assetBiddingPackage->save();

            //Handle uploads of attachments
            [$fileAttachmentsFilter, $removeAttachmentsFile] = $this->assetManager->filterFileValid($fileAttachment, 100);

            //Remove attachments item
            foreach ($removeAttachmentsFile as $removeAttachmentItem) {
                $this->deleteFile($removeAttachmentItem);
            }

            //Save file attachments
            if (!empty($fileAttachmentsFilter)) {
                $destinationPath = str_replace(['{bidding_package_id}'], [$assetBiddingPackage->id], ASSET_BIDDING_PACKAGE_ATTACHMENT_DIR) . '/';
                $this->attachmentManager->saveAttachments($assetBiddingPackage, TaskAttachment::TYPE_ASSET_BIDDING_PACKAGE, $fileAttachmentsFilter, $param['descriptionDocument'], $destinationPath);
            }

            DB::commit();
            return redirect()
                ->route('admin.AssetBiddingPackage.list')
                ->with([
                    'status_succeed' => trans('message.add_asset_bidding_package_succeed')
                ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetBiddingPackage.list')
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Edit asset bidding package
     * @param                            $id
     * @param AssetBiddingPackageRequest $request
     * @return RedirectResponse
     */
    public function editAssetBiddingPackages($id, AssetBiddingPackageRequest $request)
    {
        try {
            DB::beginTransaction();
            $param = $request->all();
            $fileAttachment = !empty($param['asset_bidding_package_file_attachments']) ? $param['asset_bidding_package_file_attachments'] : [];
            $assetBiddingPackage = AssetSetting::query()
            ->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)
            ->find($id);

            if($assetBiddingPackage){
                $assetBiddingPackage->name = $param['asset_bidding_package_name'];
                $assetBiddingPackage->code = $param['asset_bidding_package_code'];
                $assetBiddingPackage->note = $param['asset_bidding_package_note'];
                $assetBiddingPackage->save();
            }

            //Handle uploads of attachments
            [$fileAttachmentsFilter, $removeAttachmentsFile] = $this->assetManager->filterFileValid($fileAttachment, 100);

            //Remove attachments item
            foreach ($removeAttachmentsFile as $removeAttachmentItem) {
                $this->deleteFile($removeAttachmentItem);
            }

            //Save file attachments
            if (!empty($fileAttachmentsFilter)) {
                $destinationPath = str_replace(['{bidding_package_id}'], [$assetBiddingPackage->id], ASSET_BIDDING_PACKAGE_ATTACHMENT_DIR) . '/';
                $this->attachmentManager->saveAttachments($assetBiddingPackage, TaskAttachment::TYPE_ASSET_BIDDING_PACKAGE, $fileAttachmentsFilter, $param['descriptionDocument'], $destinationPath);
            }

            DB::commit();
            return redirect()
                ->route('admin.AssetBiddingPackage.list')
                ->with([
                    'status_succeed' => trans('message.edit_asset_bidding_package_succeed')
                ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->route('admin.AssetBiddingPackage.list')
                ->with([
                    'status_failed' => trans('message.server_error')
                ]);
        }
    }

    /**
     * Sort asset bidding package
     * @param Request $request
     * @return array|void
     */
    public function sortAssetBiddingPackages(Request $request)
    {
        $sort = $this->assetManager->sortRecord(AssetSetting::class, $request, AssetSetting::ASSET_BIDDING_PACKAGE);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }

    /**
     * Delete asset bidding package
     * @param $id
     * @return array
     */
    public function deleteAssetBiddingPackages($id)
    {
        try {
            DB::beginTransaction();
            $biddingPackage = AssetSetting::query()
            ->where('type', AssetSetting::ASSET_BIDDING_PACKAGE)
            ->find($id);
            if ($biddingPackage) {
                $biddingPackage->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_asset_bidding_package_succeed')
                ],
            ];
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * get list asset short name.
     *
     *
     * @return redirect
     */
    public function listAssetShortNames(){
        $assetShortNames = AssetSetting::shortName()->paginate(self::PAGINATION);
        return view('admin.site_setting.asset_short_name', ['assetShortNames' => $assetShortNames]);
    }

    /**
     * add asset short name
     *
     * @param AssetShortNameRequest $request
     */
    public function addAssetShortName(AssetShortNameRequest $request){
         DB::beginTransaction();
        try {
            $sortOrder = AssetSetting::withTrashed()->selectRaw('max(sort_order) as sort_order')->first()->sort_order;
            $assetShortName = new AssetSetting();
            $assetShortName->name = $request->name;
            $assetShortName->note = $request->note;
            $assetShortName->sort_order = $sortOrder + 1 ;
            $assetShortName->type = AssetSetting::ASSET_SHORT_NAME;
            $assetShortName->save();
            DB::commit();
            return redirect()
                ->back()
                ->with([
                    'status_succeed' => trans('language.site_setting_asset_short_name.create_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->back()
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * edit asset short name
     *
     * @param AssetShortNameRequest $request
     */
    public function editAssetShortName($id, AssetShortNameRequest $request){
        DB::beginTransaction();
        try {
            $assetShortName =  AssetSetting::find($id);
            if($assetShortName){
                $assetShortName->name = $request->name;
                $assetShortName->note = $request->note;
                $assetShortName->save();
            }
            DB::commit();
            return redirect()
                ->back()
                ->with([
                    'status_succeed' => trans('language.site_setting_asset_short_name.edit_success'),
                ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()
                ->back()
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

     /**
     * Delete asset short name
     *
     * @param int $id
     */
    public function deleteAssetShortName($id) {
        DB::beginTransaction();
        try {
            $assetShortName = AssetSetting::find($id);
            if ($assetShortName) {
                $assetShortName->delete();
            }
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('language.site_setting_asset_short_name.delete_success') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('message.server_error') ,
                ],
            ];
        }
    }

    /**
     * Sort asset short name
     *
     * @param Request $request
     * @return mix
     */
    public function sortAssetShortName(Request $request) {
        $sort = (new SortableManager())->sort(AssetSetting::class, $request->itemCurrent, $request->itemPrev);
        if ($sort) {
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success')
                ],
            ];
        }
    }
}