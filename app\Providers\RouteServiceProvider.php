<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * This namespace is applied to your controller routes.
     *
     * In addition, it is set as the URL generator's root namespace.
     *
     * @var string
     */
    protected $namespace = 'App\Http\Controllers';

    /**
     * The path to the "home" route for your application.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        //

        parent::boot();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        if (preg_match('/^http[s]?:\/\/'
            .env('HEADQUARTER_SUBDOMAIN') . '.' .env('APP_URL_BASE', 'localhost')
            .'/',url()->current())) {
            $this->mapHeadquarterWebRoutes();
            $this->mapHeadquarterApiRoutes();
        } else {
            $this->mapApiRoutes();
            $this->mapWebRoutes();
        }
    }

    /**
     * Define the "web" routes for the application.
     *
     * These routes all receive session state, CSRF protection, etc.
     *
     * @return void
     */
    protected function mapWebRoutes()
    {
        Route::middleware('web')
            ->namespace($this->namespace)
            ->group(base_path('routes/web.php'));
    }

    /**
     * Define the "api" routes for the application.
     *
     * These routes are typically stateless.
     *
     * @return void
     */
    protected function mapApiRoutes()
    {
        Route::prefix('api')
            ->middleware('api')
            ->namespace($this->namespace)
            ->group(base_path('routes/api.php'));
    }

    // for only Headquarter
    protected function mapHeadquarterWebRoutes()
    {
        Route::domain(env('HEADQUARTER_SUBDOMAIN'). '.' . env('APP_URL_BASE', 'localhost'))
            ->middleware('headquarter')
            ->namespace($this->namespace)
            ->group(base_path('routes/headquarter.php'));
    }

    // for only Headquarter Api
    protected function mapHeadquarterApiRoutes()
    {
        Route::domain(env('HEADQUARTER_SUBDOMAIN'). '.'. env('APP_URL_BASE', 'localhost'))
            ->prefix('api')
            ->middleware('api')
            ->namespace($this->namespace)
            ->group(base_path('routes/headquarterapi.php'));
    }
}
