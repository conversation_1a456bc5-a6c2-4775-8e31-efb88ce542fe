<?php


namespace App\Exports;


use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TaskProblemSheetExport implements WithColumnWidths, WithStyles
{
    protected $sheetName;
    protected $data;
    protected $users;

    function __construct($sheetName,$data,$users) {
        $this->sheetName = $sheetName;
        $this->data = $data;
        $this->users = $users;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->data;
    }

    /**
     * @return string
     */
    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 25,
            'C' => 25,
            'D' => 25,
            'E' => 25,
            'F' => 45,
            'G' => 30,
            'H' => 85,
            'I' => 16
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $styleBorderProject = [
            'outline' => [
                'borderStyle' => Border::BORDER_MEDIUM,
                'color' => array(
                    'rgb' => '000000'
                )
            ]
        ];
        $sheet->setTitle($this->sheetName);
        $sheet->setCellValue('A1', 'Bảng báo cáo công việc hàng tuần');

        $sheet->getStyle('A1:A1')->applyFromArray(array(
            'font' => [
                'bold' => true,
                'size' => 11,
                'name' => 'Arial'
            ]
        ));
        $sheet->getStyle('A2:I2')->applyFromArray(array(
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]
        ));

        $sheet->setCellValue('A2', "");
        $sheet->setCellValue('B2', "Dự án");
        $sheet->setCellValue('C2', "Nhóm trưởng");
        $sheet->setCellValue('D2', "Nhóm phó");
        $sheet->setCellValue('E2', "Thành viên");
        $sheet->setCellValue('F2', "Vấn đề");
        $sheet->setCellValue('G2', "Nguyên nhân");
        $sheet->setCellValue('H2', "Kế hoạch");
        $sheet->setCellValue('I2', "Ghi chú");

        $row=3;
        $tt = 1;
        $sheet->getStyle('A2:I2')->applyFromArray(array(
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ],
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ],
            ]
        ));
        foreach ($this->data['projects'] as $project){
            $sheet->setCellValue('A'.$row, $tt);
            $sheet->setCellValue('B'.$row, $project['project_name']);
            foreach($project['leader'] as $key=>$value){
                $sheet->setCellValue(('C'.($row+$key)), $value['full_name']);
            }
            foreach($project['deputy_group'] as $key=>$value){
                $sheet->setCellValue(('D'.($row+$key)), $value['full_name']);
            }
            foreach($project['members'] as $key=>$value){
                $sheet->setCellValue(('E'.($row+$key)), $value['full_name']);
            }
            foreach($project['tasks'] as $key=>$value){
                $sheet->setCellValue(('F'.($row+$key)), ($key+1) . '/ '.$value);
            }
            $tt++;
            $rowStart = $row;
            $row += max(count($project['leader']),count($project['deputy_group']),count($project['members']),count($project['tasks']));

            $sheet->getStyle('A'.$rowStart.':A'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('B'.$rowStart.':B'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('C'.$rowStart.':E'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('F'.$rowStart.':F'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('G'.$rowStart.':G'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('H'.$rowStart.':H'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
            $sheet->getStyle('I'.$rowStart.':I'.($row-1))->applyFromArray(array(
                'borders' => $styleBorderProject
            ));
        }

        // Style table projects
        $sheet->getStyle('A3:B'.($row-1))->applyFromArray(array(
            'font' => [
                'bold' => true,
                'color' => [
                    'rgb' => '44546A',
                ],
            ]
        ));
        $sheet->getStyle('C2:C'.($row-1))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'ED7D31',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'FFFFFF',
                ],
            ]
        ));
        $sheet->getStyle('D2:D'.($row-1))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'F4B083',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'FFFFFF',
                ],
            ]
        ));
        $sheet->getStyle('E2:E'.($row-1))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'F7CAAC',
                ],
            ],
        ));
        $sheet->getStyle('A2:A2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('B2:B2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('C2:E2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('F2:F2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('G2:G2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('H2:H2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));
        $sheet->getStyle('I2:I2')->applyFromArray(array(
            'borders' => $styleBorderProject
        ));

        // style table personel
        $sheet->getStyle('A'.($row+1).':F'.($row+2+count($this->users)))->applyFromArray(array(
            'font' => [
                'bold' => true,
                'color' => [
                    'rgb' => '44546A',
                ],
            ],
        ));
        $sheet->getStyle('G'.($row+1).':G'.($row+1))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'FF0000',
                ],
            ]
        ));
        $sheet->getStyle('H'.($row+1).':H'.($row+1))->applyFromArray(array(
            'font' => [
                'color' => [
                    'rgb' => 'FF0000',
                ],
            ],
        ));
        $sheet->getStyle('G'.($row+2).':G'.($row+2))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '5B9BD5',
                ],
            ]
        ));
        $sheet->getStyle('H'.($row+2).':H'.($row+2))->applyFromArray(array(
            'font' => [
                'color' => [
                    'rgb' => '5B9BD5',
                ],
            ],
        ));
        $sheet->getStyle('G'.($row+3).':G'.($row+3))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'A8D08D',
                ],
            ]
        ));
        $sheet->getStyle('H'.($row+3).':H'.($row+3))->applyFromArray(array(
            'font' => [
                'color' => [
                    'rgb' => 'A8D08D',
                ],
            ],
        ));
        $sheet->getStyle('G'.($row+4).':G'.($row+4))->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'D0CECE',
                ],
            ]
        ));
        $sheet->getStyle('A'.($row+2).':F'.($row+2))->applyFromArray(array(
            'borders' => [
                'top' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => array(
                        'rgb' => '5B9BD5'
                    )
                ],
                'bottom' => [
                    'borderStyle' => Border::BORDER_MEDIUM,
                    'color' => array(
                        'rgb' => '5B9BD5'
                    )
                ],
            ]
        ));

        $sheet->setCellValue('A'.($row+1), "Thống kê nhân sự");
        $sheet->setCellValue('C'.($row+1), "Số lượng dự án");
        $sheet->setCellValue('F'.($row+1), "Tổng");

        $sheet->setCellValue('H'.($row+1), "Bị trễ, vấn đề cần giải quyết gấp");
        $sheet->setCellValue('H'.($row+2), "Chờ phản hồi thông tin; vấn đề phát sinh đang chờ giải quyết");
        $sheet->setCellValue('H'.($row+3), "Vấn đề đã giải quyết xong");
        $sheet->setCellValue('H'.($row+4), "Vấn đề đang được thực hiện");

        $sheet->setCellValue('C'.($row+2), "Quản lý dự án");
        $sheet->setCellValue('D'.($row+2), "Nhóm trưởng");
        $sheet->setCellValue('E'.($row+2), "Nhóm viên, nhóm phó");

        $row +=3;
        $ttUser =1;
        foreach ($this->users as $user){
            $sheet->setCellValue('A'.$row, $ttUser);
            if (isset($this->data['personnel'][$user['id']])){
                $sheet->setCellValue('B'.$row, $user['first_name'].' '.$user['last_name']);
                $sheet->setCellValue('C'.$row, $this->data['personnel'][$user['id']]['project_manager']);
                $sheet->setCellValue('D'.$row, $this->data['personnel'][$user['id']]['leader']);
                $sheet->setCellValue('E'.$row, $this->data['personnel'][$user['id']]['member']);
                $sheet->setCellValue('F'.$row, $this->data['personnel'][$user['id']]['project_manager']+$this->data['personnel'][$user['id']]['leader']+$this->data['personnel'][$user['id']]['member']);
            }else{
                $sheet->setCellValue('B'.$row, $user['first_name'].' '.$user['last_name']);
                $sheet->setCellValue('C'.$row, 0);
                $sheet->setCellValue('D'.$row, 0);
                $sheet->setCellValue('E'.$row, 0);
                $sheet->setCellValue('F'.$row, 0);
            }
            $ttUser++;
            $sheet->getStyle('A'.$row.':F'.$row)->applyFromArray(array(
                'borders' => [
                    'top' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => array(
                            'rgb' => '5B9BD5'
                        )
                    ],
                    'bottom' => [
                        'borderStyle' => Border::BORDER_MEDIUM,
                        'color' => array(
                            'rgb' => '5B9BD5'
                        )
                    ],
                ]
            ));
            $row += 1;
        }
        $sheet->getStyle('B2:F'.($row-1))->getAlignment()->setWrapText(true);
        $sheet->getStyle('A2:F'.($row-1))->applyFromArray(array(
            'font' => [
                'size' => 12,
                'name'  => 'Calibri',
            ]
        ));
    }
}
