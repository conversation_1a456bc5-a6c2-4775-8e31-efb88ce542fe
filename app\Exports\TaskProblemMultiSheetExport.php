<?php


namespace App\Exports;


use App\Exports\TaskProblemSheetExport;
use Carbon\Carbon;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TaskProblemMultiSheetExport implements FromCollection, WithMultipleSheets
{
    protected $data;
    protected $users;

    function __construct($data,$users) {
        $this->data = $data;
        $this->users = $users;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->data;
    }

    public function sheets(): array
    {
        $sheets = [];
        if (count($this->data) == 0){
            $sheets[] = new Worksheet();
        }else{
            foreach ($this->data as $key => $value) {
                $sheetName = Carbon::createFromFormat('Y-m-d', $key)->format('d.m.y');
                $sheets[] = new TaskProblemSheetExport($sheetName, $value, $this->users);
            }
        }
        return $sheets;
    }
}
