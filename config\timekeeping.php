<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Config Device Timekeeping
    |--------------------------------------------------------------------------
    |
    | Define config variable device timekeeping.
    |
    */
    
    'device_id' => env('DEVICE_ID', ''),
    'device_ip' => env('DEVICE_IP', ''),
    'device_auth_user' => env('DEVICE_AUTH_USER', ''),
    'device_auth_pass' => env('DEVICE_AUTH_PASS', ''),
    'callback_auth_user' => env('CALLBACK_AUTH_USER', ''),
    'callback_auth_pass' => env('CALLBACK_AUTH_PASS', ''),
    'max_size_face_image' => 750 * 1024, // byte = 750KB
];
