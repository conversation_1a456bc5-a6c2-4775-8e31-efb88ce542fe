<?php

namespace App\Repositories;

use <PERSON><PERSON>\Passport\TokenRepository;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CachedTokenRepository extends TokenRepository
{
    /**
     * Cache key prefix for tokens
     */
    protected const CACHE_PREFIX = 'passport_access_token_';

    /**
     * Find an access token by its ID, using cache.
     *
     * @param string $id
     * @return mixed
     */
    public function find($id)
    {
        $cacheKey = self::CACHE_PREFIX . $id;
        
        return Cache::remember($cacheKey, now()->addMinutes(config('passport.cache_ttl', 10)), function() use ($id) {
            Log::debug('[CachedTokenRepository][find] Token cached: ' . $id);
            return parent::find($id);
        });
    }

    /**
     * Revoke an access token and clear its cache.
     *
     * @param string $id
     * @return mixed
     */
    public function revokeAccessToken($id)
    {
        Log::debug('[CachedTokenRepository][revokeAccessToken] Revoking access token: ' . $id);

        // Clear the cache for the revoked token
        $cacheKey = self::CACHE_PREFIX . $id;
        Cache::forget($cacheKey);

        return parent::revokeAccessToken($id);
    }
}