<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use App\Models\ProjectTask;

class UpdateTask
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $log;
    public $taskId;
    public $type;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(string $log, int $taskId, int $type)
    {
        $this -> log = $log;
        $this -> taskId = $taskId;
        $this -> type = $type;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
