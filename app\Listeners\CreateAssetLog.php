<?php

namespace App\Listeners;

use App\Events\UpdateAsset;
use App\Models\AssetLog;
use Illuminate\Support\Facades\Auth;

class CreateAssetLog
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UpdateAsset  $event
     * @return void
     */
    public function handle(UpdateAsset $event)
    {
        // Insert log
        $assetLog = new AssetLog();
        $assetLog -> asset_id = $event->assetId;
        $assetLog -> log = $event->log;
        $assetLog -> created_by = Auth::id();
        $assetLog -> updated_by = Auth::id();
        $assetLog->save();
    }
}
