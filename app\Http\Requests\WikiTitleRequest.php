<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Helpers\StringHelper;
class WikiTitleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $stringHelper = new StringHelper();
        $textSlug = $stringHelper->create_slug(($this->title),'_');
        $this->merge([
            'slug' => $textSlug
        ]);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $slug = $this->slug; 
        $projectId = $this->projectId;
        return [
            'title' => ['required','max:200'],
            'slug' => ['required',
                Rule::unique("project_wikis")->where(
                function ($query) use ($slug,$projectId) {
                    return $query->where(
                        [
                            ["slug", $slug],
                            ["project_id",$projectId]
                        ]
                    )->whereNull('deleted_at');
                })],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'slug.unique' => trans("validation.attributes.wiki_slug_exist")
        ];
    }
}
