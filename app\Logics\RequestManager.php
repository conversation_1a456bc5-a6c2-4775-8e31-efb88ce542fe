<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use App\Jobs\SendMailRequest;
use App\Models\Language;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Request as ModelsRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Http\Requests\RequireRequest;
use App\Models\RequestLog;
use App\Models\TaskAttachment;
use Illuminate\Support\Facades\Log;

class RequestManager
{
    /**
     * save request
     */
    public function store(RequireRequest $request)
    {
        DB::beginTransaction();
        try {
            $content = [];
            $atributeNotInContent = ['_token', '_method', 'name', 'type', 'approvers', 'followers', 'note', 'documents', 'descriptionDocument', 'status', 'user_id'];
            foreach ($request->all() as $attribute => $value) {
                if (!in_array($attribute, $atributeNotInContent)) {
                    switch ($attribute) {
                        case 'time_checkin':
                        case 'time_checkout':
                        case 'time_start':
                        case 'time_end':
                            $value = Carbon::createFromFormat('d/m/Y H:i', $value)->format('Y-m-d H:i:s');
                            break;
                        case 'time_family_business':
                            $dateFormatManager = new DateFormatManager();
                            $value = $dateFormatManager->dateFormatLanguageArray($value);
                            break;
                        case 'hour':
                        case 'mentor':
                        case 'receive_arrear_cash':
                        case 'receive_arrear':
                        case 'collect_arrear':
                        case 'btaBonus':
                        case 'percent_work':
                            $arrUserID = $request->user_id;
                            $month = $request->choose_month;
                            $arrRequest = [];
                            foreach ($arrUserID as $key => $item) {
                                $arrRequest[] = [
                                    'month' => $month,
                                    'user_id' => $item,
                                    'value' => isset($value[$key]) ? $value[$key] : 0
                                ];
                            }
                            $value = $arrRequest;
                            break;
                        default:
                            $value = $value;
                    }
                    $arrAttributeRequest = array("user_id", "choose_month", "hour", "receive_arrear_cash", "receive_arrear", "percent_work", "mentor", "collect_arrear", "btaBonus");
                    if (!in_array($attribute, $arrAttributeRequest)) {
                        $content[$attribute] = $value;
                    } else {
                        $content = $value;
                    }
                }
            }
            $requestStore = new ModelsRequest();
            $requestStore->name = $request->name;
            $requestStore->type = $request->type;
            $requestStore->content = $content;
            $requestStore->approvers = $request->approvers;
            $requestStore->followers = $request->followers;
            $requestStore->note =  $request->note;
            $requestStore->save();

            $data = ModelsRequest::find($requestStore->id);
            $destinationPath = str_replace(['{request_id}'], [$data->id], REQUEST_DIR) . '/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_REQUEST, $request->documents, $request->descriptionDocument, $destinationPath);

            $requestLog = new RequestLog();
            $requestLog->request_id =  $requestStore->id;
            $requestLog->type = RequestLog::TYPE_CREATE;
            $requestLog->save();

            // Send mail for Approvers
            $this->sendMailRequest($requestStore->type, $requestStore->id, $requestStore->approvers);
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * get request list
     *
     */
    public function getRequestList($params, $pageSize, $orderBy = [])
    {
        if(isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y',$params['from']) > Carbon::createFromFormat('d/m/Y',$params['to'])){
            return [[],[],[]];
        }
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $column = [
            'requests.id',
            'requests.name',
            'requests.content',
            'requests.type',
            'requests.status',
            'requests.updated_at',
            'requests.created_by',
            'users.first_name',
            'users.last_name',
            'seen_request_logs.created_at'
        ];
        $request =  ModelsRequest::select($column)
        ->leftJoin('users','requests.created_by','users.id')
        ->leftJoin('seen_request_logs', function($join) use ($userId)
        {
            $join->on('requests.id', '=','seen_request_logs.request_id')
                ->where('seen_request_logs.created_by','=',$userId);
        });
        if(isset($params['type'])){
            $request = $request->whereIn('requests.type',$params['type']);
        }
        if(isset($params['status'])){
            $request = $request->whereIn('requests.status',$params['status']);

        }
        if(isset($params['from'])){
            $request = $request->where(function($query)  use ($params){
                $query->where('requests.updated_at','>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'));
            });
        }
        if(isset($params['to'])){
            $request = $request->where(function($query)  use ($params){
                $query->where('requests.updated_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
            });
        }
        if (isset($params['title'])){
            $search = $stringHelper->formatStringWhereLike($params['title']);
            $request = $request->where('requests.name', 'LIKE', '%'.$search.'%');
        }
         // Order By
         if (isset($orderBy)){
            foreach ($orderBy as $key => $value){
                $request = $request->orderBy($key,$value);
            }
        }
        $requestAll = (clone $request)->where(function($query)  use ($userId){
            $query->where('requests.created_by', $userId)
                ->orwhereRaw('JSON_CONTAINS(requests.approvers, JSON_QUOTE("'. $userId .'"))')
                ->orwhereRaw('JSON_CONTAINS(requests.followers, JSON_QUOTE("'. $userId .'"))');
        });
        $requestMySelfs =  (clone $request)->where('requests.created_by', $userId);
        $requestOthers =  (clone $request)->where(function($query)  use ($userId){
            $query->orwhereRaw('JSON_CONTAINS(requests.approvers, JSON_QUOTE("'. $userId .'"))')
                ->orwhereRaw('JSON_CONTAINS(requests.followers, JSON_QUOTE("'. $userId .'"))');
        });
        if(isset($params['member'])){
            $requestAll = $requestAll->whereIn('requests.created_by',$params['member']);
            $requestOthers = $requestOthers->whereIn('requests.created_by',$params['member']);
        }
        $requestAll = $requestAll->paginate($pageSize);
        $requestMySelfs = $requestMySelfs->paginate($pageSize);
        $requestOthers = $requestOthers->paginate($pageSize);
        return [ $requestAll , $requestMySelfs , $requestOthers];
    }
    /**
     * get color and name status request
     */
    public function getNameAndBackgroundStatus($id)
    {
        switch ($id){
            case \App\Models\Request::STATUS_WAITING:
                $color = '#0791A3';
                break;
            case \App\Models\Request::STATUS_ACCEPT:
                $color = '#409834';
                break;
            case \App\Models\Request::STATUS_REFUSE:
                $color = '#b00927';
                break;
            case \App\Models\Request::STATUS_CONFIRM:
                $color = '#DDDFE1';
            break;
            default:
                $color = '#ffffff';
                break;
        }
        return $color;
    }

    /**
     * Get html filter
     * @param Request $request
     * @param array $fields
     */
    public function getFilterHtml(Request $request,$fields = []){
        $filterHtml = "";
        $dateFormatManager = new DateFormatManager();
        foreach ($fields as $field){
            $value = '';
            if ($request->has($field) && $request->$field != null){
                $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
                $tagSpanClose = '</span>';
                switch ($field){
                    case 'type':
                        $types = ModelsRequest::TYPE;
                        foreach ($types as $key => $typeItem) {
                            if(in_array($key, $request->$field))
                                $value .= $tagSpanOpen . trans('language.' . $typeItem ). $tagSpanClose;
                        }
                        break;
                    case 'status':
                        $status = ModelsRequest::STATUS;
                        foreach ($status as $key => $statusItem) {
                            if(in_array($key, $request->$field))
                                $value .= $tagSpanOpen . trans('language.' . $statusItem ). $tagSpanClose;
                        }
                        break;
                    case 'member':
                        $members = \App\User::whereIn('id',$request->member)->select('first_name', 'last_name')->get();
                        foreach ($members as $member){
                            $value .= $tagSpanOpen . $member->first_name . ' ' . $member->last_name . $tagSpanClose;
                        }
                        break;
                    case 'from':
                        $typeFormat = 'd/m/Y';
                        $value .=  $tagSpanOpen . $dateFormatManager->dateFormatInput($request->$field,$typeFormat) . $tagSpanClose;
                        break;
                    case 'to':
                        $typeFormat = 'd/m/Y';
                        $value .=  $tagSpanOpen . $dateFormatManager->dateFormatInput($request->$field,$typeFormat) . $tagSpanClose;
                        break;
                    default:
                        $value = $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $filterHtml.= $value;
            }
        }
        return $filterHtml;
    }
    /**
     * get number request view
     */
    public function getNumberIsNotView(){
        $userId = Auth::id();
        $number =  ModelsRequest::query()
            ->where(function($query)  use ($userId){
                $query->orwhereRaw('JSON_CONTAINS(requests.approvers, JSON_QUOTE("'. $userId .'")) AND requests.status = ?',[ModelsRequest::STATUS_WAITING])
                    ->orwhereRaw('JSON_CONTAINS(requests.followers, JSON_QUOTE("'. $userId .'")) AND requests.status = ?',[ModelsRequest::STATUS_ACCEPT]);
            })
            ->count();
        return $number;
    }

    /**
     * check page active
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function pageActive(Request $request){
        $pageActive = [];
        $pageActive['all'] = true;
        $pageActive['myself'] = false;
        $pageActive['other'] = false;

        if(isset($request->page_active)){
            switch ($request->page_active){
                case 'myself':
                    $pageActive['all'] = false;
                    $pageActive['myself'] = true;
                    $pageActive['other'] = false;
                    break;
                case 'other':
                    $pageActive['all'] = false;
                    $pageActive['myself'] = false;
                    $pageActive['other'] = true;
                    break;
            }
        }
        return $pageActive;
    }
    
    /**
     * Send mail for approvers or followers
     * @param $requestType
     * @param $requestID
     * @param $arrApproversID
     */
    public function sendMailRequest($requestType, $requestID, $arrApproversID){
        if ($requestID && $requestType && count($arrApproversID) > 0){
            $idCreatedByRequest = ModelsRequest::query()->find($requestID)->created_by;
            $user = User::withTrashed()->select(['last_name','first_name','email','language_id'])
                ->find($idCreatedByRequest);
            $types = ModelsRequest::TYPE;
            $arrRequestInfor = array();
            $websiteId = null;
            if (env('USE_TENANT', false)) {
                $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
            }
            $arrRequestInfor['userEmail'] = $user->email;
            $arrRequestInfor['typeRequest'] = __('language.'.$types[$requestType]);
            $arrRequestInfor['userName'] = $user->full_name;
            $arrRequestInfor['urlRequestDetail'] = route("request.show", ['id' => $requestID]);
            $arrRequestInfor['language_id'] = $user->language_id;
            $arrApprovers = User::query()->select('id', 'email','language_id')->whereIn('id', $arrApproversID)->get();
            $languages = Language::pluck('name', 'id');
            foreach($arrApprovers as $approverTag){
                App::setLocale($languages[$approverTag->language_id]);
                $arrRequestInfor['typeRequest'] = __('language.'.$types[$requestType]);
                dispatch(new SendMailRequest($approverTag, $websiteId, $arrRequestInfor))->onQueue(QUEUE_MAIL);
            }
            //set lại ngôn ngữ sau khi chuyển đổi để gửi emauil
            $language = Language::find(Auth::user()->language_id);
            $languageName = isset($arrRequestInfor['language_id']) ? $language->name : null; 
            if ($languageName) {
                App::setLocale($languageName);
            }
        }
    }
}
