<?php

namespace App\Http\Requests;

use App\Models\Request;
use App\Models\Role;
use App\User;
use Illuminate\Foundation\Http\FormRequest;

class RequireRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $usersHasRoleOperationManager = User::whereHas(
            'roles', function($q){
                $q->where('name', Role::ROLE_OPERATION_MANAGER);
            }
        )->pluck('id')->toArray();
        
        $arrValidate = [
            'name' => 'required|max:1000',
            'type' => 'required|numeric|max:255|in:'.Request::TYPE_CHECKIN.','.Request::TYPE_CHECKOUT.','.Request::TYPE_VACATION.','.Request::TYPE_FAMILY_BUSINESS.','.Request::TYPE_FREELANCER.','.Request::TYPE_OT.','.Request::TYPE_RECEIVE_ARREAR_CASH.','.Request::TYPE_MENTOR.','.Request::TYPE_RECEIVE_ARREAR.','.Request::TYPE_COLLECT_ARREAR.','.Request::TYPE_PERCENT_WORK.','.Request::TYPE_BTA.','.Request::TYPE_COLLABORATOR,
            'approvers' => 'required|array',
            'approvers.*' => 'exists:users,id',
            'followers' => 'required|array',
            'followers.*' => 'in:'.implode(',',$usersHasRoleOperationManager),
            'note' => 'nullable|max:1000',
        ];
        if (request()->type == Request::TYPE_CHECKIN) {
            $arrValidate['time_checkin'] = 'required';
        }
        elseif (request()->type == Request::TYPE_CHECKOUT) {
            $arrValidate['time_checkout'] = 'required';
        }
        elseif (request()->type == Request::TYPE_BTA) {
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
            $arrValidate['btaBonus'] = ['required','array'];
            $arrValidate['btaBonus.*'] = ['required','integer','digits_between:1,15','numeric','min:0'];
            $arrValidate['choose_month'] = ['required'];
        }
        elseif (request()->type == Request::TYPE_VACATION) {
            $arrValidate['time_start'] = 'required';
            $arrValidate['time_end'] = 'required';
        }
        elseif (request()->type == Request::TYPE_FAMILY_BUSINESS) {
            $arrValidate['time_family_business'] = ['required'];
            $arrTimeFamilyBusiness = explode(", ", request()->time_family_business);
            if (count($arrTimeFamilyBusiness) > 3) {
                $arrValidate['time_family_business'] = ['max:3'];
            }
        }
        elseif (request()->type == Request::TYPE_FREELANCER || request()->type == Request::TYPE_OT) {
            $arrValidate['hour'] = ['required','array'];
            $arrValidate['hour.*'] = ['required','min:0','max:1000','numeric'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif(request()->type == Request::TYPE_RECEIVE_ARREAR_CASH){
            $arrValidate['receive_arrear_cash'] = ['required','array'];
            $arrValidate['receive_arrear_cash.*'] = ['required','digits_between:1,15','numeric','min:0'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif( request()->type == Request::TYPE_COLLECT_ARREAR){
            $arrValidate['collect_arrear'] = ['required','array'];
            $arrValidate['collect_arrear.*'] = ['required','digits_between:1,15','numeric','min:0'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif(request()->type == Request::TYPE_RECEIVE_ARREAR){
            $arrValidate['receive_arrear'] = ['required','array'];
            $arrValidate['receive_arrear.*'] = ['required','digits_between:1,15','numeric','min:0'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif(request()->type == Request::TYPE_PERCENT_WORK){
            $arrValidate['percent_work'] = ['required','array'];
            $arrValidate['percent_work.*'] = ['required','numeric','min:0','max:100'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif(request()->type == Request::TYPE_MENTOR){
            $arrValidate['mentor'] = ['required','array'];
            $arrValidate['mentor.*'] = ['required','numeric','min:0'];
            $arrValidate['choose_month'] = ['required'];
            $arrValidate['user_id'] = ['required','array'];
            $arrValidate['user_id.*'] = ['exists:users,id','distinct'];
        }
        elseif (request()->type == Request::TYPE_COLLABORATOR) {
            $arrValidate['collaborator_date'] = ['required', 'array'];
            $arrValidate['shift'] = ['required', 'array'];
            $arrValidate['shift.*'] = ['required','digits_between:1,3'];
            $arrValidate['request_type'] = ['required','array'];
            $arrValidate['request_type.*'] = ['required','digits_between:1,2'];
            $arrValidate['collab_starttime_request'] = ['required','array'];
            $arrValidate['collab_endtime_request'] = ['required','array'];
        }
        return $arrValidate;
    }

    /**
     * @return array|string[]
     */
    public function messages(): array
    {
        return [
            'name.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.last_name')
            ]),
            'name.max' => __('message.request.input_max', [
                'attribute' => __('validation.attributes.last_name'),
                'max' => 1000
            ]),

            'type.required' => __('message.request.select_required', [
                'attribute' => __('validation.attributes.type_request')
            ]),
            'type.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.type_request')
            ]),
            'type.max' => __('validation.max.numeric', [
                'attribute' => __('validation.attributes.type_request'),
                'max' => 255,
            ]),
            'type.in' => __('validation.in', [
                'attribute' => __('validation.attributes.type_request')
            ]),

            'approvers.required' => __('message.request.select_required', [
                'attribute' => __('validation.attributes.approvers')
            ]),
            'approvers.array' => __('validation.array', [
                'attribute' => __('validation.attributes.approvers'),
            ]),
            'approvers.*.exists' => __('validation.exists', [
                'attribute' => __('validation.attributes.approvers')
            ]),

            'followers.required' => __('message.request.select_required', [
                'attribute' => __('validation.attributes.followers')
            ]),
            'followers.array' => __('validation.array', [
                'attribute' => __('validation.attributes.followers'),
            ]),
            'followers.*.in' => __('validation.in', [
                'attribute' => __('validation.attributes.followers')
            ]),

            'note.max' => __('validation.max.string', [
                'attribute' => __('validation.attributes.note'),
                'max' => 1000,
            ]),

            'time_checkin.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.time_checkin')
            ]),
            'time_checkout.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.time_checkout')
            ]),

            'time_start.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.time_start')
            ]),
            'time_end.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.time_end')
            ]),

            'time_family_business.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.time_family_business')
            ]),
            'time_family_business.max' => __('validation.max.string', [
                'attribute' => __('validation.attributes.time_family_business'),
                'max' => 3,
            ]),

            'hour.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.hour')
            ]),
            'hour.array' => __('validation.array', [
                'attribute' => __('validation.attributes.hour'),
            ]),
            'hour.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.hour')
            ]),
            'hour.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.hour'),
            ]),
            'hour.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.hour'),
                'min' => 0,
            ]),
            'hour.*.max' => __('validation.max.numeric', [
                'attribute' => __('validation.attributes.time_family_business'),
                'max' => 1000,
            ]),

            'choose_month.required' => __('message.request.select_required', [
                'attribute' => __('validation.attributes.choose_month')
            ]),

            'user_id.required' => __('message.request.select_required', [
                'attribute' => __('validation.attributes.user_id')
            ]),
            'user_id.array' => __('validation.array', [
                'attribute' => __('validation.attributes.user_id'),
            ]),
            'user_id.*.exists' => __('validation.exists', [
                'attribute' => __('validation.attributes.user_id')
            ]),
            'user_id.*.distinct' => __('validation.distinct', [
                'attribute' => __('validation.attributes.user_id')
            ]),

            'btaBonus.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.btaBonus')
            ]),
            'btaBonus.array' => __('validation.array', [
                'attribute' => __('validation.attributes.btaBonus'),
            ]),
            'btaBonus.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.btaBonus')
            ]),
            'btaBonus.*.integer' => __('validation.integer', [
                'attribute' => __('validation.attributes.btaBonus')
            ]),
            'btaBonus.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.btaBonus'),
                'min' => 1,
                'max' => 15,
            ]),
            'btaBonus.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.numeric'),
            ]),
            'btaBonus.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.btaBonus'),
                'min' => 0,
            ]),

            'receive_arrear_cash.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.receive_arrear_cash')
            ]),
            'receive_arrear_cash.array' => __('validation.array', [
                'attribute' => __('validation.attributes.receive_arrear_cash'),
            ]),
            'receive_arrear_cash.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.receive_arrear_cash')
            ]),
            'receive_arrear_cash.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.receive_arrear_cash'),
                'min' => 1,
                'max' => 15,
            ]),
            'receive_arrear_cash.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.receive_arrear_cash'),
            ]),
            'receive_arrear_cash.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.receive_arrear_cash'),
                'min' => 0,
            ]),

            'collect_arrear.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.collect_arrear')
            ]),
            'collect_arrear.array' => __('validation.array', [
                'attribute' => __('validation.attributes.collect_arrear'),
            ]),
            'collect_arrear.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.collect_arrear')
            ]),
            'collect_arrear.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.collect_arrear'),
                'min' => 1,
                'max' => 15,
            ]),
            'collect_arrear.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.collect_arrear'),
            ]),
            'collect_arrear.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.collect_arrear'),
                'min' => 0,
            ]),

            'receive_arrear.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.receive_arrear')
            ]),
            'receive_arrear.array' => __('validation.array', [
                'attribute' => __('validation.attributes.receive_arrear'),
            ]),
            'receive_arrear.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.receive_arrear')
            ]),
            'receive_arrear.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.receive_arrear'),
                'min' => 1,
                'max' => 15,
            ]),
            'receive_arrear.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.receive_arrear'),
            ]),
            'receive_arrear.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.receive_arrear'),
                'min' => 0,
            ]),

            'percent_work.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.percent_work')
            ]),
            'percent_work.array' => __('validation.array', [
                'attribute' => __('validation.attributes.percent_work'),
            ]),
            'percent_work.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.percent_work')
            ]),
            'percent_work.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.percent_work'),
            ]),
            'percent_work.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.percent_work'),
                'min' => 0,
            ]),
            'percent_work.*.max' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.percent_work'),
                'max' => '100%',
            ]),

            'mentor.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.mentor')
            ]),
            'mentor.array' => __('validation.array', [
                'attribute' => __('validation.attributes.mentor'),
            ]),
            'mentor.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.mentor')
            ]),
            'mentor.*.numeric' => __('validation.numeric', [
                'attribute' => __('validation.attributes.mentor'),
            ]),
            'mentor.*.min' => __('validation.min.numeric', [
                'attribute' => __('validation.attributes.mentor'),
                'min' => 0,
            ]),

            'collaborator_date.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.collaborator_date')
            ]),
            'collaborator_date.array' => __('validation.array', [
                'attribute' => __('validation.attributes.collaborator_date'),
            ]),

            'shift.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.shift')
            ]),
            'shift.array' => __('validation.array', [
                'attribute' => __('validation.attributes.shift'),
            ]),
            'shift.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.shift')
            ]),
            'shift.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.shift'),
                'min' => 1,
                'max' => 3,
            ]),

            'request_type.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.request_type')
            ]),
            'request_type.array' => __('validation.array', [
                'attribute' => __('validation.attributes.request_type'),
            ]),
            'request_type.*.required' => __('message.request.input_required', [
                'attribute' => __('validation.attributes.request_type')
            ]),
            'request_type.*.digits_between' => __('validation.digits_between', [
                'attribute' => __('validation.attributes.request_type'),
                'min' => 1,
                'max' => 4,
            ]),
        ];
    }
}
