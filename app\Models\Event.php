<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Event
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property date $started_at
 * @property date $ended_at
 * @property integer $repeat
 * @property integer $type
 * @property string $location
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 * @property date $deleted_at
 */
class Event extends Model
{
    use SoftDeletes;

    const MEETING_TYPE = 0;

    const NO_REPEAT = 0;
    const REPEAT_DAILY = 1;
    const REPEAT_EVERYDAY_OF_THE_WEEK = 2;
    const REPEAT_EVERY_WEEKEND = 3;
    const REPEAT_WEEKLY = 4;

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
            $data->created_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }

    public function members()
    {
        return $this->belongsToMany('App\User','event_participants','event_id','user_id');
    }

    /**
     * Scope check the user has permission to access the event
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeCheckUserPermission($query, $userId) {
        return $query->leftJoin('event_participants', 'event_participants.event_id', 'events.id')
            ->where('event_participants.user_id','=',$userId);
    }
}
