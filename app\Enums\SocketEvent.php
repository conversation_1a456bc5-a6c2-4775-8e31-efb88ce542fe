<?php

namespace App\Enums;

use <PERSON>Samp<PERSON>\Enum\Enum;

final class SocketEvent extends Enum
{
    // Socket message.
    const NEW_MESSAGE = 'newMessage';
    const UPDATE_MESSAGE = 'updateMessage';
    const DESTROY_MESSAGE = 'destroyMessage';
    const BOOKMARK_MESSAGE = 'bookmarkMessage';
    const DELETE_FILE_MESSAGE = 'deleteFileMessage';
    // Socket conversation.
    const NEW_CONVERSATION = 'newConversation';
    const UPDATE_CONVERSATION = 'updateConversation';
    const OUT_CONVERSATION = 'outConversation';
    const ACTION_MUTE_CONVERSATION = 'actionMuteConversation';
    const DELETE_CONVERSATION = 'deleteConversation';
    const REMOVE_JOIN_CONVERSATION = 'removeJoinConversation';
    const NEW_REQUEST_JOIN_CONVESATION = 'NewRequestJoinConvesation';
    const UPDATE_MEMBERS_IN_CONVERSATION = 'updateMembersInConversation';
    const REACTION = 'reaction';
    const NEW_USER_JOIN_ROOM = 'newUserJoinRoom';
    const ADMIN_ACCEPT_YOUR_JOIN = 'adminAcceptYourJoin';
    const PIN = 'pin';
    const UNPIN = 'unpin';

    // Socket contact.
    const NEW_CONTACT = 'newContact';
    const REJECT_CONTACT = 'rejectContact';
    const ACCEPT_CONTACT = 'acceptContact';
}