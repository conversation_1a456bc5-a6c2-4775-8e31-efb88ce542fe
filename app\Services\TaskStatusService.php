<?php

namespace App\Services;

use App\Models\TaskStatus;
use Illuminate\Support\Facades\DB;

class TaskStatusService
{
    public function countTask($userId, array $projectIds)
    {
        return TaskStatus::select(
                DB::raw('COUNT(IF(projects.deleted_at is null and projects.id is not null, 1, null)) AS taskOpen'),
                DB::raw('COUNT(IF(projects.deleted_at is not null, 1, null)) AS taskClose'),
                'task_status.name',
                'task_status.id'
            )
            ->leftJoin('project_tasks', function ($q) use ($userId, $projectIds){
                $q->on('project_tasks.status', 'task_status.id')
                    ->join('projects', 'projects.id', 'project_tasks.project_id')
                    ->where('project_tasks.user_id', $userId)
                    ->whereIn('project_tasks.project_id', $projectIds);
            })
            ->groupBy('task_status.id')
            ->get();
    }
    
    public function mapTaskStatus() {
        return [
            'New' => '新規',
            'In progress' => '進行中',
            'Pending' => '保留中',
            'Waiting reply' => '返信済み',
            'Replied' => '終了',
            'Feedback' => 'バグ修正',
            'Closed' => '解決済み',
            'Bug fixing' => '拒否',
            'Resolved' => 'QA確認',
            'Reject' => '開発リリース',
            'QA confirming' => 'ステージング',
            'Develop Release' => '製品リリース',
            'Staging Release' => '合計',
            'Production Release' => '合計',
        ];
    }
}