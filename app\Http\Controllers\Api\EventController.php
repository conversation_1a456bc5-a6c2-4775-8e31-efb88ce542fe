<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\EventRequest;
use App\Logics\EventManager;
use App\Models\Event;
use App\User;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class EventController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\EventRequest  $request
     * @return \Illuminate\Http\Response
     */
    public function store(EventRequest $request){
        DB::beginTransaction();
        try {
            if (isset($request->members)){
                $members = explode(',',trim($request->members,','));
                $numberUsers = User::whereIn('id',$members)->wherenull('deleted_at')->count();
                if ($numberUsers!=count($members)){
                    return response()->json([
                        'code' => Response::HTTP_UNPROCESSABLE_ENTITY,
                        'message' => trans('message.member_not_exist')
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                $request->members = $members;
            }
            $eventManager = new EventManager();
            $event = $eventManager->createEvent($request);
            DB::commit();
            return response()->json([
                'code' => Response::HTTP_OK,
                'message' => trans('message.success')
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

    }

    public function update(EventRequest $request,$id){
        DB::beginTransaction();
        try {
            if (isset($request->members)){
                $members = explode(',',trim($request->members,','));
                $numberUsers = User::whereIn('id',$members)->wherenull('deleted_at')->count();
                if ($numberUsers != count($members)){
                    return response()->json([
                        'code' => Response::HTTP_UNPROCESSABLE_ENTITY,
                        'message' => trans('message.member_not_exist')
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }
                $request->members = $members;
            }

            $event = Event::select('events.*')->CheckUserPermission(Auth::id())->find($id);
            if($event == null){
                return response()->json([
                    'code' => Response::HTTP_NOT_FOUND,
                    'message' => trans('message.event_not_exist')
                ], Response::HTTP_NOT_FOUND);
            }

            $eventManager = new EventManager();
            $event = $eventManager->updateEvent($event,$request);
            DB::commit();
            return response()->json([
                'code' => Response::HTTP_OK,
                'message' => trans('message.success')
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }

    }

    public function destroy($id){
        DB::beginTransaction();
        try {
            $event = Event::select('events.*')->CheckUserPermission(Auth::id())->find($id);
            if($event == null){
                return response()->json([
                    'code' => Response::HTTP_NOT_FOUND,
                    'message' => trans('message.event_not_exist')
                ], Response::HTTP_NOT_FOUND);
            }

            $event->delete();

            DB::commit();
            return response()->json([
                'code' => Response::HTTP_OK,
                'message' => trans('message.success')
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return response()->json([
                'code' => \Illuminate\Http\Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => trans('message.server_error')
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
