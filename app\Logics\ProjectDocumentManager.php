<?php
namespace App\Logics;

use App\Http\Controllers\FileController;
use Illuminate\Support\Facades\DB;
use App\Models\ProjectDocument;

class ProjectDocumentManager{
    const PAGINATION = 10;
    public function getAllDocument($searchDocument,$id,$pageSize=self::PAGINATION)
    {
        
        $projectDocument = ProjectDocument::select(
            'project_documents.id',
            'project_documents.description',
            'project_documents.project_id',
            'project_documents.file_name',
            'project_documents.file_path',
            'project_documents.file_size',
            'project_documents.created_at',
            'project_documents.created_by',
            'project_documents.pinned_at',
            'users.first_name',
            'users.last_name',
            'users.avatar'
        )
        ->join('users', 'users.id', '=', 'project_documents.created_by')
        ->where('project_documents.project_id', $id);
        if($searchDocument != null){
            $projectDocument = $projectDocument->where('file_name','like',"%$searchDocument%");
        }
        $projectDocument = $projectDocument->orderBy('pinned_at','DESC')->sortable(['created_at' => 'DESC'])->paginate($pageSize);
        $file_size = new FileController;
        foreach($projectDocument as $document){
            $document->file_size = $file_size->getFileSize($document->file_size);
        }
        return $projectDocument;
    }
}