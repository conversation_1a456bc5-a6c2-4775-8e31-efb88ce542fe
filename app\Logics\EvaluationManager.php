<?php

namespace App\Logics;

use App\Models\EvaluationResult;
use App\Models\ProjectGroupRole;
use App\Models\ProjectMember;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use App\Models\Evaluation;

class EvaluationManager
{
    /**
     * Get max depth of item
     * @param array $item
     */
    public function getEvaluationFormDepth($item) {
        if (isset($item['subs'])) {
            $maxDepth = 0;
            foreach ($item['subs'] as $sub) {
                $depth = $this->getEvaluationFormDepth($sub) + 1;
                if ($depth > $maxDepth) {
                    $maxDepth = $depth;
                }
            }
        } else {
            $maxDepth = 1;
        }

        return $maxDepth;
    }

    /**
     * Get item html of item
     * @param array $item
     * @param integer $depth
     * @param integer $maxDepth
     */
    public function getEvaluationFromHtml($item, $depth, $maxDepth) {
        $html = '';
        $nChilds = 0;
        if (isset($item['subs'])) {
            $firstItemHtml = null;
            foreach ($item['subs'] as $sub) {
                [$subHtml, $subChilds] = $this->getEvaluationFromHtml($sub, $depth+1, $maxDepth);
                if ($firstItemHtml == null) {
                    $firstItemHtml = $subHtml;
                } else {
                    $html .= $subHtml;
                }
                $nChilds += $subChilds;
            }
            $itemHtml = '';
            if (isset($item['label'])) {
                $itemHtml = '<td class="depth" rowspan=":nChilds">'.$item['label'].'<br>('.$item['percent'].'%)</td>';
                $itemHtml = str_replace(':nChilds', $nChilds,$itemHtml);
            }
            $html = $itemHtml.$firstItemHtml.$html;
        } else {
            $html = '<td class="depth">'.$item['label'].'<br>('.$item['percent'].'%)</td></tr>';
            if ($maxDepth-$depth > 1) {
                $html = '<td class="depth" colspan="'.($maxDepth-$depth).'">'.$item['label'].'<br>('.$item['percent'].'%)</td></tr>';
            }

            $nChilds = 1;
        }
        return [$html, $nChilds];
    }
    /**
     * Get score and comment of evaluation
     * @param array $item
     * @param string $point
     */
    public function getScoreComment($item , $point = null) {
        for ($i = 0; $i < count($item); $i++) {
            if (isset($item[$i]['subs'])) {
                $point .= $this->getScoreComment($item[$i]['subs']);
            }
            else {
                $point .= '<td data-toggle="tooltip" title="'.$item[$i]['comment'].'">'.($item[$i]['score'] != Evaluation::NOT_EVALUATE? $item[$i]['score']: trans('language.not_evaluate')).'<p class="comment-show text-break">'.$item[$i]['comment'].'</p>'.'</td>'.'</tr>';
            }
        }
        return $point;
    }
    /**
     * Get content of evaluation form
     * @param string $evaluationForm
     */
    public function getContent($evaluationForm) {
        $formContentArray = json_decode($evaluationForm, true);
        $formContent['subs'] = $formContentArray;
        $allRows = [];
        $maxDepth = $this->getEvaluationFormDepth($formContent);

        $itemHtml = $this->getEvaluationFromHtml($formContent, 0, $maxDepth)[0];
        $itemRows = explode('</tr>', $itemHtml);
        foreach ($itemRows as $itemRow) {
            $allRows[] = '<tr>'.$itemRow;
        }
        unset($allRows[count($allRows)-1]);
        return [
            'allRows' => $allRows,
            'maxDepth' => $maxDepth
        ];
    }
    /**
     * Gets the evaluation array, returns the rated score
     * @param array $content
     * @return int
     */
    public function getScore($content,$evaluations,$arrLevel=[])
    {
        $scoreItem = -1;
        $percentNotRate = 0;
        foreach ($content as $key=>$item){
            $listlevel = array_merge($arrLevel,[$key]);
            if (isset($item['subs'])){
                $listlevel[] = 'subs';
                $scoreSub = $this->getScore($item['subs'],$evaluations,$listlevel);
                if($scoreSub == Evaluation::NOT_EVALUATE){
                    $percentNotRate += $item['percent'];
                }else{
                    if($scoreItem == Evaluation::NOT_EVALUATE){
                        $scoreItem = $scoreSub*$item['percent'];
                    }else{
                        $scoreItem += $scoreSub*$item['percent'];
                    }
                }
            }else{
                $coefficient = 0;
                $totalScore = Evaluation::NOT_EVALUATE;
                foreach ($evaluations as $evaluation){
                    $itemScoreUser = json_decode($evaluation->content, true);

                    foreach ($listlevel as $level){
                        $itemScoreUser = $itemScoreUser[$level];
                    }
                    switch ($evaluation->group_role) {
                        case ProjectGroupRole::PROJECT_MANAGER:
                            $coefficientRole = ProjectGroupRole::PROJECT_MANAGER_COEFFICIENT;
                            break;
                        case ProjectGroupRole::LEADER:
                            $coefficientRole = ProjectGroupRole::LEADER_COEFFICIENT;
                            break;
                        default:
                            $coefficientRole = ProjectGroupRole::STAFF_COEFFICIENT;
                            break;
                    }
                    $scoreItemUser = $itemScoreUser['score']??0;
                    // User chooses point
                    if($scoreItemUser != Evaluation::NOT_EVALUATE){
                        if($totalScore == Evaluation::NOT_EVALUATE){
                            $totalScore = $scoreItemUser*$coefficientRole;
                            $coefficient += $coefficientRole;
                        }else{
                            $totalScore += $scoreItemUser*$coefficientRole;
                            $coefficient += $coefficientRole;
                        }
                    }
                }
                // has evaluate
                if ($totalScore != Evaluation::NOT_EVALUATE){
                    if($scoreItem == Evaluation::NOT_EVALUATE){
                        $scoreItem = $totalScore/$coefficient*$item['percent'];
                    }else{
                        $scoreItem += $totalScore/$coefficient*$item['percent'];
                    }
                }else{
                    $percentNotRate += $item['percent'];
                }
            }
        }
        if($scoreItem != Evaluation::NOT_EVALUATE){
            $scoreItem = $scoreItem / (100 - $percentNotRate);
        }
        return $scoreItem;
    }
     /**
     * Get html filter
     * @param Request $request
     * @param array $fields
     */

    public function getFilterHtml(Request $request,$fields = []){
        $filterHtml = "";
        $dateFormatManager = new DateFormatManager();
        foreach ($fields as $field){
            $value = '';
            if ($request->has($field) && $request->$field != null){
                if($field == 'members'){
                    foreach($request->members as $member){
                        $member = \App\User::find($member);
                        $value = isset($member)?($member->first_name. ' ' . $member->last_name):'';
                        $filterHtml.= '<span class="badge badge-primary badge-filter bgr">'.$value.'</span> ';
                    } 
                } elseif ('start_at' || 'end_at') {
                    $value = $request->$field;
                    $filterHtml.= '<span class="badge badge-primary badge-filter bgr">'.$dateFormatManager->dateFormatInput($request->$field,'d/m/Y').'</span> ';
                } 
                else{
                    $value = $request->$field;
                    $filterHtml.= '<span class="badge badge-primary badge-filter bgr">'.$value.'</span> ';
                }
            }
        }
        return $filterHtml;
    }

    public function createEvaluate($applyFor, $evaluationForm, $evaluator,$minTimeProject=null){
        if ($minTimeProject === null){
            $minTimeProject = SiteSetting::minTimeProject();
        }
        // Get all users who is evaluated
        $evaluatedUsers = ProjectMember::select('project_members.user_id as id')
            ->leftJoin('projects', 'projects.id', '=', 'project_members.project_id')
            ->leftJoin('users', 'users.id','project_members.user_id')
            ->leftJoin('project_roles', 'project_roles.id','project_members.role_id')
            ->leftJoin('project_group_roles', 'project_roles.group_role_id','project_group_roles.id')
            // Check the project must be active in the evaluation period
            ->where(function($query) use ($evaluationForm) {
                $query->where('projects.started_at', '<=', $evaluationForm->ended_at)
                    ->whereRaw("(projects.ended_at IS NULL OR projects.ended_at >= '".$evaluationForm->started_at."')");
            })
            // Check the evaluator must be added to the project before the evaluation end
            ->whereRaw("DATEDIFF('" . $evaluationForm->ended_at . "', project_members.created_at) + 1 >= ". $minTimeProject)
            ->whereRaw("(projects.ended_at IS NULL OR DATEDIFF(projects.ended_at, project_members.created_at) + 1 >= ". $minTimeProject.")")
            ->whereIn('project_group_roles.id', $applyFor)
            ->whereNull('users.deleted_at')
            ->groupBy('project_members.user_id')
            ->get();
        // Get all evaluations which will be sent to all evaluators
        $evaluations = [];
        foreach ($evaluatedUsers as $evaluatedUser) {
            // Get all evaluators who will evaluate the user
            $evaluators = ProjectMember::leftJoin('projects', 'projects.id', '=', 'project_members.project_id')
                ->leftJoin('project_roles', 'project_roles.id','project_members.role_id')
                ->leftJoin('project_group_roles', 'project_roles.group_role_id','project_group_roles.id')
                ->leftJoin('users', 'users.id','project_members.user_id')
                ->select('project_members.user_id')
                // The evaluator different to the evaluated user
                ->where('project_members.user_id', '!=', $evaluatedUser->id)
                // Check the project must be active in the evaluation period
                ->where(function($query) use ($evaluationForm) {
                    $query->where('projects.started_at', '<=', $evaluationForm->ended_at)
                        ->whereRaw("(projects.ended_at IS NULL OR projects.ended_at >= '".$evaluationForm->started_at."')");
                })
                // Check the evaluated user must be added to the project before the evaluation end
                ->whereRaw(
                    'project_id IN (
                        SELECT project_id
                        FROM project_members
                        LEFT JOIN projects on projects.id = project_members.project_id
                        WHERE user_id = ? AND DATEDIFF(?,project_members.created_at) + 1 >= ? AND (projects.ended_at IS NULL or DATEDIFF(projects.ended_at,project_members.created_at) + 1 >= ?)
                        GROUP BY project_id
                    )', [$evaluatedUser->id, $evaluationForm->ended_at, $minTimeProject, $minTimeProject])
                // Check the evaluator must be added to the project before the evaluation end
                ->whereRaw("DATEDIFF('" . $evaluationForm->ended_at . "', project_members.created_at) + 1  >= ". $minTimeProject)
                ->whereRaw("(projects.ended_at IS NULL OR DATEDIFF(projects.ended_at, project_members.created_at) + 1 >= ". $minTimeProject.")")
                ->whereIn('project_group_roles.id', $evaluator)
                ->whereNull('users.deleted_at')
                ->groupBy('project_members.user_id')
                ->get();

            // Check for duplicate members and send evaluation sheets to team members
            foreach ($evaluators as $evaluatorUser) {
                if (isset($evaluations[$evaluatedUser->id][$evaluatorUser->user_id])) {
                    continue;
                }
                $evaluations[$evaluatedUser->id][$evaluatorUser->user_id] = true;

                // Create a evaluation
                Evaluation::create(
                    [
                        'evaluator_id' => $evaluatorUser['user_id'],
                        'form_id' => $evaluationForm->id,
                        'user_id' => $evaluatedUser->id,
                        'content' => '',
                        'created_by' => $evaluationForm->created_by
                    ]
                );
            }
            // Create a evaluation results
            EvaluationResult::create(
                [
                    'form_id' => $evaluationForm->id,
                    'user_id' => $evaluatedUser->id,
                ]
            );
        }
    }
}
