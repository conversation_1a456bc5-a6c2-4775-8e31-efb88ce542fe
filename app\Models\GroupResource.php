<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Resource
 * @property integer $id
 * @property string $name
 * @property string $description
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */
class GroupResource extends Model
{
    use SoftDeletes;
     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    /**
     * Get resources for the group_resources
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function resources()
    {
    	return $this->hasMany('App\Models\Resource','group_id','id');
    }
}
