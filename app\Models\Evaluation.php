<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Model;
use Kyslik\ColumnSortable\Sortable;

class Evaluation extends Model
{
    use Sortable;
    protected $table = 'evaluations';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }

    const NOT_EVALUATE = -1;
}
