<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Support\Carbon;

class TaskAttachment extends Model
{
    const TYPE_PROJECT_TASK = 0;
    const TYPE_TASK_LOG = 1;
    const TYPE_PROJECT = 2;
    const TYPE_MENU = 3;
    const TYPE_REQUEST = 4;
    const TYPE_CONTEST = 5;
    const TYPE_ASSET = 6;

    const TYPE_ASSET_BIDDING_PACKAGE = 7;
    const TYPE_INVENTORY = 8;
    
    protected $table = 'task_attachments';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
            $data->updated_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_by = auth()->id();
        });
    }
}
