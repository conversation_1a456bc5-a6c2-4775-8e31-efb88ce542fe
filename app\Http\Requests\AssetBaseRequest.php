<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssetBaseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'asset_branch_name' => 'required|max:200',
        ];
        
        return $rules;
    }


    /**
     * attributes of validation rules
     *
     * @return array
     */

     public function attributes()
     {
         return [
            'asset_branch_name' => trans('language.site_setting_asset_branch.name'),
         ];
     }
}
