<?php

namespace App\Console\Commands;

use App\Logics\TenantManager;
use App\User;
use Hyn\Tenancy\Contracts\Repositories\HostnameRepository;
use Hyn\Tenancy\Contracts\Repositories\WebsiteRepository;
use Hyn\Tenancy\Environment;
use App\Models\Tenant\CompanyProfile;
use App\Models\Tenant\Hostname;
use App\Models\Tenant\Website;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class CreateTenant extends Command
{
    protected $signature = 'tenant:create {name} {email} {tenantname}';
    protected $description = 'Creates a tenant with the provided name and email address e.g. php artisan tenant:<NAME_EMAIL> cafejohn';

    public function handle()
    {
        $name = $this->argument('name');
        $email = $this->argument('email');
        $tenantname = $this->argument('tenantname');

        $tenantManager = new TenantManager();

        if ($tenantManager->tenantExists($tenantname)) {
            $this->error("A tenant with name '{$tenantname}' already exists.");
            return;
        }
        $tenant = $tenantManager->registerTenant(['sub_domain' => $tenantname]);
        app(Environment::class)->tenant($tenant["website"]);

        // Register new companyprofile
        $companyProfile = CompanyProfile::create([
            'website_id' => $tenant["website"]->id,
            'name' => $name,
            'email' => $email,
        ]);

        $password = Str::random(16);
        $tenantManager->addAdmin(['email' => $email], $password);
        $this->info("Tenant '{$tenantname}' is created and is now accessible at {$tenant['hostname']->fqdn}");
        $this->info("Admin {$email} can log in using password {$password}");
    }
}

