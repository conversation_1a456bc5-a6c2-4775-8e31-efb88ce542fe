<?php

namespace App\Http\Controllers\Api;

use App\Helpers\RequestHelper;
use App\Logics\EventManager;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Logics\TaskManager;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class CalendarController extends Controller
{
    public function getData(Request $request){
        $params = (new RequestHelper())->getParamsFromRequest($request);
        $params['member'] = [Auth::id()];

        $result = (new TaskManager())->getTaskCalendar($params);
        $events = (new EventManager())->getListEventCalendar($params);
        $result = array_merge($result, $events);

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('language.success'),
            'data' => $result
        ], Response::HTTP_OK);
    }
}
