<?php

namespace App\Models;

use App\Models\Model;
use <PERSON><PERSON>lik\ColumnSortable\Sortable;

/**
 * Class ProjectDocument
 * @property integer $id
 * @property integer $project_id
 * @property string $file_name
 * @property string $file_path
 * @property decimal $file_size
 * @property integer $created_by
 * @property dateTime $created_at
 */
class ProjectDocument extends Model
{
    use Sortable;
    protected $table = 'project_documents';
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'file_name',
        'file_size',
        'created_at',
        'pinned_at'
    ];
    public $sortable = [
        'file_name',
        'file_size',
        'created_at',
        'pinned_at'
    ];
}
