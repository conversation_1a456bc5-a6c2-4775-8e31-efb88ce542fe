<?php

namespace App\Http\Middleware;

use App\Models\Project;
use Closure;
use Illuminate\Support\Facades\Auth;

class ProjectPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $userId = Auth::id();
        $id = isset($request->projectId) ? $request->projectId : $request->id;
        $userPermission = Project::select('projects.id')
            ->where('id', $id)
            ->checkUserPermission($userId)
            ->first();
        if (!isset($userPermission)) {
            return redirect()->route('project.index');
        }
        return $next($request);
    }
}
