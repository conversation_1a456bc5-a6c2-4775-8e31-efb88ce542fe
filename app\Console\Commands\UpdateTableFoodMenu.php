<?php

namespace App\Console\Commands;

use App\Models\FoodMenu;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UpdateTableFoodMenu extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_table_food_menu  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update column date';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        FoodMenu::whereNotNull('updated_at')->update(["date" => DB::raw('DATE(updated_at)')]);
    }
}
