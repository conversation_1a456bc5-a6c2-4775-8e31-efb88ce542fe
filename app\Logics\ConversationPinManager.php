<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Models\ConversationParticipant;
use App\Models\ConversationPin;
use Exception;
use \Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class ConversationPinManager
{
    public function pinConversation($idUser, $conversationId){
        try {
            if (!$this->checkPermissionConversation($conversationId, $idUser)) {
                return [['status_error' => Response::HTTP_FORBIDDEN],__('message.conversation.action.pin')];
            }
            $conversationPin = ConversationPin::where([
                'user_id' => $idUser,
                'conversation_id' => $conversationId,
            ])->first();
            
            if($conversationPin){
                $conversationPin->delete();
                $event = SocketEvent::UNPIN;
                $action = __('message.conversation.action.unpin');
            }else{
                $conversationPin = ConversationPin::create([
                    'user_id' => $idUser,
                    'conversation_id' => $conversationId,
                ]);
                $event = SocketEvent::PIN;
                $action = __('message.conversation.action.pin');
            }
            
            // send noti to socket
            $conversationPin->receiver = [$idUser];

            // Send socket pin/unpin conversation.
            app(SocketManager::class)->emit($event, $conversationPin);
            
            //Emit event updateConversation
            $conversationManager = new ConversationManager();
            $conversationManager->sendNotificationSocketUpdateConversation($conversationId, [$idUser], ['conversation_pin']);
            return [$conversationPin, $action];
        } catch (Exception $e) {
            Log::error("[ConversationService][pinConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[ConversationService][pinConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }
    
    /**
     * check permission message
     * @param $id
     * @param $idUser
     * @param $type
     * @param string[] $column
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */

    public function checkPermissionConversation($id, $idUser)
    {
         return ConversationParticipant::query()
             ->where('conversation_participants.conversation_id', $id)
             ->where('conversation_participants.user_id', $idUser)
             ->first();
    }
}