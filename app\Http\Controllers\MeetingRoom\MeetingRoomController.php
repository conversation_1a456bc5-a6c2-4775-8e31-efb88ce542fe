<?php

namespace App\Http\Controllers\MeetingRoom;

use App\Helpers\RequestHelper;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Logics\MeetingRoomManager;
use App\Models\GroupResource;
use App\Models\Resource;
use Illuminate\Http\Request;

class MeetingRoomController extends Controller
{

    /**
     * View meeting room calendar
     * @param \Illuminate\Http\Request $request
     * @return View
     */
    public function index(Request $request){

        $filterHtml = "";
        $fields = ['group_resource', 'resource'];
        foreach ($fields as $field){
            $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
            $tagSpanClose = '</span>';
            $value = '';
            if ($request->has($field) && $request->$field!= null){
                switch ($field){
                    case 'group_resource':
                        $groupResources = GroupResource::select('name')->where('id',$request->group_resource)->first();
                        $value .= $tagSpanOpen . $groupResources->name . $tagSpanClose;
                        break;
                    case 'resource':
                        $resources = Resource::select('name')->whereIn('id',$request->resource)->get();
                        foreach ($resources as $resource){
                            $value .= $tagSpanOpen . $resource->name . $tagSpanClose;
                        }
                        break;
                    default:
                        $value = $tagSpanOpen.StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $filterHtml.= $value;
            }
        }

        // Get list resources
        $resources = [];
        if(isset($request->group_resource)){
            $resources = Resource::select(['id','name'])
                ->where('group_id', $request->group_resource)
                ->where('type', Resource::TYPE_MEETING_ROOM)
                ->orderby('id')
                ->get();
        }

        // Get list group_resources
        $groupResources = GroupResource::select(['id','name'])->orderby('id')->get();

        $time = isset($request->time)?$request->time:date('m/Y');

        //Xử lý lấy ra time
        $arrTime = explode('/',$time);

        $month = $arrTime[0];
        $year = $arrTime[1];

        $timeFormatYmd = $year . '-' . $month . '-01';

        $nextTime = date('m/Y',mktime(0, 0, 0, $month+1,1, $year));
        $prevTime = date('m/Y',mktime(0, 0, 0, $month-1,1, $year));

        $currentTime = date('m/Y');

        return view('meeting_room.meeting-room-calendar',[
            'time' => $time,
            'nextTime' => $nextTime,
            'prevTime' => $prevTime,
            'currentTime' => $currentTime,
            'timeFormatYmd' => $timeFormatYmd,
            'filterHtml' => $filterHtml,
            'groupResources' => $groupResources,
            'resources' => $resources,
        ]);
    }

    /**
     * Get data meeting room calendar
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDataMeetingRoomCalendar(Request $request){
        $time = isset($request->time)?$request->time:date('m/Y');
        //Xử lý lấy ra time
        $arrTime = explode('/',$time);
        $month = $arrTime[0];
        $year = $arrTime[1];
        $from = '01/'.$month.'/'.$year;
        $to = cal_days_in_month(CAL_GREGORIAN, $month, $year).'/'.$month.'/'.$year;
        $params = (new RequestHelper())->getParamsFromRequest($request);

        $params['from'] = $from;
        $params['to'] = $to;

        $result = (new MeetingRoomManager())->getListEventCalendar($params);

        return response()->json($result);
    }

    /**
     * get all resources from group resource
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function getResourceList(Request $request){
        $groupResourceId = $request->id;
        $resources = Resource::select(['id','name'])
            ->where('group_id', $groupResourceId)
            ->where('type', Resource::TYPE_MEETING_ROOM)
            ->orderBy('group_id')
            ->get();
        $options = [];
        if(count($resources) > 0){
            foreach ($resources as $resource) {
                $options[] = [
                    'value' => $resource->id,
                    'text' => $resource->name
                ];
            }
        }
        return [
            'status' => 200,
            'options' => $options
        ];
    }
}
