<?php

namespace App\Console\Commands;

use App\Logics\UserManager;
use App\Traits\StorageTrait;
use App\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Contracts\Filesystem\FileNotFoundException;

class UpdateUserInfoTimekeeping extends Command
{
    use StorageTrait;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_user_info_timekeeping {--userId=} {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update all user info timekeeping';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
    
    /**
     * Execute the console command.
     *
     * @return void
     * @throws FileNotFoundException|Exception
     */
    public function handle()
    {
        $this->info("UpdateUserInfoTimekeeping Start");
        $userId = $this->option('userId') ?? 0;
        $users = User::select('id', 'name', 'gender', 'face_image')
            ->whereNotNull('face_image')
            ->where('id', '>=', $userId)
            ->orderBy('id', 'asc')
            ->get();

        $isUpdate = false;
        $userManage = new UserManager();
        foreach ($users as $user) {
            $faceImage = $this->createUploadedFileFromFilePath($user->face_image);
            $response = $userManage->updateUserInfoToTimekeepingDevice($user, $isUpdate, $faceImage);
            if (isset($response["info"]["Result"]) && $response["info"]["Result"] == "Ok") {
                $this->info("UpdateUserInfoTimekeeping: Update user info timekeeping success User_id = " . $user->id);
            } else {
                $result = $response["info"]["Result"] ?? null;
                $detail = $response["info"]["Detail"] ?? null;
                $this->warn("UpdateUserInfoTimekeeping: Update user info timekeeping error response result: "
                    . $result);
                $this->warn("UpdateUserInfoTimekeeping: Update user info timekeeping error response detail: "
                    . $detail);
                $this->error("UpdateUserInfoTimekeeping error: User_id = " . $user->id);
            }
        }
        $this->info("UpdateUserInfoTimekeeping End");
    }
}
