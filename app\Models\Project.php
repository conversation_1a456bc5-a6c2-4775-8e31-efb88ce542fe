<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Support\Facades\DB;
use K<PERSON>lik\ColumnSortable\Sortable;

class Project extends Model
{
    use Sortable;

    protected $table = 'projects';
    const STATUS_OPEN=1;
    const STATUS_CLOSE=2;
    const STATUS_ALL=3;

    const OPEN = 1;
    const CLOSE = 2;
    const ALL = 3;

    const IS_SLOW = 1;
    const NOT_SLOW = 0;
    /**
     * Get the user who create project
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function userCreateProject()
    {
        return $this->belongsTo('App\User','created_by','id');
    }

    /**
     * Get the parent project of the project
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parentProject()
    {
        return $this->belongsTo(Project::class,'parent_project','id');
    }

    /**
     * Get the member who join project
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function members()
    {
        return $this->belongsToMany('App\User','project_members','project_id','user_id');
    }

    /**
     * Scope check the user has permission to access the project
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeCheckUserPermission($query, $userId) {
        return $query->leftJoin('project_members', 'project_members.project_id', 'projects.id')
            ->where(function($q) use ($userId) {
                $q->where('projects.public','=',1)
                    ->orWhere('project_members.user_id', '=', $userId);
            });
    }

    /**
     * Scope calculate progress, started_at, ended_at of the project
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeUpdateProject($query) {
        return $query->addSelect(
            DB::raw('SUM(child_tasks.progress * child_tasks.estimated_time) / SUM(child_tasks.estimated_time) AS updated_progress'),
            DB::raw('MIN(child_tasks.started_at) AS updated_started_at'),
            DB::raw('MAX(child_tasks.ended_at) AS updated_ended_at')
        )
        ->leftJoin('project_tasks AS child_tasks', function ($q) {
            $q->on('child_tasks.project_id', '=', 'projects.id')
            ->whereNull('child_tasks.parent_task');
        });
    }
}
