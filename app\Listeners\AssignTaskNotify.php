<?php

namespace App\Listeners;

use App\Events\AssignTask;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Jobs\SendEmailAssignTask;

class AssignTaskNotify
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  AssignTask  $event
     * @return void
     */
    public function handle(AssignTask $event)
    {
        dispatch(new SendEmailAssignTask($event->task, $event->websiteId))->onQueue(QUEUE_MAIL);
    }
}
