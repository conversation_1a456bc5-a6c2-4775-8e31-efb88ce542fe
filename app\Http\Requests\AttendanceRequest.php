<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AttendanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_id' => 'required',
            'checked_at' => 'required|date_format:d/m/Y H:i:s',
            'checked_type' => 'required|numeric',
            'checked_image_file' => 'nullable|mimes:jpeg,png,jpg|max:1024',
        ];
    }
}
