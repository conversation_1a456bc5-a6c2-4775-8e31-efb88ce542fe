<?php

namespace App\Http\Controllers\Project;

use App\Http\Controllers\Controller;
use App\Logics\UserManager;
use App\Models\BugTag;
use App\Models\Kanban;
use App\Models\ProjectRole;
use App\Models\ProjectTask;
use App\Models\SiteSetting;
use App\Models\TaskType;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use App\Logics\ProjectManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\AttachmentManager;
use App\Models\TaskAttachment;
use App\Helpers\StringHelper;
use App\Logics\TaskManager;
use App\Events\UpdateTask;
use App\Models\TaskAction;
use App\Models\TaskLog;
use App\Models\TaskStatus;

class TaskOfTheDayController extends Controller
{
    public function index($projectId)
    {
        $userId = Auth::id();

        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        $collectKanbans = Kanban::query()
            ->leftJoin('project_tasks', function ($join) use ($projectId) {
                $join->on('project_kanbans.status', '=', 'project_tasks.status')
                    ->where('project_tasks.project_id', $projectId)
                    ->where(function ($query) {
                        $query->where('project_tasks.type', '!=', TaskType::BUG)
                            ->orWhereNull('project_tasks.type');
                    });
            })
            ->leftJoin('project_sprints',function ($join){
                $join->on('project_tasks.sprint_id', '=', 'project_sprints.id')
                ->whereRaw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) <= 0')
                ->where('project_sprints.status',ProjectTask::SPRINT_ACTIVE);
            })
            ->where('project_kanbans.project_id', $projectId)
            ->whereNull('project_kanbans.deleted_at')
            ->select([
                'project_kanbans.id',
                'project_kanbans.title',
                'project_kanbans.status',
                DB::raw('COUNT(project_sprints.id) AS totalTaskEachStatus')
            ])
            ->groupBy('project_kanbans.id')
            ->orderBy('project_kanbans.sort_order', 'ASC')
            ->get();
        $arrKanbanID = $collectKanbans->pluck('status')->toArray();
        $displayId = SiteSetting::displayTaskProjectId();

        $collectTasks = ProjectTask::query()
            ->checkUserPermission($userId)
            ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
            ->leftJoin('users', 'project_tasks.user_id', 'users.id')
            ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
            ->leftJoin('bug_tags', 'project_tasks.bug_tag_id', 'bug_tags.id')
            ->leftJoin('project_sprints', 'project_tasks.sprint_id', 'project_sprints.id')
            ->select([
                'task_types.name as type_name',
                'project_tasks.id',
                'project_tasks.project_id',
                'project_tasks.status',
                'project_tasks.name',
                'project_tasks.key_member',
                'project_tasks.estimated_time',
                'project_tasks.ended_at',
                'users.first_name',
                'users.last_name',
                'users.id as user_id',
                'task_status.name as status_name',
                'task_status.id as status_id',
                'bug_tags.name as bug_tags_name',
                'project_sprints.status as sprint_status',
                DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
            ])
            ->where('project_tasks.project_id', $projectId)
            ->where('project_sprints.status',ProjectTask::SPRINT_ACTIVE)
            ->where(function ($query) {
                $query->where('project_tasks.type', '!=', TaskType::BUG)
                    ->orWhereNull('project_tasks.type');
            })
            ->whereIn('project_tasks.status', $arrKanbanID)
            //            ->whereDate('project_tasks.started_at', \Carbon\Carbon::now()->format('Y-m-d'))
            //            ->whereNotNull(['project_tasks.started_at','project_tasks.ended_at'])
//            ->orderBy('project_tasks.updated_at', 'DESC')
            ->orderBy('project_tasks.position')
            ->having('number_child', '=', 0)
            ->get();
        $collectTags = BugTag::query()->select(['id', 'name'])->get();

        $collectSprint = DB::table('project_sprints')
            ->select(['id', 'name'])
            ->where('project_id',$projectId)
            ->where('status',ProjectTask::SPRINT_ACTIVE)
            ->get();

        $status = DB::table('project_kanbans')->select('project_kanbans.title', 'task_status.id as status_id', 'task_status.color')
            ->leftJoin('task_status', 'task_status.id', 'project_kanbans.status')
            ->where('project_kanbans.project_id', $projectId)
            ->get();

        $collectKanbans = $this->getTimeEst($collectKanbans, $collectTasks);
        // Check user is project manager
        $userManager = new \App\Logics\UserManager();
        $isManager = $userManager->hasProjectRole(auth()->id(), $project->id, \App\Models\ProjectRole::ProjectManager);
        return view('task.task_of_the_day', [
            'collectKanbans' => $collectKanbans,
            'project' => $project,
            'collectTasks' => $collectTasks,
            'collectTags' => $collectTags,
            'collectSprint' => $collectSprint,
            'displayId' => $displayId,
            'projectId' => $projectId,
            'isManager' => $isManager,
            'status' => $status
        ]);
    }

    public function changeTaskStatus(Request $request)
    {
        try {
            $userId = Auth::id();
            $projectId = $request->projectId;
            $kanbanID = $request->kanbanId;
            $arrTaskIdOfListKanban = $request->arrTaskIdOfListKanban;
            $task = ProjectTask::query()
                ->select('project_tasks.id', 'project_tasks.status', 'project_tasks.progress')
                ->checkUserPermission($userId)
                ->where('project_tasks.id', $request->idTask)->first();
            if ($task) {
                $task->status = $request->statusLane;
                $task->progress = $request->statusLane==TaskStatus::TASK_STATUS_CLOSE?100:$task->progress;
                $taskManager = new TaskManager();
                $fieldsChange = $taskManager->getFieldsChange($task);
                //call event updateTask
                if (!empty($fieldsChange)){
                    event(new updateTask(json_encode($fieldsChange),$task->id,TaskLog::ATTRIBUTE));
                }
                $task->save();
            }

            foreach ($arrTaskIdOfListKanban as $index => $taskId) {
                ProjectTask::query()->where('id', $taskId)
                    ->update([
                        'kanban_id' => $kanbanID,
                        'position' => $index + 1
                    ]);
            }
            $collectKanbans = Kanban::query()
                ->leftJoin('project_tasks', function ($join) use ($projectId) {
                    $join->on('project_kanbans.status', '=', 'project_tasks.status')
                        ->where('project_tasks.project_id', $projectId)
                        ->where(function ($query) {
                            $query->where('project_tasks.type', '!=', TaskType::BUG)
                                ->orWhereNull('project_tasks.type');
                        });
                })
                ->leftJoin('project_sprints',function ($join){
                    $join->on('project_tasks.sprint_id', '=', 'project_sprints.id')
                    ->where('project_sprints.status',ProjectTask::SPRINT_ACTIVE)
                    ->whereRaw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) <= 0');
                })
                ->where('project_kanbans.project_id', $projectId)
                ->whereNull('project_kanbans.deleted_at')
                ->select([
                    'project_kanbans.id',
                    'project_kanbans.title',
                    'project_kanbans.status',
                    DB::raw('COUNT(project_sprints.id) AS totalTaskEachStatus')
                ])
                ->groupBy('project_kanbans.id')
                ->orderBy('project_kanbans.sort_order', 'ASC')
                ->get();

            return response()->json([
                'message' => 'success',
                'status' => 200,
                'list_kanban' => $collectKanbans
            ]);
        } catch (Exception $e) {
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return response()->json([
                'message' => 'error',
                'status' => 400
            ], 400);
        }
    }

    public function ajaxFilterData(Request $request)
    {
        try {
            $userId = Auth::id();
            $projectId = $request->projectId;

            $collectKanbans = Kanban::query()
                ->leftJoin('project_tasks', function ($join) use ($projectId) {
                    global $request;
                    $join->on('project_kanbans.status', '=', 'project_tasks.status')
                        ->where('project_tasks.project_id', $projectId)
                        ->where(function ($query) {
                            $query->where('project_tasks.type', '!=', TaskType::BUG)
                                ->orWhereNull('project_tasks.type');
                        })
                        ->where(function ($query) use ($request) {
                            $query->where('project_tasks.name', 'LIKE', "%{$request->search}%")
                                ->orWhere('project_tasks.id', 'LIKE', "%$request->search%");
                        });
                    if (isset($request->tagsId)) {
                        $join->whereIn('project_tasks.bug_tag_id', $request->tagsId);
                    }
                })
                ->leftJoin('project_sprints',function ($join) use($projectId){
                    global $request;
                    $join->on('project_tasks.sprint_id', '=', 'project_sprints.id')
                    ->where('project_sprints.project_id', $projectId)
                    ->whereRaw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) <= 0');
                    if (isset($request->sprint)) {
                        $join->whereIn('project_tasks.sprint_id', $request->sprint);
                    }else{
                        $join->where('project_sprints.status',ProjectTask::SPRINT_ACTIVE);
                    }
                })
                ->where('project_kanbans.project_id', $projectId)
                ->whereNull('project_kanbans.deleted_at')
                ->select([
                    'project_kanbans.id',
                    'project_kanbans.title',
                    'project_kanbans.status',
                    DB::raw('COUNT(project_sprints.id) AS totalTaskEachStatus')
                ])
                ->groupBy('project_kanbans.id')
                ->orderBy('project_kanbans.sort_order', 'ASC')
                ->get();

            $arrKanbanID = $collectKanbans->pluck('status')->toArray();
            $displayId = SiteSetting::displayTaskProjectId();

            $collectTasks = ProjectTask::query()
                ->checkUserPermission($userId)
                ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
                ->leftJoin('users', 'project_tasks.user_id', 'users.id')
                ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
                ->leftJoin('bug_tags', 'project_tasks.bug_tag_id', 'bug_tags.id')
                ->leftJoin('project_sprints', 'project_tasks.sprint_id', 'project_sprints.id')
                ->leftJoin('kanban_tags','project_tasks.id','kanban_tags.task_id')
                ->select([
                    'task_types.name as type_name',
                    'project_tasks.id',
                    'project_tasks.status',
                    'project_tasks.project_id',
                    'project_tasks.name',
                    'project_tasks.estimated_time',
                    'project_tasks.ended_at',
                    'project_tasks.started_at',
                    'users.first_name',
                    'users.last_name',
                    'users.id as user_id',
                    'task_status.name as status_name',
                    'task_status.id as status_id',
                    DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
                ])
                ->where('project_sprints.project_id', $projectId)
                ->where(function ($query) use ($arrKanbanID, $projectId, $request) {
                    $query->where('project_tasks.project_id', $projectId)
                        ->where(function ($query) {
                            $query->where('project_tasks.type', '!=', TaskType::BUG)
                                ->orWhereNull('project_tasks.type');
                        })
                        ->whereIn('project_tasks.status', $arrKanbanID);
                });
            if (isset($request->search)) {
                $collectTasks->where(function ($query1) use ($request) {
                    $query1->where('project_tasks.name', 'LIKE', "%{$request->search}%")
                        ->orWhere('project_tasks.id', 'LIKE', "%$request->search%");
                });
            }
            if (!empty($request->input('userSelect'))) {
                $collectTasks->whereIn('project_tasks.user_id', $request->input('userSelect'));
            }
            if (isset($request->tagsId)) {
                $collectTasks->whereIn('kanban_tags.tag_id', $request->tagsId);
            }
            if (isset($request->sprint)) {
                $collectTasks = $collectTasks->whereIn('project_tasks.sprint_id', $request->sprint);
            }else{
                $collectTasks = $collectTasks->where('project_sprints.status',ProjectTask::SPRINT_ACTIVE);
            }
            $collectTasks = $collectTasks->having('number_child', '<=', 0)->orderBy('project_tasks.updated_at', 'DESC')->orderBy('project_tasks.position')->distinct()->get();
            $collectTags = BugTag::query()->select(['id', 'name'])->get();
            $collectKanbans = $this->getTimeEst($collectKanbans, $collectTasks);
            $html = view('task.partials.list-task-kanban', [
                'collectKanbans' => $collectKanbans,
                'collectTasks' => $collectTasks,
                'displayId' => $displayId,
                'projectId' => $projectId,
                'collectTags' => $collectTags

            ])->render();
            return response()->json([
                'html' => $html,
                'status' => Response::HTTP_OK,
            ]);
        } catch (Exception $e) {
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return response()->json([
                'message' => 'error',
                'status' => 400
            ], 400);
        }
    }

    public function updateTask(Request $request, $projectId)
    {
        if ($request->ajax()) {
            $userId = Auth::id();
            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if ($project == null) {
                return redirect()->route('project.index');
            }
            $status = DB::table('project_kanbans')->select('project_kanbans.title', 'task_status.id as status_id', 'task_status.color')
                ->leftJoin('task_status', 'task_status.id', 'project_kanbans.status')
                ->where('project_kanbans.project_id', $projectId)
                ->get();
            $changeStatus = false;

            $task = ProjectTask::find($request->id);

            $task->name = $request->name;
            $task->estimated_time = $request->estimate;
            $task->key_member = $request->key_member;
            $task->user_id = $request->user_id;
            $task->started_at = isset($request->start_at) ? \Carbon\Carbon::createFromFormat('d/m/Y', $request->start_at)->format('Y-m-d 00:00:00') : $request->start_at;
            $task->ended_at = isset($request->end_at) ? \Carbon\Carbon::createFromFormat('d/m/Y', $request->end_at)->format('Y-m-d 00:00:00') : $request->end_at;
            if (isset($request->status)) {
                $task->status = $request->status;
                $changeStatus = true;
            }

            $taskManager = new TaskManager();
            $fieldsChange = $taskManager->getFieldsChange($task);
            $task->save();

            //call event updateTask
            if (!empty($fieldsChange)){
                event(new updateTask(json_encode($fieldsChange),$task->id,TaskLog::ATTRIBUTE));
            }

            $tags = BugTag::select('name')->pluck('name');
            if (isset($request->tags)) {
                $newTags = array_diff($request->tags, $tags->toArray());
                if (isset($newTags)) {
                    $newTags = collect($newTags)->map(function ($item) {
                        return ['name' => $item];
                    });
                    BugTag::insert($newTags->toArray());
                }
                $data = [];
                $idTags = BugTag::whereIn('name', $request->tags)->select('id')->pluck('id');

                DB::table('kanban_tags')->where('task_id', $task->id)->delete();

                foreach ($idTags as $id) {
                    $data[] = [
                        'task_id' => $task->id,
                        'tag_id' => $id,
                    ];
                }
                DB::table('kanban_tags')->insert($data);
            } else {
                DB::table('kanban_tags')->where('task_id', $task->id)->delete();
            }

            $task = ProjectTask::select(
                'project_tasks.id',
                'project_tasks.name',
                'project_tasks.description',
                'project_tasks.estimated_time',
                'project_tasks.key_member',
                'project_tasks.user_id',
                'project_tasks.started_at',
                'project_tasks.ended_at',
                'project_tasks.status',
                'project_tasks.project_id',
                'project_kanbans.id as kanban_id',
                'project_kanbans.title as kanban_title',
                'project_kanbans.status as kanban_status',
                'users.first_name',
                'users.last_name',
                'task_types.name as type_name',
            )
                ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
                ->leftJoin('project_kanbans', 'project_tasks.status', 'project_kanbans.status')
                ->leftJoin('users', 'project_tasks.user_id', 'users.id')
                ->where('project_tasks.id', $task->id)
                ->first();

            $listTags = BugTag::select('id', 'name')->get();

            $displayId = SiteSetting::displayTaskProjectId();

            $itemTaskHtml = view('task.kanban.item-task-kanban', ['task' => $task, 'displayId' => $displayId])->render();
            return response()->json([
                'task' => $task,
                'changeStatus' => $changeStatus,
                'status' => $status,
                'itemTaskHtml' => $itemTaskHtml
            ], Response::HTTP_OK);
        }
    }

    public function updateDescription(Request $request, $projectId)
    {
        if ($request->ajax()) {
            $userId = Auth::id();
            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if ($project == null) {
                return redirect()->route('project.index');
            }

            $task = ProjectTask::find($request->id);
            $task->description = StringHelper::escapeHtmlForSummernote($request->description);
            $taskManager = new TaskManager();
            $fieldsChange = $taskManager->getFieldsChange($task);
            $task->save();

            // Save attachments
            $data = ProjectTask::find($task->id);
            $destinationPath = str_replace(['{project_id}', '{task_id}'], [$data->project_id, $task->id], TASK_ATTACHMENT_DIR) . '/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_PROJECT_TASK, $request->documents, $request->descriptionDocument, $destinationPath, 'description');


            if (isset($fieldsChange['description'])) {
                $task = ProjectTask::find($task->id);
                $fieldsChange['description']["new_value"] = $task->description;
            }

            //call event updateTask
            if (!empty($fieldsChange)) {
                event(new updateTask(json_encode($fieldsChange), $task->id, TaskLog::ATTRIBUTE));
            }

            $taskFiles = $this->getTaskFile($task->id);

            $html = view('task.kanban.view-description', ['task' => $task])->render();
            $edit_des = view('task.kanban.item-description-task-of-day', ['task' => $task, 'showCancel' => true, 'id' => 'edit_des'])->render();
            if(!empty($taskFiles)){
                $htmlTaskFile = view('task.partials.list-file-task', ['taskFiles' => $taskFiles])->render();
            }
            return response()->json([
                'html' => $html,
                'data_edit' => $edit_des,
                'htmlTaskFile' => $htmlTaskFile
            ], Response::HTTP_OK);
        }
    }

    public function getTaskKanban(Request $request, $projectId)
    {
        if ($request->ajax()) {
            $userId = Auth::id();

            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if ($project == null) {
                return redirect()->route('project.index');
            }
            $task = ProjectTask::select(
                'project_tasks.id',
                'project_tasks.status',
                'project_tasks.name',
                'project_tasks.estimated_time',
                'project_tasks.ended_at',
                'project_tasks.started_at',
                'project_tasks.key_member',
                'project_tasks.project_id',
                'project_tasks.description',
                'users.first_name',
                'users.last_name',
                'users.id as user_id',
                'task_status.name as status_name',
                'task_status.id as status_id',
                'bug_tags.name as bug_tags_name',
                'project_kanbans.title as kanban_title',
                'task_types.name as type_name',
                'task_status.color as kanban_color'
            )->checkUserPermission($userId)
                ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
                ->leftJoin('users', 'project_tasks.user_id', 'users.id')
                ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
                ->leftJoin('bug_tags', 'project_tasks.bug_tag_id', 'bug_tags.id')
                ->leftJoin('project_kanbans', 'project_kanbans.status', 'task_status.id')
                ->where('project_tasks.id', $request->id)->first();

            $idTask = $request->id;
            $taskFiles = TaskAttachment::leftJoin('users', 'task_attachments.created_by', 'users.id')
                ->leftJoin('task_logs', function ($join) {
                    $join->on('task_logs.id', '=', 'task_attachments.related_id')
                        ->where('task_attachments.type', '=',  TaskAttachment::TYPE_TASK_LOG);
                })
                ->where(function ($query) use ($idTask) {
                    $query->where([
                        ['task_attachments.related_id', $idTask],
                        ['task_attachments.type', TaskAttachment::TYPE_PROJECT_TASK]
                    ])
                        ->orWhere([
                            ['task_logs.task_id', $idTask],
                            ['task_attachments.type', TaskAttachment::TYPE_TASK_LOG]
                        ]);
                })
                ->select([
                    'task_attachments.id',
                    'task_attachments.related_id',
                    'task_attachments.type',
                    'task_attachments.file_name',
                    'task_attachments.file_path',
                    'task_attachments.file_size',
                    'task_attachments.description',
                    'task_attachments.created_by',
                    'task_attachments.updated_by',
                    'task_attachments.created_at',
                    'task_attachments.updated_at',
                    'users.first_name',
                    'users.last_name',
                ])
                ->get();

            $taskManager = new TaskManager();
            $taskCurrentOfUser = $taskManager->getActionTaskOfUser($userId);
            $action = null;
            if (isset($taskCurrentOfUser) && $taskCurrentOfUser->type == TaskAction::START && $taskCurrentOfUser->task_id == $idTask) {
                $action = $task->user_id == $userId ? TaskAction::STOP : null;
            } else if (!isset($taskCurrentOfUser) || $taskCurrentOfUser->type == TaskAction::STOP) {
                $action = $task->user_id == $userId ? TaskAction::START : null;
            }

            $status = DB::table('project_kanbans')->select('project_kanbans.title', 'task_status.id as status_id', 'task_status.color')
                ->leftJoin('task_status', 'task_status.id', 'project_kanbans.status')
                ->where('project_kanbans.project_id', $projectId)
                ->get();

            $collectTags = BugTag::select('id', 'name')->get();

            $arrTag = DB::table('kanban_tags')->where('task_id', $task->id)->pluck('tag_id');

            $html = view('task.kanban.item-modal-task-day', [
                'task' => $task,
                'status' => $status,
                'collectTags' => $collectTags,
                'arrTag' => $arrTag->toArray(),
                'taskFiles' => $taskFiles,
                'action' => $action,
            ])->render();

            return response()->json([
                'html' => $html,
                'task' => $task
            ], Response::HTTP_OK);
        }
    }
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $sprint = DB::table('project_sprints')
                ->select(['id', 'name'])
                ->where('project_id', $request->projectId)
                ->where('status',ProjectTask::SPRINT_ACTIVE)
                ->orderBy('id','DESC')
                ->first();
            $projectTask = new ProjectTask();
            $projectTask->name = $request->name;
            $projectTask->project_id = $request->projectId;
            $projectTask->status = $request->taskStatus;
            $projectTask->type = TaskType::FEATURE;
            $projectTask->priority = 1;
            $projectTask->sprint_id = $sprint? $sprint->id: null;
            $projectTask->save();
            DB::commit();
            return response()->json([
                'status' => 200,
                'message' => 'success',
                'taskCreated' => $projectTask
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());
            return response()->json([
                'status' => 400,
                'message' => 'error',
            ]);
        }
    }

    public function getTaskFile($idTask)
    {
        $taskFiles = TaskAttachment::leftJoin('users', 'task_attachments.created_by', 'users.id')
            ->leftJoin('task_logs', function ($join) {
                $join->on('task_logs.id', '=', 'task_attachments.related_id')
                    ->where('task_attachments.type', '=',  TaskAttachment::TYPE_TASK_LOG);
            })
            ->where(function ($query) use ($idTask) {
                $query->where([
                    ['task_attachments.related_id', $idTask],
                    ['task_attachments.type', TaskAttachment::TYPE_PROJECT_TASK]
                ])
                    ->orWhere([
                        ['task_logs.task_id', $idTask],
                        ['task_attachments.type', TaskAttachment::TYPE_TASK_LOG]
                    ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();
        return $taskFiles;
    }

    /**
     * handle calculator total est task kanban
     */

    private function getTimeEst($collectKanbans, $collectTasks){
        // Create a temporary array to aggregate tasks based on status
        $taskStatusTotals = [];
        foreach ($collectTasks as $task) {
            $status = $task->status;
            $estimatedTime = $task->estimated_time;
            // If the status already exists in the array, add the estimated time
            if (isset($taskStatusTotals[$status])) {
                $taskStatusTotals[$status] += $estimatedTime;
            } else {
                // If the status doesn't exist yet, add it
                $taskStatusTotals[$status] = $estimatedTime;
            }
        }
        // Iterate through kanbans and update estimated time based on the temporary array
        foreach ($collectKanbans as $kanban) {
            $status = $kanban->status;
            if (isset($taskStatusTotals[$status])) {
                $kanban->total_estimated_time += $taskStatusTotals[$status];
            }
        }
        return $collectKanbans;
    }
}
