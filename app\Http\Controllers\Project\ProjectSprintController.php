<?php

namespace App\Http\Controllers\Project;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProjectSprintRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Logics\ProjectManager;
use App\Models\ProjectTask;
use App\Models\Sprint;
use Illuminate\Support\Facades\DB;
use App\Models\TaskStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ProjectSprintController extends Controller
{
    public function index($projectId) {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }

        $sprints = Sprint::select(
            'project_sprints.id',
            'project_sprints.name',
            'project_sprints.project_id',
            'project_sprints.status',
            'project_sprints.started_at',
            'project_sprints.ended_at',
            DB::raw('COUNT(project_tasks.id) AS count'),
        )
        ->leftjoin('project_tasks', 'project_tasks.sprint_id', 'project_sprints.id')
        ->where('project_sprints.project_id', $project->id)
        ->where('project_sprints.status', '!=', Sprint::COMPLETE)
        ->groupBy('project_sprints.id')
        ->get();

        return view('projectX.sprint', compact('project', 'sprints'));
    }

    public function store($projectId,ProjectSprintRequest $request)
    {
        DB::beginTransaction();
        try {
            $data['name'] = $request->sprint_name;
            $data['project_id'] = $projectId;
            $data['status'] = Sprint::INITIAL;
            $data['started_at'] = isset($request->started_at) ? Carbon::createFromFormat('d/m/Y',$request->started_at)->format('Y-m-d') : null;
            $data['ended_at'] = isset($request->ended_at) ? Carbon::createFromFormat('d/m/Y', $request->ended_at)->format('Y-m-d') : null;

            Sprint::create($data);
            
            DB::commit();

            return redirect()->back()->with('sprintUpdate',trans('language.success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }



    }
    public function updateSprint($projectId,Sprint $sprint,ProjectSprintRequest $request)
    {
        DB::beginTransaction();
        try {
        $data = [
            'name' => $request->sprint_name,
            'started_at' => isset($request->started_at) ? Carbon::createFromFormat('d/m/Y',$request->started_at)->format('Y-m-d') : null,
            'ended_at' => isset($request->ended_at) ? Carbon::createFromFormat('d/m/Y', $request->ended_at)->format('Y-m-d') : null,
        ];
        $sprint->update($data);
        DB::commit();
        return redirect()->back()->with('sprintUpdate', trans('language.success'));
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    public function updateStatus($projectId,Sprint $sprint)
    {
        DB::beginTransaction();
        try {
        switch ($sprint->status) {
            case Sprint::INITIAL:
                $sprint->status = Sprint::ACTIVE;
                $sprint->save();
                break;
            case Sprint::ACTIVE:
                $tasksInSprint = ProjectTask::select('id')
                ->where('sprint_id',$sprint->id)
                ->where('status','!=',TaskStatus::TASK_STATUS_CLOSE)
                ->count();
                if($tasksInSprint > 0){
                    return redirect()->back()->with('mgs',trans('language.sprint_message.comleted_error', ['value' => $tasksInSprint]));
                }
                $sprint->status = Sprint::COMPLETE;
                $sprint->save();
                break;
          }
            DB::commit();
            return redirect()->back()->with('sprintUpdate',trans('language.save_success'));

        } catch (\Exception $e) {

            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    public function destroy($projectId,Sprint $sprint,ProjectSprintRequest $request)
    {
        DB::beginTransaction();
        try {
        $userId = Auth::id();
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectId, $userId);
        if ($project == null) {
            return redirect()->route('project.index');
        }
        $sprints = ProjectTask::select(
            'project_tasks.id',
            'project_tasks.name',
            'project_sprints.project_id',
        )
        ->leftjoin('project_sprints', 'project_tasks.sprint_id', 'project_sprints.id')
        ->where('project_sprints.project_id', $project->id)
        ->where('project_sprints.id', $sprint->id)
        ->get(); 
        if($sprints->count() > 0 ){
            if(isset($request->sprint_id)){
                $idTasks = $sprints->pluck('id');
                ProjectTask::whereIn('id',$idTasks)->update(['sprint_id'=>$request->sprint_id]);
                $sprint->delete();

                DB::commit();
                return redirect()->route('project.sprint.index',['projectId'=>$projectId])->with('sprintUpdate',trans('language.sprint_message.switch_and_delete_success'));
            }
            else {
                return redirect()->route('project.sprint.index',['projectId'=>$projectId])->with('mgs',trans('language.sprint_message.switch_and_delete_fail',['value' => $sprints->count()]));
            }
        } 
        else {
            $sprint->delete();
        }

        DB::commit();
        return redirect()->back()->with('sprintUpdate',trans('language.sprint_message.delete_successs'));

        } catch (\Exception $e) {

            DB::rollBack();
            Log::error("File: " . $e->getFile() . '---Line: ' . $e->getLine() . "---Message: " . $e->getMessage());

            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
}
