<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\App;

class CheckyearRule implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $arrayHolidays = explode(', ', $value);
        foreach ($arrayHolidays as $item) {
            $newDateFormat = date_create_from_format("d/m/Y", $item);
            if (!$newDateFormat) {
                return false;
            }
            $getYear = date_format($newDateFormat, "Y");
            if (strlen($getYear) != 4 || (int)$getYear < 1000) {
                return false;
            }
        }
        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        $language = App::getLocale();
        $format = 'dd/mm/yyyy';
        switch ($language) {
            case 'en':
                $format = 'yyyy-mm-dd';
                break;
            case 'ja':
                $format = 'yyyy年mm月dd日';
                break;
            default:
                break;
        }
        return trans('validation.multi_date_format',
        [
            'format'=> $format,
            'separator' => ', '
        ]
        );
    }
}
