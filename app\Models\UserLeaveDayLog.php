<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class District
 * @property string $id
 * @property date $leave_day
 * @property string $hours
 */
class UserLeaveDayLog extends Model
{
    public $timestamps = false;

    protected $table = 'user_leave_days_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'leave_day',
        'hours'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [

    ];
}
