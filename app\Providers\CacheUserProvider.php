<?php

namespace App\Providers;

use App\User;
use Illuminate\Auth\EloquentUserProvider;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CacheUserProvider extends EloquentUserProvider
{
    /**
     * Cache key prefix for user id
     */
    protected const CACHE_PREFIX_USER_ID = 'user_id_';

    /**
     * Cache key prefix for user token
     */
    protected const CACHE_PREFIX_USER_TOKEN = 'user_token_';

    /**
     * Retrieve a user by their unique identifier, using cache to reduce database access.
     *
     * @param mixed $identifier
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveById($identifier)
    {
        $cacheKey = self::CACHE_PREFIX_USER_ID . $identifier;

        return Cache::remember($cacheKey, now()->addMinutes(config('passport.cache_ttl', 10)), 
            function () use ($identifier) {
            Log::debug('[CacheUserProvider][retrieveById] User id cached: ' . $identifier);
            return parent::retrieveById($identifier);
        });
    }

    /**
     * Retrieve a user by their unique identifier and token, using cache to reduce database access.
     *
     * @param mixed $identifier
     * @param string $token
     * @return \Illuminate\Contracts\Auth\Authenticatable|null
     */
    public function retrieveByToken($identifier, $token)
    {
        $cacheKey = self::CACHE_PREFIX_USER_TOKEN . $token;

        return Cache::remember($cacheKey, now()->addMinutes(config('passport.cache_ttl', 10)), 
            function () use ($identifier, $token) {
            Log::debug('[CacheUserProvider][retrieveByToken] User token cached: ' . $token);
            return parent::retrieveByToken($identifier, $token);
        });
    }

    /**
     * Revoke a user and clear their cache.
     *
     * @param \App\User $user
     * @return void
     */
    public static function revokeCacheUser(User $user)
    {
        Log::debug('[CacheUserProvider][revokeCacheUser] User cache revoked: ' . $user->id);

        $cacheKey = self::CACHE_PREFIX_USER_ID . $user->id;

        Cache::forget($cacheKey);

        self::forgetByAccessTokens($user);
    }

    /**
     * Clear the cache for a user by their access tokens.
     *
     * @param \App\User $user
     * @return void
     */
    protected static function forgetByAccessTokens(User $user)
    {
        foreach ($user->tokens as $token) {
            $cacheKey = self::CACHE_PREFIX_USER_TOKEN . $token->id;
            Cache::forget($cacheKey);
        }
    }
}
