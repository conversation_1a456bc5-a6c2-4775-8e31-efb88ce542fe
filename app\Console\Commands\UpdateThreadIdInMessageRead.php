<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateThreadIdInMessageRead extends Command
{
    protected $signature = 'migrate:message-read';
    protected $description = 'Migrate message_read table to update thread_id and keep only one record per user, conversation, and thread_id';

    public function handle()
    {
        $this->info('Updating thread_id in message_read...');

        $rows = DB::table('message_read')
            ->select('message_read.id as message_read_id', 'messages.reply_id')
            ->join('messages', 'messages.id', '=', 'message_read.message_id')
            ->get()
            ->chunk(1000);

        foreach ($rows as $chunk) {
            $ids = [];
            $cases = '';

            foreach ($chunk as $row) {
                $id = (int) $row->message_read_id;
                $replyId = is_null($row->reply_id) ? 'NULL' : (int) $row->reply_id;
                $ids[] = $id;
                $cases .= "WHEN {$id} THEN {$replyId} ";
            }

            $idsList = implode(',', $ids);

            DB::update("
                UPDATE message_read
                SET thread_id = CASE id
                    {$cases}
                END
                WHERE id IN ({$idsList})
            ");
        }

        $this->info('Done updating thread_id in message_read!');
        return 0;
    }
}