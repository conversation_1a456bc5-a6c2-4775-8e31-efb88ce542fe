<?php
namespace App\Logics;

use Illuminate\Support\Facades\App;
use Carbon\Carbon;

class DateFormatManager
{
    /**
     * change format date strtotime
     */
    public function dateFormatLanguage($date = null, $format)
    {
        $language = App::getLocale();

        // format time
        if($date){   
            if($format == 'd/m/Y H:i'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = date('Y-m-d H:i',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = date('Y年m月d日 H時i分',strtotime($date));
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'd/m/Y'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = date('Y-m-d',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = date('Y年m月d日',strtotime($date));
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'd/m/Y H:i:s'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = date('Y-m-d H:i:s',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = date('Y年m月d日 H時i分s秒',strtotime($date));
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'H:i'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = date('H:i',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = date('H時i分',strtotime($date));
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'H:i:s'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,$date);
                        break;
                    case 'en':
                        $dateFormat = date('H:i:s',$date);
                        break;
                    case 'ja':
                        $dateFormat = date('H時i分s秒',$date);
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'H::i::s'){
                switch ($language){
                    case 'vi':
                    case 'en':
                    case 'ja':
                        $dateFormat = date('H:i:s',$date);
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == 'd/m H:i'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format,strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = date('m-d H:i',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = date('m月d日 H時i分',strtotime($date));
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
            if($format == '%A %d/%m/%Y'){
                switch ($language){
                    case 'vi':
                        $dateFormat = strftime($format, strtotime($date));
                        break;
                    case 'en':
                        $dateFormat = strftime('%A %Y-%m-%d',strtotime($date));
                        break;
                    case 'ja':
                        $dateFormat = strftime('%Y年%m月%d日 (%A)',strtotime($date));
                        $dateFormat = str_replace('曜日','',$dateFormat);
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
        } else {
            if($format == 'd/m/Y'){
                switch ($language){
                    case 'vi':
                        $dateFormat = date($format);
                        break;
                    case 'en':
                        $dateFormat = date('Y-m-d');
                        break;
                    case 'ja':
                        $dateFormat = date('Y年m月d日');
                        break;
                    default:
                        break;
                }
                return $dateFormat;
            }
        }
        
    }
    /**
     * change time format timestamp
     */
    public function dateFormatInput($date, $format)
    {
        $language = App::getLocale();
        if($format == 'd/m/Y'){
            switch ($language){
                case 'vi':
                    $dateFormat = $date;
                    break;
                case 'en':
                    $dateFormat = Carbon::createFromFormat('d/m/Y', $date)->format('Y-m-d');
                    break;
                case 'ja':
                    $dateFormat = Carbon::createFromFormat('d/m/Y', $date)->format('Y年m月d日');
                    break;
                default:
                    break;
            }
            return $dateFormat;
        }
        if($format == 'm/Y'){
            switch ($language){
                case 'vi':
                    $dateFormat = $date;
                    break;
                case 'en':
                    $dateFormat = Carbon::createFromFormat('m/Y', $date)->format('Y-m');
                    break;
                case 'ja':
                    $dateFormat = Carbon::createFromFormat('m/Y', $date)->format('Y年m月');
                    break;
                default:
                    break;
            }
            return $dateFormat;
        }
    }
    /**
     * change date array format date string
     */
    public function dateFormatInputArray($date, $format)
    {
        $language = App::getLocale();
        $date2 = [];
        foreach ($date as $key => $item) {
            if ($date && $format == 'd/m/Y') {
                switch ($language) {
                    case 'vi':
                        $date2[$key] = date($format, strtotime($item));
                        break;
                    case 'en':
                        $date2[$key] = date('Y-m-d', strtotime($item));
                        break;
                    case 'ja':
                        $date2[$key] = date('Y年m月d日', strtotime($item));
                        break;
                    default:
                        break;
                }
            }
        }
        return implode(', ', $date2);
    }
    public function dateFormatLanguageArray($date = null)
    {
        $language = App::getLocale();
        $arrDateFamilyBusiness = explode(", ",$date);
        $dateFormat = [];
        foreach ($arrDateFamilyBusiness as $item) {
            switch ($language) {
                case 'vi':
                    $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('Y-m-d');
                    break;
                case 'en':
                    $dateFormat[] = Carbon::createFromFormat('Y-m-d', $item)->format('Y-m-d');
                    break;
                case 'ja':
                    $dateFormat[] = Carbon::createFromFormat('Y年m月d日', $item)->format('Y-m-d');
                    break;
                default:
                    break;
            }
        }
        return $dateFormat;
    }
    public function dateFormatLanguageArrayV2($date = null, $checkDateHoliday = false)
    {
        $language = App::getLocale();
        $arrDateFamilyBusiness = explode(", ",$date);
        $dateFormat = [];
        foreach ($arrDateFamilyBusiness as $item) {
            switch ($language) {
                case 'vi':
                    if (!$checkDateHoliday){
                        $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('d/m/Y');
                        
                    }else{
                        $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('Y-m-d');
                    }
                    break;
                case 'en':
                    $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('Y-m-d');
                    break;
                case 'ja':
                    if(!$checkDateHoliday){
                        $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('Y年m月d日');
                    }else{
                        $dateFormat[] = Carbon::createFromFormat('d/m/Y', $item)->format('Y-m-d');
                    }
                    break;
                default:
                    break;
            }
        }
        return implode(", ",$dateFormat);
    }

}
