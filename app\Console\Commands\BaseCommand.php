<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Symfony\Component\Console\Exception\RuntimeException;
use Illuminate\Console\Scheduling\Schedule;

abstract class BaseCommand extends Command
{
    /**
     * @var Connection
     */
    private $connection;

    /**
     * @var WebsiteRepository
     */
    private $websites;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        // Tenant
        if (env('USE_TENANT', false)) {
            $this->websites = app(\Hyn\Tenancy\Contracts\Repositories\WebsiteRepository::class);
            $this->connection = app(\Hyn\Tenancy\Database\Connection::class);
        }
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (env('USE_TENANT', false)) {
            $website_id = $this->option('website_id');
            try{
                $website = $this->websites->query()->where('id', $website_id)->firstOrFail();
                $this->connection->set($website);
                app(\Hyn\Tenancy\Environment::class)->tenant($website);
                $this->info('Running Command on website_id: ' . $website_id);

                $this->_handle();

                $this->connection->purge();
            } catch (ModelNotFoundException $e) {
                throw new RuntimeException(
                    sprintf(
                        'The tenancy website_id=%d does not exist.',
                        $website_id
                    )
                );
            }
        } else {
            $this->_handle();
        }
    }

    // Implement the command here
    protected abstract function _handle();

    /**
     * Command schedule
     */
    protected function scheduleCommand(Schedule $schedule)
    {
        // Set the Scheduler to our Tenant Aware Command. Store the returned Event.
        $scheduledEvent = $schedule->command(get_class($this));

        if (env('USE_TENANT', false)) {
            // Init a new variable to alter the Event's command string
            $commandString = null;

            // Setup your schedule
            $scheduledEvent
                // Setup our `before` Hook
                ->before(function () use ($scheduledEvent, &$commandString) {
                    // Retreive current tenant
                    $tenant = app(\Hyn\Tenancy\Environment::class)->tenant();

                    // Retreive and store the inital generated command. See note below*
                    if ($commandString === null) {
                        $commandString = $scheduledEvent->command;
                    }

                    // Replace the Event's generated command
                    // Adding our tenant's `website_id` option
                    $scheduledEvent->command = sprintf(
                        "%s --website_id=%d",
                        $commandString,
                        $tenant->id
                    );
                });
        }

        return $scheduledEvent;
    }
}
