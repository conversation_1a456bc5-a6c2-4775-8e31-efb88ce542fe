<?php

namespace App\Listeners;

use App\Events\UpdateTask;
use App\Models\TaskLog;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Auth;

class CreateTaskLog
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UpdateTask  $event
     * @return void
     */
    public function handle(UpdateTask $event)
    {
        // Insert log
        $taskLog = new TaskLog();
        $taskLog -> task_id = $event->taskId;
        $taskLog -> log = $event->log;
        $taskLog -> type = $event->type;
        $taskLog -> created_by = Auth::id();
        $taskLog -> updated_by = Auth::id();
        $taskLog->save();
    }
}
