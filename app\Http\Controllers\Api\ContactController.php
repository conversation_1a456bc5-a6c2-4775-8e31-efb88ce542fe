<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Requests\ContactAcceptOrRejectRequest;
use App\Http\Requests\ContactAddRequest;
use App\Logics\ContactManager;
use App\Models\Contacts;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ContactController extends AbstractApiController
{
    /**
     * @var ContactManager
     */
    protected $contactManager;

    public function __construct(ContactManager $contactManager)
    {
        $this->contactManager = $contactManager;
    }

    /**
     * Get list users without in contacts
     * @param Request $request
     * @return json|Respond|mixed
     * @throws Exception
     */
    public function listNoContact(Request $request)
    {
        try {
            $data = $this->contactManager->listNoContact($request);
            $msg = __('message.contact.noContact.success');
            if ($request->has('type_search') && $request->has('keyword') && $data->total() == 0){
                $msg = __('message.contact.noContact.notExist');
                return $this->renderJsonResponse([], $msg, Response::HTTP_NOT_FOUND);
            }
            if ($data->total() == 0) {
                $msg = __('message.contact.noContact.fail');
            };
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error("[ContactController][listNoContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][listNoContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Sent request add contact
     * @param ContactAddRequest $request
     * @return json|Respond
     * @throws Exception
     */
    public function addNewContact(ContactAddRequest $request)
    {
        try {
            $data = $this->contactManager->addNewContact($request);
            $msg = __('message.contact.addContact.success');
            if (!empty($data) && isset($data['hasSendRequest']) && $data['hasSendRequest']){
                $msg = trans('message.contact.addContact.hasSentRequestContact');
                return $this->renderJsonResponse([], $msg, Response::HTTP_CONFLICT);
            }
            if (!empty($data) && isset($data['received_invitation']) && $data['received_invitation']){
                $msg = $data['name'] . ' ' .  __('message.contact.addContact.hasSentRequest');
                return $this->renderJsonResponse([], $msg, Response::HTTP_CONFLICT);
            }
            if (!empty($data) && isset($data['were_friend']) && $data['were_friend']){
                $msg = __('message.contact.addContact.wereFriends');
                return $this->renderJsonResponse([], $msg, Response::HTTP_CONFLICT);
            }
            if (empty($data)) {
                $msg = __('message.contact.addContact.fail');
                return $this->respondBadRequest($msg);
            }
            return $this->renderJsonResponse([], $msg);
        } catch (Exception $e) {
            Log::error("[ContactController][addNewContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][addNewContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get list contacts of user
     * @param Request $request
     * @return JsonResponse|mixed
     * @throws Exception
     */
    public function listContact(Request $request)
    {
        try {
            $data = $this->contactManager->getListContact($request);
            return $this->respondWithPagination($data, __('message.contact.listContact.success'));
        } catch (Exception $e) {
            Log::error("[ContactController][listContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][listContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get list of received requests
     * @param Request $request
     * @return json|mixed
     * @throws Exception
     */
    public function listReceivedRequest(Request $request)
    {
        try {
            $data = $this->contactManager->getListReceivedRequest($request);
            return $this->respondWithPagination($data, __('message.contact.listReceivedContact.success'));
        } catch (Exception $e) {
            Log::error("[ContactController][listReceivedRequest] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][listReceivedRequest] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get list sent request
     * @param Request $request
     * @return json|mixed
     * @throws Exception
     */
    public function listSentRequest(Request $request)
    {
        try {
            $data = $this->contactManager->getListSentRequest($request);
            return $this->respondWithPagination($data, __('message.contact.listSentContact.success'));
        } catch (Exception $e) {
            Log::error("[ContactController][listSentRequest] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][listSentRequest] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Get user detail information
     * @param         $userId
     * @param Request $request
     * @return json|Respond
     * @throws Exception
     */
    public function getDetailInformationUser($userId, Request $request)
    {
        try {
            $data = $this->contactManager->getDetailInformationUser($userId, $request);
            $msg = __('message.contact.detailInformation.success');
            if (empty($data)){
                $msg = __('message.contact.detailInformation.fail');
                return $this->respondNotFound($msg);
            };
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e){
            Log::error("[ContactController][getDetailInformationUser] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][getDetailInformationUser] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Accept or reject request
     * @param ContactAcceptOrRejectRequest $request
     * @return json|Respond
     * @throws Exception
     */
    public function acceptOrRejectContact(ContactAcceptOrRejectRequest $request)
    {
        try {
            $contactId = (int)$request->get('contact_id');
            $type = (int)$request->get('type_request');
            $data = $this->contactManager->acceptOrRejectContact($contactId, $type);
            $msg = __('message.contact.acceptContact.success');
            if (empty($data)){
                $msg = __('message.contact.acceptContact.notFound');
                return $this->renderJsonResponse([], $msg, Response::HTTP_NOT_FOUND);
            }
            //In case it was canceled on another device - User received
            if (isset($data['error_code']) && $data['error_code'] === Response::HTTP_NOT_MODIFIED){
                $msg = __('message.contact.rejectContact.isReceivedRefused');
                return $this->respondBadRequest($msg);
            }
            //In case the other party has accepted the request
            if (isset($data['error_code']) && $data['error_code'] === Contacts::STATUS_ACCEPTED){
                $msg = __('message.contact.acceptContact.fail');
                return $this->respondBadRequest($msg);
            }
            //In case it was canceled on another device - User sender
            if (isset($data['error_code']) && $data['error_code'] === Contacts::STATUS_REJECTED){
                $msg = __('message.contact.rejectContact.isSendRefused');
                return $this->respondBadRequest($msg);
            }
            //Case of successfully refusing to make friends
            if (isset($data['type']) && $data['type'] === Contacts::STATUS_REJECTED){
                $msg = __('message.contact.rejectContact.success');
                return $this->respondUpdated([], $msg);
            }
            if (isset($data['error_code']) && $data['error_code'] === Response::HTTP_NOT_FOUND){
                $msg = __('message.contact.acceptContact.notFound');
                return $this->renderJsonResponse([], $msg, Response::HTTP_NOT_FOUND);
            }
            return $this->respondUpdated($data['data'], $msg);
        } catch (Exception $e){
            Log::error("[ContactController][acceptOrRejectContact] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ContactController][acceptOrRejectContact] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
}