<?php

namespace App\Logics;

use App\Enums\BTASettingType;
use App\Helpers\StringHelper;
use App\Models\BTASetting;
use App\Models\Customer;
use App\Models\Prefecture;
use App\Traits\ImageTrait;
use App\Traits\StorageTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class CustomerManager
{
    use StorageTrait;
    use ImageTrait;

    /**
     * Get all customers
     * @param $request Request
     * @return Customer
     */
    public function getAllCustomers(Request $request)
    {
        $customers = Customer::select('customers.*')
            ->with([
                'ageSetting',
                'genderSetting',
                'jobSetting',
                'levelOfInquirySetting',
                'feedbackSetting',
                'levelCareSetting',
                'targetSetting',
                'languageProficiencySetting',
                'modesStudySetting',
                'concernsSetting',
                'dataChannelsSetting',
                'user',
                'prefecture'
            ]);
        $stringHelper = new StringHelper();
        if (isset($request->id)) {
            $items = array_filter(array_map("trim", explode(",", $request->id)));
            if (!empty($items)) {
                $customers->whereIn('customers.id', explode(",", $request->id));
            }
        }
        if (isset($request->name)) {
            $name = $stringHelper->formatStringWhereLike($request->name);
            $customers->where('customers.name', 'LIKE', '%' . $name . '%');
        }
        if (isset($request->note)) {
            $note = $stringHelper->formatStringWhereLike($request->note);
            $customers->where('customers.note', 'LIKE', '%' . $note . '%');
        }
        if (isset($request->age)) {
            $customers->whereIn('customers.age', $request->age);
        }
        if (isset($request->gender)) {
            $customers->whereIn('customers.gender', $request->gender);
        }
        if (isset($request->job)) {
            $customers->whereIn('customers.job', $request->job);
        }
        if (isset($request->address)) {
            $customers->whereIn('customers.address', $request->address);
        }
        if (isset($request->level_of_inquiry)) {
            $customers->whereIn('customers.level_of_inquiry', $request->level_of_inquiry);
        }
        if (isset($request->feedback)) {
            $customers->whereIn('customers.feedback', $request->feedback);
        }
        if (isset($request->level_care)) {
            $customers->whereIn('customers.level_care', $request->level_care);
        }
        if (isset($request->target)) {
            $customers->whereIn('customers.target', $request->target);
        }
        if (isset($request->language_proficiency)) {
            $customers->whereIn('customers.language_proficiency', $request->language_proficiency);
        }
        if (isset($request->modes_study)) {
            $customers->whereIn('customers.modes_study', $request->modes_study);
        }
        if (isset($request->concerns)) {
            $customers->whereIn('customers.concerns', $request->concerns);
        }
        if (isset($request->data_channels)) {
            $customers->whereIn('customers.data_channels', $request->data_channels);
        }
        if (isset($request->creation_date)) {
            $creationDate = Carbon::createFromFormat('d/m/Y', $request->creation_date)->format('Y-m-d');
            $customers->whereDate('customers.creation_date', $creationDate);
        }
        if (isset($request->user_id)) {
            $customers->whereIn('customers.user_id', $request->user_id);
        }
        $relationFields = [
            'age' => 'age_settings',
            'gender' => 'gender_settings',
            'job' => 'job_settings',
            'level_of_inquiry' => 'level_of_inquiry_settings',
            'feedback' => 'feedback_settings',
            'level_care' => 'level_care_settings',
            'target' => 'target_settings',
            'language_proficiency' => 'language_proficiency_settings',
            'modes_study' => 'modes_study_settings',
            'concerns' => 'concerns_settings',
            'data_channels' => 'data_channels_settings',
        ];
        $sortFields = array_keys($relationFields);
        if (isset($request->sort, $request->direction) && in_array($request->sort, $sortFields)) {
            $alias = $relationFields[$request->sort];
            $customers->leftJoin("bta_settings as {$alias}", "customers.{$request->sort}", '=', "{$alias}.id")
                ->orderBy("{$alias}.name", $request->direction);
        }
        if (isset($request->sort, $request->direction) && $request->sort == 'address') {
            $customers->orderBy("prefectures.name", $request->direction);
        }
        if (isset($request->sort, $request->direction) && in_array($request->sort, ['id', 'creation_date'])) {
            $customers->orderBy("customers.{$request->sort}", $request->direction);
        }
        return $customers;
    }

    /**
     * get all Prefectures
     * @return Collection
     */
    public function getAllPrefectures()
    {
        return Prefecture::all();
    }

    /**
     * get all BTA settings
     * @return Collection
     */
    public function getAllBTASettings()
    {
        return BTASetting::all();
    }

    /**
     * get BTA settings by type
     * @return Collection
     */
    public function getAllBTASettingsDividedIntoType()
    {
        $allSettings = BTASetting::select('id', 'name', 'type')->get();

        $types = [
            'ageSettings' => BTASettingType::AGE()->value,
            'genderSettings' => BTASettingType::GENDER()->value,
            'jobSettings' => BTASettingType::JOB()->value,
            'levelOfInquirySettings' => BTASettingType::LEVEL_OF_INQUIRY()->value,
            'targetSettings' => BTASettingType::TARGET()->value,
            'languageProficiencySettings' => BTASettingType::LANGUAGE_PROFICIENCY()->value,
            'modesStudySettings' => BTASettingType::MODES_STUDY()->value,
            'concernsSettings' => BTASettingType::CONCERNS()->value,
            'levelCareSettings' => BTASettingType::LEVEL_CARE()->value,
            'feedbackSettings' => BTASettingType::FEEDBACK()->value,
            'dataChannelsSettings' => BTASettingType::DATA_CHANNELS()->value,
        ];

        $btaSettings = [];
        foreach ($types as $key => $type) {
            $btaSettings[$key] = $allSettings->where('type', $type);
        }

        return $btaSettings;
    }

    /**
     * store
     * @param $id
     * @param $param
     * @return Collection
     */
    public function save($id, $param)
    {
        $data = self::getDataInParam($param);
        if (empty($id)) {
            Customer::create($data);
        } else {
            $customer = self::getDataById($id);
            if (!$customer) {
                abort(404);
            }
            $customer->update($data);
        }
    }

    /**
     * get customer by id
     * @param $id
     * @return Customer
     */
    public function getDataById($id) {
        return Customer::find($id);
    }

    /**
     * getDataInParam
     * @param $param
     * @return array
     */
    public function getDataInParam($param)
    {
        $creation_date = !empty($param['creation_date']) ? Carbon::createFromFormat('d/m/Y', $param['creation_date'])->format('Y-m-d') : null;
        return [
            'creation_date' => $creation_date,
            'user_id' => $param['user_id'] ?? null,
            'name' => $param['name'] ?? null,
            'age' => $param['age'] ?? null,
            'gender' => $param['gender'] ?? null,
            'job' => $param['job'] ?? null,
            'address' => $param['address'] ?? null,
            'level_of_inquiry' => $param['level_of_inquiry'] ?? null,
            'target' => $param['target'] ?? null,
            'language_proficiency' => $param['language_proficiency'] ?? null,
            'modes_study' => $param['modes_study'] ?? null,
            'concerns' => $param['concerns'] ?? null,
            'level_care' => $param['level_care'] ?? null,
            'feedback' => $param['feedback'] ?? null,
            'data_channels' => $param['data_channels'] ?? null,
            'note' => $param['note'] ?? null,
        ];
    }
}
