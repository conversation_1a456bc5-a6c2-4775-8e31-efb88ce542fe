<?php

namespace App\Console\Commands;

use App\Models\Language;
use App\Models\ProjectTask;
use App\Models\ProjectRole;
use App\Models\TaskType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Console\Scheduling\Schedule;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\Report\ReportMultiSheetExport;
use Carbon\Carbon;
use App\Jobs\SendMailReportTest;
use Illuminate\Support\Facades\App;
use App\Traits\StorageTrait;

class AutoSendReport extends BaseCommand
{
    use StorageTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auto:send_report {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail to report test';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle()
    {
        $daySend = ['01', '15'];
        $day = Carbon::now()->day;
        $time = '';
        if ($day < 15) {
            $time = Carbon::now()->subMonth()->format('Y-m') . '-' . $daySend[1];
        } else {
            $time = Carbon::now()->format('Y-m') . '-' . $daySend[0];
        }

        $taskBugs = ProjectTask::select(
            'project_tasks.id',
            'project_tasks.project_id',
            'project_tasks.function_screen',
            'bug_classifies.name as bug_classify_name',
            DB::raw('CONCAT_WS(" ",users.first_name,users.last_name) as user_name'),
            'project_tasks.user_id',
            'bug_ranges.name as bug_range_name',
            'bug_reasons.name as bug_reason_name',
            'bug_severities.name as bug_severity_name'
        )
        ->leftJoin('users', 'users.id', 'project_tasks.user_id')
        ->leftJoin('bug_classifies', 'bug_classifies.id', 'project_tasks.bug_classify_id')
        ->leftJoin('bug_ranges', 'bug_ranges.id', 'project_tasks.bug_range_id')
        ->leftJoin('bug_reasons', 'bug_reasons.id', 'project_tasks.bug_reason_id')
        ->leftJoin('bug_severities', 'bug_severities.id', 'project_tasks.bug_severity_id')
        ->with([
            'project' => function ($q1) {
                $q1->select('id', 'projects.name');
                $q1->with(['members' => function ($q2) {
                    $q2->select(
                        'id',
                        DB::raw('CONCAT_WS(" ",users.first_name,users.last_name) as pm_name'),
                        'users.email',
                        'users.language_id'
                    );
                    $q2->where('project_members.role_id', ProjectRole::ProjectManager);
                }]);
            }
        ])
        ->where('project_tasks.type', TaskType::BUG)
        ->whereRaw('DATE(project_tasks.created_at) >= ?', $time)
        ->whereRaw('DATE(project_tasks.created_at) < ?', Carbon::now()->format('Y-m-d'))
        ->get()->toArray();

        $collectTaskBugs = collect($taskBugs);
        $languages = Language::pluck('name', 'id');
        $groupByProject = $collectTaskBugs->groupBy('project_id')->all();
        $info = [];
        $result = [];

        if (!Storage::disk(FILESYSTEM)->exists(TEMP_DIR)) {
            $this->makeDirectory(TEMP_DIR);
        }

        $websiteId = null;
        $pathPrefix = "";
        if (env('USE_TENANT', false)) {
            $websiteId = $this->option('website_id');
            $pathPrefix = app(\Hyn\Tenancy\Website\Directory::class)->path();
        }
        foreach ($groupByProject as $value) {
            foreach ($value[0]['project']['members'] as $val) {
                $info[$value[0]['project']['id']][] = [
                    'project_name' => $value[0]['project']['name'],
                    'PM_name' => $val['pm_name'],
                    'PM_email' => $val['email'],
                    'language_id' => $val['language_id'] ?? 1
                ];
            }

            $fcw = $value->countBy('function_screen');
            foreach ($fcw as $k => $val) {
                $result[$value[0]['project_id']]['function_screen'][] = ['name' => $k, 'num_bugs' => $val];
            }
            $bugClassifies = $value->countBy('bug_classify_name');
            foreach ($bugClassifies as $k => $val) {
                $result[$value[0]['project_id']]['bug_classify_name'][] = ['name' => $k, 'num_bugs' => $val];
            }
            $bugReasons = $value->countBy('bug_reason_name');
            foreach ($bugReasons as $k => $val) {
                $result[$value[0]['project_id']]['bug_reason_name'][] = ['name' => $k, 'num_bugs' => $val];
            }
            $bugRanges = $value->countBy('bug_range_name');
            foreach ($bugRanges as $k => $val) {
                $result[$value[0]['project_id']]['bug_range_name'][] = ['name' => $k, 'num_bugs' => $val];
            }
            $bugSeverities = $value->countBy('bug_severity_name');
            foreach ($bugSeverities as $k => $val) {
                $result[$value[0]['project_id']]['bug_severity_name'][] = ['name' => $k, 'num_bugs' => $val];
            }
            $userName = array_column($value->all(), 'user_name', 'user_id');
            $userIds = $value->countBy('user_id');
            foreach ($userIds as $k => $val) {
                $result[$value[0]['project_id']]['user_name'][] = ['id' => $k, 'user_name' => $userName[$k], 'num_bugs' => $val];
            }

            if ($result && $info) {
                $arrLanguage = array_unique(array_column($info[$value[0]['project_id']], 'language_id'));
                $arrFilePath = [];
                foreach($arrLanguage as $val){
                    $name = $languages[$val];
                    if ($name) {
                        App::setLocale($name);
                    }
                    $subFileName = $subFileName = trans('language.report', [
                        'projectName' => htmlspecialchars($info[$value[0]['project_id']][0]['project_name']),
                        'timeStart' => $time,
                        'timeEnd' => Carbon::now()->subDay()->format('Y-m-d')
                    ]);
                    $fileName = $pathPrefix . TEMP_DIR . '/' . $subFileName . '.xlsx';
                    Excel::store(new ReportMultiSheetExport($result[$value[0]['project_id']]), $fileName);
                    $arrFilePath[$val] = $fileName;
                }

                $check = true;
                foreach ($info[$value[0]['project_id']] as $val) {
                    dispatch(new SendMailReportTest($val, $arrFilePath[$val['language_id']] , $check))->onQueue(QUEUE_MAIL);
                    $check = false;
                }
            }
 
        }
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)
            ->twiceMonthly(1, 15, '08:00');
    }
}
