<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Logics\MessageManager;

class ConversationParticipant extends Model
{
    protected $table = 'conversation_participants';

    const ERROR_PERMISSION = 1;
    const ERROR_EMPTY_ADMIN = 2;
    const ERROR_NOT_FOUND = 3;
    const ERROR_KICKED = 4;
    const IS_MEMBER = 0;
    const IS_ADMIN = 1;
    const MUTE = 1;
    const NOT_MUTE = 0;
    const STATUS_DELETE = 0;
    const STATUS_NOT_DELETE = 1;
    
    // Status when an invitation has been sent from another user to the current user.
    const SENT_INVITE_CONTACT = 1;
    // Status when the current user receives an invitation from the logged in user
    const RECEIVED_INVITE_CONTACT = 2;
    
    // Status when an invitation has been sent from another user to the current user.
    const ADD_MEMBERS_GROUP = 0;
    const UPDATE_MEMBERS_GROUP = 1;
    const REMOVE_MEMBERS_GROUP = 2;


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'conversation_id', 'user_id', 'admin', 'status', 'deleted_at'
    ];
    
    protected $appends = ['most_recent_message'];
    
    /**
     * Get the most_recent_message .
     *
     * @return string
     */

    public function getMostRecentMessageAttribute()
    {
        $messageManager = new MessageManager();
        return $messageManager->getMostRecentMessage($this->content_text, $this->content_file);
    }
}
