<?php
namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ExportTimekeepingFormRequest extends FormRequest {
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @param Illuminate\Http\Request $request
     * @return array
     */
    public function rules() {
        $rules = [
            'choose_month' => 'required|date_format:m/Y',
            'choose_holidays' => 'nullable|multi_date_format:d/m/Y,","',
        ];
        return $rules;
    }

    /**
     * Get the message that apply to the request.
     *
     * @return array
     */
    public function messages() {
        $messages = [

        ];

        return $messages;
    }

    /**
     * Configure the validator instance.
     *
     * @param Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator) {

    }
}
