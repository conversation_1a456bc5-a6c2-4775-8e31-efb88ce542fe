<?php

namespace App\Http\Requests;

use App\Logics\UserManager;
use App\Models\ProjectRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ProjectSprintRequest extends FormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {

        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole(Auth::user()->id,$this->projectId,ProjectRole::ProjectManager);
        return $isManager;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'project_id' => $this->projectId
        ]);
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];
        $method = $this->method();
        if( $method == 'POST'){
            $rules['sprint_name'] = ['required','max:255'];
            $rules['project_id'] =  ['required','exists:projects,id'];
            $rules['started_at'] = ['required','date_format:d/m/Y'];
            $rules['ended_at'] = ['required','date_format:d/m/Y','after_or_equal:started_at'];
        } 
        elseif( $method == 'PUT'){
            $rules['sprint_name'] = ['required','max:255'];
            $rules['project_id'] =  ['required','exists:projects,id'];
            $rules['started_at'] = ['required','date_format:d/m/Y'];
            $rules['ended_at'] = ['required','date_format:d/m/Y','after_or_equal:started_at'];

        } 
        elseif ( $method == 'DELETE'){
            $rules['sprint_id'] = ['exists:project_sprints,id'];
            if($this->has('sprint_id')){
                $rules['sprint_id'] = ['required','exists:project_sprints,id'];
            }
        }

        return $rules;
    }
}
