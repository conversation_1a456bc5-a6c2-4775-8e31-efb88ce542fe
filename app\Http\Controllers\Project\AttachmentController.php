<?php

namespace App\Http\Controllers\Project;

use App\Models\Role;
use App\Models\TaskAttachment;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Logics\AttachmentManager;
use App\Models\Project;
use App\Models\ProjectTask;
use App\Models\TaskLog;
use App\Traits\StorageTrait;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Log;

class AttachmentController extends Controller
{
    use StorageTrait;

    /**
     * Show attachment
     */
    public function show($id, Request $request)
    {
        $file = (new AttachmentManager())->getAttachment($id);

        $userPermission = $this->checkUserPermission($file);
        if ($userPermission == null) {
            return redirect()->route('project.index');
        }

        if (!Storage::disk(FILESYSTEM)->exists($file->file_path)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->response($file->file_path, $file->file_name);
    }

    /**
     * Download the attachment file
     */
    public function download($id)
    {
        $file = (new AttachmentManager())->getAttachment($id);

        $userPermission = $this->checkUserPermission($file);
        if ($userPermission == null) {
            return redirect()->route('project.index');
        }
        if (!Storage::disk(FILESYSTEM)->exists($file->file_path)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->download($file->file_path, $file->file_name);
    }
    /**
     * Delete the attachment by id
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function destroy(Request $request)
    {
        $file = (new AttachmentManager())->getAttachment($request->id);
        if ($file == null) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.document_not_exist'),
                ]
            ];
        }
        $userPermission = $this->checkUserPermission($file);

        if ($userPermission == null) {
            return response()->json([
                'status' => 403,
            ], Response::HTTP_FORBIDDEN);
        }
        try{
            // Delete attachment then delete url image description
            $hostname = URL::to('/');
            $imageUrl = $hostname. '/attachment/' . $file->id;
            if($file->type == TaskAttachment::TYPE_PROJECT){
                $project = Project::select('description','id')
                        ->where('id', $file->related_id)
                        ->first();
                $project->description = str_replace($imageUrl, '', $project->description);
                $project->save();
            }
            if($file->type == TaskAttachment::TYPE_PROJECT_TASK){
                $task = ProjectTask::select('description','id')
                        ->where('id', $file->related_id)
                        ->first();
                $task->description = str_replace($imageUrl, '', $task->description);
                $task->save();
            }
            if($file->type == TaskAttachment::TYPE_TASK_LOG){
                $taskLog = TaskLog::select('log','id')
                        ->where('id', $file->taskLog_id)
                        ->first();
                $taskLog->log = str_replace($imageUrl, '', $taskLog->log);
                $taskLog->save();
            }
            $this->deleteFile($file->file_path);
            $file->delete();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_document_succeed') ,
                ],
            ];
        } catch (\Exception $e){
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.server_error'),
            ]);
        }
    }

    private function checkUserPermission($file) {
        $userPermission = null;
        if($file->type == TaskAttachment::TYPE_PROJECT){
            $userPermission = Project::select('projects.id')
                    ->where('id', $file->related_id)
                    ->checkUserPermission(Auth::id())
                    ->first();
        }
        if($file->type == TaskAttachment::TYPE_TASK_LOG || $file->type == TaskAttachment::TYPE_PROJECT_TASK){
            $userPermission = ProjectTask::checkUserPermission(Auth::id())->find($file->related_id);
        }
        if($file->type == TaskAttachment::TYPE_CONTEST || $file->type == TaskAttachment::TYPE_MENU || $file->type == TaskAttachment::TYPE_ASSET){
            $userPermission = 1;
        }
        if($file->type == TaskAttachment::TYPE_REQUEST){
            $userPermission = \App\Models\Request::checkUserPermission(Auth::id())->find($file->related_id);
        }
        if ($file->type == TaskAttachment::TYPE_ASSET_BIDDING_PACKAGE && $this->checkRoleAdmin()) {
            $userPermission = 1;
        }
        if ($file->type == TaskAttachment::TYPE_INVENTORY && $this->checkRoleAdminAndAsset()) {
            $userPermission = 1;
        }
        
        return $userPermission;
    }

    /**
     * Check role admin
     *
     * @return mixed
     */
    private function checkRoleAdmin()
    {
        return Auth::user()->hasRole([Role::ROLE_SYSTEM_MANAGER]);
    }

    /**
     * Check role admin and asset
     *
     * @return mixed
     */
    private function checkRoleAdminAndAsset()
    {
        return Auth::user()->hasRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_ASSET_MANAGER]);
    }
}
