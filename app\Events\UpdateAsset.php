<?php

namespace App\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;

class UpdateAsset
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $log;
    public $assetId;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(string $log, int $assetId)
    {
        $this -> log = $log;
        $this -> assetId = $assetId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
