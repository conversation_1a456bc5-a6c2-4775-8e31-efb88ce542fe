<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;

class DateFormat
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $typeFormats = [
            'birthday',
            'started_at', 
            'ended_at',
            'signed_at',
            'id_issued_at',
            'start_date',
            'end_date',
            'start_at',
            'end_at',
            'created_at',
            'from',
            'to',
            'time',
            'choose_time',
            'choose_date',
            'choose_month',
            'choose_holidays',
            'time_start',
            'time_end',
            'time_checkin',
            'time_checkout',
            'started_at_phase2',
            'manufacturing_date',
            'usage_date',
            'purchase_date',
            'date_limit',
            'collaborator_date',
            'creation_date'
        ];
        $language = App::getLocale();

        // format time
        switch ($language){
            case 'vi':
                break;
            case 'en':
                foreach ($typeFormats as $value) {
                    if($value == "choose_holidays"){
                        if(!empty($request->$value)){
                            $arrayHolidays = explode(', ',$request->$value);
                            $arrayHolidaysNew = [];
                            foreach ($arrayHolidays as $holiday) {
                                //format theo ngôn ngữ nếu nếu thất bại là flase
                                $newDateFormat = date_create_from_format('Y-m-d', $holiday);
                                if($newDateFormat){
                                    $arrayHolidaysNew[] =  date_format($newDateFormat,"d/m/Y");
                                }else{
                                    $arrayHolidaysNew[] = $holiday;
                                }
                            }
                            $request->merge([
                                $value => implode(", ", $arrayHolidaysNew),
                            ]);
                        }
                    }elseif($value == "collaborator_date"){
                        if (!empty($request->$value)) {
                            $this->formatCollaborDate($request, $value, 'Y-m-d');
                        }
                    }else{
                        if(!empty($request->$value)){   
                            if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y-m-d', $request->$value)->format('d/m/Y'),
                                ]);
                                
                            }
                            if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y-m', $request->$value)->format('m/Y'),
                                ]);
                            }
                            if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1]) ([01]?[0-9]|2[0-3]):([0-5][0-9])$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y-m-d H:i', $request->$value)->format('d/m/Y H:i'),
                                ]);
                                
                            }
                        }
                    }
                }
                break;
            case 'ja':
                foreach ($typeFormats as $value) {
                    if($value == "choose_holidays"){
                        if(!empty($request->$value)){
                            $arrayHolidays = explode(', ',$request->$value);
                            $arrayHolidaysNew = [];
                            foreach ($arrayHolidays as $holiday) {
                                $newDateFormat = date_create_from_format('Y年m月d日', $holiday);
                                if($newDateFormat){
                                    $arrayHolidaysNew[] =  date_format($newDateFormat,"d/m/Y");
                                }else{
                                    $arrayHolidaysNew[] = $holiday;
                                }
                                 
                            }
                            $request->merge([
                                $value => implode(", ", $arrayHolidaysNew),
                            ]);
                        }
                    }elseif($value == "collaborator_date"){
                        if (!empty($request->$value)) {
                            $this->formatCollaborDate($request, $value, 'Y年m月d日');
                        }
                    }else{
                        if(isset($request->$value)){   
                            if (preg_match("/^[0-9]{4}年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y年m月d日', $request->$value)->format('d/m/Y'),
                                ]);
                            }
                            if (preg_match("/^[0-9]{4}年(0[1-9]|1[0-2])月$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y年m月', $request->$value)->format('m/Y'),
                                ]);
                            }
                            if (preg_match("/^[0-9]{4}年(0[1-9]|1[0-2])月(0[1-9]|[1-2][0-9]|3[0-1])日 ([01]?[0-9]|2[0-3])時([0-5][0-9])分$/",$request->$value)){
                                $request->merge([
                                    $value => Carbon::createFromFormat('Y年m月d日 H時i分', $request->$value)->format('d/m/Y H:i'),
                                ]);
                            }
                        }
                    }
                }
                break;
            default:
                break;
        }
        return $next($request); 
    }

    /**
     * formate date collaborator_date to d/m/Y
     *
     * @param Request $request
     * @param string $value
     * @param array $formats
     * @return void
     */
    private function formatCollaborDate(Request $request, $value, $format = 'Y-m-d')
    {
        $arrayCollaborDate = $request->$value;
        $arrayCollaborDateNew = [];
        
        foreach ($arrayCollaborDate as $collaborDate) {
            // format date to d/m/y if has value
            $newDateFormat = date_create_from_format($format, $collaborDate);
            if($newDateFormat){
                $arrayCollaborDateNew[] =  date_format($newDateFormat,"d/m/Y");
            }else{
                $arrayCollaborDateNew[] = $collaborDate;
            }
        }
        $request->merge([
            $value => $arrayCollaborDateNew,
        ]);
    }
}
