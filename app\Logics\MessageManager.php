<?php

namespace App\Logics;

use App\Enums\SocketDataKeysEnum;
use App\Enums\SocketEvent;
use App\Enums\TimeFilterEnum;
use App\Helpers\NotificationHelper;
use App\Helpers\StringHelper;
use App\Models\ConversationFile;
use App\Models\ConversationParticipant;
use App\Models\Message;
use App\Models\MessageRead;
use App\Jobs\Notification\sendNotificationMessage;
use App\Models\Conversation;
use App\Models\MessageBookmark;
use App\Models\MessagePin;
use App\Models\Thread;
use App\Services\EncryptService;
use App\Traits\StorageTrait;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use \Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

class MessageManager
{
    use StorageTrait;
    const NOTIFICATION_MESSAGE = 'send_message';

    protected $encryptService;

    public function __construct() {
        $this->encryptService = new EncryptService();
    }
    
/**
     * list message
     * @param $conversationId
     * @param $idUser
     * @param $type
     * @param $request
     * @return array|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws Exception
     */
    public function list($conversationId, $idUser, $request, $messageId = null)
    {
        try {
            $response = [];
            if (!ConversationManager::checkPermissionConversation($conversationId, $idUser)) {
                return ['status_error' => Message::ERROR_PERMISSION];
            }
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $page = is_numeric($request->get('page')) ? (int)$request->get('page') : 1;
            $queryMessageId = is_numeric($request->get('query_message_id')) ? (int)$request->get('query_message_id') : null;
            $behaviorScroll = is_numeric($request->get('behavior_scroll')) ? (int)$request->get('behavior_scroll') : Message::SCROLL_OLD_MESSAGE;
            //query list message reply
            $messageReply = Message::query()
                ->where('messages.conversation_id', $conversationId)
                ->join('users', 'users.id', 'messages.user_id')
                ->select([
                    'messages.id',
                    DB::raw('(JSON_OBJECT(
                        "id", messages.id,
                        "user_id", users.id,
                        "full_name", CONCAT_WS(" ", users.first_name, users.last_name),
                        "avatar", users.avatar)
                    ) AS content'),
                ]);
            //query lisst message thread
            $messageThread = Message::query()
                ->join('users', 'users.id', 'messages.user_id')
                ->whereNotNull('messages.reply_id')
                ->select([
                    'messages.id',
                    'messages.reply_id',
                    DB::raw('JSON_ARRAYAGG(JSON_OBJECT(
                        "user_id", users.id,
                        "full_name", CONCAT_WS(" ",users.first_name, users.last_name),
                        "avatar", users.avatar,
                        "is_current_user", CASE WHEN users.id = ' . $idUser . ' THEN 1
                        ELSE 0 END
                    )) AS list_user_replies'),
                    DB::raw('COUNT(messages.reply_id) AS message_count')
                ])
                ->where('messages.conversation_id', $conversationId)
                ->groupBy('messages.reply_id');
            //query to check a message reply has been read or not
            $replyIsRead = Message::query()
                ->select('messages.reply_id')
                ->selectRaw('COUNT(messages.id) as reply_is_read')
                // Join with a subquery that message_reply_unread counts for conversation in table message_read
                ->leftJoin(DB::raw('(
                    SELECT 
                        m2.reply_id, 
                        MAX(message_read.message_id) as max_read_reply_id
                    FROM messages m2
                    LEFT JOIN message_read ON message_read.message_id = m2.id
                    WHERE m2.deleted_at IS NULL 
                    AND message_read.user_id = ' . $idUser . '
                    GROUP BY m2.reply_id
                    ) as max_read'), function ($join) {
                    $join->on('messages.reply_id', '=', 'max_read.reply_id');
                })
                ->where('messages.conversation_id', $conversationId)
                ->whereNotNull('messages.reply_id')
                ->whereRaw('messages.id > IFNULL(max_read.max_read_reply_id, 0)')
                ->where('messages.user_id', '!=', $idUser)
                ->groupBy('messages.reply_id');
            
            $data = Message::query()->where('messages.conversation_id', $conversationId);

            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
            if(!empty($messageId)){
                /// If messages in thread
                $data = $data->where('messages.reply_id', $messageId);
            } else{
                /// If messages in main board
                // Get message not reply or reply sent group
                $data = $data->where(function($query) {
                    $query->where('messages.is_sent_group', Message::SENT_GROUP)
                        ->orwhereNull('messages.reply_id');
                });
            }

            // Check conversation or thread has message
            $totalMessagesInConversation = $data->count();
            Log::debug('$totalMessagesInConversation = ' . $totalMessagesInConversation);

            // Get newest message in conversation
            $newestMessageId = $data->max('messages.id');
            Log::debug('$newestMessageId = ' . $newestMessageId);

            // When open the conversation, jump to last unread message
            $jumpToMessageId = null;
            // Find the last unread message in main chat
            $lastUnreadMessageId = null;
            $lastReadMessageId = MessageRead::query()
                ->where('conversation_id', $conversationId)
                ->where('user_id', $idUser)
                ->when(
                    $messageId,
                    function ($query, $value) {
                        return $query->where('thread_id', $value);
                    },
                    function ($query) {
                        return $query->whereNull('thread_id');
                    },
                )
                ->max('message_id');
            Log::debug('$lastReadMessageId = ' . $lastReadMessageId);

            // If the user opens the conversation for the first time, return to the latest messages page
            if (!empty($lastReadMessageId)) {
                // Get last unread message in main
                $lastUnreadMessageId = clone $data;
                $lastUnreadMessageId = $lastUnreadMessageId
                    ->where('messages.user_id', '!=', $idUser)
                    ->when(!empty($lastReadMessageId), function ($query) use ($lastReadMessageId) {
                        return $query->where('messages.id', '>', $lastReadMessageId);
                    })
                    ->min('messages.id');
            } else if (!empty($messageId)) {
                // If user hasn't read any messages in the thread, get the first message in the thread
                $lastUnreadMessageId = $data->min('messages.id');
            }
            
            $data = $data->join('conversation_participants', function ($join) use ($idUser) {
                $join->on('conversation_participants.conversation_id', '=', 'messages.conversation_id')
                    ->where('conversation_participants.user_id', '=', $idUser);
                })
                ->leftJoin('users', function ($join){
                    $join->on('messages.user_id', '=', 'users.id');
                })
                ->leftjoin('message_pins','messages.id','message_pins.message_id')
                ->leftJoin('message_bookmarks', function ($join) use ($idUser){
                    $join->on('message_bookmarks.message_id', '=', 'messages.id')
                        ->where('message_bookmarks.user_id', '=', $idUser);
                })
                ->leftjoin('message_reaction','messages.id','message_reaction.message_id')
                ->leftjoin('users as user_reaction','user_reaction.id','message_reaction.user_id');

            // If the user is not scrolling through messages, jump to the last unread message
            if (!($queryMessageId && $behaviorScroll)) {
                Log::debug('$lastUnreadMessageId = ' . $lastUnreadMessageId);
                if (!empty($lastUnreadMessageId)) {
                    $jumpToMessageId = $lastUnreadMessageId;
                }
            }

            // Case jump to specific message when click notification
            if ($request->get('id_search') && is_numeric($request->get('id_search'))) {
                $jumpToMessageId = intval($request->get('id_search'));
            }

            // Jump to specific message
            if (isset($jumpToMessageId)) {
                Log::debug('$jumpToMessageId = ' . $jumpToMessageId);
                // Check the message being jumped to is on page 1
                $jumpQuery = clone $data;
                $numberNewMessagesFromJumpMessage = $jumpQuery
                    ->where('messages.id', '>=', $jumpToMessageId)
                    ->count();
                Log::debug('$numberNewMessagesFromJumpMessage = ' . $numberNewMessagesFromJumpMessage);
                $isJumpMessageInPage1 = $numberNewMessagesFromJumpMessage > $perPage ? false : true;

                // If the message being jumped to is not on page 1, fetch newer messages starting from the this message
                if (!$isJumpMessageInPage1) {
                    $behaviorScroll = Message::SCROLL_NEW_MESSAGE;
                    $queryMessageId = $jumpToMessageId - 1;
                }
            }
            
            //Handle scroll message
            if ($queryMessageId && $behaviorScroll) {
                if ($behaviorScroll === Message::SCROLL_NEW_MESSAGE) {
                    $data = $data->where('messages.id', '>', $queryMessageId)
                        ->orderBy('messages.id', 'ASC');
                } else if ($behaviorScroll === Message::SCROLL_OLD_MESSAGE) {
                    $data = $data->where('messages.id', '<', $queryMessageId)
                    ->orderBy('messages.id', 'DESC');
                }
            }

            // Get other information of messages
            $data = $data->leftJoinSub($messageThread, 'messageThread', function ($joinReply) {
                $joinReply->on('messages.id', '=', 'messageThread.reply_id');
            })
            ->leftJoinSub($messageReply, 'messageReply', function ($joinReply) {
                $joinReply->on('messages.reply_id', '=', 'messageReply.id');
            })
            ->leftJoinSub($replyIsRead, 'replyIsRead', function ($joinReply) {
                $joinReply->on('messages.id', '=', 'replyIsRead.reply_id');
            });
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
            $decryptedContentNoTi = $this->encryptService->getDecryptedRaw('messages.content_text');
            $data = $data->select([
                    'messages.id',
                    DB::raw($decryptedContentText),
                    DB::raw($decryptedQuoteText),
                    'messages.content_file',
                    'messages.reply_id',
                    'messages.group_message',
                    DB::raw('IF(messages.created_at = messages.updated_at, NULL,messages.updated_at) as `time_updated_message`'),
                    DB::raw('(CASE WHEN (messages.user_id =' . $idUser . ') THEN 1 ELSE 0 END) AS message_type_user'),
                    DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") as time_send_message'),
                    DB::raw('CONCAT("[",IF(ISNULL(message_reaction.reaction),NULL,
                    GROUP_CONCAT(JSON_OBJECT("user_id" , user_reaction.id,"name" , 
                    CONCAT_WS( " ", user_reaction.first_name, user_reaction.last_name ), "avatar", 
                    user_reaction.avatar, "reaction", message_reaction.reaction, "is_current_user" , 
                    CASE WHEN user_reaction.id = ' . $idUser . ' THEN 1 ELSE 0 END))),"]") AS list_reaction'),
                    'messageThread.list_user_replies',
                    'users.id as user_id',
                    DB::raw('CONCAT_WS(" ",users.first_name, users.last_name) as name'),
                    'users.avatar',
                    DB::raw('(CASE WHEN JSON_CONTAINS(messages.remind_users, "'. Auth::guard('api')->user()->id .'") THEN 1 ELSE 0 END) AS isTo'),
                    'messageThread.message_count',
                    DB::raw('(CASE WHEN message_pins.id is null THEN 0 ELSE 1 END) AS status_pin'),
                    DB::raw('(CASE WHEN message_bookmarks.id is null THEN 0 ELSE 1 END) AS status_bookmark'),
                    'messageReply.content as message_reply',
                    DB::raw('IFNULL(replyIsRead.reply_is_read, 0) as reply_is_read'),
                    'message_pins.user_id as user_id_pin',
                    // Check if current user can unpin message or not as field can_unpin 1 or 0
                    DB::raw('(CASE 
                                WHEN conversation_participants.admin = ' . ConversationParticipant::IS_ADMIN . '            
                                OR message_pins.user_id = ' . $idUser . ' 
                                THEN 1
                                ELSE 0
                            END) AS can_unpin'),
                    // Check content text is noti or not
                    DB::raw("(CASE 
                            WHEN $decryptedContentNoTi
                                REGEXP \"^\\\\[Noti:(\\\\d+)?-?([\\\\s\\\\S]*?)\\\\]\" 
                            THEN 1 
                            ELSE 0 
                        END) AS is_text_noti_generate")
            ]);
            if (!empty($messageId)) {
                $response['message_reply'] = (clone $data)->whereNull('messages.reply_id')->orWhere('messages.id', $messageId)->first();
            }
            $data = $data->groupBy('messages.id')
                ->orderBy('messages.id', 'DESC')
                ->paginate($perPage);

            // Check if newest message in list then return 1, else null
            $isNewestMessageInList = false;
            if ($totalMessagesInConversation == 0) {
                $isNewestMessageInList = true;
            }
            foreach($data as $msg) {
                if ($msg['id'] == $newestMessageId) {
                    $isNewestMessageInList = true;
                    break;
                }
            }
            if (!$isNewestMessageInList) {
                $page = null;
            }

            // nếu cuộc trò chuyện đang ẩn ở 2 người thì mở lên
            Conversation::where('id', $conversationId)
                            ->where('is_hide', Conversation::HIDE)
                            ->update(['is_hide'=> Conversation::NOT_HIDE]);
            $response['unread_message_id'] = $lastUnreadMessageId;
            $response['newest_message_id'] = $newestMessageId;
            $response['code'] = Response::HTTP_OK;
            $response['message'] = __('message.message.success');
            $response['current_page'] = $page;
            $response['total_page'] = $data->lastPage();
            $response['per_page'] = $data->perPage();
            if (isset($checkSearch)) {
                $response['per_page'] = $perPage;
                $response['total_page'] = ceil($data->total() /  $response['per_page']);
            }
            $response['total'] = $data->total();

            // If scroll new message then reverse list data
            if ($behaviorScroll == Message::SCROLL_NEW_MESSAGE) {
                $data = $data->reverse()->values();
            } else {
                $data = $data->items();
            }
            $response['data'] = $data;

            return $response;
        } catch (Exception $e) {
            Log::error("[MessageService][list] error " . $e->getMessage());
            throw new Exception('[MessageService][list] error ' . $e->getMessage());
        }
    }

    /**
     * Search message
     */
    public function searchMessage($userId, $params)
    {
        try {
            // get all conversation id of user
            $conversationsSubQr = ConversationParticipant::query()
                ->where('user_id', $userId);

            // filter conversation
            if (!empty($params['conversation_id'])) {
                $conversationsSubQr->where('conversation_id', $params['conversation_id']);
            }
            
            $decryptedContentTextSearch = $this->encryptService->getDecryptedRaw('messages.content_text');
            $messageQr = Message::query()
                ->joinSub($conversationsSubQr, 'conversationsSub', function ($join) {
                    $join->on('messages.conversation_id', '=', 'conversationsSub.conversation_id');
                })->leftJoin('users', 'users.id', 'messages.user_id');
            if(array_key_exists('keyword', $params)) {
                // Search with keywords
                $stringHelper = new StringHelper();
                $keywords =  explode(' ', $params['keyword']);
                foreach ($keywords as $keyword) {
                    $keyword_search = $stringHelper->formatStringWhereLike($keyword);
                    
                    $messageQr->whereRaw(
                        "BINARY LOWER(
                            REGEXP_REPLACE(
                                CAST({$decryptedContentTextSearch} AS CHAR),
                                '(\\\\[toall\\\\])|(\\\\[To:(\\\\d+)?-?\\)|(\\\\[Noti:(\\\\d+)?-?([\\\\s\\\\S]*?)\\\\])',
                                ''
                            )
                        ) LIKE ?",
                        '%' . mb_strtolower($keyword_search, 'UTF-8') . '%'
                    );
                }
            }

            // filter with custom time
            $hasTimeRangeType = array_key_exists('time_range_type', $params);
            if ($hasTimeRangeType && $params['time_range_type'] == TimeFilterEnum::FILTER_CUSTOM) {
                if (!empty($params['from'])) {
                    $messageQr->whereDate('messages.created_at', '>=', Carbon::createFromFormat('d/m/Y', $params['from'])->format('Y-m-d'));
                }

                if (!empty($params['to'])) {
                    $messageQr->whereDate('messages.created_at', '<=', Carbon::createFromFormat('d/m/Y', $params['to'])->format('Y-m-d'));
                }
            }

            // filter with time range
            if ($hasTimeRangeType && $params['time_range_type'] != TimeFilterEnum::FILTER_CUSTOM) {
                $date = Carbon::now()->subDays(7)->format('Y-m-d'); // default 7 days ago
                switch ($params['time_range_type']) {
                    case TimeFilterEnum::FILTER_30_DAYS_AGO:
                        $date = Carbon::now()->subDays(30)->format('Y-m-d');
                        break;
                    case TimeFilterEnum::FILTER_90_DAYS_AGO:
                        $date = Carbon::now()->subDays(90)->format('Y-m-d');
                        break;
                }
                $messageQr->whereDate('messages.created_at', '>=', $date);
            }

            // filter sender
            if (!empty($params['senders'])) {
                $arrSender = json_decode($params['senders']);
                if(is_array($arrSender)){
                    $messageQr->whereIn('messages.user_id', $arrSender);
                }
            }

            if (!empty($params['last_record_id'])) {
                $messageQr->where('messages.id', '<', $params['last_record_id']);
            }

            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
            $messageQr->select([
                'messages.id',
                DB::raw($decryptedContentText),
                DB::raw($decryptedQuoteText),
                'messages.content_file',
                'messages.reply_id',
                'messages.group_message',
                'messages.conversation_id',
                DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") as time_send_message'),
                'users.id as user_id',
                DB::raw('CONCAT_WS(" ",users.first_name, users.last_name) as name'),
                'users.avatar',
                DB::raw('(CASE WHEN JSON_CONTAINS(messages.remind_users, "'. $userId .'") THEN 1 ELSE 0 END) AS isTo'),
            ])->groupBy('messages.id')
            ->orderBy('messages.id', 'DESC');

            $perPage = array_key_exists('per_page', $params) && is_numeric($params['per_page']) ? (int)$params['per_page'] : PER_PAGE;
            return $messageQr->paginate($perPage);
        } catch (Exception $e) {
            Log::error('[MessageManager][search] File: ' . $e->getFile() . ' -- Line: ' . $e->getLine() . ' -- Message: ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * create message
     * @param $conversationId
     * @param $user
     * @param $request
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function store($conversationId, $user, $request)
    {
        try {
            $socketManager = new SocketManager();
            $stringHelper = new StringHelper();
            $rollback = false;
            $statusConversationParticipant = ConversationManager::checkPermissionConversation($conversationId, $user->id);
            if (empty($statusConversationParticipant)) {
                return ['status_error' => Message::ERROR_PERMISSION];
            }
            $dataContentFile = $request->input('content_file');
            $dataContentText = $request->input('content_text');
            //Convert protocol in content text
            $dataContentText = $stringHelper->convertProtocolToLowercase($dataContentText);
            $dataContentQuote = $request->input('quote_text');
            $contentNotificationText = $request->input('content_text');
            $groupMessage = $request->input('group_message');
            $replyId = $request->input('reply_id');
            $errorPath = [];
            $result = null;

            //check reply message has exist in conversation.
            if ($request->filled('reply_id')) {
                $messageReplyExisted = Message::query()
                    ->where('id', $request->input('reply_id'))
                    ->where('conversation_id', $conversationId)
                    ->exists();
                if (!$messageReplyExisted) {
                    return ['status_error' => Message::ERROR_DATA];
                }
            }
            // Chuyển file từ thư mục tạm sang thư mục chính
            if (!empty($dataContentFile)) {
                if ($groupMessage) {
                    $result = Message::query()
                        ->where('messages.conversation_id', $conversationId)
                        ->where('messages.user_id', $user->id)
                        ->where('messages.group_message', $groupMessage)
                        ->first();
                }
                [$dataContentFile, $errorPath] = $this->handleMovedFileMessage($dataContentFile, MESSAGE_DIR . $conversationId . '/');

                if (empty(json_decode($dataContentFile, true)) && !$result) {
                    return ['status_error' => Message::ERROR_DATA];
                }
                if (!$result) {
                    $collection = new Collection(json_decode($dataContentFile, true));
                    $collection = $collection->countBy(function ($val) {
                        return $val['type'];
                    })->all();

                    if (isset($collection[Message::EXTENSION_IMAGE])) {
                        $contentNotificationText = __('message.message.notification.extension_image', ['image' => $collection[Message::EXTENSION_IMAGE]]);
                    }
                    if (isset($collection[Message::EXTENSION_VIDEO])) {
                        $contentNotificationText = __('message.message.notification.extension_video', ['video' => $collection[Message::EXTENSION_VIDEO]]);
                    }
                    if (isset($collection[Message::EXTENSION_IMAGE]) && isset($collection[Message::EXTENSION_VIDEO])) {
                        $contentNotificationText = __('message.message.notification.extension_image_video', ['image' => $collection[Message::EXTENSION_IMAGE], 'video' => $collection[Message::EXTENSION_VIDEO]]);
                    }
                    if (isset($collection[Message::EXTENSION_RADIO])) {
                        $contentNotificationText = __('message.message.notification.extension_radio');
                    }
                    if (isset($collection[Message::EXTENSION_FILE_OTHER])) {
                        $contentNotificationText = __('message.message.notification.extension_file_other', ['file' => $collection[Message::EXTENSION_FILE_OTHER]]);
                    }
                }
            }

            DB::beginTransaction();
            $rollback = true;
            $listMember = [];
            if ($result) {
                $result->quote_text = $dataContentQuote;
                $result->content_text = $dataContentText;
                $result->content_file = $dataContentFile;
                $remind_users = $this->handleToUserRegex($dataContentText, $result->conversation_id);
                $result->remind_users = $remind_users;
                $result->is_sent_group = $request->is_sent_group ? Message::SENT_GROUP : 0;
                $result->save();
                //If the message is a file, create a record in the conversation_files table and update the content_file column of the message
                if (!empty($dataContentFile)) {
                    ConversationFileManager::store(json_decode($dataContentFile, true), $result->id);
                    //Refresh message after update content_file field
                    $result->refresh();
                }
                // if ($result->reply_id) {
                //     $dataReply = $this->getReplyContent($conversationId, json_decode($result->reply_id));
                //     $result->reply_content = $dataReply;
                // }
            } else {
                //handle to user
                $remind_users = $this->handleToUserRegex($dataContentText, $conversationId);
                $encryptContentText = $this->encryptService->encryptedData($dataContentText);
                $encryptQuoteText = $this->encryptService->encryptedQuoteData($dataContentQuote);
                $result = Message::create([
                    'content_text' => $encryptContentText,
                    'content_file' => $dataContentFile,
                    'conversation_id' => (int) $conversationId,
                    'user_id' => $user->id,
                    'reply_id' => $request->input('reply_id') ? $request->input('reply_id') : null,
                    'remind_users' => $remind_users ? json_encode($remind_users) : null,
                    'group_message' => $groupMessage,
                    'quote_text' => $encryptQuoteText,
                    'is_sent_group' => $request->is_sent_group ? Message::SENT_GROUP : 0
                ]);
                // if ($result->reply_id) {
                //     $dataReply = $this->getReplyContent($conversationId, json_decode($result->reply_id));
                //     $result->reply_content = $dataReply;
                // }
                
                //The sender reads previous messages
                self::read($result->conversation_id, $result->id, $user->id);
                //If the message is a file, create a record in the conversation_files table and update the content_file column of the message
                if (!empty($dataContentFile)) {
                    ConversationFileManager::store(json_decode($dataContentFile, true), $result->id);
                    //Refresh message after update content_file field
                    $result->refresh();
                }
                // Get the list of people in the conversation group
                $dataConversationParticipant = ConversationParticipantManager::GetConversationParticipant($conversationId, $user->id, ['id','user_id', 'status', 'updated_at']);
                $listMember = $dataConversationParticipant->pluck('user_id')->toArray();
                $idUser = Auth::guard('api')->user()->id;
            }
            //If the message is text, update the record to the conversation_link table
            if (!empty($dataContentText)) {
                ConversationFileManager::getLinkInMessage($dataContentText, $result->id);
            }
            DB::commit();

            // Emit event newMessage to socket
            $listMember = array_merge($listMember, [$user->id]);
            $dataNotiSocket = [
                'conversation_id' => $conversationId,
                'id' => $result->id,
                "content_text" => $dataContentText,
                "content_file" => $result->content_file,
                "error_path" => $result->error_path,
                "created_at" => $result->created_at->format("Y-m-d"),
                "updated_at" => $result->updated_at->format("Y-m-d"),
                "avatar" => $user->avatar,
                "user_id" => $user->id,
                'name' => $user->full_name,
                "receiver" => $listMember,
                "quote_text" => $dataContentQuote,
                "reply_id" => $result->reply_id,
                "message_type_user" => ($result->user_id == $idUser) ? 1 : 0,
                "time_send_message" => $result->created_at->format("H:i d/m/Y"),
                "list_reaction" => [],
                "list_user_replies" => [],
                "message_reply" => Message::query()
                    ->where('messages.conversation_id', $conversationId)
                    ->where('messages.id', $result->reply_id)
                    ->join('users', 'users.id', 'messages.user_id')
                    ->select([
                        "messages.id",
                        "users.id as user_id",
                        DB::raw('CONCAT_WS(" ",users.first_name, users.last_name) as full_name'),
                        "users.avatar"
                    ])->first(),
                "isTo" => 0,
                "group_message" => $result->group_message,
                "message_count" => null,
                'is_sent_group' => $result->is_sent_group,
                'is_message_in_my_thread' => 0,
                'is_text_noti_generate' => $this->isTextNotiGenerate($dataContentText) ? 1 : 0
            ];
            $usersInThread = $this->getUsersInThread($result->reply_id);

            foreach($listMember as $member) {
                // Set data for receiver
                $dataNotiSocket['receiver'] = [$member];

                // Set data for isTo
                $dataNotiSocket['isTo'] = 0;
                if (in_array($member, $remind_users)) {
                    $dataNotiSocket['isTo'] = 1;
                }

                // Set data for is_message_in_my_thread
                $dataNotiSocket['is_message_in_my_thread'] = 0;
                if (!is_null($result->reply_id) && in_array($member, $usersInThread)) {
                    $dataNotiSocket['is_message_in_my_thread'] = 1;
                }

                // Emit socket newMessage
                $socketManager->emit(SocketEvent::NEW_MESSAGE, $dataNotiSocket);
            }
            
            // Send notifications via firebase
            $conversation = Conversation::query()->find($conversationId, ['name', 'avatar', 'type']);
            $dataNotification = [
                'title' => $conversation->type == Conversation::TYPE_MULTI_USER ?
                    $conversation->name : $user->full_name,
                'content' => ($conversation->type == Conversation::TYPE_MULTI_USER ?
                $user->full_name.': ' : '').$contentNotificationText,
                'avatar' => $conversation->type == Conversation::TYPE_MULTI_USER ?
                    $conversation->avatar : $user->avatar,
                'notification_type' => NotificationHelper::NOTIFICATION_TYPE_MESSAGE,
                'id' => $result->id,
                'reply_id' => $result->reply_id,
                'conversation_id' => $conversationId,
                'conversation_type' => $conversation->type,
                "remind_users" => $result->remind_users
            ];
            $websiteId = null;
            if (env('USE_TENANT', false)) {
                $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
            }
            dispatch(new sendNotificationMessage($dataNotification, $dataConversationParticipant, $websiteId))
                ->onQueue(config('queue.queueType.message'));

            //Emit event updateConversation in main message
            if (empty($replyId) || ($result->is_sent_group ==  Message::SENT_GROUP)) {
                $socketManager = new SocketManager();
                $dataSocketUpdateConversation = [
                    'conversation_id' => (int)$conversationId,
                    'user_id' => $user->id,
                    'message_id' => $result->id,
                    'time_send_message' => $this->formatMessageTime($result->created_at->format("Y-m-d H:i:s")),
                    'time_send_message_default' => $result->created_at->format("Y-m-d H:i:s"),
                    'most_recent_message' => $this->getMostRecentMessage($dataContentText, $dataContentFile),
                    'receiver' => $listMember,
                    'remind_users' => $remind_users,
                    'is_edit' => Message::NOT_EDITED
                ];
                $socketManager->emit(SocketEvent::UPDATE_CONVERSATION, $dataSocketUpdateConversation);
            }
            //Add another information for API
            $result->name = $user->first_name . ' ' . $user->last_name;
            $result->avatar = $user->avatar;
            $result->time_send_message = date('H:i d/m/Y', strtotime($result->updated_at));
            $result->reply_content = null;
            $result->error_path = $errorPath;
            $result->content_text = $dataContentText;
            $result->quote_text = $dataContentQuote;
            return $result;
        } catch (Exception $e) {
            Log::error('[MessageManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
            if ($rollback) {
                DB::rollBack();
            }
            throw new Exception('[MessageManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Check if text contains notification pattern
     * @param $dataContentText
     * @return bool
    */
    public function isTextNotiGenerate($dataContentText){
        if (empty($dataContentText)) {
            return false;
        }
        $notiPatternRegex = '/^\[Noti:(\d+)?-?([\s\S]*?)\]/';
        return (bool) preg_match($notiPatternRegex, $dataContentText);
    }

    /**
     * update message
     * @param $messageId
     * @param $user
     * @param $request
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function update($messageId, $user, $request)
    {
        try {
            $stringHelper = new StringHelper();
            $rollback = false;
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text');
            $result = Message::query()
                ->where('messages.id', $messageId)
                ->where('messages.user_id', $user->id)
                ->whereRaw("CAST({$decryptedContentText} AS CHAR) 
                NOT REGEXP '^\\\\[Noti:(\\\\d+)?-?([\\\\s\\\\S]*?)\\\\]'")
                ->first();
            if (empty($result)) {
                return ['status_error' => Message::ERROR_PERMISSION];
            }
            $dataContentFile = $request->input('content_file');
            $dataContentText = $request->input('content_text');
            //Convert protocol in content text
            $dataContentText = $stringHelper->convertProtocolToLowercase($dataContentText);
            $contentNotificationText = $request->input('content_text');
            $replyId = $request->input('reply_id');
            $errorPath = [];

            // Chuyển file từ thư mục tạm sang thư mục chính
            if (!empty($dataContentFile)) {
                [$dataContentFile, $errorPath] = $this->handleMovedFileMessage($dataContentFile, MESSAGE_DIR . $result->conversation_id . '/');
                if (empty(json_decode($dataContentFile, true))) {
                    return ['status_error' => Message::ERROR_DATA];
                }
            }
            DB::beginTransaction();
            $rollback = true;
            if ($result) {
                $originalRemindUsers = json_decode($result->remind_users, true) ?: [];
                $encryptedContentText = $this->encryptService->encryptedData($dataContentText);
                $result->content_text = ($dataContentText !== null) ? $encryptedContentText : $result->content_text;
                $result->content_file = !empty($dataContentFile) ? $dataContentFile : $result->content_file;
                $remind_users = $this->handleToUserRegex($dataContentText, $result->conversation_id);
                $result->remind_users = $remind_users;
                $result->save();
                // if ($result->reply_id) {
                //     $dataReply = $this->getReplyContent($result->conversation_id, json_decode($result->reply_id));
                //     $result->reply_content = $dataReply;
                // }
                $changedUsers = array_merge(
                    array_diff($remind_users, $originalRemindUsers),
                    array_diff($originalRemindUsers, $remind_users)
                );
                $hasRemindUsersChanged = count($changedUsers) > 0;
                //Người gửi đọc các tn trước đó
                self::read($result->conversation_id, $result->id, $user->id);
                // Lấy list người trong nhóm hội thoại
                $dataConversationParticipant = ConversationParticipantManager::GetConversationParticipant($result->conversation_id, $user->id, ['id','user_id', 'status', 'updated_at']);
                // send noti to socket
                $dataNotiSocket = [
                    'conversation_id' => $result->conversation_id,
                    'id' => $result->id,
                    'reply_id' => $result->reply_id,
                    "content_text" => $dataContentText,
                    "content_file" => $result->content_file,
                    'group_message' => $result->group_message,
                    'is_sent_group' => $result->is_sent_group,
                    "receiver" => array_merge($dataConversationParticipant->pluck('user_id')->toArray(),[$user->id]),
                    "remind_users" => $remind_users,
                    'time_updated_message' => ($result->created_at == $result->updated_at) ? NULL : $result->updated_at
                ];
                //Emit event updateMessage
                $socketManager = new SocketManager();
                $socketManager->emit(SocketEvent::UPDATE_MESSAGE, $dataNotiSocket);
            }
            //If the message is a file, create a record in the conversation_files table and update the content_file column of the message
            if (!empty($dataContentFile)) {
                ConversationFile::where('message_id', $result->id)->where('type', '!=', ConversationFile::TYPE_LINK)->delete();
                ConversationFileManager::store(json_decode($dataContentFile, true), $result->id);
                //Refresh message after update content_file field
                $result->refresh();
            }
            //If the message is text, update the record to the conversation_link table
            if (!empty($dataContentText)) {
                ConversationFile::where('message_id', $result->id)->where('type', ConversationFile::TYPE_LINK)->delete();
                ConversationFileManager::getLinkInMessage($dataContentText, $result->id);
            }
            DB::commit();
            // Get last message in conversation
            $lastMessage = $this->getLastMessageInConversation($result->conversation_id);
            $listKeySocketUpdateConversation = [];
            if ($hasRemindUsersChanged) {
                $listKeySocketUpdateConversation[] = 'is_edit';
                $listKeySocketUpdateConversation[] = 'remind_users';
            }
            if ($lastMessage && $lastMessage->id == $messageId) {
                $listKeySocketUpdateConversation = array_merge($listKeySocketUpdateConversation, [
                    'most_recent_message',
                    'time_send_message'
                ]);
            }            
            //Emit event updateConversation in main message
            if (!empty($listKeySocketUpdateConversation)) {
                $conversationManager = new ConversationManager();
                $additionalData = [];
                if (in_array('is_edit', $listKeySocketUpdateConversation)) {
                    $additionalData = [
                        'remind_users' => $remind_users, 
                        'original_remind_users' => $originalRemindUsers, 
                        'edited_message_id' => $messageId
                    ];
                }
                $conversationManager->sendNotificationSocketUpdateConversation(
                    $result->conversation_id,
                    [],
                    $listKeySocketUpdateConversation,
                    $additionalData
                );
            }          
            //Add another information for API
            $result->name = $user->first_name . ' ' . $user->last_name;
            $result->time_send_message = date('H:i d/m/Y', strtotime($result->updated_at));
            $result->reply_content = null;
            $result->error_path = $errorPath;
            $result->reply_id = $replyId;
            $result->content_text = $dataContentText;
            $result->quote_text = $request->input('quote_text');
            return $result;
        } catch (Exception $e) {
            Log::error('[MessageManager][update] line ' . $e->getLine() . ' error ' . $e->getMessage());
            if ($rollback) {
                DB::rollBack();
            }
            throw new Exception('[MessageManager][update] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * destroy message
     * @param $messageId
     * @param $user
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function destroy($messageId, $user)
    {
        try {
            $socketManager = new SocketManager();
            $rollback = false;
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'decrypted_content_text');
            $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'decrypted_quote_text');
            $result = Message::query()
                ->where('messages.id', $messageId)
                ->where('messages.user_id', $user->id)
                ->select([
                    'messages.*',
                    DB::raw($decryptedContentText),
                    DB::raw($decryptedQuoteText),
                ])
                ->first();
            if (empty($result)) {
                return ['status_error' => Message::ERROR_PERMISSION];
            }
            DB::beginTransaction();
            $rollback = true;
            //Send notification socket to all user in conversation
            $conversation = Conversation::query()
                ->with('list_user:users.id')
                ->find($result->conversation_id);

            //Check if it's a pinned message
            $messagePin = Message::query()
                ->select([
                    'messages.conversation_id',
                    'messages.id AS messages_id',
                ])
                ->join('message_pins', 'messages.id', 'message_pins.message_id')
                ->join('conversations', 'messages.conversation_id', 'conversations.id')
                ->where('messages.id', $messageId)
                ->first();

            if (!empty($messagePin)) {
                //List of users receiving notifications
                $messagePin->receiver = $conversation->list_user->pluck('id')->toArray();
                //Delete message in message_pin table
                MessagePin::query()
                    ->where('message_id', $messageId)
                    ->delete();
                //Emit event unpin
                $socketManager->emit(SocketEvent::UNPIN, $messagePin->toArray());
            }

            // Get last message in conversation before delete messages
            $lastMessage = $this->getLastMessageInConversation($result->conversation_id);

            $messageListRequest = new Request([
                "per_page" => 2,
            ]);
            $messageList = $this->list($result->conversation_id, $result->user_id, $messageListRequest, $result->reply_id);
            $lastReadMessageId = isset($messageList['data'][0]->id) ? $messageList['data'][0]->id : null;
            $newLastReadMessageId = isset($messageList['data'][1]->id) ? $messageList['data'][1]->id : null;
             //delete bookmark origin message and all bookmark in thread which is in message destroyed
            $bookmarkIdToDelete = Message::where('id', $messageId)
                ->orWhere('reply_id', $messageId)
                ->pluck('id');
            if ($bookmarkIdToDelete->isNotEmpty()) {
                MessageBookmark::whereIn('message_id', $bookmarkIdToDelete)->delete();
            }
            
            // Delete reply message.
            if (empty($result->reply_id)) {
                Message::query()->where('reply_id', $result->id)->delete();
            }
            
            $result->delete();
            ConversationFile::where('message_id', $messageId)->delete();

            //If message in replied and is the last message in thread
            if(($lastReadMessageId === (int)$messageId) && $newLastReadMessageId)
            {
                $usersHasReadLastMessage = MessageRead::where('message_id', $messageId)
                ->pluck('user_id')
                ->toArray();

                $usersHasReadNewLastMessage = MessageRead::where('message_id', $newLastReadMessageId)
                ->pluck('user_id')
                ->toArray();

                $usersHasNotReadNewLastMessage = array_diff($usersHasReadLastMessage, $usersHasReadNewLastMessage);

                MessageRead::where('message_id', $messageId)->whereIn('user_id', $usersHasNotReadNewLastMessage)
                    ->update(['message_id' => $newLastReadMessageId]);
            }
            //If message is a thread, delete in message_read
            MessageRead::where('thread_id', $messageId)->delete();
            
            DB::commit();

            if ($result->reply_id) {
                $newestMessageId = $messageId == $lastReadMessageId ? ($newLastReadMessageId ?? null) : $lastReadMessageId;
            } else {
                $lastItemConversation = $this->getLastMessageInConversation($result->conversation_id);
                $newestMessageId = $lastItemConversation->id ?? null;
            }

            //Emit event destroyMessage
            $socketManager->emit(SocketEvent::DESTROY_MESSAGE, [
                'id' => $result->id,
                'conversation_id' => $result->conversation_id,
                'reply_id' => $result->reply_id,
                'is_sent_group' => $result->is_sent_group,
                'receiver' => $conversation->list_user->pluck('id')->toArray(),
                'newest_message_id' => $newestMessageId,
            ]);

            //Emit event updateConversation in main message if it is the last message
            if (!empty($lastMessage) && $lastMessage->id == $messageId) {
                $conversationManager = new ConversationManager();
                $listKeySocketUpdateConversation = [
                    'most_recent_message',
                    'time_send_message'
                ];
                $conversationManager->sendNotificationSocketUpdateConversation(
                    $result->conversation_id,
                    [],
                    $listKeySocketUpdateConversation
                );
            }
            $result->content_text = $result->decrypted_content_text;
            $result->quote_text = $result->decrypted_quote_text;
            $result->makeHidden(['decrypted_content_text', 'decrypted_quote_text']);
            return $result;
        } catch (Exception $e) {
            Log::error('[MessageManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
            if ($rollback) {
                DB::rollBack();
            }
            throw new Exception('[MessageManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Handle Upload message.
     *
     * @param $files
     * @param $uploadPath
     * @return array|false|string
     */
    public function handleMovedFileMessage($files = [], $uploadPath = '')
    {
        $savePath = [];
        $files = json_decode($files);
        $errorPath = [];
        if (is_null($files)) {
            return $savePath;
        }
        if (!Storage::disk(FILESYSTEM)->exists($uploadPath)) {
            Storage::disk(FILESYSTEM)->makeDirectory($uploadPath);
        }
        foreach ($files as $file) {
            if (isset($file->file_path) && isset($file->name) && isset($file->type) && Storage::disk(FILESYSTEM)->exists($file->file_path)) {
                $errorStatus = false;
                $new_path = str_replace('tmp/', $uploadPath, $file->file_path);
                $dataFile = [
                    'file_path' => $new_path,
                    'name' => $file->name,
                    'type' => $file->type,
                ];
                if ($file->type == Message::EXTENSION_VIDEO || $file->type == Message::EXTENSION_IMAGE) {
                    if (!(isset($file->width) && isset($file->height) && isset($file->thumbnail) && Storage::disk(FILESYSTEM)->exists($file->thumbnail))) {
                        $errorStatus = true;
                        $errorPath[] = json_decode(json_encode($file), true);
                    } else {
                        $new_path_thumbnail = str_replace('tmp/', $uploadPath, $file->thumbnail);
                        $dataFile['width'] = $file->width;
                        $dataFile['height'] = $file->height;
                        $dataFile['thumbnail'] = $new_path_thumbnail;
                        Storage::disk(FILESYSTEM)->move($file->thumbnail, $new_path_thumbnail);
                    }
                }

                $dataFile['size'] = '';
                $size = Storage::disk(FILESYSTEM)->size($file->file_path);
                if ($size < 1024 * 1024) {
                    $dataFile['size'] = number_format($size / 1024, 2) . ' KB';
                } else {
                    $dataFile['size'] = number_format($size / 1024 / 1024, 2) . ' MB';
                }
                if (!$errorStatus) {
                    Storage::disk(FILESYSTEM)->move($file->file_path, $new_path);
                    $savePath[] = $dataFile;
                }
            } else {
                $errorPath[] = json_decode(json_encode($file), true);
            }
        }
        return [json_encode($savePath), $errorPath];
    }
    
    /**
     * get reply content
     * @param $id
     * @param $replyId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    public function getReplyContent($id, $replyId) {
        $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
        $data = Message::query()
            ->where('messages.conversation_id', $id)
            ->whereIn('messages.id', $replyId)
            ->select(
                [
                    "id",
                    DB::raw($decryptedContentText),
                    "content_file"
                ]
            )
            ->get()
            ->toArray();
        foreach ($data as $key => $item) {
            if (!empty($item['content_file'])) {
                $data[$key]['content_file'] = json_encode([json_decode($item['content_file'])[0]]);
            }
        }
        return $data;
    }

    /**
     * handle to user
     * @param $dataContentText
     * @param $conversationId
     * @return array
     */
    public function handleToUserRegex($dataContentText, $conversationId){
        $ToAllPatternRegex = '/\[toall\]/';
        $getUserConversation = [];
        $getUserId = Auth::guard('api')->user()->id;
        if(preg_match_all($ToAllPatternRegex, $dataContentText, $matches)){
            $getUserConversation = ConversationParticipant::where('conversation_id', $conversationId)
            ->where('conversation_participants.user_id', '!=', $getUserId)
            ->pluck('user_id')->toArray();
            return $getUserConversation;
        } else {
            $userId = [];
            $ToPatternRegex = '/\[To:(\d+)-/';
            if(preg_match_all($ToPatternRegex, $dataContentText, $matches)){
                foreach ($matches[1] as $key => $match) {
                    $userId[] = (int)$match;
                }
                $userId = array_values(array_unique($userId));
                return $userId;
            }
        }
        return [];
    }
    
    /**
     * read message
     * @param $conversationId
     * @param $idUser
     * @param $messageId
     * @return bool
     * @throws Exception
     */
    public function read($conversationId, $messageId, $idUser, $sendNotificationSocket = false,
        $isReadInThread = true)
    {
        try {
            if (!ConversationManager::checkPermissionConversation($conversationId, $idUser)) {
                return null;
            }
            $threadId = null;
            // Check if $messageId and thread_id exists
            $messageRecord = Message::where('id', $messageId)
                ->where('conversation_id', $conversationId)
                ->whereNull('deleted_at')
                ->first();
            if (!$messageRecord) {
                return null;
            }
            // If the message is a reply, get its parent message
            if ($messageRecord->reply_id !== null) {
                $replyRecord = Message::where('id', $messageRecord->reply_id);
                if (!$replyRecord) {
                    return null;
                }
                $threadId = $messageRecord->reply_id;
            }
            // Check if the user has read the message or not. If not, insert data to message_read table
            $now = Carbon::now()->format('Y-m-d H:i:s');
            DB::beginTransaction();
            $existingRecord = MessageRead::where('user_id', $idUser)
                ->where('conversation_id', $conversationId)
                ->whereRaw('thread_id <=> ?', [$threadId])
                ->orderByDesc('message_id')
                ->first();
            $check = true;
            if (!$existingRecord || ($messageId > $existingRecord->message_id)) {
                $check = MessageRead::updateOrCreate(
                        [
                            'user_id' => $idUser,
                            'conversation_id' => $conversationId,
                            'thread_id' => $threadId,
                        ],
                        [
                            'message_id' => $messageId,
                            'updated_at' => $now,
                        ]
                    ) !== null;
                
                if ($sendNotificationSocket) {
                    //set data for send socket after read message by queue
                    $tenantId = '';
                    if (env('USE_TENANT', false)) {
                        $tenantId = 'tenant_' . app(\Hyn\Tenancy\Environment::class)->website()->id . '_';
                    }
                    Redis::sadd("{$tenantId}read_message_queue", "{$idUser}" . GROUP_CONCAT_SEPARATOR . "{$conversationId}");

                    // If user read the message in thread, emit event updateMessage to message reader
                    $message = Message::find($messageId);
                    if ($message && isset($message->reply_id)) {
                        $threadId = $message->reply_id;
                        $hasUnreadReplyMessageInThread = $this->checkThreadHasUnreadMessage($threadId, $idUser);
                        $socketManager = new SocketManager();
                        $updateMessageSocketData = [
                            'id' => $threadId,
                            'has_unread_reply_message' => $hasUnreadReplyMessageInThread,
                            'receiver' => [$idUser],
                            'conversation_id' => $conversationId
                        ];
                        $socketManager->emit(SocketEvent::UPDATE_MESSAGE, $updateMessageSocketData);
                    }
                }
            }

            // If the message in thread is sent to the group, update the last read message for the main chat
            if ($threadId && $messageRecord->is_sent_group == Message::SENT_GROUP && !$isReadInThread) {
                $mainChatMessageRead = MessageRead::where('user_id', $idUser)
                    ->where('conversation_id', $conversationId)
                    ->whereNull('thread_id')
                    ->orderByDesc('message_id')
                    ->first();
                if (!$mainChatMessageRead || ($messageId > $mainChatMessageRead->message_id)) {
                    // Update or create the main chat read record
                    MessageRead::updateOrCreate(
                        [
                            'user_id' => $idUser,
                            'conversation_id' => $conversationId,
                            'thread_id' => null,
                        ],
                        [
                            'message_id' => $messageId,
                            'updated_at' => $now,
                        ]
                    );
                }
            }

            DB::commit();

            return $check;
        } catch (Exception $e) {
            Log::error("[MessageManager][read] error " . $e->getMessage());
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * pin message
     * @param $idUser
     * @param $request
     * @return bool
     * @throws Exception
     */
    public function pinMessage($idUser, $messageId){
        try {
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $getConversationId = Message::select([
                    'messages.conversation_id',
                    'messages.id AS messages_id',
                    DB::raw($decryptedContentText),
                    'messages.content_file',
                    'messages.reply_id',
                    'messages.user_id',
                    'users.avatar',
                    'conversations.type',
                    DB::raw('CONCAT_WS(" ",users.first_name, users.last_name) as full_name'),
                    DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") AS time_send_message'),
                ])
                ->join('users','users.id', '=', 'messages.user_id')
                ->join('conversations', 'messages.conversation_id', 'conversations.id')
                ->where('messages.id', $messageId)
                ->first();
            if (!ConversationManager::checkPermissionConversation($getConversationId->conversation_id, $idUser)) {
                return [['status_error' => Response::HTTP_FORBIDDEN],__('message.message.action.pin')];
            }
            if($this->checkMessagePin($getConversationId->conversation_id, $messageId) >= MessagePin::PIN_MESSAGE_MAX){
                return [['status_error' => Response::HTTP_BAD_REQUEST], ''];
            }
            $canUnpinMessages = false;

            $messagePin = MessagePin::where([
                'message_id' => $messageId,
            ])->first();
            if ($messagePin) {
                $conversationParticipantManager = new ConversationParticipantManager();
                $canUnpinMessages = $conversationParticipantManager->isAdminInConversation($idUser, $getConversationId->conversation_id) || $messagePin->user_id === $idUser;
                if (!$canUnpinMessages) {
                    return [['status_error' => Response::HTTP_FORBIDDEN], __('message.message.action.unpin')];
                }

                $messagePin->delete();
                $event = SocketEvent::UNPIN;
                $action = __('message.message.action.unpin');
            }else{
                $messagePin = messagePin::updateOrCreate([
                    'message_id' => $messageId,
                ],[
                    'user_id' => $idUser,
                    'message_id' => $messageId,
                ]);
                $event = SocketEvent::PIN;
                $action = __('message.message.action.pin');
            }

            //Get list member in conversation
            $getConversationParticipants = ConversationParticipantManager::GetConversationParticipant($getConversationId->conversation_id, null, ['user_id', 'admin']);
            $listReceiverUserId = [];
            $listReceiverUserIdCanUnpinMessage = [];

            $getConversationParticipants->map(function ($conversation) use ($getConversationId, $idUser, &$listReceiverUserId, &$listReceiverUserIdCanUnpinMessage) {
                // Check user can unpin if can unpin mapping to $listReceiverUserId[] else mapping to $listReceiverUserIdCanUnpinMessage
                $canUnpinMessages = $conversation->admin == ConversationParticipant::IS_ADMIN ||  $conversation->user_id == $idUser;
                if (in_array($getConversationId->type, [Conversation::TYPE_TWO_USER, Conversation::TYPE_MULTI_USER])) {
                    //Case group chat or two member
                    if (!$canUnpinMessages) {
                        $listReceiverUserId[] = $conversation->user_id;
                    }else {
                        $listReceiverUserIdCanUnpinMessage[] = $conversation->user_id;
                    }
                } else {
                    //Case My chat
                    $listReceiverUserId = [$idUser];
                }
            });
            // send noti to socket
            $messagePin->time_pin_message = date('H:i d/m/Y',strtotime($messagePin->created_at));
            $messagePin['user_id_pin'] = $idUser;
            // Send notification to users can unpin the message
            $socketManager = new SocketManager();
            if ($listReceiverUserIdCanUnpinMessage) {
                $messagePin['receiver'] = $listReceiverUserIdCanUnpinMessage;
                $messagePin['can_unpin'] = 1;
                $messagePin = collect($messagePin)->merge($getConversationId);
                $socketManager->emit($event, $messagePin);
            }
            // Send notification to other users
            if ($listReceiverUserId) {
                $messagePin['receiver'] = $listReceiverUserId;
                $messagePin['can_unpin'] = 0;
                $messagePin = collect($messagePin)->merge($getConversationId);
                $socketManager->emit($event, $messagePin);
            }
            
            return [$messagePin, $action];
        } catch(\Illuminate\Database\QueryException $e){
            Log::debug("[MessageManager][pinMessage] line " . $e->getLine() . " error " . $e->getMessage());
        }
         catch (Exception $e) {
            Log::error("[MessageManager][pinMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageManager][pinMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    public function checkMessagePin($getConversationId, $messageId){
        $getMessagePin = MessagePin::join('messages', 'messages.id', 'message_pins.message_id')
            ->where('messages.conversation_id', $getConversationId)
            ->where('message_pins.message_id', '!=', $messageId)
            ->count();
        return $getMessagePin;
    }

    /**
     * list file message
     * @param $request
     * @throws Exception
     */
    public function getListFileMessage($request){
        try {
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : PER_PAGE;
            $param = $request->only(['conversation_id', 'type', 'user_id', 'filter_from', 'filter_to', 'typeToFilter', 'keyword']);
            $type = json_decode($param['type']);

            // Get all conversations id of the user
            $userId = Auth::guard('api')->user()->id;
            $userConversationsSubQr = ConversationParticipant::query()
                ->where('user_id', $userId);

            // Filter conversation
            if (!empty($param['conversation_id'])) {
                $userConversationsSubQr->where('conversation_id', $param['conversation_id']);
            }

            // Conversation info sub query
            $conversationManager = new ConversationManager();
            $conversationInfoSubQr = $conversationManager->getConversationInfoSubQr($userId);

            $data = ConversationFile::query()
                            ->join('messages', 'messages.id', "conversation_files.message_id")
                            ->leftJoin("users", "messages.user_id","users.id")
                            ->leftJoin("departments", "users.department_id","departments.id")
                            ->joinSub($userConversationsSubQr, 'userConversationsSubQr', function ($join) {
                                $join->on('messages.conversation_id', '=', 'userConversationsSubQr.conversation_id');
                            })
                            ->joinSub($conversationInfoSubQr, 'conversationInfoSubQr', function ($join) {
                                $join->on('messages.conversation_id', '=', 'conversationInfoSubQr.conversation_id');
                            })
                            ->select([
                                "conversation_files.id",
                                "conversation_files.message_id",
                                "messages.conversation_id",
                                "messages.user_id",
                                DB::raw('CAST(messages.reply_id AS UNSIGNED INTEGER) as reply_id'),
                                "users.avatar as user_avatar",
                                "departments.name as department",
                                DB::raw("CONCAT_WS(' ',users.first_name, users.last_name) as user_name"),
                                'conversation_files.name', 
                                'conversation_files.thumbnail', 
                                'conversation_files.path', 
                                'conversation_files.size',
                                "conversation_files.created_at",
                                'conversation_files.type',
                                'conversationInfoSubQr.conversation_name',
                            ])
                            ->whereIn('type', $type);
                            
            // lọc
            if(!empty($param['user_id'])){
                $data->where('messages.user_id', $param['user_id']);
            };

            // Filter by custom time
            $hasTimeRangeType = array_key_exists('typeToFilter', $param);
            if ($hasTimeRangeType && $param['typeToFilter'] == TimeFilterEnum::FILTER_CUSTOM) {
                if(!empty($param['filter_from'])){
                    $from = Carbon::parse($param['filter_from'])->format('Y-m-d');
                    $data->where("conversation_files.created_at", ">=", $from);
                };
                if(!empty($param['filter_to'])){
                    $to = Carbon::parse($param['filter_to'])->format('Y-m-d');
                    $data->whereDate("conversation_files.created_at", "<=", $to);
                };
            }

            // Filter by range time
            if($hasTimeRangeType && $param['typeToFilter'] != TimeFilterEnum::FILTER_CUSTOM){
                // lấy bản ghi ngày hôm qua
                if($param["typeToFilter"] == ConversationFile::FILTER_YESTERDAY){
                    $yesterday = Carbon::now()->subDay()->format('Y-m-d');
                    $data->whereDate("conversation_files.created_at", '=',$yesterday);
                }
                // lấy bản ghi tuần trước hoặc tháng trước
                $dateStart = Carbon::now()->subWeek()->startOfWeek()->toDateString();
                $dateEnd = Carbon::now()->subWeek()->endOfWeek()->toDateString();
                if($param["typeToFilter"] == ConversationFile::FILTER_LAST_MONTH){
                    $dateStart = Carbon::now()->startOfMonth()->subMonthsNoOverflow()->toDateString();
                    $dateEnd = Carbon::now()->subMonthsNoOverflow()->endOfMonth()->toDateString();
                }
                if($param["typeToFilter"] == ConversationFile::FILTER_LAST_MONTH || $param["typeToFilter"] == ConversationFile::FILTER_LAST_WEEK){
                    $data->whereBetween("conversation_files.created_at", [$dateStart.' 00:00:00', $dateEnd.' 23:59:59']);
                }

                // Filter by 7, 30, 90 days ago
                if ($param["typeToFilter"] == TimeFilterEnum::FILTER_7_DAYS_AGO
                    || $param["typeToFilter"] == TimeFilterEnum::FILTER_30_DAYS_AGO
                    || $param["typeToFilter"] == TimeFilterEnum::FILTER_90_DAYS_AGO
                ) {
                    $date = Carbon::now()->subDays(7)->format('Y-m-d'); // default 7 days ago
                    switch ($param['typeToFilter']) {
                        case TimeFilterEnum::FILTER_30_DAYS_AGO:
                            $date = Carbon::now()->subDays(30)->format('Y-m-d');
                            break;
                        case TimeFilterEnum::FILTER_90_DAYS_AGO:
                            $date = Carbon::now()->subDays(90)->format('Y-m-d');
                            break;
                    }
                    $data->whereDate('conversation_files.created_at', '>=', $date);
                }
            };
            
            if(isset($param['keyword'])){
                $stringHelper = new StringHelper();
                $keyword_search = $stringHelper->formatStringWhereLike($param['keyword']);
                $data->where("conversation_files.name", 'LIKE', '%'.$keyword_search.'%');
            };
            $data = $data->orderBy('conversation_files.created_at', 'DESC')->paginate($perPage);
            $response['code'] = Response::HTTP_OK;
            $response['message'] = __('message.message.file.success');
            $response['current_page'] = is_numeric($request->get('page')) ? (int)$request->get('page') : 1;
            $response['total_page'] = $data->lastPage();
            $response['per_page'] = $data->perPage();
            $response['total'] = $data->total();
            $data = $data->groupBy(function ($item) {
                return $item->created_at->format('d M, Y');
            })->toArray();
            $returnData = [];
            foreach ($data as $key => $value) {
                $returnData[] = [
                    'date' => $key,
                    'content_file' => $value
                ];
            }
            $response['data'] = $returnData;
            return $response;
        } catch (Exception $e) {
            Log::error("[MessageManager][getListFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageManager][getListFileMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /*
     * Get list pin message
     * @param $userId
     * @param $conversationId
     * @return array|Builder[]|\Illuminate\Database\Eloquent\Collection
     * @throws Exception
     */
    public function getListPinMessage($userId, $conversationId)
    {
        try {
            $checkPermission = ConversationManager::checkPermissionConversation($conversationId, $userId);
            if (empty($checkPermission)){
                return ['error_status' => Message::ERROR_PERMISSION];
            }
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $listPinMessage = MessagePin::query()
                ->join('messages','message_pins.message_id', '=', 'messages.id')
                ->join('users','users.id', '=', 'messages.user_id')
                ->leftJoin('conversation_participants', function ($join) use ($userId) {
                    $join->on('conversation_participants.conversation_id', '=', 'messages.conversation_id')
                         ->where('conversation_participants.admin', '=', ConversationParticipant::IS_ADMIN)
                         ->where('conversation_participants.user_id', '=', $userId);
                })
                ->where('messages.conversation_id', $conversationId)
                ->limit(MessagePin::PIN_MESSAGE_MAX)
                ->select([
                    'messages.id AS messages_id',
                    DB::raw($decryptedContentText),
                    'messages.content_file',
                    'messages.reply_id',
                    'messages.user_id',
                    'users.avatar',
                    'message_pins.user_id AS user_id_pin',
                    DB::raw('CONCAT_WS(" ",users.first_name, users.last_name) as full_name'),
                    DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") AS time_send_message'),
                    DB::raw('DATE_FORMAT(message_pins.created_at, "%H:%i %d/%m/%Y") AS time_pin_message'),
                    DB::raw('IF(conversation_participants.user_id IS NOT NULL OR message_pins.user_id = ' . $userId . ', 1, 0) AS can_unpin')
                ])
                ->orderByDesc('message_pins.created_at')
                ->groupBy('messages.id')
                ->get();

            $messageCount = $listPinMessage->count();
            foreach ($listPinMessage as $item){
                $item['message_count'] = $messageCount;
            }

            return $listPinMessage;
        } catch (Exception $e) {
            Log::error("[MessageManager][getListPinMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageManager][getListPinMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }


    /*
     * delete file message
     * @param $userId
     * @param $fileId
     * @return array|Builder[]|\Illuminate\Database\Eloquent\Collection
     * @throws Exception
     */
    public function deleteFileMessage($userId, $fileId)
    {
        try {
            $subAdmin = ConversationParticipant::where('conversation_participants.user_id', '=', $userId)
                ->where('conversation_participants.admin',ConversationParticipant::IS_ADMIN);

            $checkPermission = ConversationFile::where('conversation_files.id', $fileId)
            ->join('messages', 'conversation_files.message_id', '=', 'messages.id')
            ->leftJoinSub($subAdmin, 'participant_subquery', function ($join) {
                $join->on('participant_subquery.conversation_id', '=', 'messages.conversation_id');
            })
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->where(function ($query) use ($userId) {

                $query->where('messages.user_id', $userId)
                    ->orWhere(function ($query) use ($userId) {
                        $query->where('participant_subquery.user_id', $userId)->where('conversations.type', Conversation::TYPE_MULTI_USER);
                    });
            })
            ->select([
                'messages.id AS message_id',
                'messages.conversation_id'
            ])
            ->first();
            if(empty($checkPermission)){
                return ['error_status' => ConversationParticipant::ERROR_PERMISSION];
            }
            //Query get file
            $file = ConversationFile::query()->find($fileId);
            //Get message contains file need to delete
            $decryptedContentText = $this->encryptService->getDecryptedRaw('content_text', 'content_text');
            $message = Message::select([
                    'id',
                    'content_file',
                    DB::raw($decryptedContentText),
                    'conversation_id',
                    'reply_id',
                    'group_message',
                    'is_sent_group',
                    'updated_at',
                    'created_at'
                ])
                ->where('id', $checkPermission->message_id)
                ->first();
            if (!$message) {
                return abort(404);
            }
            //Check message has files
            if (!empty($message->content_file)) {
                $contentFiles = json_decode($message->content_file);
                //In content_file of the message, find the file need to delete and set is_delete = true
                foreach ($contentFiles as $fileItem) {
                    if ($fileItem->file_path === $file->path) {
                        $fileItem->is_delete = true;
                        break;
                    }
                }
                //Update latest messages.content_file
                $message->timestamps = false;

                $message->update([
                    'content_file' => empty($contentFiles) ? null : json_encode($contentFiles)
                ]);

                $message->timestamps = true;
            }
            //Delete file in Storage
            $this->deleteFile($file->path);

            //Delete thumbnail of file in Storage
            if (!empty($file->thumbnail)) {
                $this->deleteFile($file->thumbnail);
            }

            //Delete file in ConversationFile table
            $file = $file->delete();

            //Get file information to send socket notification
            $result = ConversationFile::withTrashed()->where('id', $fileId)->first();

            $dataConversationParticipant = ConversationParticipantManager::GetConversationParticipant($checkPermission->conversation_id, null, ['user_id']);
            $listUserReceiver = $dataConversationParticipant->pluck('user_id')->toArray();
            // Send socket delete file message.
            app(SocketManager::class)->emit(SocketEvent::DELETE_FILE_MESSAGE, [
                "id" => $result->id,
                "message_id" => $result->message_id,
                "name" => $result->name,
                "thumbnail" => $result->thumbnail,
                "path" => $result->path,
                "size" => $result->size,
                "type" => $result->type,
                "created_at" => $result->created_at->format("Y-m-d"),
                "updated_at" => $result->updated_at->format("Y-m-d"),
                "deleted_at" => $result->deleted_at->format("Y-m-d"),
                "receiver" => $listUserReceiver,
            ]);
            //Send notification update message
            app(SocketManager::class)->emit(SocketEvent::UPDATE_MESSAGE, [
                'conversation_id' => $message->conversation_id,
                'id' => $message->id,
                'content_text' => $message->content_text,
                'content_file' => $message->content_file,
                'reply_id' => $message->reply_id,
                'group_message' => $message->group_message,
                'is_sent_group' => $message->is_sent_group,
                'receiver' => $listUserReceiver,
            ]);
            return $file;
        } catch (Exception $e) {
            Log::error("[MessageManager][deleteFileMessage] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageManager][deleteFileMessage] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Get list thread of the user
     */
    public function getListThread($userId, $params)
    {
        $number_last_reply_messages = 1; // Number of reply messages each thread returns
        $perPage = array_key_exists('per_page', $params) && is_numeric($params['per_page']) ? 
            (int)$params['per_page'] : PER_PAGE;

        // Conversation info sub query
        $conversationManager = new ConversationManager();
        $conversationInfoSubQr = $conversationManager->getConversationInfoSubQr($userId);
        
        $decryptedContentText  = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
        $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
        // Get the list of threads that the user participates in
        $threadsQr = DB::table('messages')
            ->select(
                'conversations.id AS conversation_id'
                ,'conversations.type AS conversation_type'
                , 'conversationInfoSubQr.conversation_name'
                , 'messages.id AS message_id'
                , DB::raw($decryptedContentText)
                , DB::raw($decryptedQuoteText)
                , 'messages.content_file'
                , 'messages.reply_id'
                , DB::raw('CASE WHEN JSON_CONTAINS(messages.remind_users, "' . $userId . '") THEN 1 ELSE 0 END AS is_to')
                , 'messages.created_at AS sent_at'
                , 'messages.user_id AS sender_id'
                , DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) AS sender_name')
                , 'users.avatar as sender_avatar'
                , DB::raw('COUNT(reply_messages.id) AS number_reply_messages')
                , DB::raw('MAX(reply_messages.id) AS last_reply_message_id')
            )
            // Filter conversations that users participate in
            ->join('conversations', 'conversations.id', '=', 'messages.conversation_id')
            ->join('conversation_participants', function($join) use ($userId) {
                $join->on('conversation_participants.conversation_id', '=', 'conversations.id')
                    ->where('conversation_participants.user_id', $userId);
            })
            ->leftJoin('messages AS reply_messages', 'reply_messages.reply_id', '=', 'messages.id')
            ->join('users', 'users.id', '=', 'messages.user_id')
            // Join to get conversation information
            ->joinSub($conversationInfoSubQr, 'conversationInfoSubQr', function ($join) {
                $join->on('messages.conversation_id', '=', 'conversationInfoSubQr.conversation_id');
            })
            ->whereNull('messages.deleted_at')
            ->whereNull('reply_messages.deleted_at')
            ->whereNull('messages.reply_id')
            ->groupBy('messages.id')
            /**
             * Filter threads that the user is a member of on following these cases:
             * - The sender of the original message of the thread 
             * - Users who chatted in the thread
             * - Users who mentioned in thread
             */
            ->havingRaw("COUNT(reply_messages.id) > 0 
                AND (MIN(messages.user_id) = ? OR MAX(reply_messages.user_id = ?) = 1
                OR JSON_CONTAINS(JSON_ARRAYAGG(reply_messages.remind_users), '$userId') 
			    OR JSON_CONTAINS(JSON_ARRAYAGG(messages.remind_users), '$userId'))"
                , [$userId, $userId])
            // Sort by thread with newest reply message in thread
            ->orderByRaw('MAX(reply_messages.id) DESC');
            
            // Get data from last_record_id
            if (isset($params['last_record_id'])) {
                $threadsQr->havingRaw('MAX(reply_messages.id) < ?', [$params['last_record_id']]);
            }

            // Filter by replied message id
            if (isset($params['replied_message_id'])) {
                $threadsQr->where('messages.id', $params['replied_message_id']);
            }
            $decryptedReplyContent  = $this->encryptService->getDecryptedRaw('last_reply_messages.content_text');
            $decryptedReplyQuote = $this->encryptService->getDecryptedRaw('last_reply_messages.quote_text');
            $threads = DB::table(DB::raw("({$threadsQr->toSql()}) as threadsQr"))
                ->mergeBindings($threadsQr)
                ->select(
                    'threadsQr.*'
                    , DB::raw('CASE WHEN message_read.message_id IS NULL THEN 1 ELSE 0 END has_unread_reply_message')
                    // Get some of the latest reply messages in the thread
                    , DB::raw('
                        (SELECT
                            CONVERT(JSON_ARRAYAGG(JSON_OBJECT(
                                "id", last_reply_messages.id, 
                                "content_text", 
                                CAST(' . $decryptedReplyContent . ' AS CHAR),
                                "quote_text", 
                                CAST(' . $decryptedReplyQuote . ' AS CHAR),
                                "content_file", last_reply_messages.content_file,
                                "reply_id", last_reply_messages.reply_id,
                                "is_to", CASE WHEN JSON_CONTAINS(last_reply_messages.remind_users, "' . $userId . '") 
                                    THEN 1 ELSE 0 END,
                                "sent_at", last_reply_messages.created_at,
                                "sender_id", last_reply_messages.user_id,
                                "sender_name", CONCAT_WS(" ", users.first_name, users.last_name),
                                "sender_avatar", users.avatar
                            )), JSON)
                        FROM
                            (SELECT * FROM messages AS reply_messages 
                                WHERE reply_messages.reply_id = threadsQr.message_id 
                                AND reply_messages.deleted_at IS NULL
                                ORDER BY reply_messages.id DESC LIMIT ' . $number_last_reply_messages . '
                            ) last_reply_messages 
                            JOIN users ON last_reply_messages.user_id = users.id
                        ) last_reply_messages'
                    )
                )
                // Determine if a thread has unread messages
                ->leftJoin('message_read', function($join) use ($userId) {
                    $join->on('message_read.message_id', '=', 'threadsQr.last_reply_message_id')
                        ->whereRaw('message_read.user_id = ' . $userId);
                })
                ->orderByRaw('threadsQr.last_reply_message_id DESC');

            // Filter by filter type
            if (isset($params['filter_type'])) {
                switch ($params['filter_type']) {
                    case Thread::FILTER_TYPE_UNREAD:
                        $threads->whereNull('message_read.message_id');
                        break;
                }
            }

            // Pagination
            $threads = $threads->paginate($perPage);
            
            // Cast string to json array some fields of threads
            foreach($threads as $thread) {
                $last_reply_messages = json_decode($thread->last_reply_messages);
                $thread->last_reply_messages = $last_reply_messages;
            }
            
            return $threads;
    }

    /**
     * Check the thread has unread reply messages
     */
    private function checkThreadHasUnreadMessage($threadId, $userId) {
        // Get last message id in thread
        $lastMessageIdInThread = Message::query()
            ->where('reply_id', $threadId)
            ->max('id');
        
        // Check user has read last message in thread
        $hasReadLastMessage = MessageRead::query()
            ->where('message_id', $lastMessageIdInThread)
            ->where('user_id', $userId)
            ->count();
        
        // If user has read last message in thread then has not unread message
        $hasUnreadMessage = $hasReadLastMessage > 0 ? 0 : 1;

        return $hasUnreadMessage;
    }

    /**
     * Get users in thread
     */
    public function getUsersInThread($threadId) {
        /**
         * User in threads in following these cases:
         * - The sender of the original message of the thread 
         * - Users who chatted in the thread
         * - Users who mentioned in thread
         */
        $usersInMessages = Message::select(
            'user_id',
            'remind_users'
        )->where(function($query) use($threadId) {
            $query->where('id', $threadId)
                ->orWhere('reply_id', $threadId);
        })
        ->distinct()
        ->get()->toArray();

        $usersInThread = [];
        foreach($usersInMessages as $message) {
            $usersInThread = array_merge(
                $usersInThread, 
                [$message['user_id']], 
                json_decode($message['remind_users'], true) ?? []
            );
        }

        $usersInThread = array_unique($usersInThread);
        
        return $usersInThread;
    }
    
    /**
     * Handle content text of last message in conversation
     * - Replace [To:userId-userName] to ""
     * - Replace [ToAll] to ""
     * - Replace [Noti:userId-userName to userName
     * - Remove all text after first line break
     * @param string $dataContentText
     * @return string
     */
    public function handleLastMessageContentText(string $dataContentText) : string
    {
        $lines = explode("\n", $dataContentText);
        $patternTo = '/^\[To:\d+-(.*?)\](.*?)$/';
        $patternToall = '/^\[toall\](.*?)$/';
        $pattern = '/\[To:\d+-.*?\]|\[toall\]/i';

        $processLine = function($line) use ($patternTo, $patternToall, $pattern) {
            if (preg_match($patternTo, $line) || preg_match($patternToall, $line)) {
                // Split string into parts based on pattern
                $parts = preg_split($pattern, $line, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);
                // Trim result and filter element empty
                $parts = array_filter(array_map('trim', $parts));
                return reset($parts) ?? '';
            } else {
                $result = preg_replace_callback(
                    [
                        '/\[Noti:(\d+)?-?([\s\S]*?)\]/',
                        '/^(.*?)\[To:\d+-.*?\].*$/i',
                        '/^(.*?)\[toall\].*$/i'
                    ],
                    function ($matches) {
                        if (isset($matches[2]) && strpos($matches[0], '[To:') === 0) {
                            return trim($matches[2]) ?: ' ';
                        }
                        if (isset($matches[2]) && strpos($matches[0], '[Noti:') === 0) {
                            return $matches[2];
                        }
                        if (isset($matches[1]) && strpos($matches[0], '[toall]') === 0) {
                            return trim($matches[1]) ?: ' ';
                        }
                        if (!empty($matches[1])) {
                            return trim($matches[1]);
                        }
                        return $matches[0];
                    },
                    $line
                );
            }

            return trim($result);
        };

        $result = '';
        foreach ($lines as $line) {
            $result = $processLine(trim($line));
            if (!empty($result)) {
                break;
            }
        }

        return $result;
    }

    /**
     * Retrieves the last message sent in a conversation, either sent to the entire group or not a reply to another message.
     *
     * @param int $conversationId The ID of the conversation to retrieve the last message from.
     *
     * @return \Illuminate\Database\Eloquent\Model|object|null The last message object or null if the conversation does not exist.
     */
    private function getLastMessageInConversation($conversationId) {
        return Message::query()
        ->select('id')
        ->where('conversation_id', $conversationId)
        ->where(function($query) {
            $query->where('is_sent_group', Message::SENT_GROUP)
                ->orwhereNull('reply_id');
        })
        ->orderBy('created_at', 'desc')
        ->first();
    }
    /**
     * delete messages by conversationId
     * @param int $conversationId
     * @return void
     */
    public static function deleteMessageByConversation($conversationId)
    {
        $messageIds = Message::query()
            ->select(['id', 'conversation_id'])
            ->where('conversation_id', $conversationId)
            ->pluck('id');
        if (!$messageIds->isNotEmpty()) {
            return;
        }
        Message::query()->whereIn('id', $messageIds)->delete();
        ConversationFileManager::deleteFileByMessageIds($messageIds);
    }
    
    /**
     * Format message time based on the created_at timestamp.
     * @param string $created_at format "Y-m-d H:i:s
     * @return string Formatted time string.
     */
    public function formatMessageTime($time) {
        $time = Carbon::parse($time);
        if ($time->isToday()) {
            // Same day → display hour:minute AM/PM
            return $time->format('h:i A');
        } elseif ($time->isCurrentYear()) {
            // Same year → display day/month
            return $time->format('d/m');
        } else {
            // Different year → display day/month/year
            return $time->format('d/m/Y');
        }
    }

    /**
     * Retrieves the most recent message from the provided content.
     *
     * @param string|null $dataContentText The text content of the message.
     * @param string|null $dataContentFile The file content of the message.
     * @return string The most recent message.
     */
    public function getMostRecentMessage($dataContentText, $dataContentFile)
    {
        $message = '';
        $currentContentText = $dataContentText ?? '';
        if(strlen($currentContentText) > 0) {
            $messageManager = new MessageManager();
            $message = $messageManager->handleLastMessageContentText($dataContentText);
        } else if(!empty($dataContentFile)){
            $listFile = json_decode($dataContentFile, true);
            if (!empty($listFile)) {
                $fileRecent = end($listFile);
                $formatMessage = [
                    Message::EXTENSION_IMAGE => __('message.message.type.image'),
                    Message::EXTENSION_VIDEO => __('message.message.type.video'),
                    Message::EXTENSION_FILE_OTHER => __('message.message.type.file'),
                    Message::EXTENSION_RADIO => __('message.message.type.record'),
                ];
                foreach ($formatMessage as $key => $value) {
                    if($key == $fileRecent['type']){
                        $message = $value;
                        break;
                    }
                }
            }
        }
        return $message;
    }
}