<?php

namespace App\Models;

use App\Models\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Auth;
use App\Logics\AssetManager;

/**
 * Class Asset
 *
 * @property integer $id
 * @property string $name
 * @property string $name_in_contract
 * @property string $name_short
 * @property integer $management_unit_id
 * @property integer $type_id
 * @property integer $department_id
 * @property integer $user_id
 * @property string $asset_code
 * @property string $handover_record_code
 * @property string $seri_number
 * @property string $asset_supplier_id
 * @property string $country_of_manufacture
 * @property string $manufacturer
 * @property date $manufacturing_date
 * @property string $asset_category
 * @property date $purchase_date
 * @property date $usage_date
 * @property integer $condition_id
 * @property string $source_of_origin
 * @property string $premises
 * @property string $location
 * @property string $description
 * @property string $note
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon $deleted_at
 */

class Asset extends Model
{
    use SoftDeletes;
    protected $table = 'assets';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'name_in_contract',
        'asset_short_name_id',
        'management_unit_id',
        'type_id',
        'department_id',
        'user_id',
        'asset_code',
        'handover_record_code',
        'seri_number',
        'asset_supplier_id',
        'country_of_manufacture',
        'manufacturer',
        'manufacturing_date',
        'asset_category',
        'purchase_date',
        'usage_date',
        'condition_id',
        'source_of_origin',
        'premises',
        'location',
        'description',
        'note',
        'original_price',
        'residual_value',
        'bidding_package'
    ];

    public $sortable = [
        'id',
        'name',
        'management_unit_id',
        'type_id',
        'asset_code',
        'handover_record_code',
        'seri_number',
        'department_id',
        'user_id',
        'condition_id',
        'purchase_date',
        'original_price',
        'residual_value',
        'bidding_package'
    ];

    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->updateAssetCode();
        });
        self::updating(function ($data) {
            $data->updateAssetCode();
        });
    }

    public function updateAssetCode (){
        if(empty($this->asset_code) && !empty($this->type_id)) {
            $assetCode = PropertyType::find($this->type_id)->property_code;
            if(empty($assetCode)){
                $this->asset_code = null;
            }else{
                $lastValue = Asset::where('asset_code', 'like', $assetCode . '%')
                                ->orderByDesc('asset_code')
                                ->value('asset_code');
                if(!empty($lastValue)){
                    $this->asset_code = $assetCode.'00001';
                }
                $nextValue = str_pad((int)substr($lastValue, -5) + 1, 5, '0', STR_PAD_LEFT);
                $this->asset_code = $assetCode.$nextValue;
            }
        }
    }

    public function scopeGetAssetByRole($query)
    {
        // If user has System Manager role or user has Asset Manager role manage all agency then view all assets
        $user = Auth::user();
        $listAssetManagementAgency = (new AssetManager())->getListAssetManagementAgencyByUser();
        if ($user->hasRole([Role::ROLE_SYSTEM_MANAGER]) 
            || ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $listAssetManagementAgency))) {
            return $query;
        }
        
        // User has Asset Manager role manage some agency then only action with assets belong to their manage agency
        if ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && !in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $listAssetManagementAgency)) {
            $query->where(function($query) use ($listAssetManagementAgency, $user){
                $query->whereIn('assets.management_unit_id', $listAssetManagementAgency);
                $query->orWhere('assets.department_id', $user->department_id);
                $query->orWhere('assets.user_id', $user->id);
            });
        } else {
            $query->where("assets.user_id", $user->id);
            if (isset($user->department_id))
                $query->orWhere('assets.department_id', $user->department_id);
            
        }
        return $query;
    }
}
