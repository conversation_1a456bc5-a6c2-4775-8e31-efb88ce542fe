<?php

namespace App\Logics;

use App\Models\Resource;


class ResourceManager
{
    /**
     * get list meeting rooms.
     *
     * @return \Illuminate\Http\Response
     */
    public function getMeetingRoom() {
        $resources =  Resource::join('group_resources', 'resources.group_id', 'group_resources.id')
                ->where('resources.type', Resource::TYPE_MEETING_ROOM)
                ->whereNull('group_resources.deleted_at')
                ->select('resources.id', 'resources.name' ,'group_resources.name as group_name')
                ->get();
        $resources = $resources->groupBy('group_name')
                            ->map(function ($group) {
                                return $group->map(function ($value) {
                                    return [ "id" => $value->id, "name" => $value->name ];
                                });
                            });
        return $resources;
    }
}
