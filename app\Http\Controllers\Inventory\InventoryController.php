<?php

namespace App\Http\Controllers\Inventory;

use App\Http\Controllers\Controller;
use App\Http\Requests\InventoryRequest;
use App\Logics\AssetManager;
use App\Logics\AttachmentManager;
use App\Logics\InventoryManager;
use App\Models\Inventory;
use App\Models\TaskAttachment;
use App\Models\UserInventory;
use App\Traits\StorageTrait;
use Exception;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class InventoryController extends Controller
{
    use StorageTrait;
    const PAGE_SIZE = 15;
    protected $inventoryManager;
    protected $assetManager;
    protected $attachmentManager;
    /**
     * Dependency injection
     */
    public function __construct(
        InventoryManager $inventoryManager,
        AssetManager $assetManager,
        AttachmentManager $attachmentManager
    )
    {
        $this->inventoryManager = $inventoryManager;
        $this->assetManager = $assetManager;
        $this->attachmentManager = $attachmentManager;
    }

    public function index(Request $request)
    {
        $orderBy = isset($request->sort) ? [$request->sort => $request->direction, 'id' => 'DESC'] : ['id' => 'DESC'];
        $inventory = $this->inventoryManager->getListInventory($request->all(), self::PAGE_SIZE, $orderBy);
        $filterHtml = $this->inventoryManager->getFilterHtml($request, ['name', 'start_at', 'end_at', 'status']);
        $status = [
            Inventory::STATUS_OPEN => trans('language.opening'),
            Inventory::STATUS_CLOSE => trans('language.closed'),
        ];
        $data = [
            'inventory' => $inventory,
            'status' => $status,
            'filterHtml' => $filterHtml,
        ];
        return view('asset.inventory.index', $data);
    }

    public function delete($id)
    {
        $asset = Inventory::find($id);

        if ($asset == null) {
            return [
                'status' => ResponseAlias::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('message.inventory_not_exist'),
                ]
            ];
        }

        $asset->delete();

        return [
            'status' => ResponseAlias::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_inventory_succeed') ,
            ],
            'redirect' => route('asset.index'),
        ];
    }

    /**
     * Create inventory
     * @return Application|Factory|View
     */
    public function create()
    {
        return view('asset.inventory.create');
    }

    /**
     * Store inventory
     * @param InventoryRequest $request
     * @return RedirectResponse
     */
    public function store(InventoryRequest $request)
    {
        DB::beginTransaction();
        try {
            $params = $request->only(['inventory_name', 'inventory_user', 'start_date', 'end_date', 'status', 'note', 'descriptionDocument', 'asset_file']);
            $dataInsertInventory = $this->inventoryManager->getAllDataInParams($params);
            $inventory = Inventory::query()->create($dataInsertInventory);

            //Handle insert inventory user
            if (!empty($params['inventory_user'])) {
                $dataInsertUserInventory = [];
                foreach ($params['inventory_user'] as $user) {
                    $dataInsertUserInventory[] = [
                        'user_id' => $user,
                        'inventories_id' => $inventory->id
                    ];
                }
                UserInventory::query()->insert($dataInsertUserInventory);
            }

            //Handle upload attachment
            $fileAttachments = !empty($params['asset_file']) ? $params['asset_file'] : [];
            [$fileAttachmentsFilter, $removeAttachmentsFile] = $this->assetManager->filterFileValid($fileAttachments, 100);
            //Remove attachments item
            foreach ($removeAttachmentsFile as $removeAttachmentItem) {
                $this->deleteFile($removeAttachmentItem);
            }

            //Save file attachments
            if (!empty($fileAttachmentsFilter)) {
                $destinationPath = str_replace(['{inventory_id}'], [$inventory->id], INVENTORY_ATTACHMENT_DIR) . '/';
                $this->attachmentManager->saveAttachments($inventory, TaskAttachment::TYPE_INVENTORY, $fileAttachmentsFilter, $params['descriptionDocument'], $destinationPath);
            }

            DB::commit();
            return redirect()
                ->route('inventory.create')
                ->with([
                    'status_succeed' => trans('message.add_inventory_succeed')
                ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.create_inventory_failed')
            ]);
        }
    }

    /**
     * Edit inventory
     * @param $id
     * @return Application|Factory|View|void
     */
    public function edit($id)
    {
        try {
            $inventory = Inventory::query()
                ->with(['users'])
                ->findOrFail($id);

            $listStatus = [
                Inventory::STATUS_OPEN => trans('language.opening'),
                Inventory::STATUS_CLOSE => trans('language.inventory_status_completed'),
            ];

            $listFileAttachment = TaskAttachment::query()
                ->leftJoin('users', 'task_attachments.created_by', 'users.id')
                ->where(function ($query) use ($id) {
                    $query->where([
                        ['task_attachments.related_id', $id],
                        ['task_attachments.type', TaskAttachment::TYPE_INVENTORY]
                    ]);
                })
                ->select([
                    'task_attachments.id',
                    'task_attachments.related_id',
                    'task_attachments.type',
                    'task_attachments.file_name',
                    'task_attachments.file_path',
                    'task_attachments.file_size',
                    'task_attachments.description',
                    'task_attachments.created_by',
                    'task_attachments.updated_by',
                    'task_attachments.created_at',
                    'task_attachments.updated_at',
                    'users.first_name',
                    'users.last_name',
                ])
                ->get();
            return view('asset.inventory.edit', [
                'inventory' => $inventory,
                'listStatus' => $listStatus,
                'listFile' => $listFileAttachment
            ]);
        } catch (Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            abort(404);
        }
    }

    /**
     * Update inventory
     * @param                  $id
     * @param InventoryRequest $request
     * @return RedirectResponse
     */
    public function update($id, InventoryRequest $request)
    {
        try {
            DB::beginTransaction();
            $params = $request->only(['inventory_name', 'inventory_user', 'start_date', 'end_date', 'status', 'note', 'descriptionDocument', 'asset_file']);
            $dataInventory = $this->inventoryManager->getAllDataInParams($params);
            $inventory = Inventory::query()->find($id);
            $inventory->update($dataInventory);

            //Handle update inventory user
            UserInventory::query()->where('inventories_id', $id)->forceDelete();
            if (!empty($params['inventory_user'])) {
                $dataInsertUserInventory = [];
                foreach ($params['inventory_user'] as $user) {
                    $dataInsertUserInventory[] = [
                        'user_id' => $user,
                        'inventories_id' => $inventory->id
                    ];
                }
                UserInventory::query()->insert($dataInsertUserInventory);
            }

            //Handle upload attachment
            $fileAttachments = !empty($params['asset_file']) ? $params['asset_file'] : [];
            [$fileAttachmentsFilter, $removeAttachmentsFile] = $this->assetManager->filterFileValid($fileAttachments, 100);
            //Remove attachments item
            foreach ($removeAttachmentsFile as $removeAttachmentItem) {
                $this->deleteFile($removeAttachmentItem);
            }

            //Save file attachments
            if (!empty($fileAttachmentsFilter)) {
                $destinationPath = str_replace(['{inventory_id}'], [$inventory->id], INVENTORY_ATTACHMENT_DIR) . '/';
                $this->attachmentManager->saveAttachments($inventory, TaskAttachment::TYPE_INVENTORY, $fileAttachmentsFilter, $params['descriptionDocument'], $destinationPath);
            }
            DB::commit();
            return redirect()
                ->route('inventory.edit', [ 'id' => $id ])
                ->with([
                    'status_succeed' => trans('message.update_inventory_succeed')
                ]);
        } catch (Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.update_inventory_failed')
            ]);
        }

    }
}