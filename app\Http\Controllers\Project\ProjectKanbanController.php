<?php

namespace App\Http\Controllers\Project;

use App\Http\Controllers\Controller;
use App\Http\Requests\KanbanListCreateEditRequest;
use App\Http\Requests\KanbanListsSortOrderRequest;
use App\Logics\ProjectManager;
use App\Logics\UserManager;
use App\Models\Project;
use App\Models\ProjectKanban;
use App\Models\ProjectRole;
use App\Models\TaskStatus;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProjectKanbanController extends Controller
{
    /**
     * Display kanban lists.
     * 
     * @param integer $projectId
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function index($projectId)
    {
        $userId = Auth::id();
        $project = Project::where('projects.id', '=', $projectId)
            ->leftJoin('project_sprints', 'projects.id', 'project_sprints.project_id')
            ->select(['projects.id', 'projects.name', 'project_sprints.id as project_sprint_id'])
            ->first();
        if ($project) {
            //check if current user is project manager
            $userManager = new UserManager();
            $isManager = $userManager->hasProjectRole($userId, $projectId, ProjectRole::ProjectManager);
            if ($isManager) {
                // get all kanban lists of project
                $kanbanLists = ProjectKanban::select([
                        'project_kanbans.id',
                        'project_kanbans.title',
                        'project_kanbans.status',
                        'task_status.name as statusName',
                        'task_status.color as statusColor',
                        'project_kanbans.sort_order'
                    ])
                    ->join('task_status', 'task_status.id', '=', 'status')
                    ->where('project_id', $projectId)
                    ->orderBy('sort_order', 'ASC')
                    ->get();

                // get task statuses
                $allTaskStatuses = TaskStatus::select(
                    [
                        'task_status.id', 
                        'task_status.name',
                        DB::raw('(case when `project_kanbans`.`project_id` is null then 0 else 1 end) as `exists`'),
                    ])
                    ->leftJoin('project_kanbans', function ($join) use ($projectId) {
                        $join->on('task_status.id', '=', 'project_kanbans.status')
                            ->where('project_kanbans.project_id', $projectId)
                            ->whereNull('project_kanbans.deleted_at');
                    })
                    ->get();
                return view('projectX.kanban', [
                    'kanbanLists' => $kanbanLists, 
                    'project' => $project,
                    'isManager' => $isManager,
                    'taskStatuses' => $allTaskStatuses,
                ]);
            }
            return view('projectX.kanban', [
                'project' => $project,
                'isManager' => $isManager,
            ]);
        } else {
            return redirect()->route('project.index');
        }
    }

    /**
     * Create new kanban list.
     * 
     * @param \App\Http\Requests\KanbanListCreateEditRequest $request
     * @param mixed $projectId
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function createKanbanList(KanbanListCreateEditRequest $request, $projectId)
    {
        DB::beginTransaction();
        try {
            $userId = Auth::id();

            // Check project permission
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if(!$project)
                return redirect()->route('project.index');

            $kanbanListData = [
                'title' => $request->get('title'),
                'status' => $request->get('status'),
                'project_id' => $projectId,
                'sort_order' => \Carbon\Carbon::now(),
            ];

            // check if status exists
            $existsStatus = ProjectKanban::select('status')
                ->where('project_id', $projectId)
                ->where('status', $kanbanListData['status'])
                ->first();
            if ($existsStatus) {
                return redirect()->route('project.kanban', ['projectId' => $projectId])->with([
                    'status' => 200,
                    'status_failed' => trans('message.create_kanban_list_failed'),
                ]);
            }

            // create kanban list
            ProjectKanban::create($kanbanListData);
            DB::commit();
            return redirect()->route('project.kanban', ['projectId' => $projectId])->with([
                'status_succeed' => trans('message.create_kanban_list_success'),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Update base info of kanban list.
     * 
     * @param \App\Http\Requests\KanbanListCreateEditRequest $request
     * @return \Illuminate\Http\RedirectResponse|mixed
     */
    public function editKanbanList(KanbanListCreateEditRequest $request)
    {
        DB::beginTransaction();
        try {
            $userId = Auth::id();

            // Check kanban list exists
            $kanbanListId = $request->kanbanListId;
            $kanbanList = ProjectKanban::where('id', $kanbanListId)->first();
            if (!$kanbanList) {
                return redirect()->back()->with([
                    'status_failed' => trans('message.kanban_list_not_exist'),
                ]);
            }

            // Check project permission
            $projectId = $kanbanList->project_id;
            $projectManager = new ProjectManager;
            $project = $projectManager->checkProjectPermission($projectId, $userId);
            if(!$project)
                return redirect()->route('project.index');

            // Kanban list data
            $kanbanListData = [
                'title' => $request->title,
                'status' => $request->status,
            ];

            // Check if status exists
            $existsStatus = ProjectKanban::select('status')
                ->where('project_id', $projectId)
                ->where('id', '<>', $kanbanListId)
                ->where('status', $kanbanListData['status'])
                ->first();
            if ($existsStatus) {
                return redirect()->route('project.kanban', ['projectId' => $projectId])->with([
                    'status_failed' => trans('message.create_kanban_list_failed'),
                ]);
            }

            // Update a kanban list
            $kanbanList->update($kanbanListData);
            DB::commit();
            return redirect()->route("project.kanban", ['projectId' => $projectId])->with([
                'status_succeed' => trans('message.edit_kanban_list_success'),
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Sort order index of kanban list.
     * 
     * @param \App\Http\Requests\KanbanListsSortOrderRequest $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|mixed
     */
    public function sortOrderKanbanLists(KanbanListsSortOrderRequest $request, $projectId)
    {
        $userId = Auth::id();

        // Check kanban list exists
        $listCurrentId = $request->itemCurrent;
        $projectKanban = ProjectKanban::where('id', $listCurrentId)->first();
        if (!$projectKanban) {
            return response()->json([
                'status_failed' => true,
            ]);
        }

        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($projectKanban->project_id, $userId);
        if(!$project)
            return redirect()->route('project.index');
    
        // Generate new value for sort_order of position changed item
        $listPrevId = $request->itemPrev;
        $listNextId = $request->itemNext;

        $kanbanListPrev = ProjectKanban::select('sort_order')
            ->where('id', $listPrevId)
            ->first();
        $kanbanListNext = ProjectKanban::select('sort_order')
            ->where('id', $listNextId)
            ->first();
        $minTime = isset($kanbanListPrev->sort_order) ? strtotime($kanbanListPrev->sort_order) : 0;
        $maxTime = isset($kanbanListNext->sort_order) ? strtotime($kanbanListNext->sort_order) : time();
        $kanbanListCurrentSortOrder = rand($minTime, $maxTime);
        try {
            // Update sort_order of position changed item
            $projectKanban->update(['sort_order' => date('Y-m-d H:i:s', $kanbanListCurrentSortOrder)]);
            return response()->json([
                'status_succeed' => true,
            ]);
        } catch (\Exception $err) {
            return response()->json([
                'status_failed' => $err,
            ]);
        }
    }

    /**
     * Delete a kanban list.
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteKanbanList(Request $request)
    {
        $userId = Auth::id();

        // Check kanban list exists
        $kanbanListId = $request->kanbanListId;
        $kanbanList = ProjectKanban::where('id', $kanbanListId)->first();
        if (!$kanbanList) {
            return response()->json([
                'status' => 404,
                'msg' => trans('message.kanban_list_not_exist'),
            ]);
        }
        // Check project permission
        $projectManager = new ProjectManager;
        $project = $projectManager->checkProjectPermission($kanbanList->project_id, $userId);
        if(!$project)
            return response()->json([
                'status' => 404,
                'msg' => trans('message.project_not_exist'),
            ]);

        $kanbanList->delete();
        return response()->json([
            'status' => 200,
            'msg' => trans('message.delete_kanban_list_success'),
        ]);
    }
}

