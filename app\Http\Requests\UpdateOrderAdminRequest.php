<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
class UpdateOrderAdminRequest extends FormRequest
{
    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        if (!isset($this->order)) {
            $this->merge([
                'order' => [null]
            ]);
        }
    }
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order.*' => 'required',
        ];
    }
    /**
     * Notice wrong format of rules
     *
     * @return array<string, mixed>
     */
    public function messages()
    {
        return [
            'required' =>  $this->type == 'textarea' ? trans('message.Please_enter_the_dish') : trans('message.Please_choose_a_dish'),
        ];
    }
    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            if ($validator->errors()->isNotEmpty()) {
                $validator->errors()->add('modal', $this->modal);
            }
        });
    }
}
