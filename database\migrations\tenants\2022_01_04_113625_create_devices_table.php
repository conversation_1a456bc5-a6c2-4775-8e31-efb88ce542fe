<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateDevicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('devices', function (Blueprint $table) {
            $table->increments('id');
            $table->string('device_id',200)->nullable();
            $table->string('mac_address',200)->nullable();
            $table->string('name',200)->nullable();
            $table->string('local_ip_address',200)->nullable();
            $table->string('public_ip_address',200)->nullable();
            $table->unsignedTinyInteger('status')->nullable();
            $table->dateTime('last_connection')->nullable();
            $table->unsignedInteger('website_id')->nullable();
            $table->unsignedInteger('created_by')->nullable();
            $table->unsignedInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('devices');
    }
}
