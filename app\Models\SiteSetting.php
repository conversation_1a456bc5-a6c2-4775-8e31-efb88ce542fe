<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class SiteSetting
 * @property integer $id
 * @property string $name
 * @property string $value
 * @property integer $created_by
 * @property integer $updated_by
 * @property dateTime $created_at
 * @property dateTime $updated_at
 */
class SiteSetting extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'site_settings';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    // id of attribute in site_setting table
    const MORNING_START_ID = 1;
    const MORNING_END_ID = 2;
    const AFTERNOON_START_ID = 3;
    const AFTERNOON_END_ID = 4;
    const DAY_OFF_ID = 5;
    const MIN_TIME_PROJECT_ID = 6;
    const MEETING_ID = 7;
    const DISPLAY_TASK_PROJECT_ID = 8;
    const GANTT_TASK_COLOR_MODE = 9;
    const DISPLAY_COLUMN_TASK = 10;
    const CREATE_EVALUATION_WEEK_ID = 11;
    const COMPANY_LIST_ID = 12;
    const SALARY_PROCESS_ID = 13;
    const STATUS_PERSONNEL_ID = 14;
    const ADMIN_EMAIL_ID = 15;
    const VEHICLE_TYPE_ID =16;
    const IP_TIMEKEEPING = 17;
    const USED_MODULES = 18;

    const SUNDAY = 1;
    const MONDAY = 2;
    const TUESDAY = 3;
    const WEDNESDAY = 4;
    const THURSDAY = 5;
    const FRIDAY = 6;
    const SATURDAY = 7;

    const MEETING_PRIVATE = 0;
    const MEETING_PUBLIC = 1;

    const HIDE_TASK_PROJECT_ID = 0;
    const SHOW_TASK_PROJECT_ID = 1;
    const GANTT_TASK_COLOR_BY_PROGRESS = 0;
    const GANTT_TASK_COLOR_BY_LEVEL = 1;
    const NOT_CREATE_EVALUATION_WEEK = 0;
    const CREATE_EVALUATION_WEEK = 1;

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
        self::saving (function ($data) {
            $data->updated_by = auth()->id();
        });
    }


    /**
     * Get morning start time.
     *
     * @return string
     */
    public static function morningStart(){
        $result = self::find(self::MORNING_START_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = MORNING_START;
        }
        return($result);
    }

    /**
     * Get morning end time.
     *
     * @return string
     */
    public static function morningEnd(){
        $result = self::find(self::MORNING_END_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = MORNING_END;
        }
        return($result);
    }

    /**
     * Get afternoon start time.
     *
     * @return string
     */
    public static function afternoonStart(){
        $result = self::find(self::AFTERNOON_START_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = AFTERNOON_START;
        }
        return($result);
    }

    /**
     * Get afternoon end time.
     *
     * @return string
     */
    public static function afternoonEnd(){
        $result = self::find(self::AFTERNOON_END_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = AFTERNOON_END;
        }
        return($result);
    }

    /**
     * Check working in Saturday
     *
     * @return bool
     */
    public static function workingSATURDAY(){
        $result = self::find(self::DAY_OFF_ID);
        if ($result){
            $dayoff =  json_decode(self::find(self::DAY_OFF_ID)->value, true);
            if (!is_array($dayoff)){
                $dayoff = [];
            }
            $result = !in_array(self::SATURDAY, $dayoff);
        } else {
            $result = WORKING_SATURDAY;
        }

        return $result;
    }

    /**
     * Check working in Sunday
     *
     * @return bool
     */
    public static function workingSUNDAY(){
        $result = self::find(self::DAY_OFF_ID);
        if ($result){
            $dayoff =  json_decode(self::find(self::DAY_OFF_ID)->value, true);
            if (!is_array($dayoff)){
                $dayoff = [];
            }
            $result = !in_array(self::SUNDAY, $dayoff);
        } else {
            $result = WORKING_SUNDAY;
        }

        return $result;
    }

    /**
     * Check min time project
     *
     * @return bool
     */
    public static function minTimeProject(){
        $result = self::find(self::MIN_TIME_PROJECT_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = 0;
        }
        return($result);
    }

	/**
     * Get setting meet.
     *
     * @return string
     */
    public static function settingMeet(){
        $result = self::find(self::MEETING_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = self::MEETING_PRIVATE;
        }
        return($result);
    }

    /**
     * Get task project id display mode
     *
     * @return string
     */
    public static function displayTaskProjectId(){
        $result = $displayTaskProjectId = self::find(self::DISPLAY_TASK_PROJECT_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = self::SHOW_TASK_PROJECT_ID;
        }
        return($result);
    }

    /**
     * Get gantt task color mode
     *
     * @return string
     */
    public static function ganttTaskColorMode(){
        $result = $displayTaskProjectId = self::find(self::GANTT_TASK_COLOR_MODE);
        if ($result){
            $result = $result->value;
        } else {
            $result = self::GANTT_TASK_COLOR_BY_PROGRESS;
        }
        return($result);
    }

    /**
     * Check auto create evaluation week
     *
     * @return bool
     */
    public static function createEvaluationWeek(){
        $result = self::find(self::CREATE_EVALUATION_WEEK_ID);
        if ($result){
            $result = $result->value;
        } else {
            $result = 0;
        }
        return($result);
    }

    /**
     * Check fee parking
     *
     * @return mixed
     */
    public static function getPriceVehicleType($type)
    {
        $vehicle = SiteSetting::find(self::VEHICLE_TYPE_ID);
        $parking_fee = json_decode($vehicle->value, true);
        $parking_fee = !empty($type) ? (int)($parking_fee[$type]['cost']) : (int)$parking_fee['motorbike']['cost'];
        return $parking_fee;
    }
}
