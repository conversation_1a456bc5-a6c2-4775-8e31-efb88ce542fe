<?php
namespace App\Imports;

use App\Models\Asset;
use App\Models\Department;
use App\Models\PropertyManagementAgency;
use App\Models\PropertyStatus;
use App\Models\PropertyType;
use App\Rules\NoEmojiRule;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class FirstSheetImport implements ToCollection
{
    public $data;
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function collection(Collection $rows)
    {
        $this->data = $rows;
        $chunkSize = 1000;
        $time = time();
        Log::info('[FirstSheetImport][collection] start import at: ' . $time);
        try {
            DB::beginTransaction();
            $rows->shift();
            $rows->chunk($chunkSize)->each(function ($chunk) {
                $data = $chunk->map(function($asset){
                    if (isset($asset['manufacturing_date'])) {
                        $asset['manufacturing_date'] =  Carbon::createFromFormat('d/m/Y', $asset['manufacturing_date'])->format('Y-m-d');
                    }
                    if (isset($asset['purchase_date'])) {
                        $asset['purchase_date'] = Carbon::createFromFormat('d/m/Y', $asset['purchase_date'])->format('Y-m-d');
                    }
                    if (isset($asset['usage_date'])) {
                        $asset['usage_date'] = Carbon::createFromFormat('d/m/Y', $asset['usage_date'])->format('Y-m-d');
                    }
                    return $asset;
                });
                Asset::insert($data->toArray());
            });
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            Log::error("[AssetsImport][collection] import asset error: " . $exception->getMessage());
        }
        Log::info('[FirstSheetImport][collection] end import at: ' . (time() - $time));
    }

    public function rules(): array
    {
        $today = Carbon::today()->format("d/m/Y");
        return [
            'name' => ['required', 'max:200', new NoEmojiRule()],
            'name_in_contract' => ['nullable', 'max:200', new NoEmojiRule()],
            'name_short' => ['nullable', 'max:200', new NoEmojiRule()],
            "management_unit_id" => ['nullable', 'exists:property_management_agency,id'],
            "type_id" => ['nullable', 'exists:property_types,id'],
            "department_id" => ['nullable', 'exists:departments,id'],
            "user_use_asset" => ['nullable', 'exists:users,id'],
            "asset_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "handover_record_code" => ['nullable', 'max:50', new NoEmojiRule()],
            "seri_number" => ['nullable', 'max:50', new NoEmojiRule()],
            "supplier" => ['nullable', 'max:1000', new NoEmojiRule()],
            "country_of_manufacture" => ['nullable', 'max:1000', new NoEmojiRule()],
            "manufacturer" => ['nullable', 'max:200', new NoEmojiRule()],
            "manufacturing_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            'asset_category' => ['nullable', 'max:200', new NoEmojiRule()],
            "purchase_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            "usage_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            "condition_id" => ['nullable', 'exists:property_status,id'],
            "source_of_origin" => ['nullable', 'max:1000', new NoEmojiRule()],
            "premises" => ['nullable', 'max:200', new NoEmojiRule()],
            "asset_location" => ['nullable', 'max:1000', new NoEmojiRule()],
            "asset_description" => ['nullable', new NoEmojiRule()],
            "note" => ['nullable', new NoEmojiRule()],
            'original_price' => ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,2})?$/'],
            'residual_value'=> ['nullable', 'numeric', 'regex:/^\d+(\.\d{1,2})?$/'],
        ];
    }

    public function chunkSize(): int
    {
        return 500;
    }

    public function headingRow(): int
    {
        return 2;
    }

    /**
     * @return array
     */
    public function customValidationAttributes()
    {
        return [
            'name' => trans('language.asset_name'),
            'name_in_contract' => trans('language.name_in_contract'),
            'name_short' => trans('language.name_short'),
            'management_unit_id' => trans('language.management_unit_id'),
            'type_id' => trans('language.type_asset'),
            'department_id' => trans('language.department_use_asset'),
            'asset_code' => trans('language.asset_code'),
            'handover_record_code' => trans('language.handover_record_code'),
            'seri_number' => trans('language.seri_number'),
            'supplier' => trans('language.supplier'),
            'country_of_manufacture' => trans('language.country_of_manufacture'),
            'manufacturer' => trans('language.manufacturer'),
            'manufacturing_date' => trans('language.manufacturing_date'),
            'asset_category' => trans('language.asset_category'),
            'purchase_date' => trans('language.purchase_date'),
            'usage_date' => trans('language.usage_date'),
            'condition_id' => trans('language.condition'),
            'source_of_origin' => trans('language.source_of_origin'),
            'premises' => trans('language.premises'),
            'original_price' => trans('language.original_price'),
            'residual_value' => trans('language.residual_value'),
            'location' => trans('language.asset_location'),
            'description' => trans('language.asset_description'),
            'note' => trans('language.asset_note')
        ];
    }

    public function map($row): array
    {
        try {
            $propertyManagerAgencies = $this->propertyManagerAgencies();
            $types = $this->types();
            $departments = $this->departments();
            $conditions = $this->conditions();
            $row = array_map('trim', $row);
            return [
                'name' => $row['ten_tai_san'] ?? null,
                'name_in_contract' => $row['ten_tai_san_theo_hop_dong'] ?? null,
                'name_short' => $row['ten_tai_san_rut_gon'] ?? null,
                'management_unit_id' => isset($row['don_vi_quan_ly_tai_san']) ? array_search($row['don_vi_quan_ly_tai_san'], $propertyManagerAgencies) : null,
                'type_id' => isset($row['loai_tai_san']) ? array_search($row['loai_tai_san'], $types) : null,
                'department_id' => isset($row['don_vi_su_dung']) ? array_search($row['don_vi_su_dung'], $departments) : null,
                'asset_code' => $row['ma_tai_san'] ?? null,
                'handover_record_code' => $row['ma_so_bbbg'] ?? null,
                'seri_number' => $row['so_serial'] ?? null,
                'supplier' => $row['nha_cung_cap'] ?? null,
                'country_of_manufacture' => $row['nuoc_san_xuat'] ?? null,
                'manufacturer' => $row['hang_san_xuat'] ?? null,
                'manufacturing_date' => isset($row['ngay_san_xuat']) ? $this->transformDate($row['ngay_san_xuat']) : null,
                'asset_category' => $row['chung_lai'] ?? null,
                'purchase_date' => isset($row['ngay_mua']) ? $this->transformDate($row['ngay_mua'])  : null,
                'usage_date' => isset($row['ngay_su_dung']) ? $this->transformDate($row['ngay_su_dung']) : null,
                'condition_id' => isset($row['tinh_trang']) ? array_search($row['tinh_trang'] , $conditions) : null,
                'source_of_origin' => $row['nguon_hinh_thanh'] ?? null,
                'premises' => $row['co_so'] ?? null,
                'original_price' => $row['nguyen_gia'] ?? null,
                'residual_value' => $row['gia_tri_con_lai'] ?? null,
                'location' => $row['vi_tri'] ?? null,
                'description' => $row['mo_ta'] ?? null,
                'note' => $row['ghi_chu'] ?? null,
            ];
        } catch (Exception $exception) {
            Log::error("[AssetsImport][map] import asset error: " . $exception->getMessage() . " line: " .$exception->getLine() );
            return [];
        }
    }

    /**
     * Get propertyManagerAgencies.
     *
     * @return array
     */
    private function propertyManagerAgencies()
    {
        return  PropertyManagementAgency::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get types.
     *
     * @return array
     */
    private function types()
    {
        return PropertyType::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get departments.
     *
     * @return array
     */
    private function departments()
    {
        return Department::query()->get()->pluck('name', 'id')->toArray();
    }

    /**
     * Get PropertyStatus.
     *
     * @return array
     */
    private function conditions()
    {
        return PropertyStatus::query()->get()->pluck('name', 'id')->toArray();
    }

    public function startRow(): int
    {
        return 4;
    }
    /**
     * Transform a date value into a Carbon object.
     *
     * @return \Carbon\Carbon|null
     */
    private function transformDate($value, $format = 'd/m/y')
    {
        try {
            return Carbon::instance(\PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($value));
        } catch (Exception $e) {
            return Carbon::createFromFormat($format, $value);
        }
    }
}