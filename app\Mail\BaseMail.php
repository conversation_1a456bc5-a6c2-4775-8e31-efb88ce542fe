<?php

namespace App\Mail;

use Illuminate\Support\Facades\App;
use Illuminate\Mail\Mailable;
use App\Models\Language;

class BaseMail extends Mailable
{

    protected $language;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($language_id = null)
    {
        $this->language_id = $language_id;
        $language = Language::find($language_id);
        $name = isset($language) ? $language->name : null; 
        if ($name) {
            App::setLocale($name);
        }
    }
}
