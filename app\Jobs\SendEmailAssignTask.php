<?php

namespace App\Jobs;

use App\Mail\CreateUserMail;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Mail\AssignTaskMail;

class SendEmailAssignTask extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task;

    /**
     * Create a new job instance.
     *
     * @param $user
     */
    public function __construct($task, $websiteId=null)
    {
        // Inherit from the parent
        parent::__construct($websiteId);

        $this->task = $task;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Inherit from the parent
        parent::handle();

        $task = $this->task;
        $email = new AssignTaskMail($this->task);
        Mail::to($task['userEmail'])->send($email);
    }
}
