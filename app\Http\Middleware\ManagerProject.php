<?php

namespace App\Http\Middleware;

use App\Logics\UserManager;
use App\Models\ProjectRole;
use Closure;
use Illuminate\Support\Facades\Auth;

class ManagerProject
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $userManager = new UserManager();
        if(isset($request->projectId)){
            $isManager = $userManager->hasProjectRole(Auth::user()->id,$request->projectId,ProjectRole::ProjectManager);
            if($isManager){
                return $next($request);
            }
            else{
                return redirect()->route('project.sprint.index',['projectId'=>$request->projectId]);
            }
        }
       
        return redirect()->route('project.index');
    }
}
