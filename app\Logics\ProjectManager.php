<?php

namespace App\Logics;

use App\Models\Project;
use App\Models\ProjectGroupRole;
use App\Models\ProjectMember;
use App\Models\ProjectPin;
use App\Models\ProjectRole;
use App\Models\ProjectTask;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProjectManager
{
    /**
     * Get the projects which user is a member or projects is public
     * @param integer $userId
     * @param null $exceptProjectId
     * @return
     */
    public function getProjectList($userId, $exceptProjectId=null, $projectId=null,$delete_at = true){
        $projects = Project::select('projects.id','projects.name','projects.started_at','projects.ended_at')
            ->checkUserPermission($userId)
            ->orderBy('projects.created_at','DESC')
            ->groupBy('projects.id');
        if($delete_at){
            $projects = $projects->where('projects.deleted_at', null);
        }
        if ($exceptProjectId != null) {
            $projects = $projects->where('projects.id', '!=', $exceptProjectId);
        }
        if ($projectId != null) {
            $projects = $projects->orwhere('projects.id',  $projectId);
        }

        $projects = $projects->get();
        return $projects;
    }

    /**
     * Get a list of members with the same project or who are members of the public project
     * @param integer $userId
     */
    public function getListUserWithProject($userId){
        $user = ProjectMember::select('users.id', 'users.first_name', 'users.last_name', 'users.avatar', 'users.email', 'positions.name as position')
            ->distinct()
            ->leftJoin('users','project_members.user_id','users.id')
            ->leftjoin('positions', 'positions.id','users.position_id')
            ->leftJoin('projects','projects.id','project_members.project_id')
            ->where(function($query) use($userId)  {
                $query->where('projects.public', '=',1 )
                    ->orwhereIn('project_members.project_id',function($subQuery) use ($userId){
                        $subQuery->select('project_id')
                            ->from('project_members')
                            ->where('user_id', $userId);
                    });
            });
        return $user;
    }

    /**
     * Check project_pin
     * @param integer $projectId
     * @param integer $userId
     */
    public function checkProjectPin($projectId,$userId){
        $projectPin = ProjectPin::where('user_id',$userId)->where('project_id',$projectId)->first();
        return isset($projectPin);
    }

    /**
     * Check project permission
     * @param integer $projectId, $userId
     * @param $project
     */
    public function checkProjectPermission($projectId, $userId) {
        $project = Project::select('name','id')
            ->where('id',$projectId)
            ->checkUserPermission($userId)
            ->first();

        return $project;
    }

    /**
     * Get group role in projrct
     * @param integer $projectId
     * @param integer $user
     */
    public function getGroupRoleInProject($userId, $project){
        return ProjectMember::select('project_group_roles.id')
            ->join('project_roles', 'project_roles.id', 'project_members.role_id')
            ->join('project_group_roles', 'project_group_roles.id', 'project_roles.group_role_id')
            ->where('project_members.user_id',$userId)
            ->where('project_members.project_id',$project)
            ->pluck('id')
            ->toArray();
    }

    /* Check project is slow
     */
    function checkIsSlow($project){
        if (empty($project->started_at) || empty($project->ended_at)){
            return false;
        }
        $started_at = date('Y-m-d',strtotime($project->started_at));
        $ended_at = date('Y-m-d',strtotime($project->ended_at));
        if(strtotime($project->ended_at) <= strtotime("-1 days")){
            $processDefault = 100;
        }else{
            $workDays = (new WorkingTimeManager())->getWorkdays($started_at,$ended_at);
            $workedDays = (new WorkingTimeManager())->getWorkdays($started_at,date('Y-m-d',strtotime("-1 days")));
            $processDefault = round($workedDays>$workDays?100:100/$workDays*$workedDays, 0);
        }
        if ($processDefault > round($project->progress)){
            $numberTaskSLow = ProjectTask::where('project_id',$project->id)->where('is_slow',ProjectTask::IS_SLOW)->count();
            if($numberTaskSLow==0){
                return false;
            }
            return true;
        }
        return false;
    }

    public function getNumberIsSlow(){
        $number = Project::checkUserPermission(Auth::id())
            ->where('projects.is_slow',Project::IS_SLOW)
            ->count();
        return $number;
    }

    /**
     * Get leader in project
     * @param integer $projectId
     * @param integer $roleId
     */
    public function getMemberByRole($projectId, $roleId){
        return ProjectMember::select(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name) as full_name'),'users.id')
            ->join('users','users.id', 'project_members.user_id')
            ->where('project_members.project_id',$projectId)
            ->where('project_members.role_id',$roleId)
            ->get()
            ->toArray();
    }

    /**

     * Get group role in project
     * @param integer $projectId
     * @param integer $user
     */
    public function UserHasGroupRolePMLeader($userId, $project){
        $roles = $this->getGroupRoleInProject($userId,$project);
        if (in_array(ProjectGroupRole::PROJECT_MANAGER, $roles) || in_array(ProjectGroupRole::LEADER, $roles))
            return true;
        else
            return false;
    }

    /* Get leader in project
     * @param integer $projectId
     * @param integer $roleId
     */
    public function getMembers($projectId){

        return ProjectMember::select(DB::raw('CONCAT_WS(" ", users.first_name,users.last_name) as full_name'),'users.id')
            ->join('users','users.id', 'project_members.user_id')
            ->join('project_roles','project_roles.id', 'project_members.role_id')
            ->where('project_members.project_id',$projectId)
            ->groupBy("users.id")
            ->havingRaw("min(project_roles.group_role_id) = ".ProjectGroupRole::MEMBER)
            ->get()
            ->toArray();
    }

    /**
     * Check role in project
     */
    public function checkHasProjectManager($roleId)
    {
        $checkHasManager = false;
        if( $roleId == ProjectGroupRole::PROJECT_MANAGER){
            $checkHasManager = true;
        }
        return $checkHasManager;
    }

    public function getProjectManager($projectId)
    {
        return ProjectMember::select(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name) as full_name'),'users.id')
            ->join('users','users.id', 'project_members.user_id')
            ->where('project_members.project_id',$projectId)
            ->where('project_members.role_id',ProjectRole::ProjectManager)
            ->first();
    }

    // update start, end of project when task change
    public function updateProject($projectID){
        $project = Project::select(
                'projects.id',
                'projects.progress',
                'projects.started_at',
                'projects.ended_at'
            )->where('projects.id', '=', $projectID);

        // Update progress, started_at, ended_at for parent project
        $project = $project->updateProject()->first();
        $project->progress = $project->updated_progress;
        $project->started_at = $project->updated_started_at;
        $project->ended_at = $project->updated_ended_at;

        // Update is_slow
        $is_slow = $this->checkIsSlow($project);
        $project->is_slow = $is_slow ? ProjectTask::IS_SLOW : ProjectTask::NOT_SLOW;

        // Update parent project
        $project->save();
    }
}
