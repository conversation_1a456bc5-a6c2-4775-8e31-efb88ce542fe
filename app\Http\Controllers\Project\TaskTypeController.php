<?php

namespace App\Http\Controllers\Project;

use App\Logics\ProjectManager;
use App\Models\TaskType;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class TaskTypeController extends Controller
{
    public function getTaskType(Request $request){
        $userId = Auth::id();
        $hasRolePMLeader = (new ProjectManager())->UserHasGroupRolePMLeader($userId, $request->project);
        if($hasRolePMLeader){
            $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();
        }else{
            $taskTypes = TaskType::select(['id','name'])->where('id','!=',TaskType::PROBLEM)->orderby('sort_order')->get();
        }
        return response()->json([
            'status' => Response::HTTP_OK,
            'data' => $taskTypes,
            'hasRolePMLeader' => $hasRolePMLeader
        ], Response::HTTP_OK);
    }
}
