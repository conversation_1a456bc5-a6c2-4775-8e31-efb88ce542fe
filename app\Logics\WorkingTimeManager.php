<?php


namespace App\Logics;

use App\Models\Attendance;
use App\Models\Request;
use App\Models\SiteSetting;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class WorkingTimeManager
{
    public $morningStart;
    public $morningEnd;
    public $afternoonStart;
    public $afternoonEnd;
    private $startMorning;
    private $startAfternoon;
    private $startNight;
    private $endMorning;
    private $endAfternoon;
    private $endNight;

    public function __construct()
    {
        $this->morningStart = strtotime(SiteSetting::morningStart())+ PADDING_TIME;
        $this->morningEnd = strtotime(SiteSetting::morningEnd());
        $this->afternoonStart = strtotime(SiteSetting::afternoonStart())+ PADDING_TIME;
        $this->afternoonEnd = strtotime(SiteSetting::afternoonEnd());
        $this->endMorning = '12:00:00';
        $this->endAfternoon = '17:30:00';
        $this->endNight = '22:00:00';
        $this->startMorning = '08:00:00';
        $this->startAfternoon = '13:30:00';
        $this->startNight = '18:00:00';
    }
    /**
     * Get work days between 2 date
     */
    public function getWorkdays($date1, $date2){
        if ($date1 == $date2){
            return 1;
        }
        $start = strtotime($date1);
        $end   = strtotime($date2);
        $workdays = 0;
        for ($i = $start; $i <= $end; $i = strtotime("+1 day", $i)) {
            $day = date("w", $i);  // 0=sun, 1=mon, ..., 6=sat
            if(($day != 0 && $day != 6) || ($day == 0 && SiteSetting::workingSUNDAY() == true) || ($day == 6 && SiteSetting::workingSATURDAY() == true)){
                $workdays++;
            }
        }
        return $workdays;
    }
    
    /**
     * Calculate time between t1 and t2
     * @param $t1
     * @param $t2
     * @return float|int
     */
    private static function minusTime($t1, $t2, $bonusPaddingTime = true)
    {
        if ($t2 < $t1)
            return 0;
        $time = $t2 - $t1;
        if ($bonusPaddingTime) {
            $time += PADDING_TIME;
        }
        $time = round($time / 3600.0, 2);

        return $time;
    }
    
    /** Get work time in 1 days */
    public function getWorkTimeInOneDay($startTime,$endTime, $bonusPaddingTime = true)
    {
        if ($startTime == 0 || $endTime == 0) {
            return 0;
        }
        $morningTime = 0;
        if ($this->morningStart < $endTime) {
            if ($endTime < $this->afternoonStart)
                $morningTime = min(
                    self::minusTime($this->morningStart, $this->morningEnd, $bonusPaddingTime),
                    self::minusTime($startTime, $endTime, $bonusPaddingTime),
                    self::minusTime($this->morningStart, $endTime, $bonusPaddingTime),
                    self::minusTime($startTime, $this->morningEnd, $bonusPaddingTime),
                );
            else
                $morningTime = min(
                    self::minusTime($this->morningStart, $this->morningEnd, $bonusPaddingTime), 
                    self::minusTime($startTime, $this->morningEnd, $bonusPaddingTime)
                );
        }
        $afternoonTime = 0;
        if ($startTime < $this->afternoonEnd) {
            if ($startTime < $this->morningEnd)
                $afternoonTime = min(
                    self::minusTime($this->afternoonStart, $endTime, $bonusPaddingTime), 
                    self::minusTime($this->afternoonStart, $this->afternoonEnd, $bonusPaddingTime),
                );
            else
                $afternoonTime = min(
                    self::minusTime($this->afternoonStart, $this->afternoonEnd, $bonusPaddingTime),
                    self::minusTime($this->afternoonStart, $endTime, $bonusPaddingTime),
                    self::minusTime($startTime, $this->afternoonEnd, $bonusPaddingTime),
                    self::minusTime($startTime, $endTime, $bonusPaddingTime)
                );
        }
        
        // Makeup time is up to 30 minutes if the user works full time
        $makeupTime = 0;
        if ($startTime < $this->morningEnd && $endTime > $this->afternoonEnd) {
            $makeupTime = min(0.5, self::minusTime($this->afternoonEnd, $endTime, false));
        }

        return min(FULL_TIME, round($morningTime + $afternoonTime + $makeupTime, 2));
    }

    /**
     * Calculate working time in one day for Collaborator user
     *
     * @param int $year
     * @param int $month
     * @param string $date
     * @param int $userId
     * @param bool $isHoliday
     * @param array $maxCheckedInOutInDay
     * @return float
     */
    public function getCollaboratorWorkTimeInOneDay($year, $month, $date, $userId, $maxCheckedInOutInDay, $requestCollaborator, $isHoliday = false) {
        $duration = 0;
        $attendanceTime = [];
        // If is holiday and has full attendance set duration is full date
        if($maxCheckedInOutInDay && $isHoliday) {
            [$checkedIn, $checkedOut] = $maxCheckedInOutInDay;
            if(!$checkedIn || !$checkedOut) {
                ['duration' => $duration, 'attendences' => $attendanceTime];
            }
            $duration = self::minusTime($checkedIn, $checkedOut, false);
            // count time OT
            $requestCollaborator = $this->handleRequestCollaborator($requestCollaborator, $date) ?? [];
            $timeOT = 0;
            foreach ($requestCollaborator as $item) {
                switch ($item['request_type']) {
                    // Request work OT
                    case Request::COLLABORATOR_WORK:
                        $timeOT += self::minusTime(strtotime($item['collab_starttime_request']), strtotime($item['collab_endtime_request']), false);
                        break;
                    default:
                        break;
                }
            }
            $duration += $timeOT;
        } else {
            // Get attendanceTime in 1 day
            $attendanceTime = Attendance::select(
                DB::raw('TIME(checked_at) as checked_at')
            )
            ->whereMonth('checked_at', $month)
            ->whereYear('checked_at', $year)
            ->where('user_id', $userId)
            ->whereDate('checked_at', $date)
            ->orderBy('checked_at', 'asc')
            ->get()->toArray();

            if (!count($attendanceTime)) {
                ['duration' => $duration, 'attendences' => $attendanceTime];
            }
            
            // define shift rules
            $shiftRules = [
                ['start' => '00:00:00', 'end' => $this->startAfternoon], // shift 1
                ['start' => $this->endMorning, 'end' => $this->startNight], // shift 2
                ['start' => $this->endAfternoon, 'end' => '23:59:59'], // shift 3
            ];

            // handle groups time in 1 day follow shift rules
            $dataShift = $this->groupsShiftTime($attendanceTime, $shiftRules);
            $groups = $dataShift['result'];
            $attendanceTime = $dataShift['time'];

            // caculate duration if in shift have length items > 2 return 0 else caculate duration of shift
            $groups = array_map(function ($group) use ($date, $requestCollaborator, $groups) {
                $isHasNextShift = true; 
                if (!$group) {
                    return ['duration' => 0];
                }
                $index = array_search($group, $groups);

                // Check if the next group is empty or null
                if (isset($groups[$index + 1]) && empty($groups[$index + 1])) {
                    $isHasNextShift = false;
                }
                // Check if user has a request checkout early for that date
                $requestCollaborator = $this->handleRequestCollaborator($requestCollaborator, $date, $index) ?? [];
                $timeCheckin = min($group);
                $timeCheckout = max($group);

                $shiftBoundaries = [
                    0 => ['start' => $this->startMorning, 'end' => $this->endMorning],
                    1 => ['start' => $this->startAfternoon, 'end' => $this->endAfternoon],
                    2 => ['start' => $this->startNight, 'end' => $this->endNight],
                ];
            
                if (isset($shiftBoundaries[$index])) {
                    $timeCheckin = max($timeCheckin, $shiftBoundaries[$index]['start']);
                    $timeCheckout = min($timeCheckout, $shiftBoundaries[$index]['end']);
                }
                // Check groups next shift is not empty and is not last shift apply logic change request if has
                $isNotLastShift = $index != count($groups) - 1 && $isHasNextShift;
                // check has request fulltime and calculate time OT
                $isRequestFull = false;
                $timeOT = 0;
                foreach ($requestCollaborator as $item) {
                    switch ($item['request_type']) {
                        case Request::COLLABORATOR_WORK_FULL:
                            $isRequestFull = true;
                            break;
                        case Request::COLLABORATOR_WORK:
                            $timeOT += self::minusTime(strtotime($item['collab_starttime_request']), strtotime($item['collab_endtime_request']), false);
                            break;
                        default:
                            break;
                    }
                }

                if (count($group) > 2 && !$isRequestFull && $isNotLastShift) {
                    $latestTimeBeforeNoon = null;
                    foreach ($group as $time) {
                        $endTime = ($index == 0) ? $this->endMorning : $this->endAfternoon;
                        if ($time < $endTime && ($latestTimeBeforeNoon === null || $time > $latestTimeBeforeNoon)) {
                            $latestTimeBeforeNoon = $time;
                        }
                    }

                    if ($latestTimeBeforeNoon !== null) {
                        $timeCheckout = $latestTimeBeforeNoon;
                    }
                }

                // caculate duration in shift
                $duration = !$timeCheckin || !$timeCheckout ? 0 : self::minusTime(
                    strtotime($timeCheckin),
                    strtotime($timeCheckout),
                    false
                );
                $duration += $timeOT;
                return [
                    'duration' => $duration,
                ];
            }, $groups);

            // caculate total duration
            $duration = array_sum(array_column($groups, 'duration'));
        }
        return ['duration' => $duration, 'attendences' => $attendanceTime];
    }

    /**
     * Group times into shifts based on given rules
     *
     * @param array $data
     * @param array $rules
     * @return array
     */

    private function groupsShiftTime($data, $rules) {
        $times = array_map(function($record) {
            return $record['checked_at'];
        }, $data);

        $indexLastShift = null;

        // Filter data before groups
        $timeFilter = $this->hanleFilterTimeAttendances($times);
        $results = array_fill(0, count($rules), []);
        $currentShiftIndex = 0;

        foreach ($timeFilter as $time) {
            while ($currentShiftIndex < count($rules)) {
                $currentRule = $rules[$currentShiftIndex];
                $start = $currentRule['start'];
                $end = $currentRule['end'];
                $nextShiftStart = $rules[$currentShiftIndex + 1]['start'] ?? '23:59:59';

                // if time is in current shift
                if ($time > $start && $time < $end) {
                    $results[$currentShiftIndex][] = $time;

                    // if time past next shift
                    if ($time >= $nextShiftStart) {
                        $currentShiftIndex++;
                    }
                    break;
                } else {
                    // if time past next shift
                    $currentShiftIndex++;
                }
            }
        }

        // handle time between morning shifts and afternoon shifts
        if ($results[0] && count($results[0]) > 2) {
            $lastMorningItem = end($results[0]);

            if ($lastMorningItem > $this->endMorning && count($results[1]) == 1) {
                array_pop($results[0]);
                array_unshift($results[1], $lastMorningItem);
            }
        }

        // handle time between afternoon shifts and night shifts
        if ($results[1] && count($results[1]) > 2) {
            $lastAfternoonItem = end($results[1]);

            if ($lastAfternoonItem > $this->endAfternoon && count($results[2]) == 1) {
                array_pop($results[1]);
                array_unshift($results[2], $lastAfternoonItem);
            }
        }

        // handle firt time in shift compare with start next shift
        for ($i = 0; $i < count($results) - 1; $i++) {
            $nextShiftStart = $rules[$i + 1]['start'];
    
            if (!empty($results[$i]) && $results[$i][0] >= $nextShiftStart) {
                array_unshift($results[$i + 1], array_shift($results[$i]));
            }
        }

        $response = [
            'time' => $times,
            'result' => $results,
        ];
        return $response;
    }

    /**
     * Filters out attendance times that occur within 15 seconds of each other.
     *
     * @param array $attendances An array of attendance timestamps.
     * @return array An array of filtered timestamps where consecutive times are more than 15 seconds apart.
     */
    private function hanleFilterTimeAttendances ($attendances) {
        $filteredTimes = [];
        $lastTime = null;

        foreach ($attendances as $time) {
            $currentTime = Carbon::parse($time);

            if ($lastTime && $lastTime->diffInSeconds($currentTime) <= 15) {
                continue;
            }

            $filteredTimes[] = $time;
            $lastTime = $currentTime;
        }

        return $filteredTimes;
    }

    /**
     * handle logic request collaborator on a specific day and shift
     * @param array $requestCollaborator array of request collaborator
     * @param string $date date to check
     * @param int $shift shift to check
     * @return bool true if user has a request collaborator, false otherwise
     */
    private function handleRequestCollaborator($requestCollaborator, $date, $shift = null) {
        $allContentItems = [];
        if (!count($requestCollaborator)) {
            return $allContentItems;
        }

        foreach ($requestCollaborator as $item) {
            if (isset($item['content']) && is_array($item['content'])){
                // Groups contents
                $allContentItems = array_merge($allContentItems, $item['content']);
            }
        }
        $filteredItems = array_filter($allContentItems, function ($contentItem) use ($date, $shift) {
            if ($shift === null) {
                return isset($contentItem['collaborator_date']) && $contentItem['collaborator_date'] == $date;
            } else {
                return isset($contentItem['collaborator_date']) && $contentItem['collaborator_date'] == $date
                    && isset($contentItem['shift']) && $contentItem['shift'] == $shift + 1;
            }
        });
        return $filteredItems;
    }

    /**
     * Get work time between 2 time
     */
    public function getWorkTime($start, $end){
        $time = [];
        $startDay = date('Y-m-d',strtotime($start));
        $endDay = date('Y-m-d',strtotime($end));
        $startTime = strtotime(date('H:i:s',strtotime($start)));
        $endTime = strtotime(date('H:i:s',strtotime($end)));
        if ($startDay == $endDay){
            $time[$startDay] = self::getWorkTimeInOneDay($startTime,$endTime);
        }else{
            for ($i = strtotime($startDay); $i <= strtotime($endDay); $i += 86400){
                $day = date("w", $i);  // 0=sun, 1=mon, ..., 6=sat
                
                //if day is saturday or sunday then time=0
                if(($day == 0 && SiteSetting::workingSUNDAY() == false) || ($day == 6 && SiteSetting::workingSATURDAY() == false) || $i > strtotime(date('Y-m-d').' 23:59:59')){
                    $time[date('Y-m-d',$i)] = 0;
                }else{
                    if ($i == strtotime($startDay)){
                        $time = array_merge($time,self::getWorkTime($start,$startDay.' '.SiteSetting::workingSUNDAY()));
                    }elseif ($i == strtotime($endDay)){
                        $time = array_merge($time,self::getWorkTime($endDay.' '.SiteSetting::morningStart(),$end));
                    }else{
                        $time[date('Y-m-d',$i)] = FULL_TIME;
                    }
                }
            }
        }
        return $time;
    }
    /**
     * Get time between 2 time
     */
    public function diffTime($startDate, $endDate)
    {
        $times = $startDate->diffInMinutes($endDate);
        if ($times < 60) {
            $diffTime =  trans("language.time.minutes",[
                'minutes' => $times,
            ]);
        }
        if ($times >= 60 && $times < 1440) {
            $hours =  floor($times/60);
            $diffTime =  trans("language.time.hours",[
                'hours' => $hours,
            ]);
        }
        if ($times >= 1440 && $times <= 43200) {
            $days =  floor($times/1440);
            $diffTime =  trans("language.time.days",[
                'days' => $days,
            ]);
        }
        if ($times >= 43200 && $times <= 518400) {
            $months =  floor($times/43200);
            $diffTime =  trans("language.time.months",[
                'months' => $months,
            ]);
        }
        if ($times > 518400) {
            $years =  floor($times/518400);
            $diffTime =  trans("language.time.years",[
                'years' => $years,
            ]);
        }
        return $diffTime;
    }
}