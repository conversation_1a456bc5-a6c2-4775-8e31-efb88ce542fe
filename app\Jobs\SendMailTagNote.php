<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use App\Mail\NoteTagMail;

class SendMailTagNote extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $userTag;
    protected $task;

    /**
     * Create a new job instance.
     *
     * @param $user
     */
    public function __construct($userTag, $websiteId=null, $task)
    {
        parent::__construct($websiteId);

        $this->userTag = $userTag;
        $this->task = $task;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        parent::handle();

        $userTag = $this->userTag;
        $email = new NoteTagMail($this->task);
        Mail::to($userTag['email'])->send($email);
    }
}
