<?php

namespace App\Notifications;

use App\Models\Language;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\App;
use Illuminate\Notifications\Messages\MailMessage;

class ResetPassword extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * The password reset token.
     *
     * @var string
     */
    public $token;

    /**
     * The domain of the current website
     */
    public $domain;

    /**
     * The callback that should be used to build the mail message.
     *
     * @var \Closure|null
     */
    public static $toMailCallback;

    /**
     * Create a notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct($token, $domain)
    {
        $this->token = $token;
        $this->domain = $domain;
        $this->queue = QUEUE_RESET_PASSWORD_NOTIFICATION;
    }

    /**
     * Get the notification's channels.
     *
     * @param  mixed  $notifiable
     * @return array|string
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        if (static::$toMailCallback) {
            return call_user_func(static::$toMailCallback, $notifiable, $this->token);
        }

        $user = User::where('email', $notifiable->email)->first();
        $language = Language::find($user->language_id);
        if ($language) {
            App::setLocale($language->name);
        }
        if (isset($user)) {
            return (new MailMessage)
                ->from(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME'))
                ->subject(trans('passwords.reset_password_notification'))
                ->greeting(trans('language.hello',[
                    'first_name' => $user->first_name ,
                    'last_name' => $user->last_name
                ]))
                ->line(trans('passwords.reset_password_notification_reason'))
                ->action(trans('language.reset_password'), url($this->domain . route('password.reset', $this->token, false)))
                ->line(trans('passwords.reset_password_no_request'));
        }

        return null;
    }

    /**
     * Set a callback that should be used when building the notification mail message.
     *
     * @param  \Closure  $callback
     * @return void
     */
    public static function toMailUsing($callback)
    {
        static::$toMailCallback = $callback;
    }
}
