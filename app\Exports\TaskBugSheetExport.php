<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;

class TaskBugSheetExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    function __construct($result)
    {
        // $this->tasks = $tasks;
        $this->result = $result;
    }
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return collect($this->result);
    }

     /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.number_order'),
            trans('language.project'),
            trans('language.function_screen'),
            trans('language.date_bug'),
            trans('language.user_bug'),
            trans('language.content_bug'),
            trans('language.detail_description'),
            trans('language.reasons'),
            trans('language.user_function'),
            trans('language.user_handle'),
            trans('language.bug_classify'),
            trans('language.bug_range'),
            trans('language.bug_reason'),
            trans('language.bug_severity'),
            trans('language.bug_tag'),
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 5,
            'B' => 20,
            'C' => 25,
            'D' => 20,
            'E' => 25,
            'F' => 20,
            'G' => 50,
            'H' => 25,
            'I' => 25, 
            'J' => 25,
            'K' => 15,
            'L' => 25,
            'M' => 35,
            'N' => 20,
            'O' => 20,
        ];
    }

    public function styles($sheet)
    {
        $sheet->setTitle('List Bug');
        $alignLeft = [
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT,
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ]  
        ];

        $sheet->getStyle("B:B")->applyFromArray($alignLeft);
        $sheet->getStyle("H:H")->applyFromArray($alignLeft);
        $sheet->getStyle("F:F")->applyFromArray($alignLeft);
        $sheet->getStyle("E:E")->applyFromArray($alignLeft);
        $sheet->getStyle("C:C")->applyFromArray($alignLeft);

        $alignCenter = [
            'alignment' => [
                'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ],   
        ];

        $sheet->getStyle("A:A")->applyFromArray($alignCenter);
        $sheet->getStyle("M:M")->applyFromArray($alignCenter);
        $sheet->getStyle("D:D")->applyFromArray($alignCenter);
        $sheet->getStyle("D:D")->applyFromArray($alignCenter);
        $sheet->getStyle("H:H")->applyFromArray($alignCenter);
        $sheet->getStyle("I:I")->applyFromArray($alignCenter);
        $sheet->getStyle("J:J")->applyFromArray($alignCenter);
        $sheet->getStyle("K:K")->applyFromArray($alignCenter);
        $sheet->getStyle("L:L")->applyFromArray($alignCenter);
        $sheet->getStyle("N:N")->applyFromArray($alignCenter);
        $sheet->getStyle("O:O")->applyFromArray($alignCenter);
        
        $sheet->getStyle('A1:O1')->applyFromArray(array(
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));

        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];

        $num = collect($this->result)->count() + 1;

        $sheet->getStyle('A1:O'.$num)->applyFromArray($styleArray);
        $sheet->getStyle('H')->getAlignment()->setWrapText(true);

        
    }
}
