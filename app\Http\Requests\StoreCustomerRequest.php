<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rule = [
            'name' => ['required', 'max:255'],
        ];
        return $rule;
    }

    public function attributes()
    {
        return [
            'name' => trans('language.customer_name'),
        ];
    }


    /**
     * @return array|string[]
     */
    public function messages(): array
    {
        return [];
    }
}
