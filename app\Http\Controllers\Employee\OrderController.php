<?php

namespace App\Http\Controllers\Employee;

use App\Exports\OrderByMonthExport;
use App\Exports\OrderExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\ExportOrderByMonthRequest;
use App\Http\Requests\MenuRequest;
use App\Http\Requests\OrderLunchRequest;
use App\Http\Requests\StoreOrderRequest;
use App\Logics\DateFormatManager;
use App\Models\FoodMenu;
use App\Models\FoodOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;
use App\Logics\FoodOrderManager;
use App\Models\TaskAttachment;
use App\Logics\AttachmentManager;
use  App\Http\Requests\UpdateOrderAdminRequest;
use App\Http\Requests\CheckDateRequest;
use App\Http\Requests\OrderMenuRequest;
use function PHPUnit\Framework\stringContains;

class OrderController extends Controller
{
    const PAGINATE = 10;
     
    /**
     * order lunch
     */
    public function orderLunch(CheckDateRequest $request) {
        $date = isset($request->choose_date)?Carbon::createFromFormat('d/m/Y',$request->choose_date)->format('Y-m-d'):date('Y-m-d');

        $foodMenu = FoodMenu::whereDate('date', $date)->first();
        $menu = null; 
        $updatedMenuAt = null;
        if ($foodMenu) {
            $menu = json_decode($foodMenu->menu, true);
            $updatedMenuAt = $foodMenu->updated_at;
        }
        $orders = (new FoodOrderManager())->listOrder($request, null ,$date);
        $userOrder = (new FoodOrderManager())->getUserOrder($date);
        return view('employee.order_lunch', [ 
            'menu' => $menu, 
            'orders' => $orders,
            'userOrder' => $userOrder,
            'updatedMenuAt' => $updatedMenuAt,
            'date' => $date,
        ]);
    }

    /**
     * update menu
     * @param MenuRequest $request
     */
    public function updateMenu(OrderLunchRequest $request) {
        DB::beginTransaction();
        try{
            $data = [];
            $data['time'] = $request->time;
            $data['option'] = $request->option?str_replace("\r\n", "\n", $request->option):null;
            if ($data['option']) {
                $data['option'] = explode("\n", $data['option']);  
                foreach ($data['option'] as $key => $value) {
                    $data['option'][$key] = trim($value);
                }
            }

            $date = isset($request->choose_date)?Carbon::createFromFormat('d/m/Y',$request->choose_date)->format('Y-m-d'):date('Y-m-d');
            $foodMenu = FoodMenu::whereDate('date', $date)->first();
            if ($foodMenu) {
                $foodMenu->menu = $request->menu??"";
                $foodMenu->save(); 
            } else {
                $foodMenu = FoodMenu::create(['menu' => $request->menu??"", 'date' => $date]);  
            }
            
            $destinationPath = MENU_DIR.'/';
            (new AttachmentManager())->saveAttachments($foodMenu, TaskAttachment::TYPE_MENU, $request->documents, $request->descriptionDocument, $destinationPath, 'menu');
            $data['menu'] = $foodMenu->menu;
          
            $foodMenu->menu = json_encode($data);
            $foodMenu->save();
            DB::commit();
            return back()->with([
                'status_succeed' => trans('message.update_menu_success')
            ]);
        } catch(\Exception $e){
            DB::rollBack();
            Log::error($e);
        }
    }

    /**
     * save menu employee
     */
    public function order(OrderMenuRequest $request)
    {
        $user = Auth::user();
        $foodMenu = FoodMenu::whereDate('date', $request->currentChooseDate)->first();
        if(!$foodMenu){
            return [
                'status' => Response::HTTP_RESET_CONTENT,
                'message' => trans('message.not_menu')
            ];
        }
        
        $updatedMenuAt = $foodMenu->updated_at?date('Y-m-d H:i:s', strtotime($foodMenu->updated_at)):'';
        if ($request->updateMenuAt != $updatedMenuAt) {
            return [
                'status' => Response::HTTP_RESET_CONTENT,
                'message' => trans('message.notify_updated_menu')
            ];
        }

        $checkOrderTime = FoodOrderManager::checkOrderTime($request->currentChooseDate);
        if (!$checkOrderTime) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => trans('message.error_expired_order')
            ];
        }

        DB::beginTransaction();
        try{
            $foodOrder = FoodOrder::select('food_orders.id')->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
                ->where('user_id', $user->id)
                ->whereDate('food_menus.date', $request->currentChooseDate)
                ->first();
            if (!$foodOrder) {
                $foodOrder = new FoodOrder();
            }
            $note = isset($request->note) ? GROUP_CONCAT_SEPARATOR . $request->note . CONCAT_SEPARATOR : '';
            $foodOrder->user_id = $user->id;
            $foodOrder->order = $request->order .' '.$note;
            $foodOrder->menu_id = $foodMenu->id;
            $foodOrder->save();
            DB::commit();
            $orders = $this->listOrder($request);
            return [
                'status' => Response::HTTP_OK,
                'html' => $orders['html'],
                'message' => trans('message.order_success')
            ];
        } catch(\Exception $e){
            DB::rollBack();
            Log::error($e);
        }
    }
    /**
     * create menu by admin
     */
    public function createOrderAdmin(StoreOrderRequest $request)
    {
        $foodMenu = FoodMenu::whereDate('date', $request->currentChooseDate)->first();
        if(!$foodMenu){
            redirect()->route('employee.orderLunch')->with([
                'status_failed' => trans('message.not_menu'),
            ]);
        }
        DB::beginTransaction();
        try{
            $members = $request->member;
            foreach($members as $member){
                $foodOrder = FoodOrder::where('food_orders.menu_id', $foodMenu->id)
                ->where('user_id', $member)
                ->first();
                if (!$foodOrder) {
                    $foodOrder = new FoodOrder();
                }
                $foodOrder->user_id = $member;
                if ($request->order) {
                    $orders = join('◞ ',$request->order);
                } else {
                    $orders = "";
                }
                $note = isset($request->note) ? GROUP_CONCAT_SEPARATOR . $request->note . CONCAT_SEPARATOR : '';
                $foodOrder->order = $orders . ' '.$note;
                $foodOrder->menu_id = $foodMenu->id;
                $foodOrder->save();
            }
            DB::commit();
            return redirect()->back()->with([
                'status_succeed' => trans('message.create_order_successed'),
            ]);
        } catch(\Exception $e){
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->back()
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
        
    }
    /**
     * save menu employee
     */
    public function updateOrderAdmin(UpdateOrderAdminRequest $request)
    {
        DB::beginTransaction();
        try{
            $foodOrder = FoodOrder::select('food_orders.id')
                ->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
                ->where('food_orders.user_id', $request->user_id)
                ->whereDate('food_menus.date', $request->currentChooseDate)
                ->first();
            if ($request->order) {
                $orders = join('◞ ',$request->order);
            } else {
                $orders = "";
            }
            $note = isset($request->note) ? GROUP_CONCAT_SEPARATOR . $request->note . CONCAT_SEPARATOR : '';
            $foodOrder->order = $orders. ' '.$note;
            $foodOrder->save();
            DB::commit();
            return redirect()->back()->with([
                'status_succeed' => trans('message.update_order_successed'),
            ]);
        } catch(\Exception $e){
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->route('employee.orderLunch')
                ->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * delete menu employee
     */
    public function deleteOrderAdmin(Request $request)
    {
        DB::beginTransaction();
        try{
            $foodOrder = FoodOrder::select('food_orders.id')->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
                ->where('food_orders.user_id', $request->user_id)
                ->whereDate('food_menus.date', $request->currentChooseDate)
                ->first();
            $foodOrder->delete();
            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.delete_order_successd') ,
                ],
            ];
        } catch(\Exception $e){
            DB::rollBack();
            Log::error($e);
        }
    }
    /**\employee\order-lunch
     * Delete order
     * @return array
     */
    public function deleteOrder(Request $request) {
        $checkOrderTime = FoodOrderManager::checkOrderTime($request->currentChooseDate);
        if (!$checkOrderTime) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => trans('message.error_expired_order')
            ];
        }
        $user = Auth::user();
        $foodOrder = FoodOrder::select('food_orders.id')->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
            ->where('food_orders.user_id', $user->id)
            ->whereDate('food_menus.date', $request->currentChooseDate)
            ->first();
        if ($foodOrder) {
            $foodOrder->forceDelete();
            $orders =  $this->listOrder($request);

            return [
                'status' => Response::HTTP_OK,
                'html' => $orders['html'],
                'message' => trans('message.delete_order_successd')
            ];
        } else {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'message' => trans('message.error_not_order')
            ];
        }
    }
    public function statisticalOrder(Request $request)
    {        
        $orders = FoodOrder::select(
            'food_orders.id',
            'food_orders.menu_id',
            'food_orders.user_id',
            'food_orders.order',
            'food_orders.updated_at',
            'food_orders.created_at',
            'food_menus.id',
            'food_menus.date',
        ) 
        ->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
        ->whereDate('food_menus.date', $request->currentChooseDate)
        ->orderBy('food_orders.created_at', 'desc' )
        ->get();
        $count = count($orders);
        $listOrder = [];
        foreach($orders as $item){
            array_push($listOrder,$item->order);
        }
        $foodMenu = FoodMenu::whereDate('food_menus.date', $request->currentChooseDate)->first();
        $menu = null; 
        if ($foodMenu) {
            $menu = json_decode($foodMenu->menu, true);
        }
        $orderEmployee = [];
        $arrayOrder = [];
        $arrayStringReplaceK = ['k ','ko ', 'kg ', 'k0 ', 'K ', 'KO ', 'K0 ', 'KG ', 'Ko ', 'Kg '];
        if(isset($menu['option']) && (count($menu['option']))){
            foreach($listOrder as $value){
                $order = (new FoodOrderManager())->getFoodAndNote($value);
                $food = explode("◞ ",$order['food']);
                $arrayIntersect = array_intersect($food,$menu['option']);
                
                if(count($arrayIntersect) > 1){
                    array_push($orderEmployee, $value);
                }
                elseif(count($arrayIntersect) == 1) {
                    if(isset($arrayOrder[$arrayIntersect[0]]['total'])){
                        $arrayOrder[$arrayIntersect[0]]['total'] += 1;
                    }else{
                        $arrayOrder[$arrayIntersect[0]]['total'] = 1;
                    }
                    $note = $order['note'];
                    if($note){
                        $note = preg_replace('/\s+/', ' ', $note);
                        $note = str_replace($arrayStringReplaceK, 'không ', $note);
                        if(!isset($arrayOrder[$arrayIntersect[0]]['notes'])){
                            $arrayOrder[$arrayIntersect[0]]['notes'] = [];
                        }
                        array_push($arrayOrder[$arrayIntersect[0]]['notes'],mb_strtolower($note,'UTF-8'));
                    }
                }
                else{
                    $count -= 1;
                }
            }
            foreach ($arrayOrder as $key=>$order) {
                if(isset($order['notes'])){
                    $arrayOrder[$key]['notes'] = array_count_values($order['notes']);
                }
            }
        } else {
            $orderEmployee = $listOrder;
        }
        ksort($arrayOrder);
        // return [ $count,$arrayOrder,$orderEmployee ];
        $htmlStatistical = view('employee.partial.statistical', [
            'menu' => json_decode($foodMenu->menu, true),
            'statistical' => $arrayOrder,
            'count' => $count,
            'orderEmployee' => $orderEmployee
        ])
        ->render();
        return [
            'status' => Response::HTTP_OK,
            'htmlStatistical' => $htmlStatistical,
            'message' => trans('message.order_success')
        ];
    }
    /**
     * export order
     */
    public function exportOrders(Request $request)
    {
        $date = isset($request->choose_date) ? Carbon::createFromFormat('d/m/Y',$request->choose_date)->format('Y-m-d') : Carbon::now()->format('Y-m-d');
        $orders = (new FoodOrderManager())->listOrder($request, true, $date);
        $result = [];
        $index = 1;
        foreach($orders as $item){
            $item->order = str_replace(GROUP_CONCAT_SEPARATOR, "(", $item->order);
            $item->order = str_replace(CONCAT_SEPARATOR, ")", $item->order);
            $data = [];
            $data['number_order'] = $index;
            $data['name'] =  $item->name;
            $data['order'] =  $item->order;
            $index += 1;
            $result[] = $data;
        }
        $pathPrefix = env('USE_TENANT', false) ? app(\Hyn\Tenancy\Website\Directory::class)->path() : '';
        $path = TEMP_DIR . '/'. Str::random(25).'.xlsx';
        if($request->choose_date == date('d/m/Y')){
            $nameFile = trans('language.order_lunch.order_list').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
        }else{
            $nameFile = trans('language.order_lunch.order_list').'_'.$date.'.xlsx';
        }
        Excel::store(new OrderExport($result), $pathPrefix.$path);
        return Storage::disk(FILESYSTEM)->download($path, $nameFile);
    }
        /**
     * list order
     * @param $menu
     * @return array
     */
    public function listOrder(Request $request) {
        $date = isset($request->choose_date)?Carbon::createFromFormat('d/m/Y',$request->choose_date)->format('Y-m-d'):(isset($request->currentChooseDate)?$request->currentChooseDate:date('Y-m-d'));
        $foodMenu = FoodMenu::whereDate('date', $date)->first();
        if ($foodMenu) {
            $menu = json_decode($foodMenu->menu, true);
        } else {
            $menu = null;   
        }
        $orders = (new FoodOrderManager())->listOrder($request, null, $date);
        $param['orders'] = $orders;
        $param['menu'] = $menu;
        $html = view('employee.partial.list_order', $param)->render();
        
        return [
            'status' => Response::HTTP_OK,
            'html' => $html,
        ];
    }
    
    /**
     * export order by month
     * @param ExportOrderByMonthRequest $request
     */
    public function exportOrdersByMonth(ExportOrderByMonthRequest $request){
        $endDayMonth = Carbon::createFromFormat('m/Y',$request->choose_month)->endOfMonth()->format('Y-m-d');
        $firstDayMonth = Carbon::createFromFormat('m/Y',$request->choose_month)->firstOfMonth()->format('Y-m-d');
        $data = FoodOrder::select([
            'food_menus.date as date',
            DB::raw('COUNT(food_orders.id) AS count'),
        ])
            ->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
            ->whereDate('date', '>=', $firstDayMonth)
            ->whereDate('date', '<=', $endDayMonth)
            ->groupBy('date')
            ->orderBy('date', 'ASC')
            ->get()
            ->toArray();
        $dateFormatManager = new DateFormatManager();
        foreach ($data as $key => $val){
            $data[$key]['date'] = $dateFormatManager->dateFormatLanguage($val['date'],'d/m/Y');
        }

        $nameFile = trans('language.order_lunch.file_statistics_by_month').'_'.Carbon::createFromFormat('m/Y', $request->choose_month)->format('m-Y').'.xlsx';
        return Excel::download(new OrderByMonthExport($data), $nameFile);
    }

    /**
     * Check member order exist
     * @param Request $request
     * return Response $json
     */
    public function ajaxCheckMember(Request $request){
        $foodOrder = FoodMenu::where('date',$request->currentChooseDate)
            ->first()
            ->load('foodOrder');
        $CheckExit = $foodOrder->foodOrder->whereIn('user_id',$request->member);
        return response()->json($CheckExit->isEmpty()); // trả về false thì jquery validate sẽ thông báo lỗi
    }
}
