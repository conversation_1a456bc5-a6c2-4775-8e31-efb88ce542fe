<?php

namespace App\Helpers;

use App\Models\TaskAttachment;
use App\Traits\StorageTrait;
use ZipArchive;
use RecursiveIteratorIterator;
use RecursiveDirectoryIterator;
use Illuminate\Support\Facades\Storage;


class ZipHelper {

    use StorageTrait;

    /**
     * Zip file or folder
     */
    public function zip($source, $outputFilePath) {
        // Check the source is exists
        if (!Storage::disk(FILESYSTEM)->exists($source)) {
            return false;
        }

        // Get real path for our folder
        $rootPath = Storage::disk(FILESYSTEM)->path($source);
        $outputFilePath = Storage::disk(FILESYSTEM)->path($outputFilePath);

        // Initialize archive object
        $zip = new ZipArchive();
        $zip->open($outputFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        /** @var SplFileInfo[] $files */
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($rootPath),
            RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $name => $file)
        {
            // Skip directories (they would be added automatically)
            if (!$file->isDir())
            {
                // Get real and relative path for current file
                $filePath = $file->getRealPath();
                $relativePath = substr($filePath, strlen($rootPath) + 1);

                // Add current file to archive
                $zip->addFile($filePath, $relativePath);
            }
        }

        // Zip archive will be created only after closing object
        return $zip->close();
    }
}
