<?php

namespace App\Console\Commands;

use App\Models\Wiki;
use Illuminate\Support\Facades\DB;

class UpdateSlugWikiPage extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_slug_wiki_page {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update slug';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        Wiki::withTrashed()
            ->whereNotNull('title')
            ->whereNull('slug')
            ->update(["slug" => DB::raw("REPLACE(title, ' ', '_')")]);
    }
}
