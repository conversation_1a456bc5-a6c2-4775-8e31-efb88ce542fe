<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class CompanyProfile
 * @property integer $id
 * @property integer $website_id
 * @property string $name
 * @property string $headoffice
 * @property string $phonenumber
 * @property integer $created_by
 * @property integer $updated_by
 */
class CompanyProfile extends Model
{
    use SoftDeletes;

    protected $connection= 'system';

    protected $table = 'companyprofiles';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * @return BelongsTo
     */
    public function website()
    {
        return $this->belongsTo(config('tenancy.models.website'));
    }
}
