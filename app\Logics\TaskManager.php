<?php

namespace App\Logics;

use App\Models\BugTag;
use App\Models\Project;
use App\Models\ProjectGroupRole;
use App\Models\ProjectMember;
use App\Models\ProjectRole;
use App\Models\ProjectTask;
use App\Models\TaskAction;
use App\Models\TaskPriority;
use App\Models\TaskStatus;
use App\Models\TaskType;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Helpers\StringHelper;
use App\Helpers\ZipHelper;
use App\Models\BugClassify;
use App\Models\BugRange;
use App\Models\BugReason;
use App\Models\BugSeverity;
use App\Models\ProjectSprint;
use App\Models\TaskAttachment;
use App\Traits\StorageTrait;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Route;
use App\Models\SiteSetting;
use Illuminate\Support\Collection;
use Illuminate\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;

class TaskManager
{
    use StorageTrait;
    const SUB_DAYS = 20;
    const ADD_MONTHS = 3;
    const TASK_LEVEL_1 = 1;
    const TASK_LEVEL_2 = 2;
    const TASK_LEVEL_3 = 3;
    const TASK_LEVEL_4 = 4;
    const TASK_LEVEL_5 = 5;
    /**
     * Get task list
     *
     */
    public function getTaskList($params, $pageSize, $orderBy = []){
        if(isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y',$params['from']) > Carbon::createFromFormat('d/m/Y',$params['to'])){
            return [[],0];
        }
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $columns = [
            'project_tasks.id',
            'project_tasks.parent_task',
            'project_tasks.description',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.progress',
            'projects.name as project_name',
            'projects.id as project_id',
            'task_types.name as type_name',
            'task_types.id as type_id',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
            'project_tasks.estimated_time',
            'project_tasks.name',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'users.avatar',
            'project_tasks.updated_at',
            'projects.started_at as project_start',
            'projects.ended_at as project_end',
            'projects.progress as project_progress',
            'projects.description as project_description',
            'project_tasks.is_slow',
            'project_tasks.created_at',
            'project_tasks.warning',
            'project_tasks.level',
            DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'),
            DB::raw('CONCAT_WS(" " , created.first_name, created.last_name) as created_name'),
            DB::raw('CONCAT_WS(" " , key_member.first_name, key_member.last_name) as key_member_name'),
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child'),
            'bug_ranges.name as bug_range_name',
            'bug_reasons.name as bug_reason_name',
            'bug_classifies.name as bug_classify_name',
            'bug_severities.name as bug_severity_name',
            'bug_tags.name as bug_tag_name',
            'project_tasks.function_screen',
            'project_tasks.reason',
            'project_tasks.key_member',
            'project_tasks.created_by',
        ];
        $tasks = ProjectTask::select($columns)
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->leftJoin('users as created','project_tasks.created_by','created.id')
            ->leftJoin('users as key_member','project_tasks.key_member','key_member.id')
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->leftJoin('task_status','project_tasks.status','task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
            ->leftjoin('bug_ranges','project_tasks.bug_range_id', 'bug_ranges.id')
            ->leftjoin('bug_reasons', 'project_tasks.bug_reason_id', 'bug_reasons.id')
            ->leftjoin('bug_classifies', 'project_tasks.bug_classify_id', 'bug_classifies.id')
            ->leftjoin('bug_severities', 'project_tasks.bug_severity_id', 'bug_severities.id')
            ->leftjoin('bug_tags', 'project_tasks.bug_tag_id', 'bug_tags.id');

        // Order By
        if (isset($orderBy)){
            foreach ($orderBy as $key => $value){
                $tasks = $tasks->orderBy($key,$value);
            }
        }
        // Filter
        $count = count($params);
        if((isset($params['project']) && $count==1) || $count==0 ){
            $params['status'] = ['0'];
        }
        if(isset($params['taskId'])){
            $items = array_filter(array_map("trim",explode(",", $params['taskId'])));
            $items = collect($items)->filter(function ($value) {
                return ctype_digit($value); //trường hợp input là: '15.0' hoặc là '15@152' thì vẫn là false,chỉ true khi input là dạng text số nguyên                     
            });
                $tasks = $tasks->whereIn('project_tasks.id',$items);
        }
        if(isset($params['parentTask'])){
            $idParent = array_filter(array_map("trim",explode(",", $params['parentTask']))); 
            $idParent = collect($idParent)->filter(function ($value) {
                return ctype_digit($value);  //trường hợp input là: '15.0' hoặc là '15@152' thì vẫn là false,chỉ true khi input là dạng text số nguyên
            });
                $tasks = $tasks->WhereIn('project_tasks.parent_task',$idParent); 

        }

        if(isset($params['project'])){
            $tasks = $tasks->where('project_tasks.project_id',$params['project']);
        } else{
            $tasks = $tasks->where('projects.deleted_at', null);
        }
        if(isset($params['type'])){
            $tasks = $tasks->whereIn('project_tasks.type',$params['type']);
        }
        if(isset($params['status'])){
            $tasks->where(function ($query) use ($params){
                if(in_array('0',$params['status'])){
                    $query = $query->orWhere('project_tasks.status', '!=', TaskStatus::TASK_STATUS_CLOSE);
                }
                if(in_array('warning',$params['status'])){
                    $query = $query->orWhere('project_tasks.warning', ProjectTask::WARNING);
                }
                if(in_array(TaskStatus::TASK_STATUS_SLOW,$params['status'])){
                    $query = $query->orWhere('project_tasks.is_slow',ProjectTask::IS_SLOW);
                }
                $listIdStatus = array_diff($params['status'], ['0',TaskStatus::TASK_STATUS_SLOW]);
                if(count($listIdStatus)>0){
                    $query = $query->orWhereRaw('project_tasks.status in ('.implode(',',$listIdStatus).')');
                }
            });

        }
        if(isset($params['priority'])){
            $tasks = $tasks->whereIn('project_tasks.priority',$params['priority']);
        }
        if(isset($params['member'])){
            $tasks = $tasks->whereIn('project_tasks.user_id',$params['member']);
        }
        if(isset($params['bug_tag'])){
            $tasks = $tasks->whereIn('project_tasks.bug_tag_id',$params['bug_tag']);
        }
        if(isset($params['bug_range'])){
            $tasks = $tasks->whereIn('project_tasks.bug_range_id',$params['bug_range']);
        }
        if(isset($params['sprint_id'])){
            $tasks = $tasks->whereIn('project_tasks.sprint_id',$params['sprint_id']);
        }
        if(Route::is('projects.tasks') || Route::is('project.tasks') || Route::is('projects.tasks.list') || Route::is('project.tasks.list') || Route::is('tasks.export') || Route::is('projects.tasks.export')){
            if(isset($params['from'])){
                $tasks = $tasks->where(function($query)  use ($params){
                    $query->where('project_tasks.created_at','>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'));
                });
            }
            if(isset($params['to'])){
                $tasks = $tasks->where(function($query)  use ($params){
                    $query->where('project_tasks.created_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                });
            }
        }
        else{
            if(isset($params['from'])){
                $tasks = $tasks->where(function($query)  use ($params){
                    $query->where('project_tasks.started_at','>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'))
                        ->orwhere('project_tasks.ended_at', '>=', Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00'));
                });
            }
            if(isset($params['to'])){
                $tasks = $tasks->where(function($query)  use ($params){
                    $query->where('project_tasks.started_at','<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'))
                        ->orwhere('project_tasks.ended_at', '<=', Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59'));
                });
            }
        }
        
        if (isset($params['taskTitle'])){
            $search = $stringHelper->formatStringWhereLike($params['taskTitle']);
            $tasks = $tasks->where('project_tasks.name', 'LIKE', '%'.$search.'%');
        }
        if (isset($params['select'])){
            $select = explode(',',$params['select']);
            $tasks = $tasks->whereIn('project_tasks.id',$select);
        }
        $arrayTaskId = (clone $tasks)->get()->pluck('id')->toArray();
        $totalEstimatedTime = (clone $tasks)
            ->where(function($query)  use ($arrayTaskId){
                $query->wherenotIn('project_tasks.parent_task',  $arrayTaskId)
                    ->orwhere('project_tasks.parent_task', null);
            })
            ->sum('project_tasks.estimated_time');
        // Pagination
        if ($pageSize == null){
            $tasks = $tasks->get();
        }else{
            // $tasks = $tasks->sortable()->paginate($pageSize);
            $tasks = $tasks->paginate($pageSize);
        }
        return [$tasks, $totalEstimatedTime];
    }

    /**
     * This function only using for gantt screen
     * Get task list in Gantt
     * @param $params
     * @param $pageSize
     * @param $orderBy
     */
    public function getTaskListInGantt($params, $pageSize, $orderBy = []){
        if(isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y',$params['from']) > Carbon::createFromFormat('d/m/Y',$params['to'])){
            return [[],0];
        }
        $userId = Auth::id();
        $stringHelper = new StringHelper();
        $columns = [
            'project_tasks.id',
            'project_tasks.parent_task',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.progress',
            'projects.name as project_name',
            'projects.id as project_id',
            'task_types.name as type_name',
            'task_types.id as type_id',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
            'project_tasks.estimated_time',
            'project_tasks.name',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'projects.started_at as project_start',
            'projects.ended_at as project_end',
            'projects.progress as project_progress',
            'projects.description as project_description',
            'project_tasks.is_slow',
            'project_tasks.warning',
            'project_tasks.level',
            DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'),
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child'),
            'project_tasks.key_member',
            'project_tasks.created_by',
        ];
        $tasks = ProjectTask::select($columns)
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->leftJoin('task_status','project_tasks.status','task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
            ->whereNull('project_tasks.parent_task')
            ->with(['_nLevelTask' => function($q1) use ($columns, $userId){
                $q1->select(
                    $columns
                )
                ->checkUserPermission($userId)
                ->leftJoin('users','project_tasks.user_id','users.id')
                ->leftJoin('task_types','project_tasks.type','task_types.id')
                ->leftJoin('task_status','project_tasks.status','task_status.id')
                ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id');
            }]);

        // Order By
        if (isset($orderBy)){
            foreach ($orderBy as $key => $value){
                $tasks = $tasks->orderBy($key,$value);
            }
        }

        if(isset($params['project'])){
            $tasks = $tasks->where('project_tasks.project_id',$params['project']);
        } else{
            $tasks = $tasks->where('projects.deleted_at', null);
        }

        // Pagination
        if ($pageSize == null){
            $tasks = $tasks->get();
        }else{
            $tasks = $tasks->get()->toArray();
            $tasks = $this->flatten($tasks);
            $data = new Collection();
            foreach($tasks as $item){
                $data->push((object)$item);
            }

            // Filter
            $count = count($params);
            if((isset($params['project']) && $count==1) || $count==0 ){
                $params['status'] = ['0'];
            }

            if(isset($params['taskId'])){
                $idTask = ctype_digit($params['taskId']) ? $params['taskId'] : '';
                $data = $data->where('id',$idTask);
            }
            if(isset($params['parentTask'])){
                $items = array_filter(array_map("trim",explode(",", $params['parentTask'])));   
                $items = collect($items)->filter(function ($value) {
                    return ctype_digit($value); //trường hợp input là '15.0' hoặc là '15@152' thì vẫn là false, chỉ true khi input là dạng text số nguyên
                });
                $data = $data->whereIn('parent_task',$items);
            }  

            if(isset($params['type'])){
                $data = $data->whereIn('type_id',$params['type']);
            }

            if(isset($params['status'])){
                $conditions = [];
                $data = $data->filter(function($item) use($params, $conditions){
                    if(in_array('0',$params['status'])){
                        $conditions[] = $item->status_id != TaskStatus::TASK_STATUS_CLOSE;
                    }
                    if(in_array('warning',$params['status'])){
                        $conditions[] = $item->warning == ProjectTask::WARNING;
                    }
                    if(in_array(TaskStatus::TASK_STATUS_SLOW,$params['status'])){
                        $conditions[] = $item->is_slow == ProjectTask::IS_SLOW;
                    }
                    $listIdStatus = array_diff($params['status'], ['0',TaskStatus::TASK_STATUS_SLOW]);
                    if(count($listIdStatus)>0){
                        $conditions[] = in_array($item->status_id, $listIdStatus);
                    }
                    return in_array(true, $conditions);
                });
            }

            if(isset($params['priority'])){
                $data = $data->whereIn('priority_id',$params['priority']);
            }
            if(isset($params['member'])){
                $data = $data->whereIn('user_id',$params['member']);
            }
            if(isset($params['bug_tag'])){
                $data = $data->whereIn('project_tasks.bug_tag_id',$params['bug_tag']);
            }
            if(isset($params['bug_range'])){
                $data = $data->whereIn('project_tasks.bug_range_id',$params['bug_range']);
            }

            if(isset($params['from'])){
                $conditions = [];
                $data = $data->filter(function($item) use($params, $conditions){
                    $conditions[] = $item->started_at >= Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00');
                    $conditions[] = $item->ended_at >= Carbon::createFromFormat('d/m/Y',$params['from'])->format('Y-m-d 00:00:00');
                    return in_array(true, $conditions);
                });
            }
            if(isset($params['to'])){
                $conditions = [];
                $data = $data->filter(function($item) use($params, $conditions){
                    $conditions[] = $item->started_at <= Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59');
                    $conditions[] = $item->ended_at <= Carbon::createFromFormat('d/m/Y',$params['to'])->format('Y-m-d 23:59:59');
                    return in_array(true, $conditions);
                });
            }

            if (isset($params['taskTitle'])){
                $search = $stringHelper->formatStringWhereLike($params['taskTitle']);
                $data = $data->filter(function($item) use ($search) {
                    return stripos($item->name,$search) !== false;
                });
            }
            $path = '';
            if(Route::is('projects.gantt')){
                $path = route('projects.gantt');
            }else if(Route::is('project.gantt')){
                $path = route('project.gantt', ['id' => $params['project']]);
            }
            $data = $this->configPaginate($data, $pageSize, isset($params['page'])?$params['page']:null, ['path'=> $path] );
            $tasks = $data;
        }

        return [$tasks];
    }

    /**
     * config paginate page
     * @param $items
     * @param int $perPage
     * @param $page
     * @param array $options
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function configPaginate($items, $perPage = 50, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }

    /**
     * Convert multidimensional array to one dimensional array
     * @param $array
     * @return array
     */
    function flatten($array) {
        $result = [];
        foreach ($array as $item) {
            if (is_array($item)) {
                $result[] = array_filter($item, function($array) {
                    return ! is_array($array);
                });
                $result = array_merge($result, $this->flatten($item));
            }
        }
        return array_filter($result);
    }

    /**
     * Get list tasks child
     * @param integer $taskId
     */
    public function getTaskChildList($taskId){
        $userId = Auth::id();
        $columns = [
            'project_tasks.id',
            'project_tasks.name',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.estimated_time',
            'project_tasks.project_id',
            'task_types.name as type_name',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'users.avatar',
            'project_tasks.progress',
            'project_tasks.priority',
            'task_priorities.name as priority_name',
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
        ];
        $tasks = ProjectTask::select($columns)
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->leftJoin('task_status','project_tasks.status','task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
            ->where('project_tasks.parent_task',$taskId)
            ->orderBy('project_tasks.id','DESC')
            ->get();
        return $tasks;
    }

    /**
     * Get backgroundStatus
     * @param integer $status
     */
    public function getBackgroundStatus($status,$isCalendar = false){
        $taskStatus = TaskStatus::find($status);
        if(empty($taskStatus)){
            return '';
        }
        return $taskStatus->color;
    }

    /**
     * Get backgroundPriority
     * @param integer $priority
     */
    public function getBackgroundPriority($priority,$isCalendar = false){
        $taskPriority = TaskPriority::find($priority);
        if(empty($taskPriority)){
            return '';
        }
        return $taskPriority->color;
    }
     /**
     * Get getTextColor
     * @param integer $priority
     */
    public function getTextColor($color){
        if(empty($color)){
            return "color:black;";
        }
        $hex = str_replace('#', '', $color);
        $c_r = hexdec(substr($hex, 0, 2));
        $c_g = hexdec(substr($hex, 2, 2));
        $c_b = hexdec(substr($hex, 4, 2));
        $rgb =  (($c_r * 299) + ($c_g * 587) + ($c_b * 114)) / 1000;
        if($rgb > 130){
            return "color:black;";
        } else {
            return "color:white;";
        }
    }
    /**
     * Get number child of task
     * @param integer $taskId
     */
    public function getNumberChild($taskId){
        $numberChild = ProjectTask::
        where('project_tasks.parent_task',$taskId)
            ->count();
        return $numberChild;
    }

    /**
     * Get html filter
     * @param Request $request
     * @param array $fields
     */
    public function getFilterHtml(Request $request,$fields = []){
        $filterHtml = "";
        if(count($request->all()) == 0){
            $request->merge([
                'status' => ['0'],
            ]);
        }
        $dateFormatManager = new DateFormatManager();
        foreach ($fields as $field){
            $value = '';
            if ($request->has($field) && $request->$field != null){
                $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
                $tagSpanClose = '</span>';
                switch ($field){
                    case 'taskId':
                        $idItems = array_filter(array_map("trim",explode(",", StringHelper::escapeHtml($request->taskId))));
                        if(!empty($idItems)){
                        foreach($idItems as $idItem){
                            $value .= $tagSpanOpen ."#". $idItem . $tagSpanClose;
                            }
                        }
                        break;
                    case'parentTask':
                        $idItems = array_filter(array_map("trim",explode(",", StringHelper::escapeHtml($request->parentTask))));
                        if(!empty($idItems)){
                        foreach($idItems as $idItem){
                            $value .= $tagSpanOpen ."#". $idItem . $tagSpanClose;
                            }
                        }
                        break;
                    case 'project':
                        $project = \App\Models\Project::find($request->project);
                        $value = $tagSpanOpen . (isset($project)?StringHelper::escapeHtml($project->name):'') . $tagSpanClose;
                        break;
                    case 'type':
                        $types = \App\Models\TaskType::whereIn('id',$request->type)->get();
                        foreach ($types as $type){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($type->name) . $tagSpanClose;
                        }
                        break;
                    case 'status':
                        if(in_array("0",$request->status)){
                            $value .= $tagSpanOpen . trans('language.opening') . $tagSpanClose;
                        }
                        if(in_array("warning",$request->status)){
                            $value .= $tagSpanOpen . trans('language.warning') . $tagSpanClose;
                        }
                        if (in_array(TaskStatus::TASK_STATUS_SLOW,$request->status)){
                            $value .= $tagSpanOpen . trans("language.slow_progress") . $tagSpanClose;
                        }
                        $listIdStatus = array_diff($request->status, ['0', TaskStatus::TASK_STATUS_SLOW]);
                        if (count($listIdStatus) > 0) {
                            $status = \App\Models\TaskStatus::whereIn('id', $listIdStatus)->get();
                            foreach ($status as $statusItem) {
                                $value .= $tagSpanOpen . StringHelper::escapeHtml($statusItem->name) . $tagSpanClose;
                            }
                        }

                        break;
                    case 'priority':
                        $prioritys = \App\Models\TaskPriority::whereIn('id',$request->priority)->get();
                        foreach ($prioritys as $priority){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($priority->name) . $tagSpanClose;
                        }
                        break;
                    case 'bug_tag':
                        $bugTags = \App\Models\BugTag::whereIn('id',$request->bug_tag)->get();
                        foreach ($bugTags as $item){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($item->name) . $tagSpanClose;
                        }
                        break;
                    case 'bug_range':
                        $bugRanges = \App\Models\BugRange::whereIn('id',$request->bug_range)->get();
                        foreach ($bugRanges as $item){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($item->name) . $tagSpanClose;
                        }
                        break;
                    case 'member':
                        $members = \App\User::whereIn('id',$request->member)->select('first_name', 'last_name')->get();
                        foreach ($members as $member){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($member->first_name . ' ' . $member->last_name) . $tagSpanClose;
                        }
                        break;
                    case 'sprint_id':
                        $sprint_id = DB::table('project_sprints')->whereIn('id',$request->sprint_id)->get();
                        foreach ($sprint_id as $sprint){
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($sprint->name) . $tagSpanClose;
                        }
                        break;    
                    case 'start_date':
                    case 'end_date':
                    case 'start_at':
                    case 'end_at':
                    case 'from':
                    case 'to':
                    case 'time':
                        $typeFormat = 'd/m/Y';
                        if(Route::currentRouteName() == 'projects.calendar' || Route::currentRouteName() == 'project.calendar'){
                            $typeFormat = 'm/Y';
                        }
                        if((Route::currentRouteName() == 'projects.effort' || Route::currentRouteName() == 'project.effort') && $request->has('type') && $request->type == 'month' ){
                            $typeFormat = 'm/Y';
                        }
                        $value .=  $tagSpanOpen . $dateFormatManager->dateFormatInput($request->$field,$typeFormat) . $tagSpanClose;
                        break;
                    default:
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $filterHtml.= $value;
            }
        }
        return $filterHtml;
    }

    /**
     * Get list filedChange
     * @param ProjectTask $task
     */
    public function getFieldsChange(ProjectTask $task, $isCreated = false){
        $result = [];
        if($isCreated){
            $attributes = ['project_id','type','name','description','status','priority','user_id','parent_task','started_at','ended_at','estimated_time','progress','key_member','function_screen', 'bug_classify_id','bug_reason_id','bug_severity_id','bug_range_id','reason', 'bug_tag_id', 'sprint_id'];
            foreach($attributes as $attribute){
                if(!empty($task->$attribute)){
                    $fieldsChange[$attribute] = $task->$attribute;
                }
            }
        } else {
            $fieldsChange = $task->getDirty();
        }
        $fieldsNotSave = ['updated_at', 'updated_by','warning','level'];
        foreach ($fieldsChange as $field => $newValue){
            if (!in_array($field,$fieldsNotSave)){
                switch ($field){
                    case 'project_id':
                        $newProject = empty($newValue)?null:Project::find($newValue);
                        $oldProject = $isCreated?null:(empty($task->getOriginal($field))?null:Project::find($task->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newProject)?$newProject->name:'',
                            'old_value' => isset($oldProject)?$oldProject->name:'',
                        ];
                        break;
                    case 'type':
                        $result[$field] = [
                            'new_value' => TaskType::find($newValue)->name,
                            'old_value' => $isCreated?'':(TaskType::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'status':
                        $result[$field] = [
                            'new_value' => TaskStatus::find($newValue)->name,
                            'old_value' => $isCreated?'':(TaskStatus::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'priority':
                        $result[$field] = [
                            'new_value' => TaskPriority::find($newValue)->name,
                            'old_value' => $isCreated?'':(TaskPriority::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'user_id':
                        $newUser = empty($newValue)?null:User::find($newValue);
                        $oldUser = $isCreated?null:(empty($task->getOriginal($field))?null:User::find($task->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newUser)?$newUser->first_name.' '.$newUser->last_name:'',
                            'old_value' => isset($oldUser)?$oldUser->first_name.' '.$oldUser->last_name:''
                        ];
                        break;
                    case 'parent_task':
                        $newParent = empty($newValue)?null:ProjectTask::find($newValue);
                        $oldParent = $isCreated?null:(empty($task->getOriginal($field))?null:ProjectTask::find($task->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newParent)?$newParent->name:'',
                            'old_value' => isset($oldParent)?$oldParent->name:'',
                        ];
                        break;
                    case 'started_at':
                    case 'ended_at':
                        $result[$field] = [
                            'new_value' => isset($newValue)?date('d/m/Y',strtotime($newValue)):'',
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?'':date('d/m/Y',strtotime($task->getOriginal($field)))),
                        ];
                        break;
                    case 'key_member':
                        $newUser = empty($newValue)?null:User::find($newValue);
                        $oldUser = $isCreated?null:(empty($task->getOriginal($field))?null:User::find($task->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newUser)?$newUser->first_name.' '.$newUser->last_name:'',
                            'old_value' => isset($oldUser)?$oldUser->first_name.' '.$oldUser->last_name:''
                        ];
                        break;
                    case 'function_screen':  
                        $result[$field] = [
                            'new_value' => $task->$field,
                            'old_value' => $isCreated?'':($task->getOriginal($field))
                        ];
                        break;
                    case 'bug_classify_id':
                        $result[$field] = [
                            'new_value' => BugClassify::find($newValue)->name,
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:BugClassify::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;    
                    case 'bug_reason_id':
                        $result[$field] = [
                            'new_value' => BugReason::find($newValue)->name ?? '',
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:BugReason::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'bug_range_id':
                        $result[$field] = [
                            'new_value' => BugRange::find($newValue)->name,
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:BugRange::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'bug_severity_id':
                        $result[$field] = [
                            'new_value' => BugSeverity::find($newValue)->name,
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:BugSeverity::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'bug_tag_id':
                        $result[$field] = [
                            'new_value' => BugTag::find($newValue)->name ?? '',
                            'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:BugTag::withTrashed()->find($task->getOriginal($field))->name)
                        ];
                        break;
                    case 'reason':
                        $newValue = $task->getAttribute($field);
                        $oldValue = $isCreated?'':($task->getOriginal($field));
                        $logNewValue = [];
                        $logOldValue = [];
                        foreach($newValue[0] as $fieldItem => $valueItem){
                            if(!empty($oldValue)){
                                if($valueItem != $oldValue[0][$fieldItem]){
                                    $logNewValue[$fieldItem] = $valueItem;
                                    $logOldValue[$fieldItem] = $oldValue[0][$fieldItem];
                                }
                            }else{
                                if(!empty($valueItem)){
                                    $logNewValue[$fieldItem] = $valueItem;
                                }
                                $logOldValue = "";
                            }
                        }
                        $result[$field] = [
                            'new_value' => $logNewValue,
                            'old_value' => $logOldValue
                        ];
                        break;
                        case 'sprint_id':
                            $result[$field] = [
                                'new_value' => ProjectSprint::find($newValue)->name ?? '',
                                'old_value' => $isCreated?'':(empty($task->getOriginal($field))?null:ProjectSprint::withTrashed()->find($task->getOriginal($field))->name)
                            ];
                        break;
                    default:
                        $result[$field] = [
                            'new_value' => $newValue,
                            'old_value' => $isCreated?'':($task->getOriginal($field))
                        ];
                        break;
                }
            }
        }
        return $result;
    }

    /**
     * Add task to jsonGantt
     * @param array $json
     * @param object $task
     * @param string $pParent
     */
    public function addTaskToDataGantt($json,$task,$pParent,$hasChild,$mode){
        $level = $json[$pParent]['level'] + 1;
        $key = 'T'.$task->id;
        $color = $this->getColorGantt($task, $mode, $level);
        $startedAt = isset($task->started_at)?date('Y-m-d',strtotime($task->started_at)):'';
        $endedAt = isset($task->ended_at)?date('Y-m-d',strtotime("+1 day", strtotime($task->ended_at))):'';
        if ($startedAt <= Carbon::now()->startOfWeek()->subDays(self::SUB_DAYS)->format('Y-m-d') && empty(request()->input('from'))) {
            $startedAt = Carbon::now()->startOfWeek()->subDays(self::SUB_DAYS)->format('Y-m-d');
        }
        if (!empty(request()->input('from')) && $startedAt <= Carbon::createFromFormat('d/m/Y',request()->input('from'))->format('Y-m-d')) {
            $startedAt = Carbon::createFromFormat('d/m/Y',request()->input('from'))->format('Y-m-d');
        }
        if ($endedAt >= Carbon::now()->addMonths(self::ADD_MONTHS)->endOfWeek()->subDays(1)->format('Y-m-d') && empty(request()->input('to'))) {
            $endedAt = Carbon::now()->addMonths(self::ADD_MONTHS)->endOfWeek()->subDays(1)->format('Y-m-d');
        }
        if (!empty(request()->input('to')) && $endedAt >= Carbon::createFromFormat('d/m/Y',request()->input('to'))->format('Y-m-d')) {
            $endedAt = Carbon::createFromFormat('d/m/Y',request()->input('to'))->format('Y-m-d');
        }
        $displayId = SiteSetting::displayTaskProjectId();
        if ($displayId) {
            $pName = '<a class="text-'.$color.' level'.$level.'" href="'.route('task.show',['id'=>$task->id]).'">'.$task->type_name.' #'.$task->id.': '.'<span class="text-black">'.$task->name.'</span></a>';
        } else {
            $pName = '<a class="text-'.$color.' level'.$level.'" href="'.route('task.show',['id'=>$task->id]).'">'.'<span class="text-black">'.$task->name.'</span></a>';
        }
        $value = [
            'pID' => 'T'.$task->id,
            'pName' => $pName,
            'pLink' => route('task.show',['id'=>$task->id]),
            'pParent' => $pParent,
            'group' => $hasChild ? 1 : 0,
            'pOpen' => $hasChild > 0 ? 1 : 0,
            'pStart' => $startedAt,
            'pEnd' =>  $endedAt,
            'pComp' => isset($task->progress)?round($task->progress):0,
            "pClass" => (empty($task->progress)?"progress-zero ":"")."bg-".$color,
            'ptooltiptemplate' => view('partials.gantt-tooltip-template',['data'=>$task,'isTask'=>true])->render(),
            'level' => $level
        ];
        $json = $this->insert_into_array($json,$pParent,$key,$value);
        return $json;
    }

    /**
     * Convert data task to Data Gantt
     * @param $tasks
     */
    public function convertDataTaskToDataGantt($tasks){
        $result = [];
        $sort = [];
        $tasksAfterSort = [];
        $tasksId = [];
        $tasksParentId = [];
        $mode = SiteSetting::ganttTaskColorMode();

        //Add project to sort
        foreach($tasks as $task){
            if (!array_key_exists ( 'P'.$task->project_id , $sort )){
                $project = Project::select('projects.id','projects.name','projects.started_at','projects.ended_at','projects.created_by','projects.progress')
                    ->checkUserPermission(Auth::id())
                    ->with(['userCreateProject' => function($q) {
                        $q->select('id', 'first_name', 'last_name');
                    }])
                    -> where('projects.id',$task->project_id)
                    ->first();
                $startedAt = isset($project->started_at)?date('Y-m-d',strtotime($project->started_at)):'';
                $endedAt = isset($project->ended_at)?date('Y-m-d',strtotime($project->ended_at)):'';
                if ($startedAt <= Carbon::now()->startOfWeek()->subDays(self::SUB_DAYS)->format('Y-m-d') && empty(request()->input('from'))) {
                    $startedAt = Carbon::now()->startOfWeek()->subDays(self::SUB_DAYS)->format('Y-m-d');
                    $pPLanStart = Carbon::now()->startOfWeek()->subDays(14)->format('Y-m-d');
                }
                if ($endedAt >= Carbon::now()->addMonths(self::ADD_MONTHS)->endOfWeek()->subDays(1)->format('Y-m-d') && empty(request()->input('to'))) {
                    $endedAt = Carbon::now()->addMonths(self::ADD_MONTHS)->endOfWeek()->subDays(1)->format('Y-m-d');
                    $pPlanEnd = Carbon::now()->addMonths(self::ADD_MONTHS)->endOfWeek()->subDays(1)->format('Y-m-d');
                }
                if (!empty(request()->input('from')) && $startedAt <= Carbon::createFromFormat('d/m/Y',request()->input('from'))->format('Y-m-d')) {
                    $startedAt = Carbon::createFromFormat('d/m/Y',request()->input('from'))->format('Y-m-d');
                }
                if (!empty(request()->input('to')) && $endedAt >= Carbon::createFromFormat('d/m/Y',request()->input('to'))->format('Y-m-d')) {
                    $endedAt = Carbon::createFromFormat('d/m/Y',request()->input('to'))->format('Y-m-d');
                }
                $sort['P'.$task->project_id] =[
                    'pID' => 'P'.$task->project_id,
                    'pName' => '<a href="'.route('project.show',['id'=>$project->id]).'">'.$project->name.'</a>',
                    'pLink' => route('project.show',['id'=>$project->id]),
                    'group' => 1,
                    'pOpen' => 1,
                    'pParent' => 0,
                    'pStart' => $startedAt,
                    'pEnd' => $endedAt,
                    'pPlanStart' => $pPLanStart ?? '',
                    'pPlanEnd' =>  $pPlanEnd ?? '',
                    'pComp' =>  isset($project->progress)?round($project->progress):0,
                    'pClass' => (empty($project->progress)?"progress-zero ":"").'ggroupblack',
                    'ptooltiptemplate' => view('partials.gantt-tooltip-template',['data'=>$project,'isTask'=>false])->render(),
                    'level' => 0
                ];
            }
            $tasksId[] = $task->id;
            if (!in_array($task->parent_task,$tasksParentId)){
                $tasksParentId[] = $task->parent_task;
            }
        }
        // sort tasks by level
        foreach(array_reverse(($tasks->toArray())['data']) as $task){
            if (empty($task->parent_task)|| !in_array($task->parent_task,$tasksId)){
                if (empty($task->parent_task)){
                    $hasChild = in_array($task->id,$tasksParentId);
                    $sort = $this->addTaskToDataGantt($sort,$task,'P'.$task->project_id,$hasChild, $mode);
                }else {
                    $hasChild = in_array($task->id,$tasksParentId);
                    [$sort,$tasksId] = $this->addTaskHasParent($sort,$mode,$task,$tasksId,$hasChild);
                }

            }else{
                $tasksAfterSort[] = $task;
            }
        }
        if (count($tasksAfterSort) > 0){
            $sort = $this->sortDataTaskChild($sort,$tasksAfterSort,$tasksParentId, $mode);
        }

        foreach($sort as $key=>$value){
            $result[] = $value;
        }
        return $result;
    }

    function addTaskHasParent($sort,$mode,$task,$tasksId,$hasChild){
        $columns = [
            'project_tasks.id',
            'project_tasks.parent_task',
            'project_tasks.description',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.progress',
            'projects.name as project_name',
            'projects.id as project_id',
            'task_types.name as type_name',
            'task_types.id as type_id',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
            'project_tasks.estimated_time',
            'project_tasks.name',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'users.avatar',
            'project_tasks.updated_at',
            'projects.started_at as project_start',
            'projects.ended_at as project_end',
            'projects.progress as project_progress',
            'projects.description as project_description',
            'project_tasks.is_slow',
            'project_tasks.created_at',
            DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'),
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
        ];
        $taskParent = ProjectTask::select($columns)
            ->checkUserPermission(Auth::id())
            ->leftJoin('users', 'project_tasks.user_id', 'users.id')
            ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
            ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
            ->leftJoin('task_priorities', 'project_tasks.priority', 'task_priorities.id')
            ->where('project_tasks.id', $task->parent_task)
            ->first();
        if ($taskParent->parent_task != null) {
            [$sort,$tasksId] = $this->addTaskHasParent($sort, $mode, $taskParent, $tasksId, true);
        } else {
            $tasksId[] = $taskParent->id;
            $sort = $this->addTaskToDataGantt($sort, $taskParent, 'P' . $task->project_id, true, $mode);
        }

        $sort = $this->addTaskToDataGantt($sort,$task,'T'.$task->parent_task,$hasChild, $mode);
        return [$sort,$tasksId];
    }

    /**
     * Add item into array after item key
     */
    function insert_into_array( $array, $search_key, $insert_key, $insert_value ) {

        $new_array = array();

        foreach( $array as $key => $value ) {

            // COPY THE CURRENT KEY/VALUE FROM OLD ARRAY TO A NEW ARRAY
            $new_array[$key] = $value;

            // ONLY IF CURRENT KEY IS THE KEY WE ARE SEARCHING FOR, AND WE WANT TO INSERT AFTER THAT FOUNDED KEY
            if ($key === $search_key)
                $new_array[$insert_key] = $insert_value;

        }

        return $new_array;

    }

    /**
     * Sort Data task child
     */
    function sortDataTaskChild($sort,$tasks,$tasksParentId,$mode){
        $tasksAfterSort = [];
        foreach($tasks as $task) {
            if (array_key_exists('T' . $task->parent_task, $sort)){
                $hasChild = in_array($task->id,$tasksParentId);
                $sort = $this->addTaskToDataGantt($sort,$task,'T'.$task->parent_task,$hasChild,$mode);
            }else{
                $tasksAfterSort[] = $task;
            }
        }

        if (count($tasksAfterSort) > 0){
            $sort = $this->sortDataTaskChild($sort,$tasksAfterSort,$tasksParentId,$mode);
        }
        return $sort;
    }

    /**
     * Get status deadline
     */
    function getColorGantt($task, $mode, $level){
        $color='';

        if ($mode == SiteSetting::GANTT_TASK_COLOR_BY_PROGRESS) {
            if (empty($task->started_at) || empty($task->ended_at)){
                return $color;
            }
            $started_at = date('Y-m-d',strtotime($task->started_at));
            $ended_at = date('Y-m-d',strtotime($task->ended_at));
            if(strtotime($task->ended_at) <= strtotime("-1 days")){
                $processDefault = 100;
            }else{
                $workDays = (new WorkingTimeManager())->getWorkdays($started_at,$ended_at);
                $workedDays = (new WorkingTimeManager())->getWorkdays($started_at,date('Y-m-d',strtotime("-1 days")));
                $processDefault = round($workedDays>$workDays?100:100/$workDays*$workedDays, 0);
            }

            if (round($task->progress) == 0 && $task->started_at > now()) {
                $statusTask = 'not_start_yet';
            } else if ($processDefault > round($task->progress)){
                if($task->number_child == 0){
                    $statusTask = 'is_slow';
                }else{
                    $numberTaskChildSLow = ProjectTask::where('parent_task',$task->id)->where('is_slow',ProjectTask::IS_SLOW)->count();
                    if($numberTaskChildSLow == 0){
                        $statusTask = 'on_progress';
                    }else{
                        $statusTask = 'is_slow';
                    }
                }
            }else if($processDefault == round($task->progress)){
                $statusTask = 'on_progress';
            }else{
                $statusTask = 'over_progress';
            }
            
            switch ($statusTask) {
                case 'not_start_yet':
                    $color = 'gray';
                    break;
                case 'is_slow':
                    $color = 'maroon';
                    break;
                case 'on_progress':
                case 'over_progress':
                    $color = 'yellow';
                    if (round($task->progress) == 100) {
                        $color = 'olive';
                    }
                    break;
            }
            
        } else {
            switch ($level) {
                case self::TASK_LEVEL_1:
                    $color = 'blue-1';
                    break;
                case self::TASK_LEVEL_2:
                    $color = 'blue-2';
                  break;
                case self::TASK_LEVEL_3:
                    $color = 'blue-3';
                  break;
                case self::TASK_LEVEL_4:
                    $color = 'blue-4';
                  break;
                case self::TASK_LEVEL_5:
                    $color =  'blue-5';
                  break;
                default:
                    $color = 'blue-6';
            }
        }

        return $color;
    }

    /**
     * Update all ancestor tasks and parent project of the changed task
     */
    public function updateAncestorTasksAndProject($task)
    {
        // Update all ancestor tasks
        // -----------------------------
        if ($task->parent_task != null) {
            $this->updateParentTask($task->parent_task);
        }
        // Update the parent project
        // -----------------------------
        (new ProjectManager())->updateProject($task->project_id);
    }

    /**
     * Get current task of user
     */
    function getActionTaskOfUser($user){
        $taskAction = TaskAction::where('user_id',$user)
                        ->orderBy('action_at','DESC')
                        ->first();
        return $taskAction;
    }

    /**
     * Check if the task is slow
     */
    function checkIsSlow($task){
        if (empty($task->started_at) || empty($task->ended_at)){
            return false;
        }
        $started_at = date('Y-m-d',strtotime($task->started_at));
        $ended_at = date('Y-m-d',strtotime($task->ended_at));
        if(strtotime($task->ended_at) <= strtotime("-1 days")){
            $processDefault = 100;
        }else{
            $workDays = (new WorkingTimeManager())->getWorkdays($started_at,$ended_at);
            $workedDays = (new WorkingTimeManager())->getWorkdays($started_at,date('Y-m-d',strtotime("-1 days")));
            $processDefault = round($workedDays>$workDays?100:100/$workDays*$workedDays, 0);
        }
        if ($processDefault > round($task->progress)){
            if ($task->number_child == 0) {
                return true;
            } else {
                $numberTaskChildSLow = ProjectTask::where('parent_task', $task->id)->where('is_slow', ProjectTask::IS_SLOW)->count();
                if ($numberTaskChildSLow == 0) {
                    return false;
                }
                return true;
            }
        }
        return false;
    }
    /**
     * Get task list with TaskType::CHANGE
     */
    function listTaskWithTypeChange($tasks, $filepath){
        $taskId = [];
        foreach($tasks as $task){
            $taskId[] = $task->id;
        }
        $taskFiles = TaskAttachment::leftJoin('task_logs',function ($join) {
            $join->on('task_logs.id','=', 'task_attachments.related_id')
                 ->where('task_attachments.type', '=',  TaskAttachment::TYPE_TASK_LOG);
        })
        ->where(function($query) use($taskId){
            $query->whereIn('task_attachments.related_id',$taskId)
                ->where('task_attachments.type', TaskAttachment::TYPE_PROJECT_TASK);
        })
        ->orWhere(
            function($query) use($taskId){
                $query->whereIn('task_logs.task_id',$taskId)
                    ->where('task_attachments.type', TaskAttachment::TYPE_TASK_LOG);
        })
        ->select([
            DB::raw('(CASE
            WHEN task_attachments.type = '.TaskAttachment::TYPE_PROJECT_TASK.' THEN task_attachments.related_id
            ELSE task_logs.task_id
            END) AS task_id'),
            'task_attachments.id',
            'task_attachments.related_id',
            'task_attachments.type',
            'task_attachments.file_name',
            'task_attachments.file_path',
        ])
        ->get();

        $result = [];
        $index = 1;
        foreach($tasks as $task){
            $data = [];
            $data['number_order'] = $index;
            $data['project'] =  $task->project_name;
            $data['name'] =  $task->name;
            $data['detail_description'] =  $this->formatDescriptionToExcel($task->description);
            $data['estimated_time'] =  $task->estimated_time;
            foreach($taskFiles as $taskFile){
                $number = 0;
                if($task->id == $taskFile->task_id)
                {
                    $name = explode('.',$taskFile->file_name);
                    $destination = $filepath.'/'.FILE_DIR.$name[0].'.'.$name[1];
                    $filename = $taskFile->file_name;

                    while(Storage::disk(FILESYSTEM)->exists($destination)){
                        $number +=1;
                        $destination = $filepath.'/'.FILE_DIR.$name[0].'('.$number.').'.$name[1];
                        $filename = $name[0].'('.$number.').'.$name[1];
                    }
                    if (Storage::disk(FILESYSTEM)->exists($taskFile->file_path)) {
                        Storage::disk(FILESYSTEM)->copy($taskFile->file_path,$destination);
                        $data['attachments'][] = $filename;
                    }
                }

            }
            $index += 1;
            $result[] = $data;
        }
        return $result;
    }

    /**
     * Get task list with TaskType::BUG
     */
    function listTaskBug($tasks){
        $result = [];
        $index = 1;
        $dateFormatManager = new DateFormatManager();
    
        foreach($tasks as $task){
            $taskContentReasons = $task->reason ? $task->reason[0]['reasons'] : '';
            $taskContentFix     = $task->reason ? $task->reason[0]['fix']: '';
            $taskContentRange   = $task->reason ? $task->reason[0]['range'] : '';
            $reason             = '';
            if(trim($taskContentReasons) || trim($taskContentFix) || trim($taskContentRange)) {
                $reasons    = trans('language.reasons').": ". $taskContentReasons;
                $fix        =  "\n". trans('language.fix').": ". $taskContentFix;
                $range      = "\n". trans('language.range').": ". $taskContentRange;
                $reason     = $reasons.$fix.$range;
            }
        
            $data = [];
            $data['number_order'] = $index;
            $data['project_name'] = $task->project_name;
            $data['function_screen'] =  $task->function_screen;
            $data['date_bug'] = $dateFormatManager->dateFormatLanguage($task->created_at,'d/m/Y');
            $data['user_bug'] = $task->created_name;
            $data['content_bug'] = $task->name;
            $data['description'] = $this->formatDescriptionToExcel($task->description, true);
            $data['reason'] = $reason;
            $data['user_function'] = $task->user_name;
            $data['user_handle'] = $task->key_member_name;
            $data['bug_classify_id'] = $task->bug_classify_name;
            $data['bug_range_id'] =  $task->bug_range_name;
            $data['bug_reason_id'] =  $task->bug_reason_name;
            $data['bug_severity_id'] = $task->bug_severity_name;
            $data['bug_tag_id'] = $task->bug_tag_name;

            $index += 1;
            $result['data'][] = $data;
        }
        
        $fcw = $tasks->countBy('function_screen');
        foreach ($fcw as $k => $val) {
            $result['dataSheet']['function_screen'][] = ['name' => $k, 'num_bugs' => $val];
        }
        $bugClassifies = $tasks->countBy('bug_classify_name');
        foreach ($bugClassifies as $k => $val) {
            $result['dataSheet']['bug_classify_name'][] = ['name' => $k, 'num_bugs' => $val];
        }
        $bugReasons = $tasks->countBy('bug_reason_name');
        foreach ($bugReasons as $k => $val) {
            $result['dataSheet']['bug_reason_name'][] = ['name' => $k, 'num_bugs' => $val];
        }
        $bugRanges = $tasks->countBy('bug_range_name');
        foreach ($bugRanges as $k => $val) {
            $result['dataSheet']['bug_range_name'][] = ['name' => $k, 'num_bugs' => $val];
        }
        $bugSeverities = $tasks->countBy('bug_severity_name');
        foreach ($bugSeverities as $k => $val) {
            $result['dataSheet']['bug_severity_name'][] = ['name' => $k, 'num_bugs' => $val];
        }
        $userName = array_column($tasks->all(), 'user_name', 'user_id');
        $userIds = $tasks->countBy('user_id');
        foreach ($userIds as $k => $val) {
            $result['dataSheet']['user_name'][] = ['id' => $k, 'user_name' => $userName[$k] ?? '', 'num_bugs' => $val];
        }

        return collect($result);
    }

    function getTaskCalendar($params){
        [$tasks, $totalEstimatedTime] = $this->getTaskList($params, null, ['project_tasks.id' => 'DESC']);
        $result =[];
        $displayId = SiteSetting::displayTaskProjectId();
        foreach($tasks as $task){
            $result[] = [
                'is_event' => 0,
                'id' => $task->id,
                'title' => $task->name,
                'start' => empty($task->started_at)?'':date('Y-m-d',strtotime($task->started_at)),
                'end' => empty($task->ended_at)?'':date('Y-m-d',strtotime("+1 day", strtotime($task->ended_at))),
                'project' => $task->project_name,
                'assignee' => $task->first_name. ' ' .$task->last_name,
                'priority' => [
                    'text' => $task->priority_name,
                    'background' => $this->getBackgroundPriority($task->priority_id,true),
                    'color' => $this->getTextColor($this->getBackgroundPriority($task->priority_id,true))
                ],
                'estimated_time' => $task->estimated_time,
                'type' => $task->type_name,
                'url' => route('task.show', ['id'=>$task->id]),
                'progress' => round($task->progress).'%',
                'status' => [
                    'text' => $task->status_name,
                    'background' => $this->getBackgroundStatus($task->status_id,false),
                    'color' => $this->getTextColor($this->getBackgroundStatus($task->status_id,false))
                ],
                'color' => $this->getBackgroundPriority($task->priority_id,true),
                'project_start' => empty($task->project_start)?'':date('Y-m-d',strtotime($task->project_start)),
                'project_end' => empty($task->project_end)?'':date('Y-m-d',strtotime($task->project_end)),
                'project_progress' => $task->project_progress,
                'project_url' => route('project.show', ['id'=> $task->project_id]),
                'project_description' => $task->project_description,
                'started_at' => empty($task->started_at)?'':date('Y-m-d H:i:s',strtotime($task->started_at)),
                'ended_at' => empty($task->ended_at)?'':date('Y-m-d H:i:s', strtotime($task->ended_at)),
                'displayId' => $displayId,
            ];
        }
        return $result;
    }

    public function getNumberIsSlow(){
        $number = ProjectTask::checkUserPermission(Auth::id())
            ->where('project_tasks.is_slow',ProjectTask::IS_SLOW)
            ->count();
        return $number;
    }

    function convertDataExportProblem($tasks){
        $rolesOfGroupLeader = ProjectRole::select('id')
            ->where('group_role_id',ProjectGroupRole::LEADER)
            ->orderby('id','ASC')
            ->get()
            ->pluck('id')
            ->toArray();
        $results = [];
        foreach ($tasks as $task){
            if(empty($task->started_at) && empty($task->ended_at)){
                $weekStart = Carbon::createFromFormat('Y-m-d H:i:s',$task->created_at)->startOfWeek()->format('Y-m-d');
                $weekEnd = Carbon::createFromFormat('Y-m-d H:i:s',$task->created_at)->endOfWeek()->format('Y-m-d');
            }else if(empty($task->started_at) && !empty($task->ended_at)){
                $weekStart = Carbon::createFromFormat('Y-m-d H:i:s',$task->ended_at)->startOfWeek()->format('Y-m-d');
                $weekEnd = Carbon::createFromFormat('Y-m-d H:i:s',$task->ended_at)->endOfWeek()->format('Y-m-d');
            }elseif(!empty($task->started_at) && empty($task->ended_at)){
                $weekStart = Carbon::createFromFormat('Y-m-d H:i:s',$task->started_at)->startOfWeek()->format('Y-m-d');
                $weekEnd = Carbon::createFromFormat('Y-m-d H:i:s',$task->started_at)->endOfWeek()->format('Y-m-d');
            }else{
                $weekStart = Carbon::createFromFormat('Y-m-d H:i:s',$task->started_at)->startOfWeek()->format('Y-m-d');
                $weekEnd = Carbon::createFromFormat('Y-m-d H:i:s',$task->ended_at)->endOfWeek()->format('Y-m-d');
            }
            for($i=strtotime($weekStart);$i<=strtotime($weekEnd);$i+=604800){
                if (!isset($results[date('Y-m-d',$i)])){
                    $results[date('Y-m-d',$i)]['projects'] = [];
                    $results[date('Y-m-d',$i)]['personnel'] = $this->personnelStatistics(date('Y-m-d',$i),date('Y-m-d',$i+518400));
                }
                if (!isset($results[date('Y-m-d',$i)]['projects'][$task->project_id])){
                    $results[date('Y-m-d',$i)]['projects'][$task->project_id]['tasks']=[];
                    $results[date('Y-m-d',$i)]['projects'][$task->project_id]['project_name']=$task->project_name;
                    $results[date('Y-m-d',$i)]['projects'][$task->project_id]['leader']=isset($rolesOfGroupLeader[0])?(new ProjectManager())->getMemberByRole($task->project_id,$rolesOfGroupLeader[0]):[];
                    $results[date('Y-m-d',$i)]['projects'][$task->project_id]['deputy_group']=isset($rolesOfGroupLeader[1])?(new ProjectManager())->getMemberByRole($task->project_id,$rolesOfGroupLeader[1]):[];
                    $results[date('Y-m-d',$i)]['projects'][$task->project_id]['members']=(new ProjectManager())->getMembers($task->project_id);
                }
                $results[date('Y-m-d',$i)]['projects'][$task->project_id]['tasks'][]=$task->name;
            }
        }
        ksort($results);
        return $results;
    }

    public function personnelStatistics($startTime=null, $endTime=null){
        $userId = Auth::id();
        $projectIds = ProjectTask::select('project_tasks.project_id')
            ->checkUserPermission($userId)
            ->distinct();
        if(empty($startTime)){
            $projectIds = $projectIds->where(function($query)  use ($startTime){
                $query->where('project_tasks.started_at','>=', $startTime . ' 00:00:00')
                    ->orwhere('project_tasks.ended_at', '>=', $startTime . ' 00:00:00');
            });
        }
        if(empty($endTime)){
            $projectIds = $projectIds->where(function($query)  use ($endTime){
                $query->where('project_tasks.started_at','<=', $endTime . ' 23:59:59')
                    ->orwhere('project_tasks.ended_at', '<=', $endTime . ' 23:59:59');
            });
        }
        $projectIds = $projectIds->get()->pluck('project_id')->toArray();
        $roleLeader = ProjectRole::select('id')
            ->where('group_role_id',ProjectGroupRole::LEADER)
            ->orderby('id','ASC')
            ->first();
        $users = ProjectMember::select(
                DB::raw('project_members.user_id as id'),
                DB::raw('min(group_role_id) as group_role')
            )
            ->join('project_roles','project_roles.id','project_members.role_id')
            ->whereIn('project_members.project_id', $projectIds)
            ->groupBy('project_members.user_id', 'project_id')
            ->get();
        $result=[];
        foreach ($users as $user){
            if (!isset($result[$user->id])){
                $result[$user->id]=[
                    'project_manager' => 0,
                    'leader' => 0,
                    'member' => 0,
                ];
            }
            switch ($user->group_role){
                case ProjectGroupRole::PROJECT_MANAGER:
                    $result[$user->id]['project_manager'] += 1;
                    break;
                case ProjectGroupRole::LEADER:
                    $userIsLeader = ProjectMember::select('user_id')
                        ->where('user_id',$user->id)
                        ->where('role_id',$roleLeader->id)
                        ->where('project_id', $user->project_id)
                        ->get();

                    if (count($userIsLeader)>0){
                        $result[$user->id]['leader'] += 1;
                    }else{
                        $result[$user->id]['member'] += 1;
                    }
                    break;
                case ProjectGroupRole::MEMBER:
                    $result[$user->id]['member'] += 1;
                    break;
                default:
                    break;
            }
        }
        return $result;
    }

    public function updateSlowTaskHasChild($task){
        $taskChilds = ProjectTask::select([
                'id',
                'started_at',
                'ended_at',
                'progress',
                'is_slow',
                DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
            ])
            ->where('status', '!=', TaskStatus::TASK_STATUS_CLOSE)
            ->where('parent_task', $task->id)
            ->get();
        if (!empty($taskChilds)) {
            foreach ($taskChilds as $taskChild) {
                if($taskChild->number_child == 0){
                    $this->updateSlowTaskHasChild($taskChild);
                }
                $isSlow = (new TaskManager())->checkIsSlow($taskChild);
                $taskChild->is_slow = $isSlow ? ProjectTask::IS_SLOW : ProjectTask::NOT_SLOW;
                $taskChild->save();
            }
        }

        $isSlow = (new TaskManager())->checkIsSlow($task);
        $task->is_slow = $isSlow ? ProjectTask::IS_SLOW : ProjectTask::NOT_SLOW;
        $task->save();
    }

    public function updateParentTask($parentID){
        $parentTask = ProjectTask::select(
            'project_tasks.id',
            'project_tasks.progress',
            'project_tasks.parent_task',
            'project_tasks.project_id',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.limit',
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
        )->where('project_tasks.id', $parentID);

        // Update progress, estimated_time, started_at, ended_at for parent task
        $parentTask = $parentTask->updateTask()->first();
        $parentTask->progress = $parentTask->updated_progress;
        $parentTask->estimated_time = $parentTask->updated_estimated_time;
        if (!$parentTask->limit) {
            $parentTask->started_at = $parentTask->updated_started_at;
            $parentTask->ended_at = $parentTask->updated_ended_at;
        }
        // Update is_slow
        $is_slow = (new TaskManager())->checkIsSlow($parentTask);
        $parentTask->is_slow = $is_slow ? ProjectTask::IS_SLOW : ProjectTask::NOT_SLOW;
        // Update parent task
        $parentTask->save();
        return $parentTask;
    }

    /**
     * check limit parent
     */
    public function checkLimitParent($parent_task)
    {
        $projectTask = ProjectTask::find($parent_task);
        if($projectTask->limit){
            return $projectTask;
        } else {
            if(isset($projectTask->parent_task)){
                return $this->checkLimitParent($projectTask->parent_task);
            } else {
                return null;
            }
        }
    }

    /**
     * update level task child
     */
    public function updateLevelTaskChild($arrayTaskId, $levelTask)
    {
        $taskChilds = ProjectTask::select('id')
            ->whereIn('parent_task',$arrayTaskId)
            ->get()->pluck('id')->toArray();
        if (!empty($taskChilds)){
            ProjectTask::whereIn('id',$taskChilds)->update(['level' => $levelTask + 1]);
            $this->updateLevelTaskChild($taskChilds, $levelTask + 1);
        }
    }
    /**
     * color level
     */
    public function colorLevel($colorId)
    {
        switch ($colorId) {
            case 1:
                $color = 'rgb(0, 119, 255)';
                break;
            case 2:
                $color = 'rgb(111, 177, 252)';
            break;
            case 3:
                $color = 'rgb(68, 107, 167)';
            break;
            case 4:
                $color = 'rgb(67, 96, 122)';
            break;
            case 5:
                $color =  'rgb(100, 118, 126)';
            break;
            default:
                $color = 'rgb(146, 146, 146)';
        }
        return $color;
    }


    /**
     * Format description to excel
     * @param $description
     * @param false $removeImg
     * @return string
     */
    private function formatDescriptionToExcel($description, $removeImg = false){
        if($removeImg) {
            $description = preg_replace("/<img[^>]+\>/i", '', $description); // remove Img tag
            $description = preg_replace("/<br>/i", '', $description); 
            $description = str_replace("<p>", " ", $description);
        }
        $arrayStringDescription = explode( '</p>', $description );
        $arrayStringDescription = array_filter(array_map("trim", $arrayStringDescription));
        $result = "";
        foreach($arrayStringDescription as $item){
            $item = (new StringHelper())->newLineCharacter($item);
            $result .= $item;
        }
        $result = str_replace('&nbsp;',' ',strip_tags( $result));
        
        return $result;
    }
}
