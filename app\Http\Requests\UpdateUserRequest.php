<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'avatar' => 'nullable|mimes:jpeg,png,jpg,gif,svg,jfif|max:10240',
            'language' => 'nullable|exists:languages,id'
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'avatar.mimes' =>  __('validation.mimes', ['attribute' => __('validation.attributes.avatar')]),
            'avatar.max' =>  __('validation.max.file', [
                'attribute' => __('validation.attributes.avatar'),
                'max' => '10240'
            ]),

            'language.exists' => __('validation.exists', ['attribute' => __('validation.attributes.language')])
        ];
    }

}
