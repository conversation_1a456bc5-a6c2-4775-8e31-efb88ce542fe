<?php

namespace App\Http\Requests\Admin\SiteSetting;

use Illuminate\Foundation\Http\FormRequest;

class TaskPriorityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|max:200|unique:task_priorities,name,'.$this->route('id').',id,deleted_at,NULL',
        ];
    }
    /**
     * Get the notification.
     *
     * @return array
    */
    public function messages()
    {
        return [
            'name.unique' => trans('message.task_priority_exist'),
        ];
    }
}
