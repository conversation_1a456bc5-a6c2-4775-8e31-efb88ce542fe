<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Position;
use Illuminate\Http\Request;


class DepartmentController extends Controller
{
    /**
     * Get all positions of the department
     *
     * @param $department_id
     * @return array
     */
    public function getPositions(Request $request) {
        $department_id = $request -> id;
        $positions = Position::where('department_id', $department_id)->get();

        $options = [];
        foreach ($positions as $position) {
            $options[] = [
                'value' => $position->id,
                'text' => $position->name
            ];
        }

        return [
            'status' => 200,
            'options' => $options
        ];
    }
}
