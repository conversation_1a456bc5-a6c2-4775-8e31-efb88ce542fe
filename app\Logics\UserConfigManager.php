<?php

namespace App\Logics;

use App\UserConfig;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserConfigManager
{
    /**
     * Handle update or create config user setting
     * @param  $request
     * @return Builder|Model
     * @throws Exception
     */
    public function updateOrCreateConfig($request)
    {
        $userId = Auth::guard('api')->user()->id;
        DB::beginTransaction();
        try {
            $data = [
                'key' => KEY_USER_CONFIG,
                'value' => $request->input('value')
            ];
            $result = UserConfig::query()
                ->where('user_id', $userId)
                ->where('key', KEY_USER_CONFIG)
                ->updateOrCreate(['user_id' => $userId], $data);
            DB::commit();
            return $result;
        } catch (Exception $e) {
            Log::error("[UserConfigManager][updateOrCreateConfig] line " . $e->getLine() . " error " . $e->getMessage());
            DB::rollBack();
            throw new Exception("[UserConfigManager][updateOrCreateConfig] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Handle get list config user setting
     * @throws Exception
     */
    public function getConfig()
    {
        $userId = Auth::guard('api')->user()->id;
        try {
            return UserConfig::query()
                ->where('user_id', $userId)
                ->where('key', KEY_USER_CONFIG)
                ->select(['key', 'value'])
                ->first();
        } catch (Exception $e) {
            Log::error("[UserConfigManager][getConfig] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[UserConfigManager][getConfig] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * Check user enable push notification
     * @param $typeNotification - Type notification Firebase or Email
     * @param $userId - User ID
     * @return bool
     * @throws Exception
     */
    public function checkPushNotification($typeNotification, $userId)
    {
        try {
            //Get config by user id
            $getConfig = UserConfig::query()
                ->where('user_id', $userId)
                ->where('key', KEY_USER_CONFIG)
                ->first(['value']);
            if (!empty($getConfig)) {
                $valueConfig = json_decode($getConfig->getAttributeValue('value'), true);
                if (isset($valueConfig[$typeNotification]) && $valueConfig[$typeNotification] === false) {
                    return false;
                }
            }
            return true;
        } catch (Exception $e) {
            Log::error("[UserConfigManager][checkPushNotification] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[UserConfigManager][checkPushNotification] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
}