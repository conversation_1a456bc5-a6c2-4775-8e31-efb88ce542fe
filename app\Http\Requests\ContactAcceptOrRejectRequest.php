<?php

namespace App\Http\Requests;

use App\Models\Contacts;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContactAcceptOrRejectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'contact_id' => 'required|exists:contacts,id',
            'type_request' => ['required', Rule::in([ Contacts::STATUS_ACCEPTED, Contacts::STATUS_REJECTED ])]
        ];
    }
}
