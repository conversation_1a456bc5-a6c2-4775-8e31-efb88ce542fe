<?php

namespace App\Console\Commands;

use App\Traits\StorageTrait;
use Illuminate\Console\Scheduling\Schedule;

class ClearTemporaryDirectory extends BaseCommand
{
    use StorageTrait;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:clear_temporary_directory  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear temporary directory';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        $deleteDirectory = $this->deleteDirectory(TEMP_DIR);
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->dailyAt('3:00');
    }
}
