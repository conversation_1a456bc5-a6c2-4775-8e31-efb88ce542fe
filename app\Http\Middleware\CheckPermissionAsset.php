<?php

namespace App\Http\Middleware;

use App\Models\Asset;
use App\Models\Role;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Logics\AssetManager;

class CheckPermissionAsset
{
    /**
     * Handle an incoming request.
     *
     * @param  Request $request
     * @param Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = Auth::user();

        if ($user->hasRole(Role::ROLE_SYSTEM_MANAGER)) {
            return $next($request);
        }

        if ($user->hasRole(Role::ROLE_ASSET_MANAGER)) {
            //Check if is route edit, update, delete asset
            if (!empty($request->id)) {
                $asset = Asset::find($request->id);
                if (!empty($asset)) {
                    $assetManager = new AssetManager();
                    if ($assetManager->checkRoleUpdate($asset)) {
                        return $next($request);
                    } else {
                        abort(403);
                    }
                } else {
                    abort(403);
                }
            }
            return $next($request);
        }
    }
}
