<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class OrderExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    function __construct($result) {
        $this->result = $result;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    { 
        return (collect($this->result));
    }
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.number_order'),
            trans('language.name_employee'),
            trans('language.order_lunch.order'),
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 40,
            'C' => 60,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:C1')->applyFromArray(array(
            'borders' => [
                'all' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));
        $sheet->getStyle('A1:C'.(count($this->result)))->getAlignment()->setWrapText(true);
    } 
}
