<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class UpdateConversationIdInMessageRead extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update-message-read-conversations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command update conversation_id in message_read from messages table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating conversation_id in message_read...');

        $rows = DB::table('message_read')
            ->select('message_read.id as message_read_id', 'messages.conversation_id')
            ->join('messages', 'messages.id', '=', 'message_read.message_id')
            ->get()
            ->chunk(1000);

        foreach ($rows as $chunk) {
            $ids = [];
            $cases = '';

            foreach ($chunk as $row) {
                $id = (int) $row->message_read_id;
                $conversationId = (int) $row->conversation_id;
                $ids[] = $id;
                $cases .= "WHEN {$id} THEN {$conversationId} ";
            }

            $idsList = implode(',', $ids);

            DB::update("
                UPDATE message_read
                SET conversation_id = CASE id
                    {$cases}
                END
                WHERE id IN ({$idsList})
            ");
        }
        
        $this->info('Done updating conversation_id in message_read!');
        return 0;
    }
}
