<?php

namespace App\Logics;


use App\Models\Attendance;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Models\SiteSetting;
use App\Models\UserLeaveDayLog;
use App\Models\UserRemainingLeaveDay;
use App\User;
use App\Enums\WorkingTypeEnum;
use App\Models\Holiday;
use App\Models\Request as ModelsRequest;
use App\Services\GoogleMapService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class TimekeepingManager
{
    public $morning_start;
    public $morning_end;
    public $afternoon_start;
    public $afternoon_end;
    public function __construct()
    {
        $this->morning_start = strtotime(SiteSetting::morningStart()) + PADDING_TIME;
        $this->morning_end = strtotime(SiteSetting::morningEnd());
        $this->afternoon_start = strtotime(SiteSetting::afternoonStart()) + PADDING_TIME;
        $this->afternoon_end = strtotime(SiteSetting::afternoonEnd());

    }
    /**
     * Get attendances of user by user_id
     * @param $user_id
     * @param null $start_date
     * @param null $end_date
     * @param null $sort
     * @return mixed
     */
    public function getUserAttendances($user_id, $start_date, $end_date, $sort = null, $deleted = false, $direction = null) {
        $attendances = Attendance::select(DB::raw('
            user_id,
            deleted_at,
            CONCAT_WS(" ",first_name, last_name) AS full_name,
            DATE(checked_at) date,
            TIME(MIN(checked_at)) checked_in,
            TIME(MAX(checked_at)) checked_out,
            GROUP_CONCAT(IFNULL(metadata, "{}") ORDER BY checked_at ASC SEPARATOR ", ") AS metadata,
            GROUP_CONCAT(IFNULL(checked_type, "") ORDER BY checked_at ASC SEPARATOR ", ") AS checked_type_array',
        ))
            ->leftJoin('users', 'users.id', 'attendances.user_id');

        if ($user_id != null) {
            $attendances = $attendances->whereIn('user_id', $user_id);
        }

        if (isset($start_date)) {
            $attendances->whereDate('checked_at', '>=', date('Y-m-d', strtotime($start_date)));
        } else {
            $attendances->whereDate('checked_at', '>=', date('Y-m-1', strtotime('this month')));
        }
        if (isset($end_date)) {
            $attendances->whereDate('checked_at', '<=', date('Y-m-d', strtotime($end_date)));
        }
        if ($deleted == true){
            $attendances->where('deleted_at', '!=', null);
        }
        if ($deleted == false){
            $attendances->where('deleted_at', null);
        }

        $attendances->groupBy(DB::raw('DATE(checked_at)'),'user_id');

        if (!$sort) {
            $attendances = $attendances->orderByRaw('date DESC, checked_in DESC');
        }

        $sortable = ['date', 'users.last_name', 'checked_in', 'checked_out'];
        if (!in_array($direction, ['asc', 'desc'])) {
            $direction = 'asc';
        }
        if (in_array($sort, $sortable)) {
            $attendances = $attendances->orderBy($sort, $direction);
        }

        return $attendances;
    }

    /**
     * Calculate working duration in day
     * @param $attendance
     * @return array
     */
    public function calcWorkingDuration($attendance, $export = false) {
        [$checkedIn, $checkedOut] = $this->getTimeCheckedInAndCheckedOut($attendance);
        $late_join = $this->checkLateJoin($checkedIn);
        $hasTypeGps = false;
        $checkedInImage = null;
        $checkedOutImage = null;
        $early_leave = true;
        $locationCheckIn = '';
        $locationCheckOut = '';

        $user = User::withTrashed()->find($attendance->user_id);
        if ($checkedOut == null || (($this->morning_end <= $checkedOut && $checkedOut <= $this->afternoon_start && $user->working_type != WorkingTypeEnum::FULL_TIME ) || $checkedOut >= $this->afternoon_end))
            $early_leave = false;

        $duration = (new WorkingTimeManager())->getWorkTimeInOneDay($checkedIn, $checkedOut);

        // Get checked image
        $checkedInImage = $checkedOutImage = null;
        if ($export == false) {
            $checkedInImage = $checkedIn ? Attendance::select('checked_image')
            ->where('checked_at', $attendance->date.' '.$attendance->checked_in)->first()['checked_image'] : null;
            $checkedOutImage = $checkedOut ? Attendance::select('checked_image')
            ->where('checked_at', $attendance->date.' '.$attendance->checked_out)->first()['checked_image'] : null;
        }

        // Get location name if has meta data location. Get first location check in and last location check out
        if ($attendance->metadata) {
            $locations = json_decode("[$attendance->metadata]", true) ?? [];
            $locationCheckIn = $checkedIn && isset($locations[0]['location']) ? $locations[0]['location'] : '';
            $locationCheckOut = $checkedOut && isset(end($locations)['location'])
                ? end($locations)['location'] : '';
        }


        // Get first checked type and last checked type in checked_type_array and check if has type GPS return true
        $checkedTypeArray = explode(',', $attendance->checked_type_array);
        if (!empty($checkedTypeArray) && 
            ($checkedTypeArray[0] == Attendance::GPS_CHECK || end($checkedTypeArray) == Attendance::GPS_CHECK)) {
            $hasTypeGps = true;
        }

        return [
            'date' => $attendance->date,
            'checked_in' => $checkedIn,
            'checked_in_image' => $checkedInImage,
            'checked_out' => $checkedOut,
            'checked_out_image' => $checkedOutImage,
            'duration' => $duration,
            'late_join' => $late_join,
            'early_leave' => $early_leave,
            'check_in_location' => $locationCheckIn,
            'check_out_location' => $locationCheckOut,
            'has_type_gps' => $hasTypeGps,
        ];
    }
    /**
     * Update Leave Days
     */
    public function updateLeaveDays($allStaffs, $year, $month, $holidays) {
        $firstOfNextMonth = Carbon::createFromFormat('m/Y', ($month+1).'/'.$year)->firstOfMonth();
        $firstOfMonth = Carbon::createFromFormat('m/Y', $month.'/'.$year)->firstOfMonth();
        $endOfMonth = Carbon::createFromFormat('m/Y', $month.'/'.$year)->endOfMonth();

        UserLeaveDayLog::where('leave_day', '>=', $firstOfMonth)
            ->where('leave_day', '<=', $endOfMonth)
            ->delete();
        UserRemainingLeaveDay::where('month', $firstOfNextMonth)->delete();
        if ($month == RESET_LEAVE_DAY_YEARLY_MONTH) {
            UserRemainingLeaveDay::where('month', $endOfMonth->toDateString())->delete();
        }

        $attendances = Attendance::select(
            'user_id',
            DB::raw('DATE(checked_at) as date'),
            DB::raw('TIME(MIN(checked_at)) as checked_in'),
            DB::raw('TIME(MAX(checked_at)) as checked_out')
        )
        ->whereRaw('MONTH(checked_at) = '.$month)
        ->whereRaw('YEAR(checked_at) = '.$year)
            ->groupBy(DB::raw('user_id, DATE(checked_at)'))
            ->orderBy(DB::raw('attendances.user_id, DATE(checked_at)'))
            ->get();

        foreach ($allStaffs as $staff) {
            // Only update for users have not deleted before this month and signed_at <= end day of month
            if (($staff->ended_at != null && $staff->ended_at < $firstOfMonth)
                || ($staff->deleted_at != null && $staff->deleted_at < $firstOfMonth)
                || $staff->signed_at == null) {
                continue;
            }

            $attendance = $attendances->where('user_id', $staff->id)->values();

            $user_remaining_leave_hours = round((float) $staff->remaining_leave_hours + (float) $staff->plus_leave_hours, 2);
            $num = $endOfMonth->day;
            $row_it = 0;
            $late_join_cnt = 0;
            $late_join_exceed = false;

            // lay list request có ngày nghỉ trong tháng
            $vacation_days = ModelsRequest::select(
                'requests.id',
                'requests.content',
                'requests.created_by',
                'users.id as user_id'
            )->leftjoin('users', 'users.id', 'requests.created_by')
                ->where('users.id', $staff->id)
                ->where('requests.type', ModelsRequest::TYPE_VACATION)
                ->where('requests.status', ModelsRequest::STATUS_CONFIRM)
            ->where(function($query)  use ($firstOfMonth){
                $query->whereRaw('JSON_EXTRACT(content, "$.time_start")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"')
                    ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"');
                })
            ->where(function($query)  use ($endOfMonth){
                $query->whereRaw('JSON_EXTRACT(content, "$.time_start")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"')
                    ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"');
                })->get();

            $arrVacationRequest = [];
            foreach($vacation_days as $key => $vacation_day){
                $time_start = Carbon::parse($vacation_day->content['time_start']);
                $time_end = Carbon::parse($vacation_day->content['time_end']);
                if($time_start->format('Y-m-d') < $firstOfMonth){
                    for($i=1; $i<=$time_end->format('d'); $i++ ){
                        $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                        if($i==$time_start->format('d')){
                            $startTime = strtotime($time_start->format('H:i:s'));
                        }else{
                            $startTime = strtotime(MORNING_START);
                        }
                        if($i==$time_end->format('d')){
                            $endTime = strtotime($time_end->format('H:i:s'));
                        }else{
                            $endTime = strtotime(AFTERNOON_END);
                        }
                        $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);

                        $arrVacationRequest[$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                    }
                }elseif($time_end->format('Y-m-d') > $endOfMonth){
                    for($i=$time_start->format('d'); $i<=$endOfMonth->format('d'); $i++ ){
                        $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                        if($i==$time_start->format('d')){
                            $startTime = strtotime($time_start->format('H:i:s'));
                        }else{
                            $startTime = strtotime(MORNING_START);
                        }
                        if($i==$endOfMonth->format('d')){
                            $endTime = strtotime($time_end->format('H:i:s'));
                        }else{
                            $endTime = strtotime(AFTERNOON_END);
                        }
                        $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                        $arrVacationRequest[$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                    }
                }
                elseif($time_end->format('Y-m-d') == $time_start->format('Y-m-d')){
                    $startTime = strtotime($time_start->format('H:i:s'));
                    $endTime = strtotime($time_end->format('H:i:s'));
                    $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                    $arrVacationRequest[$time_start->format('Y-m-d')]=isset($arrVacationRequest[$time_start->format('Y-m-d')])?($arrVacationRequest[$time_start->format('Y-m-d')]+$hour):$hour;
                }
                else{
                    for($i=$time_start->format('d'); $i<=$time_end->format('d'); $i++ ){
                        $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                        if($i==$time_start->format('d')){
                            $startTime = strtotime($time_start->format('H:i:s'));
                        }else{
                            $startTime = strtotime(MORNING_START);
                        }
                        if($i==$time_end->format('d')){
                            $endTime = strtotime($time_end->format('H:i:s'));
                        }else{
                            $endTime = strtotime(AFTERNOON_END);
                        }
                        $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                        $arrVacationRequest[$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                    }
                }
            }

            for ($i = 1; $i <= $num; $i++) {
                $is_working_in_day = false;
                $date = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                if ($row_it < count($attendance)) {
                    $row = $attendance[$row_it];

                    if ($row['date'] == $date && $row['checked_in'] != null && $row['checked_out'] != null) {
                        $row_it += 1;

                        // Ignore if checked in holidays
                        if (isset($holidays[$row['date']])) {
                            continue;
                        }
                        if ($row['checked_in'] != $row['checked_out']) {
                            $is_working_in_day = true;

                            // If checkin in morning shift or afternoon shift then is late join
                            if ($row['checked_in'] != null
                                && (($this->morning_start - PADDING_TIME < $row['checked_in'] && $row['checked_in'] < $this->morning_end)
                                || ($this->afternoon_start - PADDING_TIME < $row['checked_in'] && $row['checked_in'] < $this->afternoon_end))) {
                                $late_join_cnt += 1;
                            }

                            if ($late_join_cnt > LATE_JOIN_EXCEED) {
                                $late_join_exceed = true;
                            }

                            $duration = self::calcWorkingDuration($row, false);
                            $duration = $duration['duration'];

                            // Check if lateJoinNumber > 2 or leave day do caculate duration working max is afternoon end (17:30:00)
                            if ($late_join_exceed || isset($arrVacationRequest[$date])) {
                                $timeCheckout = min(strtotime($row['checked_out']), strtotime(AFTERNOON_END));
                                $duration = (new WorkingTimeManager())->getWorkTimeInOneDay(
                                    strtotime($row['checked_in']),
                                    $timeCheckout
                                );
                            }

                            // If the user signed the contract before the current date, the leave cannot be used
                            if ($staff->signed_at > $date) continue;

                            $working_hours_plus = 0;
                            if (0 < (8.0 - $duration) && (8.0 - $duration) <= $user_remaining_leave_hours) {
                                $working_hours_plus = round(8.0 - $duration, 2);
                            } elseif ((8.0 - $duration) > $user_remaining_leave_hours) {
                                $working_hours_plus = round($user_remaining_leave_hours, 2);
                            }
                            // Insert working_hours_plus to user_leave_days_logs tables
                            if ($working_hours_plus > 0 && isset($arrVacationRequest[$date])) {
                                $user_remaining_leave_hours -= min($arrVacationRequest[$date],$working_hours_plus,8);
                                UserLeaveDayLog::create([
                                    'user_id' => $staff->id,
                                    'leave_day' => $date,
                                    'hours' => min($arrVacationRequest[$date],$working_hours_plus,8)
                                ]);
                            }
                        }
                    }
                }
                if (!$is_working_in_day) {
                    if (!isset($holidays[$date]) && isset($arrVacationRequest[$date]) && $staff->signed_at <= $date) {
                        if (8.0 <= $user_remaining_leave_hours) {
                            $working_hours_plus = 8.0;
                        } else {
                            $working_hours_plus = $user_remaining_leave_hours;
                        }
                        $user_remaining_leave_hours -= min($arrVacationRequest[$date],$working_hours_plus,8);
                        if ($working_hours_plus > 0) {
                            UserLeaveDayLog::create([
                                'user_id' => $staff->id,
                                'leave_day' => $date,
                                'hours' => min($arrVacationRequest[$date],$working_hours_plus,8)
                            ]);
                        }
                    }
                }
            }
            // Insert remaining leave hours to user_remaining_leave_days
            if ($month == RESET_LEAVE_DAY_YEARLY_MONTH) {
                $next_year_user_remaining_leave_hours
                    = ($user_remaining_leave_hours < RESET_LEAVE_DAY_YEARLY_HOURS)
                    ? $user_remaining_leave_hours
                    : RESET_LEAVE_DAY_YEARLY_HOURS;
                UserRemainingLeaveDay::create([
                    'user_id' => $staff->id,
                    'month' => $endOfMonth,
                    'hours' => $user_remaining_leave_hours
                ]);
                UserRemainingLeaveDay::create([
                    'user_id' => $staff->id,
                    'month' => $firstOfNextMonth,
                    'hours' => $next_year_user_remaining_leave_hours
                ]);
            } else {
                UserRemainingLeaveDay::create([
                    'user_id' => $staff->id,
                    'month' => $firstOfNextMonth,
                    'hours' => $user_remaining_leave_hours
                ]);
            }
        }
    }

    /**
     * @param $checkedIn
     * @return bool
     */
    public function checkLateJoin($checkedIn){
        $late_join = false;
        if ($checkedIn != null && (($this->morning_start < $checkedIn && $checkedIn < $this->morning_end)
                || ($this->afternoon_start < $checkedIn && $checkedIn < $this->afternoon_end)))
        {
            $late_join = true;
        }
        return $late_join;
    }

    public function checkLateJoinLunch($checkedIn, $endTime){
        $late_join = false;
        if($endTime != null){
            if($endTime < $this->morning_end && $endTime > $this->morning_start){
                if($checkedIn != null && ($this->morning_start < $checkedIn && $checkedIn < $this->morning_end && $endTime < $checkedIn)){
                    $late_join = true;
                }
            }elseif($this->afternoon_start < $endTime && $endTime < $this->afternoon_end){
                if($this->afternoon_start < $checkedIn && $checkedIn < $this->afternoon_end && $endTime < $checkedIn){
                    $late_join = true;
                }
            }
        }
        return $late_join;
    }

    public function checkEarly($checkedOut, $startTime){
        $early = false;
        if($startTime != null && $checkedOut != null && $checkedOut < $startTime){
            if($startTime < $this->morning_end && $startTime > $this->morning_start ){
                $early = true;
            }elseif($startTime < $this->afternoon_end && $startTime > $this->afternoon_start){
                $early = true;
            }
        }
        return $early;
    }

    /**
     * @param $attendance
     * @return int[]
     */
    public function getTimeCheckedInAndCheckedOut($attendance){
        $checkedIn = strtotime($attendance->checked_in);
        $checkedOut = strtotime($attendance->checked_out);
        if ($checkedIn == $checkedOut) {
            if ($checkedOut > $this->afternoon_end) {
                $checkedIn = 0;
            } else {
                $checkedOut = 0;
            }
        }
        return [$checkedIn, $checkedOut];
    }

    public function getWeekedDay($day)
    {
        $weekedDay = false;
        if(isset($day)){
            if($day == 'Saturday' || $day == 'Sunday'){
                $weekedDay = true;
            }
        }
        return $weekedDay;
    }

    public function getToday($date)
    {
        $today = \Carbon\Carbon::now();
        return $date->isSameDay($today);
    }

    public function getFutureDay($date)
    {
        $today = \Carbon\Carbon::now();
        return $date->greaterThan($today);
    }

    /**
     * Get vacation request of user by user_id
     * @param $month
     * @param $year
     * @param $user_id
     * @return array
     */
    public function getVacationRequestOfUser($month, $year, $user_id){
        $firstOfMonth = Carbon::createFromFormat('m/Y', $month.'/'.$year)->firstOfMonth();
        $endOfMonth = Carbon::createFromFormat('m/Y', $month.'/'.$year)->endOfMonth();

        $vacation_days = ModelsRequest::select(
            'requests.id',
            'requests.content',
            'requests.created_by',
            'users.id as user_id'
        )->leftjoin('users', 'users.id', 'requests.created_by')
            ->where('users.id', $user_id)
            ->where('requests.type', ModelsRequest::TYPE_VACATION)
            ->where('requests.status', ModelsRequest::STATUS_CONFIRM)
            ->where(function($query)  use ($firstOfMonth){
                $query->whereRaw('JSON_EXTRACT(content, "$.time_start")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"')
                    ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")>="'. $firstOfMonth->format('Y-m-d 00:00:00').'"');
            })
            ->where(function($query)  use ($endOfMonth){
                $query->whereRaw('JSON_EXTRACT(content, "$.time_start")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"')
                    ->orwhereRaw('JSON_EXTRACT(content, "$.time_end")<="'. $endOfMonth->format('Y-m-d 23:59:59').'"');
            })->get();
        $arrVacationRequest = [];
        foreach($vacation_days as $key => $vacation_day){
            $time_start = Carbon::parse($vacation_day->content['time_start']);
            $time_end = Carbon::parse($vacation_day->content['time_end']);
            if($time_start->format('Y-m-d') < $firstOfMonth){
                for($i=1; $i<=$time_end->format('d'); $i++ ){
                    $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                    if($i==$time_start->format('d')){
                        $startTime = strtotime($time_start->format('H:i:s'));
                    }else{
                        $startTime = strtotime(MORNING_START);
                    }
                    if($i==$time_end->format('d')){
                        $endTime = strtotime($time_end->format('H:i:s'));
                    }else{
                        $endTime = strtotime(AFTERNOON_END);
                    }
                    $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);

                    $arrVacationRequest[$key][$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                }
            }elseif($time_end->format('Y-m-d') > $endOfMonth){
                for($i=$time_start->format('d'); $i<=$endOfMonth->format('d'); $i++ ){
                    $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                    if($i==$time_start->format('d')){
                        $startTime = strtotime($time_start->format('H:i:s'));
                    }else{
                        $startTime = strtotime(MORNING_START);
                    }
                    if($i==$endOfMonth->format('d')){
                        $endTime = strtotime($time_end->format('H:i:s'));
                    }else{
                        $endTime = strtotime(AFTERNOON_END);
                    }
                    $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                    $arrVacationRequest[$key][$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                }
            }
            elseif($time_end->format('Y-m-d') == $time_start->format('Y-m-d')){
                $startTime = strtotime($time_start->format('H:i:s'));
                $endTime = strtotime($time_end->format('H:i:s'));
                $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                $arrVacationRequest[$key][$time_start->format('Y-m-d')]=isset($arrVacationRequest[$time_start->format('Y-m-d')])?($arrVacationRequest[$time_start->format('Y-m-d')]+$hour):$hour;
            }
            else{
                for($i=$time_start->format('d'); $i<=$time_end->format('d'); $i++ ){
                    $dateKey = Carbon::createFromFormat('Y-m-d', $year.'-'.$month.'-'.$i)->format('Y-m-d');
                    if($i==$time_start->format('d')){
                        $startTime = strtotime($time_start->format('H:i:s'));
                    }else{
                        $startTime = strtotime(MORNING_START);
                    }
                    if($i==$time_end->format('d')){
                        $endTime = strtotime($time_end->format('H:i:s'));
                    }else{
                        $endTime = strtotime(AFTERNOON_END);
                    }
                    $hour = (new WorkingTimeManager())->getWorkTimeInOneDay($startTime,$endTime);
                    $arrVacationRequest[$key][$dateKey]=isset($arrVacationRequest[$dateKey])?($arrVacationRequest[$dateKey]+$hour):$hour;
                }
            }
        }
        return $arrVacationRequest;
    }

    /**
     * Get user time sheet
     */
    public function getUserTimeSheet($month, $userId)
    {
        $userManager = new UserManager();
        $dateFormatManager = new DateFormatManager();
        // $user = $userManager->getUserWithLeaveDay($userId, false)->first();

        // Get timekeeping in month of user
        $chooseMonth = $month ?: date('m/Y');
        $arrTime = explode('/', $chooseMonth);
        $month = $arrTime[0];
        $year = $arrTime[1];
        $timekeepingManager = new TimekeepingManager();
        $startDate = "01/{$month}/{$year}";
        $endDate = cal_days_in_month(CAL_GREGORIAN, $month, $year) . '/' . $month . '/' . $year;
        $startDate = Carbon::createFromFormat('d/m/Y', $startDate);
        $endDate = Carbon::createFromFormat('d/m/Y', $endDate);
        $userAttendances = $timekeepingManager->getUserAttendances([$userId], $startDate, $endDate)->get();
        $allDetailedCheckTimes = $this->getDetailedCheckTimesForMultipleUsers([$userId], $startDate, $endDate);
        $data = [];
        foreach ($userAttendances as $key => $attendance) {
            $data[] = $timekeepingManager->calcWorkingDuration($attendance);
            $data[$key]['durationTimekeeping'] = $data[$key]['duration'];
            unset($data[$key]['duration']);
            $data[$key]['checked_in'] = $dateFormatManager->dateFormatLanguage($data[$key]['checked_in'], 'H::i::s');
            $data[$key]['checked_out'] = $dateFormatManager->dateFormatLanguage($data[$key]['checked_out'], 'H::i::s');

            $otherCheckedTimes = $allDetailedCheckTimes[$userId][$data[$key]['date']] ?? [];

            if (empty($data[$key]['checked_in'])) {
                $data[$key]['check_in_location'] = null;
            }

            if (empty($data[$key]['checked_out'] )) {
                $data[$key]['check_out_location'] = null;
            }
            
            $data[$key]['other_checked_times'] = array_slice($otherCheckedTimes, 1, -1);
        }

        // Get holidays in month
        $dateHolidays = Holiday::query()
            ->select('name', 'choose_holidays')
            ->where('choose_holidays', 'LIKE', "%$month/$year%")
            ->get();
        $dataHolidays = [];
        if ($dateHolidays->isNotEmpty()) {
            foreach ($dateHolidays as $key => $date) {
                $dataHolidays[$key]['holiday_name'] = $date['name'];
                $dataHolidays[$key]['choose_holidays'] = $dateFormatManager->dateFormatLanguageArrayV2($date['choose_holidays'], true);
            }
        }

        // Get vacation requests of user
        $arrVacationRequest = $timekeepingManager->getVacationRequestOfUser($month, $year, $userId);

        return [$data, $dataHolidays, $arrVacationRequest];
    }

    /**
     * Add a record of user attendance.
     *
     * This function records the attendance of the user with user_id based on
     * the provided latitude and longitude. If the location is identified,
     * it saves the attendance record with metadata including the location
     * information. If the location is not found, it returns a response with
     * the current timestamp and a message indicating the location could not
     * be determined.
     *
     * @param \Illuminate\Http\Request $request Request object containing attendance data.
     * @param int $userId User id of the user to add attendance record.
     * @return array JSON response with the attendance check-in time
     *               and location, or an error message if the process fails.
     */
    public function createTimekeeping($userId, $request)
    {
        try {
            $checkedAt = Carbon::parse($request['info']['CreateTime'] ?? '')->format('Y-m-d H:i:s') 
                ?? Carbon::now();
            $checkedType = $request->checked_type ? intval($request->checked_type) : Attendance::CHECK_IN;
            $locationName = '';

            DB::beginTransaction();
            $attendance = new Attendance();
            $attendance->user_id = $userId;
            $attendance->checked_at = $checkedAt;
            $attendance->checked_type = $checkedType;

            if ($checkedType === Attendance::GPS_CHECK) {
                $latitude = $request->latitude ?? '';
                $longitude = $request->longitude ?? '';

                // get location from lat, lng
                if (isset($request->address)){
                    $locationName = $request->address;
                }else{
                    $locationName = GoogleMapService::getLocationName($latitude, $longitude);
                }
                // insert new attendance with checked_type is GPS record via GPS
                $attendance->metadata = json_encode([
                    "latitude" => $latitude,
                    "longitude" => $longitude,
                    "location" => $locationName
                ]);
            }

            $attendance->save();

            DB::commit();
            $result = [
                "checked_at" => $attendance->checked_at ? Carbon::parse($attendance->checked_at)->format('H:i:s') : '',
                "location" => $locationName,
            ];
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Get detailed check times for multiple users, excluding check-in and check-out times
     * 
     * @param array $listUserId Array of user IDs
     * @param string $startDate Start date in Y-m-d format
     * @param string $endDate End date in Y-m-d format
     * @return array Array of check times with details, excluding first and last check
     */
    public function getDetailedCheckTimesForMultipleUsers($listUserId, $startDate, $endDate)
    {
        $dateFormatManager = new DateFormatManager();
        $allAttendances = Attendance::select(
            'attendances.id',
            'attendances.user_id',
            'attendances.checked_at',
            DB::raw('TIME(attendances.checked_at) as check_time'),
            'attendances.checked_type',
            'attendances.checked_image',
            'attendances.metadata'
        )
        ->whereIn('attendances.user_id', $listUserId)
        ->whereBetween('attendances.checked_at', [$startDate, $endDate])
        ->orderBy('attendances.user_id', 'asc')
        ->orderBy('attendances.checked_at', 'asc')
        ->orderBy('id', 'asc')
        ->get();
    
        $result = [];
        foreach ($allAttendances as $attendance) {
            $date = Carbon::parse($attendance->checked_at)->format('Y-m-d');
            $key = $attendance->user_id . '_' . $date;
    
            if (!isset($result[$key])) {
                $result[$key] = [];
            }

            $result[$key][] = [
                'id' => $attendance->id,
                'check_time' => $attendance->check_time,
                'check_type' => $attendance->checked_type,
                'check_image' => $attendance->checked_image,
                'metadata' => $attendance->metadata
            ];
        }
    
        $finalResult = [];
        foreach ($result as $key => $data) {
            [$userId, $date] = explode('_', $key);
            $finalResult[$userId][$date] = array_map(function($item) use ($dateFormatManager, $date) {
                $metadata = json_decode($item['metadata'], true) ?? [];
                return [
                    'id' => $item['id'],
                    'check_time' => $dateFormatManager->dateFormatLanguage(
                        strtotime($date . ' ' . $item['check_time']),
                        'H::i::s'
                    ),
                    'check_type' => $item['check_type'],
                    'check_image' => $item['check_image'],
                    'location' => $metadata['location'] ?? '',
                ];
            }, $data);
        }
    
        return $finalResult;
    }

    /**
     * Get a list of nearby locations based on latitude and longitude.
     *
     * @param mixed $request The request object containing 'latitude' and 'longitude' parameters.
     * @return array List of formatted locations with latitude and longitude.
     */
    public function getListLocation($request)
    {
        try {
            $latitude = $request->latitude ?? '';
            $longitude = $request->longitude ?? '';
            $listLocation = GoogleMapService::getListLocation($latitude, $longitude);
            
            $result = [];
            foreach ($listLocation as $value) {
                $result[] = [
                    'formatted_address' => $value['formatted_address'] ?? '',
                    'latitude' => $value['geometry']['location']['lat'] ?? '',
                    'longitude' => $value['geometry']['location']['lng'] ?? '',
                ];
            }
            return $result;
        } catch (\Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
