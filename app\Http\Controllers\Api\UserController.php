<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\VerifyCodeRequest;
use App\Jobs\SendEmailForgotPassword;
use App\Logics\ActivationManager;
use App\Logics\UserManager;
use App\Models\Activation;
use App\Models\Project;
use App\Models\SiteSetting;
use App\Models\Role;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class UserController extends AbstractApiController
{
    /**
     * Get avatar user
     * @param $id
     * @return mixed
     */
    public function getAvatar($id)
    {
        $userManager = new UserManager();
        $image = $userManager->getImage($id, 'avatar', BEECHAT);
        return $image;
    }

    /**
     * Get list user
     * @param $id
     * @return mixed
     */
    public function getList(Request $request)
    {
        $userId = Auth::id();
        $users = User::select([
            'users.id', 
            'users.first_name', 
            'users.last_name',
            'users.email',
            'users.company_contract',
            'positions.name as position',
            ])
            ->leftJoin('positions', 'positions.id', 'users.position_id');
        if(!empty($request->project_id)){
            $project = Project::select('projects.id', 'projects.name')
            ->checkUserPermission($userId)
            ->find($request->project_id);
            $users = $project->members()->select(['users.id', 'users.first_name', 'users.last_name', 'users.email'])
                ->distinct();
        }
        $keyword_search = $request->keyword;
        // search by key word
        $users = $users->where(function($query) use($keyword_search) {
            $query->orwhere('users.id', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere('users.email', 'LIKE', '%'.$keyword_search.'%')
                ->orwhere(DB::raw('CONCAT_WS(" ",users.first_name,users.last_name)'), 'LIKE', '%'.$keyword_search.'%');
        });

        if ($request->role) {
            $users->join("model_has_roles", "model_has_roles.model_id", "users.id")
                ->where("model_has_roles.role_id", $request->role)
                ->distinct();
        }

        $users = $users->get();
        foreach ($users as &$user) {
            $user['avatar'] = route('api.v1.user.avatar',['id'=>$user->id]);
        }

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('language.success'),
            'data' => $users
        ], Response::HTTP_OK);
    }

    public function getUsersData(){
        $users = User::select(['id', 'first_name', 'last_name','name','birthday','department_id','started_at'])->get()->toArray();
        return response()->json($users, Response::HTTP_OK);
    }

    public function getUserForDevice(){
        $morningStart = SiteSetting::morningStart();
        $morningEnd = SiteSetting::morningEnd();
        $afternoonStart = SiteSetting::afternoonStart();
        $afternoonEnd = SiteSetting::afternoonEnd();
        $sql = '
            SELECT
                users.id,
                MIN( users.name ) NAME,
                CASE
                    WHEN MONTH ( users.birthday ) = MONTH ( NOW() ) AND DAY ( users.birthday ) = DAY ( NOW() ) THEN TRUE
                    ELSE FALSE
                END birth_day,
                CASE
                    WHEN late.late_times IS NOT NULL THEN late.late_times
                    ELSE 0
                END late_times,
                CASE
                    WHEN checkin_today.user_id IS NOT NULL THEN
                    TRUE ELSE FALSE
                END checked_in,
                CASE
                    WHEN DATE( last_checked_in.checked_at ) = DATE( NOW() )
                        OR (
                            TIME( last_checked_in.checked_at ) < "'.$morningEnd.'"
                            OR ( users.working_type = 0 AND ( TIME( last_checked_in.checked_at ) < "'.$afternoonEnd.'" ) )
                        ) THEN TRUE
                    ELSE FALSE
                END previous_checked_in,
                CASE
                    WHEN DATE( last_checked_out.checked_at ) = DATE( NOW() )
                        OR ( users.working_type = 1 AND TIME( last_checked_out.checked_at ) > "'.$afternoonEnd.'" )
                        OR (
                            users.working_type = 0
                            AND (
                                ( "'.$morningEnd.'" < TIME( last_checked_out.checked_at ) AND TIME( last_checked_out.checked_at ) < "'.$afternoonStart.'" )
                                OR TIME( last_checked_out.checked_at ) > "'.$afternoonEnd.'"
                            )
                        ) THEN TRUE
                    ELSE FALSE
                END previous_checked_out,
                CASE
                    WHEN users.department_id IS NOT NULL THEN users.department_id
                    ELSE 0
                END LEVEL,
                CASE
                    WHEN users.started_at = CURDATE() THEN TRUE
                    ELSE FALSE
                END first_day,
                users.banking_account
            FROM
                users
                LEFT JOIN (
                    SELECT
                        user_id,
                        COUNT( late_check ) late_times
                    FROM
                        (
                        SELECT
                            user_id,
                            MIN( checked_at ) AS late_check
                        FROM
                            attendances
                        WHERE
                            YEAR ( checked_at ) = YEAR (NOW())
                            AND MONTH ( checked_at ) = MONTH (NOW())
                        GROUP BY
                            user_id,
                            DAY ( checked_at )
                        ) daily_check_in
                    WHERE
                        ( "'.$morningStart.'" < TIME( late_check ) AND TIME( late_check ) < "'.$morningEnd.'" )
                        OR ( "'.$afternoonStart.'" < TIME( late_check ) AND TIME( late_check ) < "'.$afternoonEnd.'" )
                    GROUP BY
                        user_id
                ) AS late ON users.id = late.user_id
                LEFT JOIN ( SELECT DISTINCT attendances.user_id FROM attendances WHERE DATE( attendances.checked_at ) = DATE( NOW() ) ) checkin_today ON users.id = checkin_today.user_id
                LEFT JOIN ( SELECT user_id, MAX( checked_at ) checked_at FROM attendances GROUP BY user_id ) last_checked_out ON users.id = last_checked_out.user_id
                LEFT JOIN (
                    SELECT
                        attendances.user_id,
                        MIN( attendances.checked_at ) checked_at
                    FROM
                        attendances
                        INNER JOIN ( SELECT user_id, MAX( checked_at ) checked_at FROM attendances GROUP BY user_id ) last_checked_out ON attendances.user_id = last_checked_out.user_id
                    WHERE
                        DATE( attendances.checked_at ) = DATE( last_checked_out.checked_at )
                    GROUP BY
                        user_id
                ) last_checked_in ON users.id = last_checked_in.user_id
            WHERE
                users.deleted_at IS NULL
            GROUP BY
                users.id';
        $users = DB::select($sql);
        return response()->json($users, Response::HTTP_OK);
    }

    /**
     * get user login
     *
     * @return  json
     */
    public function getUserLogin(){
        $userAuth = Auth::user();
        $userRole =  $userAuth->roles->pluck('id')->toArray();
        // dd($userRole);
        $userRole =  count($userRole) > 0 ? $userRole : [Role::ROLE_EMPLOYEE];
        $vehicleTypes = SiteSetting::select('value')->where('id', SiteSetting::VEHICLE_TYPE_ID)->first();
        $vehicleTypes = json_decode($vehicleTypes->value, true);
        $user = User::where('users.id', $userAuth->id)
                        ->leftJoin('languages', 'users.language_id', 'languages.id')
                        ->leftJoin('work_places', 'users.work_place_id', 'work_places.id')
                        ->leftJoin('positions', 'users.position_id', 'positions.id')
                        ->leftJoin('departments', 'users.department_id', 'departments.id')
                        ->select([
                            'users.id',
                            'languages.display_name as language',
                            'users.first_name as first_name',
                            'users.avatar as avatar',
                            'users.last_name as last_name',
                            'users.address as address',
                            'users.email as email',
                            'work_places.name as work_location',
                            'departments.name as department',
                            'positions.name as position',
                            DB::raw('CONCAT(users.last_name, users.first_name) as full_name'),
                            ])
                        ->first();
        $roles = Role::whereIn('id',$userRole)->orderBy('id', 'ASC')->get();

        $user-> isAdmin = $userAuth->hasRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_OPERATION_MANAGER]);
        if (env('USE_TENANT', false)) {
            $user->website_id = app(\Hyn\Tenancy\Environment::class)->tenant()->id;
        }
        return $user;
    }
    
    public function getListById(Request $request){
        $users = User::select('id', DB::raw('CONCAT_WS(" " , first_name, last_name) as user_name'))->withTrashed()->whereIn('id',$request->post())->pluck('user_name','id');
        return response()->json($users, Response::HTTP_OK);
    }

    /**
     * update user info
     *
     * @param   UpdateUserRequest  $request
     *
     * @return  json
     */
    public function update(UpdateUserRequest $request){
        $user = Auth::user();
        $param = !empty($request->input('language')) ? [
            'language_id' => $request->input('language')
        ] : [];
        $avatar = $request->hasFile('avatar') ? $request->file('avatar') : null;
        //--- 1. Update user in database ---
        $userManager = new UserManager();
        $userManager->updateUserProfile(
            $user,
            $param,
            $avatar,
            null
        );
        return $this->renderJsonResponse($user, __('message.user.update.success'));
    }

    /**
     * Save device token by user
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function saveDeviceTokenByUser(Request $request)
    {
        try {
            $deviceToken = $request->get('device_token');
            if (empty($deviceToken)) {
                $msg = __('language.failure');
                return $this->respondBadRequest($msg);
            }
            $userManager = new UserManager();
            $userDevice = $userManager->saveDeviceTokenByUser($deviceToken);
            $msg = __('language.success');
            return $this->renderJsonResponse($userDevice, $msg);
        } catch (Exception $e) {
            Log::error("[UserController][saveDeviceTokenByUser] Line:" . $e->getLine() . ' error:' . $e->getMessage());
            throw new Exception('[UserController][saveDeviceTokenByUser]  Line' . $e->getLine() . ' error' . $e->getMessage());
        }
    }

    /**
     * Delete device token
     * @param $deviceToken
     * @return json
     * @throws Exception
     */
    public function deleteDeviceToken($deviceToken)
    {
        if (empty($deviceToken)) {
            return $this->respondBadRequest(__('language.failure'));
        }
        $userManager = new UserManager();
        $status = $userManager->deleteDeviceToken($deviceToken, Auth::id());
        return $this->renderJsonResponse($status, __('language.success'));
    }

    /**
     * Forgot password
     * @param ForgotPasswordRequest $request
     * @return json
     * @throws Exception
     */
    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            DB::beginTransaction();
            $username = $request->input('username');

            $userManager = new UserManager();
            $user = $userManager->getUserByEmail($username);
            if (empty($user)) {
                return $this->respondBadRequest(__('language.failure'));
            }

            $activationManager = new ActivationManager();
            $checkLimitSendCode = $activationManager->checkLimitSendCode($user->id);
            if (!$checkLimitSendCode) {
                return $this->respondWithError(__('message.forgot_password.send_code_limit'));
            }

            $code = $activationManager->generateCode();
            $token = null;

            if ($user->email === $username) {
                $websiteId = null;
                if (env('USE_TENANT', false)) {
                    $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                }
                dispatch(new SendEmailForgotPassword($user, $code, $websiteId))->onQueue(QUEUE_MAIL);
            } else {
                $token = $activationManager->generateToken();
            }
            $activationManager->createActivation($user->id, $code, $token);

            DB::commit();
            return $this->renderJsonResponse([
                'token' => $token,
            ], __('language.success'));
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Verify code
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function verifyCode(VerifyCodeRequest $request)
    {
        try {
            DB::beginTransaction();
            $email = $request->input('email');
            $code = $request->input('code');

            $user = User::select('id')->where('email', $email)->first();
            if (empty($user)) {
                return $this->respondBadRequest(__('language.failure'));
            }

            $activationManager = new ActivationManager();
            $activation = $activationManager->findActivationByUserId($user->id);
            if (empty($activation) || $activation->code !== $code) {
                return $this->respondWithError(__('message.forgot_password.code_failed'));
            }

            if (Carbon::parse($activation->expired_time)->lt(Carbon::now())) {
                return $this->respondWithError(__('message.forgot_password.code_expired'));
            }

            $activation->token = $activationManager->generateToken();
            $activation->save();

            $data = [
                'email' => $email,
                'token' => $activation->token,
            ];
            DB::commit();
            return $this->renderJsonResponse($data, __('language.success'));
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Reset password
     * @param ResetPasswordRequest $request
     * @return json
     * @throws Exception
     */
    public function resetPassword(ResetPasswordRequest $request)
    {
        try {
            DB::beginTransaction();
            $token = $request->input('token');
            $password = $request->input('password');

            $activationManager = new ActivationManager();
            $activation = $activationManager->findActivationByToken($token);
            if (empty($activation)) {
                return $this->respondBadRequest(__('language.failure'));
            }
            $activation->completed = Activation::ACTIVATION_COMPLETED;
            $activation->completed_at = Carbon::now();
            $activation->save();
            User::find($activation->user_id)->update([
                'password' => Hash::make($password),
            ]);

            DB::commit();
            return $this->renderJsonResponse(true, __('language.success'));
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
