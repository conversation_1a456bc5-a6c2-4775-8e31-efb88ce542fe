<?php

namespace App\Services;

use App\Models\ProjectMember;
use Illuminate\Support\Facades\DB;

class ProjectMemberService
{
    public function getProjects($userId)
    {
        return ProjectMember::select(
                DB::raw("GROUP_CONCAT(project_roles.name SEPARATOR ', ') AS roleName"),
                'project_members.project_id AS project_id', 'projects.name AS name', 'projects.deleted_at',
                DB::raw('MIN(project_members.created_at) AS created_at')
            )
            ->join('projects', 'project_members.project_id', 'projects.id')
            ->whereRaw(DB::raw('project_members.project_id in (Select project_id from project_members where user_id = ' . $userId . ')'))
            ->where('project_members.user_id', $userId)
            ->where('projects.deleted_at', null)
            ->join('project_roles', 'project_roles.id', 'project_members.role_id')
            ->groupBy('project_members.project_id')
            ->get();
    }
    
    public function getProjectIds($userId): array
    {
        $projects = $this->getProjects($userId);
        $projectIds = [];
        foreach ($projects as $project) {
            $projectIds[] = $project->project_id;
        }
        return $projectIds;
    }
}