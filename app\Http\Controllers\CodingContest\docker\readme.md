### Run docker

```
docker build -t ntcd_php -f php/Dockerfile .
docker build -t ntcd_javascript -f javascript/Dockerfile .
docker build -t ntcd_java -f java/Dockerfile .
docker build -t ntcd_python -f python/Dockerfile .
docker run --rm -v /home/<USER>/Projects/CodingContest/code-challenge/docker:/tmp -v /home/<USER>/Projects/CodingContest/code-challenge/challenges/challenge1/testcase:/tmp/testcase ntcd_python python /tmp/main.py /tmp/testcase /tmp/output
```