<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LoginFormRequest;
use App\Models\UserDevice;
use App\Services\MessageToCounterService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Http\Response;
use App\Repositories\CachedTokenRepository;


class AuthController extends Controller
{
    /**
     * Verify login by user
     *
     * @param  LoginFormRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginFormRequest $request)
    {
        $userName = $request->input('email');
        $loginType = filter_var($userName, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';
        
        $credentials = [
            $loginType => $userName,
            'password' => $request->input('password')
        ];
        if (!Auth::attempt($credentials)) {
            return response()->json([
                'code' => Response::HTTP_FORBIDDEN,
                'message' => trans('message.error_login')
            ],
            Response::HTTP_FORBIDDEN);
        }
        $user = Auth::user();
        $tokenResult = $user->createToken('Personal Access Token');
        $token = $tokenResult->token;

        if ($request->remember_me) {
            $token->expires_at = Carbon::now()->addHours(24);
        }

        $token->save();

        // Save token device
        if (isset($request->device_token)) {
            $this->deleteUserDevice($request->device_token);
            $userDevice = new UserDevice();
            $userDevice->user_id = Auth::id();
            $userDevice->device_information = $request->device_information;
            $userDevice->device_token = $request->device_token;
            $userDevice->status = UserDevice::STATUS_ONLINE;
            $userDevice->last_connection = Carbon::now();
            $userDevice->save();
        }

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => __('language.success'),
            'data' => [
                'access_token' => $tokenResult->accessToken,
                'user_id' => $user->id,
                'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'language_id' => $user->language_id,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => route('api.v1.user.avatar', ['id' => $user->id]),
                'roles' => $user->roles->pluck('name'),
                'expires_at' => Carbon::parse(
                    $tokenResult->token->expires_at
                )->toDateTimeString(),
            ]
        ], Response::HTTP_OK);
    }
    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        if (isset($request->device_token)){
            $this->deleteUserDevice($request->device_token);
        }

        // Revoke the token that was used to authenticate the user
        app(CachedTokenRepository::class)->revokeAccessToken($request->user()->token()->id);

        return response()->json(
            [
                'code'=> Response::HTTP_OK,
                'message' => __('message.success'),
            ]
        );
    }

    /**
     * Delete user device.
     * 
     * @param $deviceToken
     * @return void
     */
    private function deleteUserDevice($deviceToken)
    {
        UserDevice::where('user_id', Auth::id())
            ->where('device_token', $deviceToken)
            ->delete();
    }
}
