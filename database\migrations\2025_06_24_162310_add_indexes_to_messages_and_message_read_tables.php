<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexesToMessagesAndMessageReadTables extends Migration
{
    public function up(): void
    {
        Schema::table('message_read', function (Blueprint $table) {
            $table->index('message_id');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->index('conversation_id');
        });
    }

    public function down(): void
    {
        Schema::table('message_read', function (Blueprint $table) {
            $table->dropIndex('message_read_message_id_index');
        });

        Schema::table('messages', function (Blueprint $table) {
            $table->dropIndex('messages_conversation_id_index');
        });
    }
}
