<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Helpers\NotificationHelper;
use App\Helpers\StringHelper;
use App\Jobs\Notification\sendNotificationConversation;
use App\Models\Contacts;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\Message;
use App\Models\MessageBookmark;
use App\Models\MessageRead;
use App\Services\EncryptService;
use App\Traits\ImageTrait;
use App\Traits\StorageTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ConversationManager
{
    use ImageTrait;
    use StorageTrait;

    const NOTIFICATION_CONVERSATION = 'create_new_conversation';
    protected $encryptService;

    public function __construct() {
        $this->encryptService = new EncryptService();
    }

    /**
     * Query to get the list of conversations for a user
     * @param int $idUser
     * @param \Illuminate\Http\Request $request
     * @param array|null $conversationIds
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function queryGetListConversationOfUser($idUser, $conversationType, $conversationIds = null)
    {
        $conversation = ConversationParticipant::query()
            ->join("conversations", function ($qr) use ($conversationType) {
                $qr->on("conversations.id", 'conversation_participants.conversation_id');

                if ($conversationType != Conversation::FILTER_WITH_HIDDEN) {
                    $qr = $qr->where('conversations.is_hide', Conversation::NOT_HIDE);
                }
            })
            ->leftJoin("conversation_pins", function ($join) use ($idUser) {
                $join->on('conversations.id', '=', 'conversation_pins.conversation_id')
                    ->where('conversation_pins.user_id', $idUser);
            })
            ->where('conversation_participants.user_id', $idUser)
            ->select([
                'conversation_participants.id',
                'conversation_participants.conversation_id',
                'conversation_participants.updated_at',
                'conversations.name',
                'conversations.type',
                'conversations.avatar',
                'conversation_participants.is_mute',
                'conversation_participants.admin',
                DB::raw('CASE WHEN conversation_pins.id is null THEN 0 ELSE 1 END as conversation_pin'),
            ]);

        if ($conversationType == Conversation::FILTER_WITH_HIDDEN) {
            $conversation->addSelect('conversations.is_hide');
        }
        
        if (!empty($conversationIds)) {
            $conversation->whereIn('conversation_participants.conversation_id', $conversationIds);
        }
        return $conversation;
    }
    
    /**
     * Query to get the last message in conversation
     * @param int $idUser
     * @param \Illuminate\Database\Eloquent\Builder $conversation
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function queryGetLastMessageInConversation($idUser, $conversation)
    {
        $query = Message::query()
                ->leftJoin('message_read', function ($join) use ($idUser) {
                    $join->on('message_read.message_id', '=', 'messages.id')
                        ->where('message_read.user_id', '=', $idUser);
                })
                ->rightJoinSub($conversation, 'conversation_account', function ($join) {
                    $join->on('messages.conversation_id', '=', 'conversation_account.conversation_id');
                })
                // Join with a subquery that finds the max read message ID for conversation in table message_read
                ->leftJoin(DB::raw('(SELECT conversation_id, MAX(message_id) AS max_message_read_id
                    FROM message_read
                    WHERE thread_id IS NULL AND user_id = ' . $idUser . '
                    GROUP BY conversation_id) AS max_message_read'), function ($join) {
                    $join->on('messages.conversation_id', '=', 'max_message_read.conversation_id');
                })
                ->whereRaw('(messages.reply_id IS NULL OR is_sent_group = 1)')
                ->select([
                    DB::raw('MAX(messages.id) as message_id'),
                    DB::raw('CASE WHEN conversation_account.type = 1 THEN 0 ELSE 
                            COUNT(CASE WHEN messages.id > IFNULL(max_message_read.max_message_read_id, 0) AND messages.user_id != ' . $idUser . ' THEN 1 ELSE NULL END) END as message_count_unread'),
                    DB::raw('CASE WHEN conversation_account.type = 1 THEN 0 ELSE COUNT(CASE WHEN messages.id > IFNULL(max_message_read.max_message_read_id, 0) AND messages.user_id != ' . $idUser . ' AND JSON_CONTAINS(COALESCE(remind_users, "[]"), "' . $idUser . '") THEN 1 ELSE NULL END) END as message_to_unread'),
                    DB::raw('MAX(CASE WHEN messages.user_id != ' . $idUser . ' AND JSON_CONTAINS(COALESCE(remind_users, "[]"), "' . $idUser . '") THEN messages.id ELSE NULL END) as last_message_id_to'),
                    'conversation_account.conversation_id',
                    'conversation_account.updated_at',
            ])
            ->groupBy('conversation_id');

        return $query;
    }

    public function listConversation($idUser, $request){
        try {
            $perPage = is_numeric($request->get('per_page')) ? (int)$request->get('per_page') : config('constants.perPage');
            $conversationId = $request->get('conversation_id');
            // Lấy danh sách cuộc trò chuyện của user
            $conversation = $this->queryGetListConversationOfUser($idUser, $request->type);

            $listAccount = ConversationParticipant::query()
                ->leftJoin('users', function ($join) {
                    $join->on('conversation_participants.user_id', '=', 'users.id');
                })
                ->joinSub($conversation, 'conversation_account', function ($join) use ($idUser) {
                    $join->on('conversation_participants.conversation_id', '=', 'conversation_account.conversation_id')
                            ->where(function ($qr) use ($idUser) {
                                $qr->where(function ($subqr) use ($idUser) {
                                    $subqr->where('conversation_account.type', '=', Conversation::TYPE_TWO_USER)
                                        ->where('conversation_participants.user_id', '!=', $idUser);
                                })->orWhere('conversation_account.type', '!=', Conversation::TYPE_TWO_USER);
                            });
                })
                ->select([
                    DB::raw('
                        (CASE
                            WHEN conversation_account.type = ' . Conversation::TYPE_MY_CHAT . ' THEN users.avatar
                            WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN users.avatar
                            ELSE conversation_account.avatar
                        END)
                        AS conversation_avatar'
                    ),
                    DB::raw('
                        (CASE
                            WHEN conversation_account.type = ' . Conversation::TYPE_MY_CHAT . ' THEN "My chat"
                            WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN IFNULL(CONCAT_WS(" ", users.first_name, users.last_name),"")
                            ELSE conversation_account.name
                        END)
                        AS conversation_name'
                    ),
                    DB::raw('
                        (CASE
                            WHEN conversation_account.type = ' . Conversation::TYPE_MULTI_USER . ' THEN COUNT(conversation_participants.user_id)
                            ELSE NULL
                        END)
                        AS total_members_in_conversation'
                    ),
                    DB::raw('GROUP_CONCAT(conversation_participants.user_id SEPARATOR "⁕ ") AS group_conversation_id'),
                    'conversation_participants.conversation_id',
                    'conversation_account.conversation_pin',
                    'conversation_account.is_mute',
                    'conversation_account.type',
                    'conversation_account.admin'
                ]);

                if ($request->type == Conversation::FILTER_WITH_HIDDEN) {
                    $listAccount->addSelect('conversation_account.is_hide')
                    ->addSelect(DB::raw('
                       (CASE
                            WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN users.id
                        END)
                        AS contact_id'
                    ));
                }

            $listAccount = $listAccount->groupBy('conversation_participants.conversation_id');
            //Lấy tin nhắn cuối cùng của user
            $message = $this->queryGetLastMessageInConversation($idUser, $conversation);
            $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
            $messageSub = Message::query()
                ->rightJoinSub($message, 'message_conversation', function ($join) {
                    $join->on('message_conversation.message_id', '=', 'messages.id');
                })
                ->select([
                    'message_conversation.conversation_id',
                    'messages.id',
                    DB::raw($decryptedContentText),
                    'messages.content_file',
                    'messages.user_id',
                    'messages.created_at',
                    'message_conversation.message_count_unread',
                    'message_conversation.message_to_unread',
                    'message_conversation.last_message_id_to',
                ]);
            $data = ConversationParticipant::query()
                ->leftJoinSub($messageSub, 'message_sub', function ($join) {
                    $join->on('message_sub.conversation_id', '=', 'conversation_participants.conversation_id');
                })
                ->joinSub($listAccount, 'list_account', function ($join) {
                    $join->on('list_account.conversation_id', '=', 'conversation_participants.conversation_id');
                });
            $type = $request->get('type');
            if (!empty($type)) {
                switch ((int) $type) {
                    case Conversation::FILTER_UNREAD:
                        $data = $data->where('message_sub.message_count_unread', '>', 0);
                        break;
                    case Conversation::FILTER_TO_UNREAD:
                        $data = $data->where('message_sub.message_to_unread', '>', 0);
                        break;
                    case Conversation::FILTER_TWO_USER:
                        $data = $data->where('list_account.type', '=', Conversation::TYPE_TWO_USER);
                        break;
                    case Conversation::FILTER_GROUP_CHAT:
                        $data = $data->where('list_account.type', '=', Conversation::TYPE_MULTI_USER);
                        break;
                    case Conversation::FILTER_MUTED:
                        $data = $data->where('list_account.is_mute', '=', ConversationParticipant::MUTE);
                        break;
                    default:
                        # code...
                        break;
                }
            }
            $data = $data->select([
                    'conversation_participants.conversation_id',
                    'message_sub.id as message_id',
                    'message_sub.user_id',
                    'message_sub.content_text',
                    'message_sub.content_file',
                    DB::raw('( CASE WHEN message_sub.message_count_unread is not null
                            THEN message_sub.message_count_unread
                            ELSE 0 END
                        ) as message_count_unread'),
                    DB::raw('( CASE WHEN message_sub.message_to_unread is not null
                        THEN message_sub.message_to_unread
                        ELSE 0 END
                        ) as message_to_unread'),
                    'message_sub.last_message_id_to',
                    'list_account.conversation_avatar',
                    'list_account.conversation_name',
                    'list_account.conversation_pin',
                    'list_account.is_mute',
                    DB::raw('(CASE WHEN (message_sub.user_id =' . $idUser . ') THEN 1 ELSE 0 END) AS message_type_user'),
                    DB::raw('( CASE WHEN DATEDIFF(CURDATE(), message_sub.created_at) = 0
                        THEN TIME_FORMAT(message_sub.created_at, "%h:%i %p") 
                        WHEN YEAR(CURDATE()) - YEAR(message_sub.created_at) = 0
                        THEN DATE_FORMAT(message_sub.created_at,"%d/%m")
                        ELSE DATE_FORMAT(message_sub.created_at,"%d/%m/%Y") END
                        ) as time_send_message'),
                    'message_sub.created_at as time_send_message_default',
                    'list_account.type',
                    'list_account.total_members_in_conversation',
                    'list_account.admin AS is_admin'
                ]);

                if ($request->type == Conversation::FILTER_WITH_HIDDEN) {
                    $data->addSelect("list_account.contact_id")
                        ->addSelect("list_account.is_hide");
                }

                if ($conversationId) {
                    $data->where('conversation_participants.conversation_id', $conversationId);
                }

                $data = $data->groupBy('conversation_participants.conversation_id')
                ->orderByDesc('list_account.conversation_pin')
                ->orderByDesc('message_sub.created_at')
                ->orderByDesc('conversation_participants.conversation_id');

            if ($request->get('conversation_name') != null) {
                $stringHelper = new StringHelper();
                $data = $data->having('conversation_name', 'like', '%' . $stringHelper->formatStringWhereLike($request->get('conversation_name')) . '%');
            }

            $data = $data->paginate($perPage);
            //Handle create invite link
            if ($data->total() > 0) {
                $data->each(function($conversation) {
                    if ($conversation->type === Conversation::TYPE_MULTI_USER) {
                        $encryptedConversation = $this->cryptConversationId($conversation->conversation_id);
                        $conversation->invite_link = route('api.v1.invitationLink', ['encryptedConversationId' => $encryptedConversation]);
                        $conversation->encrypt_conversation_id = $encryptedConversation;
                    }
                });
            }
            return $data;
        } catch (Exception $e) {
            Log::error("[ConversationService][listConversation] error " . $e->getMessage());
            throw new Exception('[ConversationService][listConversation] error ' . $e->getMessage());
        }
    }
    
    /**
     * check permission message
     * @param $id
     * @param $idUser
     * @param string[] $column
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */

    /**
     * create message
     * @param $conversationId
     * @param $userId
     * @param $request
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function store($userId, $request)
    {
        $result = null;
        try {
            DB::beginTransaction();
            if ($request->has('avatar')) {
                $file = $request->file('avatar');
                $avatar = $this->resizeImage($file->getRealPath(), CONVERSATION_AVATAR_WIDTH);
                $extension = strtolower($file->getClientOriginalExtension());
                $avatarPath = $this->uploadFileByStream($avatar, CONVERSATION_DIR . time() . Str::random(5) . '.' . $extension);
            }
            $paramUser = $request->input('users');
            $paramType = $request->input('type');
            $result = Conversation::create([
                'name' => $request->input('name'),
                'type' => $paramType,
                'avatar' => !empty($avatarPath) ? $avatarPath : null,
                'is_hide' => $paramType == Conversation::TYPE_TWO_USER ? Conversation::HIDE : Conversation::NOT_HIDE,
                'desciption' => $request->input('desciption')
            ]);
            if (!empty($result['id'])) {
                $dataInsert = array(
                    array(
                        'conversation_id' => $result['id'],
                        'user_id' => $userId,
                        'admin' => 1,
                        'status' => 1,
                        'created_at' => Carbon::now()
                    )
                );
                if (!empty($paramUser)) {
                    $users = json_decode($paramUser);
                    if ((int) $paramType === Conversation::TYPE_TWO_USER) {
                        if ((int) $users[0][0] !== $userId) {
                            $dataInsert[] = array(
                                'conversation_id' => $result['id'],
                                'user_id' => (int) $users[0][0],
                                'admin' => (int) $users[0][1],
                                'status' => 1,
                                'created_at' => Carbon::now()
                            );
                        }
                    }
                    if ((int) $paramType === Conversation::TYPE_MULTI_USER) {
                        foreach ($users as $user) {
                            if ((int) $user[0] !== $userId) {
                                $dataInsert[] = array(
                                    'conversation_id' => $result['id'],
                                    'user_id' => (int) $user[0],
                                    'admin' => (int) $user[1],
                                    'status' => 1,
                                    'created_at' => Carbon::now()
                                );
                            }
                        }
                    }
                }
                ConversationParticipant::insert($dataInsert);
                // Gửi thông báo qua firebase
                $dataNotification = [
                    'title' => $request->input('name'),
                    'content' => trans('message.conversation.notification.join'),
                    'avatar' => $result->avatar,
                    'notification_type' => NotificationHelper::NOTIFICATION_TYPE_CONVERSATION,
                    'id' => $result->id,
                ];
                $websiteId = null;
                if (env('USE_TENANT', false)) {
                    $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
                }
                dispatch(new sendNotificationConversation($dataNotification, $dataInsert, $websiteId))->onQueue(config('queue.queueType.conversation'));
                
                // Add new messaga when the conversation created successfully
                $dataContentText = trans('message.conversation.message_created_conversation');
                $encryptedContentText = $this->encryptService->encryptedData($dataContentText);
                $newMessage = Message::query()->create([
                    'user_id' => $userId,
                    'conversation_id' => $result->id,
                    'content_text' => $encryptedContentText,
                ]);
                
                //Emit event newConversation
                $allMembers =  array_merge(json_decode($paramUser),[[$userId, 1]]);
                $admins = [];
                $members = [];
                //Generate invite link
                $encryptedConversation = $this->cryptConversationId($result->id);
                $inviteLink = route('api.v1.invitationLink', ['encryptedConversationId' => $encryptedConversation]);
                $dataSocketNewConversation = [
                    'conversation_id'               => $result->id,
                    'message_id'                    => $newMessage->id,
                    'user_id'                       => $userId,
                    'content_text'                  => $dataContentText,
                    'content_file'                  => $newMessage->file,
                    'message_count_unread'          => 1,
                    'message_to_unread'             => 0,
                    'conversation_avatar'           => $result->avatar,
                    'conversation_name'             => $result->name,
                    'conversation_pin'              => 0,
                    'is_mute'                       => ConversationParticipant::NOT_MUTE,
                    'message_type_user'             => 1,
                    'time_send_message'             => $newMessage->created_at->format("H:i A"),
                    'time_send_message_default'     => $newMessage->created_at->format('Y-m-d H:i:s'),
                    'type'                          => intval($result->type),
                    'total_members_in_conversation' => count($allMembers),
                    'most_recent_message'           => $dataContentText,
                    'invite_link'                   => $inviteLink,
                    'encrypt_conversation_id'       => $encryptedConversation
                    ];
                    
                $socketManager = new SocketManager();
                foreach ($allMembers as $item) {
                    if ($item[1] === 1) {
                        $admins['receiver'][] = $item[0];
                        $admins['is_admin'] = 1;
                    }else{
                        $members['receiver'][] = $item[0];
                        $members['is_admin'] = 0;
                    }
                }
                $socketManager->emit(SocketEvent::NEW_CONVERSATION, array_merge($dataSocketNewConversation,$admins));
                $socketManager->emit(SocketEvent::NEW_CONVERSATION, array_merge($dataSocketNewConversation,$members));
            }
            DB::commit();
            return $result;
        } catch (Exception $e) {
            Log::error('[ConversationManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
            DB::rollBack();
            throw new Exception('[ConversationManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }
    /**
     * delete conversation
     * @param $conversationId
     * @param $userId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function leaveConversation($conversationId, $userId)
    {
        try {
            $checkPermission = $this->checkPermissionConversation($conversationId, $userId);
            if(empty($checkPermission)){
                return ['status_error' => Message::ERROR_PERMISSION];
            };
            $conversation = Conversation::query()->with('list_user:users.id')
                                            ->where('id', $conversationId)
                                            ->first();
            if(empty($conversation) || $conversation->type == Conversation::TYPE_MY_CHAT) {
                return ['status_error' => Message::ERROR_DATA];
            };
            DB::beginTransaction();
            $listIdUser = [];
            $event = SocketEvent::DELETE_CONVERSATION;
            $conversationType = $conversation->type;
            if($conversation->type == Conversation::TYPE_TWO_USER){
                foreach($conversation->list_user as $user){
                    $listIdUser[] = $user['id'];
                }
                ConversationParticipant::whereIn('user_id', $listIdUser)
                                            ->where('conversation_id', $conversationId)
                                            ->delete();
            }else if($conversation->type == Conversation::TYPE_MULTI_USER){
                $canOutConversation = true;
                // Check if the current user is an admin
                if($checkPermission->admin === ConversationParticipant::IS_ADMIN){
                    // Only allow deletion if there is another admin in the conversation
                    $canOutConversation = ConversationParticipant::where('conversation_id', $conversationId)
                                            ->where('user_id', '!=', $userId)
                                            ->where('admin', ConversationParticipant::IS_ADMIN)
                                            ->exists();
                };

                if($canOutConversation){
                    $listIdUser[] = $userId;
                    $event = SocketEvent::OUT_CONVERSATION;
                    // Add new messages when user out conversation.
                    $user = Auth::guard('api')->user();
                    $request = new Request();
                    $request->replace([
                        'content_text' => "[Noti:{$user->id}-{$user->full_name}" . trans('message.conversation.notification.outConversation') . "]",
                        'reply_id' => null
                    ]);
                    $messageManager = new MessageManager();
                    $messageManager->store($conversationId, $user, $request);

                    // Remove the user from the conversation
                    ConversationParticipant::where('user_id', $userId)
                                            ->where('conversation_id', $conversationId)
                                            ->delete();
                    //Emit event socket - updateMembersInConversation
                    $conversationParticipantManager = new ConversationParticipantManager();
                    $conversationParticipantManager->sendNotificationSocketUpdateMembersInConversation(intval($conversationId));
                }else{
                    return ['status_error' => ConversationParticipant::ERROR_EMPTY_ADMIN];
                };
            };
            //delete bookmark message
            MessageBookmark::query()
                ->join('messages', 'messages.id', '=', 'message_bookmarks.message_id')
                ->where('messages.conversation_id', $conversationId)
                ->whereIn('message_bookmarks.user_id', $listIdUser)
                ->delete();
            
            DB::commit();
            if(!empty($listIdUser)){
                // Send socket delete/out conversation.
                app(SocketManager::class)->emit($event, [
                    'conversation_id' => $conversationId,
                    'conversation_type' => $conversationType,
                    "receiver" => $listIdUser,
                    'delete_by'=> $userId
                ]);
            }
            return true;
        } catch (Exception $e) {
            Log::error("[ContactManager][leaveConversation] line " . $e->getLine() . " error " . $e->getMessage());
            DB::rollBack();
            throw new Exception("[ContactManager][leaveConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
    public static function checkPermissionConversation($id, $idUser, $CheckRole = null, $checkType = null)
    {
        $ConversationParticipant = ConversationParticipant::query()
            ->where('conversation_participants.conversation_id', $id)
            ->where('conversation_participants.user_id', $idUser);
        if(!empty($CheckRole)){
            $ConversationParticipant->where('conversation_participants.admin', ConversationParticipant::IS_ADMIN);
        }
        if(isset($checkType)){
            $ConversationParticipant->join('conversations', function ($qr) use ($checkType) {
                $qr->on('conversations.id', 'conversation_participants.conversation_id')
                    ->where('conversations.type', $checkType);
            });
        }
        $ConversationParticipant = $ConversationParticipant->first();
        return $ConversationParticipant;
    }

    /**
     * update conversation
     * @param $conversationId
     * @param $userId
     * @param $request
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function updateConversation($conversationId, $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $checkPermission = $this->checkPermissionConversation($conversationId, $user->id);
            if (empty($checkPermission)) {
                return ['status_error' => ConversationParticipant::ERROR_NOT_FOUND];
            }
            if(ConversationParticipant::IS_ADMIN != $checkPermission->admin){
                return ['status_error' => ConversationParticipant::ERROR_PERMISSION];
            };
            $conversation = Conversation::findOrFail($conversationId);
            DB::beginTransaction();
            if ($request->has('avatar')) {
                $file = $request->file('avatar');
                $avatar = $this->resizeImage($file->getRealPath(), CONVERSATION_AVATAR_WIDTH);
                $extension = strtolower($file->getClientOriginalExtension());
                $avatarPath = $this->uploadFileByStream($avatar, CONVERSATION_DIR . time() . Str::random(5) . '.' . $extension);
                $conversation->avatar = $avatarPath;
            }
            $conversation->name = $request->name;
            // If My Chat then don't update conversation type
            if ($conversation->type !== Conversation::TYPE_MY_CHAT) {
                $conversation->type = $request->type;
            }
            $conversation->desciption = $request->desciption;
            $conversation->save();

             // Add new messages change description
             $request = new Request();
             $request->replace([
                'content_text' => trans('message.conversation.notification.changeDescription'),
                'reply_id' => null
             ]);
             $messageManager = new MessageManager();
             $messageManager->store($conversationId, $user, $request);

            //Emit event updateConversation
            if ((int)$conversation->type === Conversation::TYPE_MULTI_USER) {
                $listKeyUpdateConversation = [
                    'conversation_avatar',
                    'conversation_name'
                ];
                $this->sendNotificationSocketUpdateConversation($conversationId, [], $listKeyUpdateConversation);
            }
            DB::commit();
            return $conversation;
        } catch (Exception $e) {
            Log::error('[ConversationManager][updateConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
            DB::rollBack();
            throw new Exception('[ConversationManager][updateConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * mute message
     * @param $conversationId
     * @param $userId
     * @return array
     * @throws Exception
     */
    public function muteConversation($userId, $conversationId) {
        try {
            $conversationCheck = $this->checkPermissionConversation($conversationId, $userId);
            if (!$conversationCheck) {
                return [['status_error' => Response::HTTP_FORBIDDEN], 'Tắt thông báo'];
            }
            $conversation = ConversationParticipant::query()
                        ->where('conversation_participants.conversation_id', $conversationId)
                        ->where('conversation_participants.user_id', $userId);
            $actionMsg = __('message.conversation.action.unmute');
            if($conversationCheck->is_mute == ConversationParticipant::MUTE) {
                $conversation->update(['is_mute'=> ConversationParticipant::NOT_MUTE]);
                $action = ConversationParticipant::NOT_MUTE;
            }else{
                $conversation->update(['is_mute'=> ConversationParticipant::MUTE]);
                $actionMsg = __('message.conversation.action.mute');
                $action = ConversationParticipant::MUTE;
            }
            if(isset($action)){
                // Send socket mute conversation.
                app(SocketManager::class)->emit(SocketEvent::ACTION_MUTE_CONVERSATION, [
                    'conversation_id' => $conversationId,
                    "receiver" => [$userId],
                    "action" => $action
                ]);
                //Emit event updateConversation
                $this->sendNotificationSocketUpdateConversation($conversationId, [$userId], ['is_mute']);
            }
            return [[], $actionMsg];
        } catch (Exception $e) {
            Log::error('[ConversationManager][muteConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
            throw new Exception('[ConversationManager][muteConversation] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * detail conversation
     * @param $conversationId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function detailConversation($conversationId)
    {
        try{
            $idUser = Auth::guard('api')->user()->id;
            $conversation = ConversationParticipant::query()
            ->join("conversations","conversations.id",'conversation_participants.conversation_id')
            ->leftJoin("conversation_pins",function($join) use($idUser){
                $join->on('conversations.id', '=', 'conversation_pins.conversation_id')
                    ->where('conversation_pins.user_id', $idUser);
            })
            ->where('conversation_participants.user_id', $idUser)
            ->select([
                'conversation_participants.id',
                'conversation_participants.conversation_id',
                'conversation_participants.updated_at',
                'conversation_participants.admin',
                'conversations.name',
                'conversations.type',
                'conversations.avatar',
                'conversation_participants.is_mute',
                'conversations.desciption',
                DB::raw('CASE WHEN conversation_pins.id is null THEN 0 ELSE 1 END as conversation_pin'),
            ]);

        $detailConversation = ConversationParticipant::query()
            ->leftJoin('users', function ($join) {
                $join->on('conversation_participants.user_id', '=', 'users.id');
            })
            ->joinSub($conversation, 'conversation_account', function ($join) use ($idUser) {
                $join->on('conversation_participants.conversation_id', '=', 'conversation_account.conversation_id')
                        ->where(function ($qr) use ($idUser) {
                            $qr->where(function ($subqr) use ($idUser) {
                                $subqr->where('conversation_account.type', '=', Conversation::TYPE_TWO_USER)
                                    ->where('conversation_participants.user_id', '!=', $idUser);
                            })->orWhere('conversation_account.type', '!=', Conversation::TYPE_TWO_USER);
                        });
            })
            ->where('conversation_account.conversation_id', $conversationId)
            ->select([
                DB::raw('
                    (CASE
                        WHEN conversation_account.type = ' . Conversation::TYPE_MY_CHAT . ' THEN users.avatar
                        WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN users.avatar
                        ELSE conversation_account.avatar
                    END)
                    AS conversation_avatar'
                ),
                DB::raw('
                    (CASE
                        WHEN conversation_account.type = ' . Conversation::TYPE_MY_CHAT . ' THEN "My chat"
                        WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN IFNULL(CONCAT_WS(" ", users.first_name, users.last_name),"")
                        ELSE conversation_account.name
                    END)
                    AS conversation_name'
                ),
                DB::raw('
                    (CASE
                        WHEN conversation_account.type = ' . Conversation::TYPE_MY_CHAT . ' THEN '.$idUser.'
                        WHEN conversation_account.type = ' . Conversation::TYPE_TWO_USER . ' THEN users.id
                        ELSE NULL
                    END)
                    AS user_id'
                ),
                'conversation_account.desciption',
                'conversation_participants.conversation_id',
                'conversation_account.conversation_pin',
                'conversation_account.is_mute',
                'conversation_account.type',
                'conversation_account.admin AS is_admin'
            ])
            ->groupBy('conversation_participants.conversation_id')
            ->first();
            if($detailConversation && $detailConversation->type == Conversation::TYPE_MULTI_USER){
                $encryptedConversation = $this->cryptConversationId($detailConversation->conversation_id);
                $detailConversation->invite_link = route('api.v1.invitationLink', ['encryptedConversationId' => $encryptedConversation]);
                $detailConversation->encrypt_conversation_id = $encryptedConversation;
            }
            return $detailConversation;
        } catch (Exception $e) {
            Log::error("[ConversationManager][detailConversation] line " . $e->getLine() . " error " . $e->getMessage());
            DB::rollBack();
            throw new Exception("[ConversationManager][detailConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * decrypt conversationId
     * @param $conversationId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function getConversationInvitation($conversationId){
        $decryptConversation = $this->decryptConversationId($conversationId);
        $getConversation = Conversation::select(['name', 'avatar'])->where('conversations.id', $decryptConversation)->first();
        return $getConversation;
    }

    /**
     * crypt conversationId
     * @param $conversationId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function cryptConversationId($conversationId){
        $ciphering = "AES-128-CBC";
        $iv_length = openssl_cipher_iv_length($ciphering);
        $options = 0;
        // 16 digit values
        $encryption_iv = '1234567891011121';
        // Alternatively, any 16 digits may be used
        // characters or numeric for iv
        $encryption_key = 'ThisIsA16ByteKey';
        $encryption = openssl_encrypt($conversationId, $ciphering, $encryption_key, $options, $encryption_iv); 
        return base64_encode($encryption);
    }
    /**
     * decrypt conversationId
     * @param $conversationId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function decryptConversationId($conversationId){
        $ciphering = "AES-128-CBC";
        $iv_length = openssl_cipher_iv_length($ciphering);
        $options = 0;
        // 16 digit values
        $decryption_iv = '1234567891011121';
        // Alternatively, any 16 digits may be used
        // characters or numeric for iv
        $decryption_key = 'ThisIsA16ByteKey';
        $decryption = base64_decode($conversationId);
        return openssl_decrypt($decryption, $ciphering, $decryption_key, $options, $decryption_iv);
    }

    /**
     * Send notification - update conversation
     * @param       $conversationId - Conversation ID
     * @param array $receivers - List users receiver notification
     * @param array $listKeySocket - Key list will be returned
     * @param array $additionalData  - List of additional data
     * @return void
     * @throws Exception
     */
    public function sendNotificationSocketUpdateConversation($conversationId, array $receivers = [], array $listKeySocket = [], array $additionalData  = [])
    {
        //Get the latest chat information
        $idUser = Auth::guard('api')->user()->id;
        $request = new Request();
        $request->replace(['conversation_id' => $conversationId]);
        $conversation = $this->listConversation($idUser, $request);
        $conversation = collect($conversation->items())->first();
        $dataSocketUpdateConversation = [
            'conversation_id'               => $conversation->conversation_id,
            'user_id'                       => $conversation->user_id,
            'message_id'                    => $conversation->message_id,
            'message_count_unread'          => $conversation->message_count_unread,
            'message_to_unread'             => $conversation->message_to_unread,
            'conversation_avatar'           => $conversation->conversation_avatar,
            'conversation_name'             => $conversation->conversation_name,
            'conversation_pin'              => $conversation->conversation_pin,
            'is_mute'                       => $conversation->is_mute,
            'time_send_message'             => $conversation->time_send_message,
            'time_send_message_default'     => $conversation->time_send_message_default,
            'is_admin'                      => $conversation->is_admin,
            'most_recent_message'           => $conversation->most_recent_message
        ];
        $remindUsers = $additionalData['remind_users'] ?? [];
        $originalRemindUsers = $additionalData['original_remind_users'] ?? [];
        $editedMessageId = $additionalData['edited_message_id'] ?? null;
        //Get list members in conversation
        $listMembersInConversation = ConversationParticipantManager::GetConversationParticipant($conversationId, null, ['user_id', 'admin', 'is_mute']);
        if (!empty($receivers)) {
            $listMembersInConversation = $listMembersInConversation->whereIn('user_id', $receivers);
        }
        // Get list users have read message
        $usersHaveRead = [];
        if (in_array('is_edit', $listKeySocket) && $editedMessageId) {
            $userIds = $listMembersInConversation->pluck('user_id')->toArray();
            $usersHaveRead = MessageRead::query()
                ->where('message_id', $editedMessageId)
                ->whereIn('user_id', $userIds)
                ->pluck('user_id')
                ->toArray();
        }
        $socketManager = new SocketManager();
        //Loop through the list of chat participants and set is_mute, is_admin, receiver by user
        $listMembersInConversation->map(function($dataConversationParticipant) use
        ($socketManager, $dataSocketUpdateConversation, $listKeySocket, $remindUsers, $originalRemindUsers, $editedMessageId, $usersHaveRead ) {
            $dataSocketUpdateConversation['is_mute'] = $dataConversationParticipant->is_mute;
            $dataSocketUpdateConversation['is_admin'] = $dataConversationParticipant->admin;
            $dataSocketUpdateConversation['receiver'] = [$dataConversationParticipant->user_id];
            $dataSocketUpdateConversation['remind_users'] = $remindUsers;
            // LOGIC IS_EDIT 
            $shouldSetIsEdit = false;
            if (in_array('is_edit', $listKeySocket) && $editedMessageId) {
                $userId = $dataConversationParticipant->user_id;
            
                // Check mention status change
                $wasUserMentioned = in_array($userId, $originalRemindUsers);
                $isUserMentioned = in_array($userId, $remindUsers);
                $mentionStatusChanged = $wasUserMentioned !== $isUserMentioned;
                
                // Check User has read message 
                $hasReadMessage = in_array($userId, $usersHaveRead);
                
                // Set is_edit = 1(EDITED) only if mention status changed AND user hasn't read the message
                $shouldSetIsEdit = $mentionStatusChanged && !$hasReadMessage;
            }

            $dataSocketUpdateConversation['is_edit'] = $shouldSetIsEdit ? Message::EDITED : Message::NOT_EDITED;
            //Refine the returned data list
            if (!empty($listKeySocket)) {
                $listKeySocket = array_merge($listKeySocket,
                    ['conversation_id', 'receiver']);
                $dataSocketUpdateConversation = array_intersect_key($dataSocketUpdateConversation, array_flip($listKeySocket));
            }
            $socketManager->emit(SocketEvent::UPDATE_CONVERSATION, $dataSocketUpdateConversation);
        });
    }

    /**
     * Get conversation's information sub query
     */
    public function getConversationInfoSubQr($userId) {
        // Conversation info sub query
        $conversationInfoSubQr = ConversationParticipant::query()
        ->leftJoin('users', function ($join) {
            $join->on('conversation_participants.user_id', '=', 'users.id');
        })
        ->join('conversations', function ($join) use ($userId) {
            $join->on('conversation_participants.conversation_id', '=', 'conversations.id')
                ->where(function ($qr) use ($userId) {
                    $qr->where(function ($subQr) use ($userId) {
                        $subQr->where('conversations.type', '=', Conversation::TYPE_TWO_USER)
                            ->where('conversation_participants.user_id', '!=', $userId);
                    })->orWhere('conversations.type', '!=', Conversation::TYPE_TWO_USER);
                });
        })
        ->select([
            'conversations.id AS conversation_id'
            , 'conversations.type AS conversation_type'
            , DB::raw('
                (CASE
                    WHEN conversations.type = ' . Conversation::TYPE_MY_CHAT . ' THEN "My chat"
                    WHEN conversations.type = ' . Conversation::TYPE_TWO_USER . 
                        ' THEN IFNULL(CONCAT_WS(" ", users.first_name, users.last_name),"")
                    ELSE conversations.name
                END)
                AS conversation_name'
            )
            , DB::raw('
                (CASE
                    WHEN conversations.type = ' . Conversation::TYPE_MY_CHAT . ' THEN users.avatar
                    WHEN conversations.type = ' . Conversation::TYPE_TWO_USER . ' THEN users.avatar
                    ELSE conversations.avatar
                END)
                AS conversation_avatar'
            ),
        ])
        ->distinct();

        return $conversationInfoSubQr;
    }
    /**
     * delete conversation by conversation id
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */
    public function deleteConversation($conversationId)
    {
        try {
            $authId = Auth::guard('api')->id();
            $conversation = Conversation::with([
                'conversationParticipant' => function ($query) use ($authId) {
                    $query->where('user_id', $authId);
                }
            ])
                ->where('id', $conversationId)
                ->where('type', Conversation::TYPE_MULTI_USER)
                ->first();

            if (empty($conversation)) {
                return ['status_error' => ConversationParticipant::ERROR_NOT_FOUND];
            };
            if ($conversation->conversationParticipant->isEmpty()) {
                return ['status_error' => ConversationParticipant::ERROR_KICKED];
            };
            $member = $conversation->conversationParticipant->first();
            if (ConversationParticipant::IS_ADMIN != $member->admin) {
                return ['status_error' => ConversationParticipant::ERROR_PERMISSION];
            }

            DB::beginTransaction();
            $conversationType = $conversation->type;
            $listIdUser = ConversationParticipantManager::disbandMembers($conversationId);
            MessageManager::deleteMessageByConversation($conversationId);
            $conversation->delete();
            DB::commit();
            // Send socket delete conversation.
            app(SocketManager::class)->emit(SocketEvent::DELETE_CONVERSATION, [
                'conversation_id' => $conversationId,
                'conversation_type' => $conversationType,
                "receiver" => $listIdUser,
                'delete_by' => $authId
            ]);
        } catch (Exception $e) {
            $error = '[ConversationManager][deleteConversation] line ' . $e->getLine() . ' error ' . $e->getMessage();
            Log::error($error);
            DB::rollBack();
            throw new Exception($error);
        }
    }
}
