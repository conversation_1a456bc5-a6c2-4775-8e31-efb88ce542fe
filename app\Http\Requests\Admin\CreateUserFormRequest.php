<?php
namespace App\Http\Requests\Admin;

use App\Models\PropertyManagementAgency;
use App\Models\Role;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\SiteSetting;
class CreateUserFormRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
     /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        if(isset($this->social_insurance_fee)){
            $number = preg_replace('/\./', '',$this->social_insurance_fee);    
            $this->merge([
                'social_insurance_fee' => (int)$number
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        $companies = SiteSetting::select('value')->where('id',SiteSetting::COMPANY_LIST_ID)->first();
        $companies = json_decode($companies->value) ?? [];
        $status = SiteSetting::select('value')->where('id',SiteSetting::STATUS_PERSONNEL_ID)->first();
        $status = json_decode($status->value) ?? [];
        $calSalaries = SiteSetting::select('value')->where('id',SiteSetting::SALARY_PROCESS_ID)->first();
        $calSalaries = json_decode($calSalaries->value) ?? [];
        $vehicleTypes = \App\Models\SiteSetting::select('value')->where('id',SiteSetting::VEHICLE_TYPE_ID)->first();
        $vehicleTypes = json_decode($vehicleTypes->value,true) ?? [];
        $keyVehicle = array_keys($vehicleTypes);
        $rules = [
            'first_name' => 'required|max:200',
            'last_name' => 'required|max:200',
            'check_name' => 'required|max:200|unique:users,name',
            'email' => 'required|email|max:200|unique:users,email',
            'personal_email' => 'string|nullable|email|max:200',
            'identity_card' => 'max:200',
            'id_issued_place' => 'max:200',
            'id_issued_at' => 'nullable|date_format:d/m/Y',
            'prefecture_id' => 'nullable|exists:prefectures,id',
            'district_id' => 'nullable|exists:districts,id',
            'commune_id' => 'nullable|exists:communes,id',
            'address' => 'max:500',
            'password' => 'nullable|min:8|max:20',
            'birthday' => 'nullable|date_format:d/m/Y',
            'phone' => 'nullable|digits_between:10,11|unique:users,phone',
            'avatar' => 'mimes:jpeg,png,jpg|max:10240',
            'face_image' => 'mimes:jpeg,png,jpg|max:10240',
            'department' => 'nullable|exists:departments,id',
            'position' => 'nullable|exists:positions,id',
            'started_at' => 'nullable|date_format:d/m/Y',
            'signed_at' => 'nullable|date_format:d/m/Y',
            'ended_at' => 'nullable|date_format:d/m/Y',
            'number_dependents' => 'nullable|numeric|min:0',
            'number_social_insurance' => 'nullable|max:200',
            'work_place_id' => 'nullable|exists:work_places,id',
            'company_insurance' => ['nullable', 'max:255', 'in:'. implode(',', $companies)],
            'company_contract' =>  ['nullable', 'max:255', 'in:'. implode(',', $companies)],
            'monthly_allowance' => 'nullable|max:255',
            'personnel_status' =>  ['nullable', 'max:255', 'in:'. implode(',', $status)],
            'salary_calculation_method' => ['nullable', 'max:255', 'in:'. implode(',', $calSalaries)],
            'hour_register' => 'nullable|numeric|min:0',
            'percent_finish' => 'nullable|numeric|min:0|max:100',
            'started_at_phase2' => 'nullable|date_format:d/m/Y',
            'vehicle_type' =>  ['nullable','in:'.implode(',', $keyVehicle)],
            'social_insurance_fee' => ['nullable','numeric','min:1','max:*********']
        ];

        //Validate management_agency
        $listManagementAgency = PropertyManagementAgency::query()
            ->select(['id'])
            ->pluck('id')
            ->merge(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY)
            ->toArray();
        $rules['management_agency'] = 'nullable|array|in:"",'.implode(",",$listManagementAgency);

        if (!auth()->user()->hasRole(\App\Models\Role::ROLE_SYSTEM_MANAGER)){
            $listIdRole = Role::select('id')
                ->whereNotIn('name',[Role::ROLE_SYSTEM_MANAGER,Role::ROLE_PROJECT_MANAGER])
                ->pluck('id')
                ->toArray();
            $rules['role'] = 'nullable|array|in:"",'.implode(",",$listIdRole);
        }else{
            $listIdRole = Role::select('id')
                    ->pluck('id')
                    ->toArray();
            $rules['role'] = 'nullable|array|in:"",'.implode(",",$listIdRole);
        }
        return $rules;
    }

    /**
     * Get the message that apply to the request.
     *
     * @return array
     */
    public function messages() {
        $messages = [

        ];

        return $messages;
    }

    /**
     * Configure the validator instance.
     *
     * @param Illuminate\Validation\Validator $validator
     * @return void
     */
    public function withValidator($validator) {

    }
}
