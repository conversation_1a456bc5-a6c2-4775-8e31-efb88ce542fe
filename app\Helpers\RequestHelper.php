<?php
namespace App\Helpers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class RequestHelper {

    /**
     * Remove parameters of request
     *
     * @param $url
     * @return mixed|string
     */
    public function parseRequestUri($url) {
        return explode('?', $url)[0];
    }

    /**
     * Replace restful api url {{id}} to number
     * @param string $url
     * @param array $params
     * @return string
     */
    public function parseUrl($url, $params)
    {
        foreach ($params as $key => $value) {
            if (strpos($url, '{' . $key . '}') > -1) {
                $url = str_replace('{' . $key . '}', $value, $url);
            }
        }
        return $url;
    }

    /**
     * Add parameters to request
     *
     * @param $method
     * @param $params
     * @param $headers
     * @return array
     */
    private function addParamsToRequest($method, $params, $headers) {
        $data = [];
        $method = strtoupper($method);

        if ($method == 'GET') {
            $data['query'] = $params;
        } else {
            $data['multipart'] = $params;
        }
        $data['headers'] = $headers;

        return $data;
    }

    /**
     * Send Request Url.
     *
     * @param string $url
     * @param string $method
     * @param array $params
     * @param array $headers
     * @return array
     */
    public function sendRequest($url, $method = 'GET', $params = [], $headers = []) {
        try {
            // Add params to request
            $url = $this->parseUrl($url, $params);
            $data = $this->addParamsToRequest($method, $params, $headers);

            $client = new \GuzzleHttp\Client();
            $response = $client->request($method, $url, $data);

            return json_decode($response->getBody()->getContents(), 1);
        } catch (\Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return null;
        }
    }

    /**
     * Download file from Storage
     * @param string $url
     * @param $directory
     * @param string $filename
     * @param string $method
     * @param array $params
     * @param array $headers
     * @return boolean
     */
    public function downloadFile($url, $directory, $filename, $method='GET', $params = [], $headers = []) {
        try {
            // Make directory if not exist
            if (!Storage::disk(FILESYSTEM)->exists($directory)) {
                Storage::disk(FILESYSTEM)->makeDirectory($directory);
            }
            $file_path = $directory.'/'.$filename;

            // Add params to request
            $url = $this->parseUrl($url, $params);
            $data = $this->addParamsToRequest($method, $params, $headers);

            // Get file content from response
            $client = new \GuzzleHttp\Client();
            $response = $client->request($method, $url, $data);
            if ($response->getStatusCode() == 200) {
                $file = $response->getBody()->getContents();
                Storage::disk(FILESYSTEM)->put($file_path, $file);
                return $file_path;
            }
            return null;
        } catch (\Exception $e) {
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return null;
        }
    }

    /**
     * Get Params from request
     * @param Request $request
     * @return array
     */
    public function getParamsFromRequest(Request $request){
        $result = $request->all();
        $result['route_name'] = $request->route()->getName();
        return $result;
    }
}
