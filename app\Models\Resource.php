<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Resource
 * @property integer $id
 * @property integer $group_id
 * @property string $name
 * @property string $description
 * @property array $managers
 * @property integer $type
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 */
class Resource extends Model
{
    use SoftDeletes;
     /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $guarded = [];
    const TYPE_MEETING_ROOM = 0;
    const TYPE_DEVICE = 1;
    const TYPES = [
        Resource::TYPE_MEETING_ROOM => 'room_meeting',
        Resource::TYPE_DEVICE => 'device',
    ];
    /**
     * Get group resource for the resource
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function groupResource()
    {
    	return $this->belongsTo('App\Models\GroupResource');
    }
}
