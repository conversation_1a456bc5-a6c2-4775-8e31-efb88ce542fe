<?php
 namespace App\Http\ViewComposers;

 use Illuminate\View\View;
 use App\Models\ProjectTask;
 use Auth;

 class GlobalComposer
 {
     public $bookmarkedTasks;
     /**
      * Create a movie composer.
      *
      * @return void
      */
     public function __construct()
     {
        $userId = Auth::id();     
        $columns = [
            'project_tasks.id',
            'project_tasks.name',
            'project_tasks.description',
            'project_tasks.updated_at',
            'projects.name as project_name',
            'projects.id as project_id',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'users.avatar',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
         ];
         $this->bookmarkedTasks  = ProjectTask::select($columns)
             ->checkUserPermission($userId)
             ->leftjoin('task_bookmarks', 'task_bookmarks.task_id', 'project_tasks.id')
             ->leftJoin('users','project_tasks.user_id','users.id')
             ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
             ->where("task_bookmarks.user_id", $userId)
             ->orderBy('task_bookmarks.created_at','DESC')
             ->get();
     }

     /**
      * Bind data to the view.
      *
      * @param  View  $view
      * @return void
      */
     public function compose(View $view)
     {
         $view->with('bookmarkedTasks', $this->bookmarkedTasks);
     }
 }