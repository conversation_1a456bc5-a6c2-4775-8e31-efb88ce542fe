<?php

namespace App\Http\Controllers\CodingContest;

use App\Helpers\RequestHelper;
use App\Models\Challenge;
use App\Http\Controllers\Controller;
use App\Http\Requests\CodingContestRequest;
use App\Logics\AttachmentManager;
use App\Logics\CodingContestManager;
use App\Models\Submission;
use App\Models\TaskAttachment;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Http\Response;

class CodingContestController extends Controller
{
    const PER_PAGE = 10;

    public const PRACTICE_TYPE = 0;
    public const CHALLENGE_TYPE = 1;

    public const LANGUAGES = ["php" => "PHP", "java" => "Java 8", "python" => "Python 3", "javascript" => "Javascript"];


    /**
     * Display a listing of the challenges.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function challenges(Request $request)
    {
        $challenge_type = $request->type;
        if (!in_array($challenge_type,[Challenge::CHALLENGE_TYPE,Challenge::PRACTICE_TYPE])) {
            return redirect(route('coding_contest.index',['type' => Challenge::PRACTICE_TYPE]));
        }
        // Get all challenges
        $challenges = Challenge::where('type', $challenge_type)
            ->with(['submissions' => function($q) {
                $q->where('user_id', auth()->id());
            }]);

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $challenges = $challenges->paginate($perPage);

        // Redirect to last page if page parameter greater than last page
        if ($challenges->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $challenges->lastPage()]));
        }
        // Redirect to first page if page parameter less than 0
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        return view('coding_contest.index', [
            'challenges' => $challenges,
        ]);
    }
    /**
     * create test
     */
    public function create()
    {
        return view('coding_contest.create');
    }
    /**
     * save test
     * 
     */
    public function store(CodingContestRequest $request)
    {
        DB::beginTransaction();
        try{
            $challenge = new Challenge();
            $challenge->name = $request->name;
            $challenge->type = $request->type;
            $challenge->problem = $request->problem;
            $challenge->save();
            if(isset($request->imgContent)){
                $imgContent = explode(GROUP_CONCAT_SEPARATOR, $request->imgContent);
                (new CodingContestManager())->saveImage($challenge->id,$imgContent);
            }
            $destinationPath = str_replace('{challenge_id}', $challenge->id, CONTEST_ANSWER_DIR).'/';
            (new AttachmentManager())->saveAttachments($challenge, TaskAttachment::TYPE_CONTEST,$request->documents, $request->descriptionDocument, $destinationPath, null);
            DB::commit();
            return redirect()->route('coding_contest.index',[
                'type' => $challenge->type
            ])->with([
                'status_succeed' => trans('message.success')
            ]);
        }catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * edit test
     */
    public function edit($id)
    {
        $challenge = Challenge::find($id);
        $taskFiles = TaskAttachment::leftJoin('users','task_attachments.created_by','users.id')
            ->where(function($query) use($id){
                $query->where([
                    ['task_attachments.related_id',$id],
                    ['task_attachments.type', TaskAttachment::TYPE_CONTEST]
                ]);
            })
            ->select([
                'task_attachments.id',
                'task_attachments.related_id',
                'task_attachments.type',
                'task_attachments.file_name',
                'task_attachments.file_path',
                'task_attachments.file_size',
                'task_attachments.description',
                'task_attachments.created_by',
                'task_attachments.updated_by',
                'task_attachments.created_at',
                'task_attachments.updated_at',
                'users.first_name',
                'users.last_name',
            ])
            ->get();
        return view('coding_contest.edit',[
            'challenge' => $challenge,
            'taskFiles' => $taskFiles,
        ]);
    }
    /**
     * update test
     * 
     */
    public function update(CodingContestRequest $request,$id)
    {
        DB::beginTransaction();
        try{
            $challenge = Challenge::find($id);
            $challenge->name = $request->name;
            $challenge->type = $request->type;
            $challenge->problem = $request->problem;
            $challenge->save();
            if(isset($request->imgContent)){
                $imgContent = explode(GROUP_CONCAT_SEPARATOR, $request->imgContent);
                (new CodingContestManager())->saveImage($challenge->id,$imgContent);
            }
            $destinationPath = str_replace('{challenge_id}', $challenge->id, CONTEST_ANSWER_DIR).'/';
            (new AttachmentManager())->saveAttachments($challenge, TaskAttachment::TYPE_CONTEST,$request->documents, $request->descriptionDocument, $destinationPath, null);
            DB::commit();
            return redirect()->route('coding_contest.index',[
                'type' => $challenge->type
            ])->with([
                'status_succeed' => trans('message.success')
            ]);
        }catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * delete test
     * 
     */
    public function destroy($id){
        DB::beginTransaction();
        try {
            $challenge = Challenge::find($id);
            if($challenge == null){
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    'msg' => [
                        'title' => trans('message.task_not_exist'),
                    ]
                ];
            }

            $challenge->delete();

            DB::commit();
            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'text' => trans('message.success') ,
                ],
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return back()->with([
                'status_failed' => trans('message.server_error'),
            ]);
        }
    }
    /**
     * Display a listing of the submissions.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function submissions(Request $request)
    {
        // Get all challenges
        $challenges = Challenge::all();

        // Get selected challenge
        $challenge_id = isset($request->challenge) ? $request->challenge : $challenges[0]->id;
        $selected_challenge = Challenge::find($challenge_id);

        $submissions = null;
        if (isset($selected_challenge)) {
            // Get submissions of selected challenge
            $submissions = Submission::where('challenge_id', $selected_challenge->id)
                ->with(['user' => function($q) {
                    $q->select(DB::raw("id, CONCAT_WS(' ', first_name, last_name) AS name"));
                }])
                ->sortable()
                ->paginate(self::PER_PAGE);
        }

        return view('coding_contest.leaderboard', [
            'challenges' => $challenges,
            'selected_challenge' => $selected_challenge,
            'submissions' => $submissions,
            'languages' => self::LANGUAGES,
        ]);
    }

    /**
     * Get source code of submission
     *
     * @param $submissionId
     * @return
     */
    public function getSourcecodeSubmission($submissionId) {
        return Submission::find($submissionId)->sourcecode;
    }

    /**
     * Show the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($type,$id)
    {
        $challenge = Challenge::find($id);
        $submission = Submission::where(["user_id" => auth()->id(), "challenge_id" => $id])->first();

        // Return error message if user not exist
        if (!$challenge instanceof Challenge) {
            return back()->with([
                'status_failed' => trans('message.practice_not_exist')
            ]);
        }

        // Store redirect url in session
        if((new RequestHelper())->parseRequestUri(url()->previous()) == route('coding_contest.index',['type'=>$type])) {
            session(['redirect.coding_contest.practice.show' => url()->previous()]);
        }

        return view('coding_contest.show', [
            'practice' => $challenge,
            'submission' => $submission,
            'languages' => self::LANGUAGES,
        ]);
    }

    /**
     * Store submission of user and result's it
     *
     * @param $user_id
     * @param $challenge_id
     * @param $language
     * @param $sourcecode
     * @param $runtime
     */
    private function storeSubmission($user_id, $challenge_id, $language, $sourcecode, $runtime) {
        $submission = Submission::where(["user_id" => $user_id, "challenge_id" => $challenge_id])->first();
        if ($submission == null) {
            Submission::create([
                "user_id" => $user_id,
                "challenge_id" => $challenge_id,
                "language" => $language,
                "sourcecode" => $sourcecode,
                "runtime" => $runtime,
            ]);
        } else {
            $submission->update([
                "language" => $language,
                "sourcecode" => $sourcecode,
                "runtime" => $runtime,
            ]);
        }
    }

    /**
     * Receive sourcecode and run with compiler corresponding to language
     *
     * @param Request $request
     * @param $challenge
     * @param $language
     * @return \Illuminate\Http\JsonResponse
     */
    public function compile(Request $request, $challenge, $language) {
        $tmpDirname = Str::random(25);
        $tmpDir = CODING_CONTEST_TMP_DIR."/$tmpDirname";
        if (!Storage::disk(FILESYSTEM)->exists($tmpDir)) {
            Storage::disk(FILESYSTEM)->makeDirectory($tmpDir);
        }

        // Check testcase of this challenge is exist
        $challengeDir = CODING_CONTEST_CHALLENGE_DIR."/$challenge/testcase";
        if (!Storage::disk(FILESYSTEM)->exists($challengeDir)) {
            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 500,
                "error" => "Test case of challenge is not exist",
            ]);
        }

        // Check has submitted source code
        if (!isset($request->sourcecode)) {
            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 400,
                "error" => "Source code is empty",
            ]);
        }

        // Store submitted source code
        $sourcecodeFile = null;
        $compiler = null;
        $dockerContainer=null;
        if ($language == "php") {
            $sourcecodeFile = "main.php";
            $compiler= "php";
            $dockerContainer = "ntcd_php";
        } else if ($language == "javascript") {
            $sourcecodeFile = "main.js";
            $compiler= "node";
            $dockerContainer = "ntcd_javascript";
        } else if ($language == "java") {
            $sourcecodeFile = "main.java";
            $compiler= "java";
            $dockerContainer = "ntcd_java";
        } else if ($language == "python") {
            $sourcecodeFile = "main.py";
            $compiler= "python";
            $dockerContainer = "ntcd_python";
        } else {
            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 400,
                "error" => "Language is not supported",
            ]);
        }
        Storage::disk(FILESYSTEM)->put("${tmpDir}/$sourcecodeFile", $request->sourcecode);

        // Generate command to compile source code
        $command = null;
        $localTmpDir = storage_path("app/$tmpDir");
        $localChallengeDir = storage_path("app/$challengeDir");
        $dockerTmpDir="/tmp/$tmpDirname";
        $dockerChallengeDir="/tmp/testcase";
        $command = "docker run --rm -v $localTmpDir:$dockerTmpDir -v $localChallengeDir:$dockerChallengeDir $dockerContainer $compiler $dockerTmpDir/$sourcecodeFile $dockerChallengeDir $dockerTmpDir/output";

        if ($command == null) {
            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 400,
                "error" => "Language is not supported",
            ]);
        }

        // Execute command
        $output=null;
        $retval=null;
        exec($command." 2>&1", $output, $retval); // Fix error Permission denied: sudo chmod 666 /var/run/docker.sock

        try {
            // Get result from docker
            $result = Storage::disk(FILESYSTEM)->get("$tmpDir/output");
            $result = json_decode($result, true);
            $runtime=$result["Runtime"];
            unset($result["Runtime"]);

            // Store new submission
            $correct = true;
            foreach ($result as $testcase => $value) {
                if (!$value) {
                    $correct = false;
                    break;
                }
            }
            if (!$correct) {
                $runtime = null;
            } else {
                $runtime = floatval($runtime);
            }
            $this->storeSubmission(auth()->id(), $challenge, $language, $request->sourcecode, $runtime);

            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 200,
                "stdout" => $result,
                "time" => $runtime,
            ]);
        } catch (Exception $e) {
            Log::error($e);
            Log::error("[Coding contest] Compile error");
            Log::error($output);
            Storage::disk(FILESYSTEM)->deleteDirectory($tmpDir);
            return response()->json([
                "status" => 500,
                "error" => "Compile error",
            ]);
        }
    }
}
