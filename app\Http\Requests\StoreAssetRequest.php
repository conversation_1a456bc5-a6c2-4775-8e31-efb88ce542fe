<?php

namespace App\Http\Requests;

use App\Models\AssetSetting;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreAssetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $today = Carbon::today()->format("d/m/Y");
        $rule = [
            'asset_name' => ['required', 'max:200'],
            'name_in_contract' => ['nullable', 'max:200'],
            'name_short' =>  ['nullable', Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_SHORT_NAME)],
            "management_unit_id" => ['nullable', 'exists:property_management_agency,id'],
            "type_id" => ['nullable', 'exists:property_types,id'],
            "department_id" => ['nullable', 'exists:departments,id'],
            "user_use_asset" => ['nullable', 'exists:users,id'],
            "asset_code" => ['nullable', 'max:50'],
            "handover_record_code" => ['nullable', 'max:50'],
            "seri_number" => ['nullable', 'max:50'],
            "supplier" => ['nullable', Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_SUPPLIER)],
            "country_of_manufacture" => ['nullable', 'max:1000'],
            "manufacturer" => ['nullable', 'max:200'],
            "manufacturing_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            'asset_category' => ['nullable', 'max:200'],
            "purchase_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            "usage_date" => ['nullable','date_format:d/m/Y', 'before_or_equal:' .$today],
            "condition_id" => ['nullable', 'exists:property_status,id'],
            "source_of_origin" => ['nullable',  Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_FORMATION_SOURCE)],
            "premises" => ['nullable', Rule::exists('asset_settings', 'id')->where('type', AssetSetting::ASSET_BRANCH)],
            "asset_location" => ['nullable', 'max:1000'],
            "asset_description" => ['nullable', 'max:10000'],
            "note" => ['nullable', 'max:10000'],
            'original_price' => ['nullable', 'regex:/^(?!0\d)([1-9]\d{0,2}(,\d{3})*(\.\d{1,3})?|0?\.\d{1,3}|0)$/',],
            'residual_value'=> ['nullable', 'regex:/^(?!0\d)([1-9]\d{0,2}(,\d{3})*(\.\d{1,3})?|0?\.\d{1,3}|0)$/',],
        ];
        return $rule;
    }


    /**
     * @return array|string[]
     */
    public function messages(): array
    {
        return [];
    }
}











