<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssetBiddingPackageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'asset_bidding_package_name' => 'required|max:200',
            'asset_bidding_package_code' => 'nullable|max:50'
        ];
        return $rules;
    }
}
