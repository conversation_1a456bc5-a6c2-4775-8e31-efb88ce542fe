<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Http\Requests\AcceptOrRejectConversationRequest;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\JoinConversation;
use App\User;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class JoinConversationManager
{
    /**
     * get list request join room
     *
     * @param   int  $currentUser
     * @param   int  $conversationId
     *
     */
    public function getListRequestJoin($currentUser, $conversationId)
    {
        try {
            $conversation = ConversationParticipant::join("conversations", function ($qr) {
                $qr->on("conversations.id", "conversation_participants.conversation_id")
                    ->where("type", Conversation::TYPE_MULTI_USER);
            })
                ->join("join_conversation", "conversations.id", "join_conversation.conversation_id")
                ->join("users", "join_conversation.user_id", "users.id")
                ->where("conversation_participants.user_id", $currentUser)
                ->where("conversation_participants.conversation_id", $conversationId)
                ->where("conversation_participants.admin", ConversationParticipant::IS_ADMIN)
                ->select([
                    'users.avatar',
                    DB::raw('CONCAT(users.first_name, " " ,users.last_name) as full_name'),
                    'conversations.id as conversation_id',
                    'conversations.name as conversation_name'
                ])
                ->get();
            return $conversation;
        } catch (Exception $e) {
            Log::error("[JoinConversationManager][getListRequestJoin] error " . $e->getMessage());
            throw new Exception('[JoinConversationManager][getListRequestJoin] error ' . $e->getMessage());
        }
    }

    /**
     * acceptOrRejectJoinConversation
     *
     * @param  AcceptOrRejectConversationRequest $request
     *
     */
    public function acceptOrRejectJoinConversation($request)
    {
        $currentUser = Auth::guard('api')->user();
        $param = $request->only(['conversation_id', 'user_id', 'type', 'role']);
        try {
            DB::beginTransaction();
            $listUserInRoom = ConversationParticipant::where('conversation_participants.conversation_id', $param['conversation_id'])
                                                        ->join('conversations', 'conversation_participants.conversation_id','conversations.id')
                                                        ->join("users", "conversation_participants.user_id", "users.id")
                                                        ->select(['users.id', 'conversation_participants.admin', 
                                                            'conversations.name as conversations_name', 
                                                            'conversations.avatar as conversations_avatar'
                                                        ])
                                                        ->get();
            $listAdminInRoom = [];
            $listUserIdInRoom = [];
            $conversationData = [];
            foreach ($listUserInRoom as $item) {
                if($item->admin == ConversationParticipant::IS_ADMIN){
                    $listAdminInRoom[] = $item->id;
                }
                $listUserIdInRoom[] = $item->id;
                if(empty($conversationData)){
                    $conversationData = [
                        'conversation_id' => $param['conversation_id'],   
                        'conversations_name' => $item->conversations_name,   
                        'conversations_avatar' => $item->conversations_avatar,   
                    ];
                }
            }

            // xóa yêu cầu join phòng
            JoinConversation::where('user_id', $param['user_id'])->where('conversation_id', $param['conversation_id'])->delete();
            if($param['type'] == JoinConversation::STATUS_ACCEPT){
                ConversationParticipant::withTrashed()
                ->updateOrCreate(
                    ['user_id' => $param['user_id'], 'conversation_id' => $param['conversation_id']], 
                    [
                        'admin' => $param['role'],
                        'deleted_at' => null
                    ]);

                $userSendRequest = User::find($param['user_id']);
                // Send notification to users in the chat when new member is added
                app(SocketManager::class)->emit(SocketEvent::NEW_USER_JOIN_ROOM, [
                    'conversation_id' => $param['conversation_id'],
                    'receiver' => $listUserIdInRoom,
                    "user_new" => [
                        'full_name' => $userSendRequest->full_name,
                        'id' => $userSendRequest->id,
                        'avatar' => $userSendRequest->avatar
                    ],
                    "user_accept" => [
                        'full_name' => $currentUser->full_name,
                        'id' => $currentUser->id,
                        'avatar' => $currentUser->avatar
                    ]
                ]);
                // Send notification to user sending join request
                app(SocketManager::class)->emit(SocketEvent::ADMIN_ACCEPT_YOUR_JOIN, [
                    'conversation' => $conversationData,
                    'receiver' => [$param['user_id']],
                ]);
            };

            // Send a message to the admins in the chat to remove the join request from the list.
            app(SocketManager::class)->emit(SocketEvent::REMOVE_JOIN_CONVERSATION, [
                'conversation_id' => $param['conversation_id'],
                'receiver' => $listAdminInRoom,
                "remove_request" => $param['user_id']
            ]);
            DB::commit();
            return [];
        } catch (Exception $e) {
            Log::error("[JoinConversationManager][acceptOrRejectJoinConversation] error " . $e->getMessage());
            DB::rollBack();
            throw new Exception('[JoinConversationManager][acceptOrRejectJoinConversation] error ' . $e->getMessage());
        }
    }
    /**
     * detail conversation
     * @param $conversationId
     * @return array|false|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws Exception
     */
    public function requestJoinConversation($conversationId)
    {
        try{
            $idUser = Auth::guard('api')->user()->id;
            $checkConversation = Conversation::where('conversations.id', $conversationId)
                ->where('conversations.type', '=', Conversation::TYPE_MULTI_USER)
                ->first();

            $actionMsg = __('message.conversation.request.success');
            if(empty($checkConversation)){
                $actionMsg = __('message.conversation.request.fail');
                return [['status_error' => Response::HTTP_FORBIDDEN], $actionMsg];
            }
            if(!empty($this->checkUserAlreadyInConversation($conversationId, $idUser))){
                return [['status_error' => Response::HTTP_BAD_REQUEST], ''];
            }
            $requestJoinConversation = JoinConversation::updateOrCreate([
                'user_id' => $idUser
            ], [
                'user_id' => $idUser,
                'conversation_id' => $conversationId
            ]);

            //bắn thông báo cho các admin trong nhóm
            $getAdmin = ConversationParticipant::where('conversation_id', $conversationId)
                ->where('conversation_participants.admin', ConversationParticipant::IS_ADMIN)
                ->pluck('user_id')->toArray();
            app(SocketManager::class)->emit(SocketEvent::NEW_REQUEST_JOIN_CONVESATION, [
                'conversation_id' => $conversationId,
                'id' => $requestJoinConversation->id,
                "created_at" => $requestJoinConversation->created_at->format("Y-m-d"),
                "updated_at" => $requestJoinConversation->updated_at->format("Y-m-d"),
                'sender' => [
                    'full_name' => Auth::guard('api')->user()->full_name,
                    'id' => Auth::guard('api')->user()->id,
                    'avatar' => Auth::guard('api')->user()->avatar
                ],
                "receiver" => $getAdmin,
            ]);

            return [$requestJoinConversation, $actionMsg];

        } catch (Exception $e) {
            Log::error("[ConversationManager][requestJoinConversation] line " . $e->getLine() . " error " . $e->getMessage());
            DB::rollBack();
            throw new Exception("[ConversationManager][requestJoinConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    //kiểm tra user đã tham gia conversation chưa
    public function checkUserAlreadyInConversation($conversationId, $idUser){
        $conversation = Conversation::where('conversations.id', $conversationId)
        ->join('conversation_participants', function ($join) use ($idUser){
            $join->on('conversation_participants.conversation_id', 'conversations.id')
                ->where('conversation_participants.user_id', $idUser);
        })->first();
        return $conversation;
    }
}
