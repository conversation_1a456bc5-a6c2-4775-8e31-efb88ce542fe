<?php

namespace App\Http\Controllers;

use App\Models\UserDevice;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class UserDeviceController extends Controller
{
    public function store(Request $request){
        UserDevice::where('device_token',$request->json('device_token'))->delete();
        $userDevice = new UserDevice();
        $userDevice->user_id = Auth::id();
        $userDevice->device_information = $request->json('device_information');
        $userDevice->device_token = $request->json('device_token');
        $userDevice->status = UserDevice::STATUS_ONLINE;
        $userDevice->last_connection = Carbon::now();
        $userDevice->save();

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('message.success') ,
            ],
        ];
    }

    public function destroy(Request $request){
        UserDevice::where('device_token',$request->json('device_token'))->delete();
        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('message.success') ,
            ],
        ];
    }
}
