<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Kyslik\ColumnSortable\Sortable;

class Holiday extends Model
{
    use Sortable;
    use SoftDeletes;
    protected $guarded = [];
    
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_at = Carbon::now();
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
            $data->created_by = auth()->id();
        });

        self::saving(function ($data) {
            $data->updated_at = Carbon::now();
            $data->updated_by = auth()->id();
        });
    }
}
