<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use K<PERSON>lik\ColumnSortable\Sortable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Wiki extends Model
{
    use Sortable;
    use SoftDeletes;

    const DEFAULT = 1;
    const NOT_DEFAULT = 0;

    protected $table = 'project_wikis';
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

     /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
        self::saving (function ($data) {
            $data->updated_by = auth()->id();
        });
    }

    /**
     * Get sub-wiki page
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function wikiPages()
    {
        return $this->hasMany(Wiki::class, 'parent');
    }

     /**
     * Get sub-wiki page by recursive relationship
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrenWikiPages()
    {
        return $this->hasMany(Wiki::class, 'parent')->with('wikiPages');
    }
    /**
     * Get the wiki that owns the wiki_parent.
     */
    public function wikiParent()
    {
        return $this->belongsTo(Wiki::class, 'parent');
    }
}
