<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AssetSetting extends Model
{
    use SoftDeletes;
    protected $table = 'asset_settings';
    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    CONST ASSET_BRANCH = 1;
    CONST ASSET_FORMATION_SOURCE = 2;
    CONST ASSET_SUPPLIER = 3;
    CONST ASSET_BIDDING_PACKAGE = 4;
    CONST ASSET_SHORT_NAME = 5;
    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
        self::saving (function ($data) {
            $data->updated_by = auth()->id();
        });
    }

    public function scopeSuppliers($query)
    {
        return $query->where('type', self::ASSET_SUPPLIER)->orderby('sort_order');
    }

    public function scopeShortName($query)
    {
        return $query->where('type', self::ASSET_SHORT_NAME)->orderby('sort_order');
    }
}
