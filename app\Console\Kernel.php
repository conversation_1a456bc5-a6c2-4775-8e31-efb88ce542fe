<?php

namespace App\Console;

use App\Console\Commands\CreateEvaluationWeek;
use App\Console\Commands\RunCommandTenant;
use App\Console\Commands\UpdateScoreKPI;
use App\Models\SiteSetting;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use App\Console\Commands\ClearTemporaryDirectory;
use App\Console\Commands\ClearCheckedImageLog;
use App\Console\Commands\UpdateDevice;
use App\Console\Commands\UpdateColumnIsSlow;
use App\Console\Commands\PushNotificationEvent;
use App\Console\Commands\UpdateLevelTask;
use App\Console\Commands\AutoSendReport;
use App\Console\Commands\CleanupAuthCodes;
use App\Console\Commands\DatabaseBackup;
use App\Console\Commands\LateWorkWarning;
use App\Console\Commands\SendMailRequest;
use App\Console\Commands\EmitEventSocketWhenReadMessage;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        ClearTemporaryDirectory::class,
        ClearCheckedImageLog::class,
        UpdateDevice::class,
        UpdateColumnIsSlow::class,
        PushNotificationEvent::class,
        UpdateScoreKPI::class,
        CreateEvaluationWeek::class,
        UpdateLevelTask::class,
        RunCommandTenant::class,
        AutoSendReport::class,
        DatabaseBackup::class,
        LateWorkWarning::class,
        SendMailRequest::class,
        CleanupAuthCodes::class,
        EmitEventSocketWhenReadMessage::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        (new ClearTemporaryDirectory())->scheduleCommand($schedule);
        (new ClearCheckedImageLog())->scheduleCommand($schedule);
        (new UpdateDevice())->scheduleCommand($schedule);
        (new UpdateColumnIsSlow())->scheduleCommand($schedule);
        (new PushNotificationEvent())->scheduleCommand($schedule);
        (new LateWorkWarning())->scheduleCommand($schedule);
        $createEvaluationWeek = SiteSetting::createEvaluationWeek();
        if($createEvaluationWeek == SiteSetting::CREATE_EVALUATION_WEEK){
            (new CreateEvaluationWeek())->scheduleCommand($schedule);
        }
        // (new AutoSendReport())->scheduleCommand($schedule);
        (new DatabaseBackup())->scheduleCommand($schedule);

        (new SendMailRequest())->scheduleCommand($schedule);
        (new CleanupAuthCodes())->scheduleCommand($schedule);

        $schedule->command($this->getCommandSignature(EmitEventSocketWhenReadMessage::class))->everyMinute();
    }

    /**
     * Get signature command from class.
     *
     * @param $commandClass
     * @return mixed|string|null
     */
    private function getCommandSignature($commandClass)
    {
        if (!class_exists($commandClass)) {
            return null;
        }
        try {
            $reflection = new \ReflectionClass($commandClass);

            if (!$reflection->hasProperty('signature')) {
                return null;
            }

            $property = $reflection->getProperty('signature');
            $property->setAccessible(true);

            $signature = $property->getValue(new $commandClass);
        } catch (\ReflectionException $e) {
            Log::error("Error getting command signature for {$commandClass}: " . $e->getMessage());
            return null;
        }

        $useTenant = env('USE_TENANT', false);
        return $useTenant ? "tenancy:run {$signature}" : $signature;
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
