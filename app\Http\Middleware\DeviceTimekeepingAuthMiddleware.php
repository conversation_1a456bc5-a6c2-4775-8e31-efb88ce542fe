<?php

namespace App\Http\Middleware;

use Closure;

class DeviceTimekeepingAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $authHeader = $request->header('Authorization');

        // If not exist header Authorization hoặc wrong format
        if (!$authHeader || !str_starts_with($authHeader, 'Basic ')) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        // Split base64 và decode
        $base64 = substr($authHeader, 6);
        $decoded = base64_decode($base64);

        // Split username:password
        [$username, $password] = explode(':', $decoded, 2);

        // Compare with user and password setting
        $validUser = config('timekeeping.callback_auth_user');
        $validPass = config('timekeeping.callback_auth_pass');

        if ($username !== $validUser || $password !== $validPass) {
            return response()->json(['message' => 'Forbidden'], 403);
        }
        return $next($request);
    }
}
