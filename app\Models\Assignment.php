<?php

namespace App\Models;

use App\Models\Model;

/**
 * Class Assignment
 * @property integer $id
 * @property integer $user_id
 * @property string $projects
 * @property string $note
 */

class Assignment extends Model
{
    protected $table = 'assignments';
    
    const TYPE_OFFICIAL_STAFF = 1;
    const TYPE_OUTSOURCER = 2;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id', 'user_id', 'projects', 'note'
    ];
}
