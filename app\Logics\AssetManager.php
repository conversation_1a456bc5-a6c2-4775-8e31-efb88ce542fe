<?php

namespace App\Logics;

use App\Helpers\StringHelper;
use App\Models\Asset;
use App\Models\AssetLog;
use App\Models\AssetSetting;
use App\Models\Department;
use App\Models\PropertyManagementAgency;
use App\Models\PropertyStatus;
use App\Models\PropertyType;
use App\Models\Role;
use App\User;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class AssetManager
{

    /**
     * Get getFieldsChange
     * @param Asset $asset
     * @return array
     */
    public function getFieldsChange(Asset $asset, $isCreated = false)
    {
        $result = [];
        $fieldsChange = [];
        if ($isCreated) {
            $attributes = [
                'name',
                'name_in_contract',
                'asset_short_name_id',
                'management_unit_id',
                'type_id',
                'department_id',
                'user_id',
                'asset_code',
                'handover_record_code',
                'seri_number',
                'asset_supplier_id',
                'manufacturer',
                'country_of_manufacture',
                'manufacturing_date',
                'asset_category',
                'purchase_date',
                'usage_date',
                'condition_id',
                'source_of_origin',
                'premises',
                'location',
                'description',
                'note',
                'residual_value',
                'original_price',
                'bidding_package',
            ];
            foreach ($attributes as $attribute) {
                if (!empty($asset->$attribute)) {
                    $fieldsChange[$attribute] = $asset->$attribute;
                }
            }
        } else {
            $fieldsChange = $asset->getDirty();
        }
        $fieldsNotSave = ['updated_at', 'updated_by'];
        foreach ($fieldsChange as $field => $newValue) {
            if (!in_array($field, $fieldsNotSave)) {
                switch ($field) {
                    case 'department_id':
                        $departmentOld = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : Department::find($asset->getOriginal($field)));
                        $departmentNew =  empty($newValue) ? null : Department::find($newValue);
                        $result[$field] = [
                            'new_value' => isset($departmentNew) ? $departmentNew->name : '',
                            'old_value' => isset($departmentOld) ? $departmentOld->name : ''
                        ];
                        break;
                    case 'user_id':
                        $newUser = empty($newValue) ? null : User::find($newValue);
                        $oldUser = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : User::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newUser) ? $newUser->first_name . ' ' . $newUser->last_name : '',
                            'old_value' => isset($oldUser) ? $oldUser->first_name . ' ' . $oldUser->last_name : ''
                        ];
                        break;
                    case 'type_id':
                        $newType = empty($newValue) ? null :  PropertyType::find($newValue);
                        $oldType = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : PropertyType::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newType) ? $newType->name : '',
                            'old_value' => isset($oldType) ? $oldType->name : ''
                        ];
                        break;
                    case 'management_unit_id':
                        $newManagement = empty($newValue) ? null :  PropertyManagementAgency::find($newValue);
                        $oldManagement = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : PropertyManagementAgency::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newManagement) ? $newManagement->name : '',
                            'old_value' => isset($oldManagement) ? $oldManagement->name : ''
                        ];
                        break;
                    case 'condition_id':
                        $newStatus = empty($newValue) ? null :  PropertyStatus::find($newValue);
                        $oldStatus = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : PropertyStatus::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newStatus) ? $newStatus->name : '',
                            'old_value' => isset($oldStatus) ? $oldStatus->name : ''
                        ];
                        break;
                    case 'premises':
                        $newPremises = empty($newValue) ? null :  PropertyStatus::find($newValue);
                        $oldPremises = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : AssetSetting::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newPremises) ? $newPremises->name : '',
                            'old_value' => isset($oldPremises) ? $oldPremises->name : ''
                        ];
                        break;
                    case 'source_of_origin':
                        $newOrigin = empty($newValue) ? null :  AssetSetting::find($newValue);
                        $oldOrigin = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : AssetSetting::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newOrigin) ? $newOrigin->name : '',
                            'old_value' => isset($oldOrigin) ? $oldOrigin->name : ''
                        ];
                        break;
                    case 'name':
                        $result['asset_name'] = [
                            'new_value' => $newValue,
                            'old_value' => $isCreated ? '' : ($asset->getOriginal($field))
                        ];
                        break;
                    case 'location':
                        $result['asset_location'] = [
                            'new_value' => $newValue,
                            'old_value' => $isCreated ? '' : ($asset->getOriginal($field))
                        ];
                        break;
                    case 'description':
                    case 'note':
                        $result[$field] = [
                            'new_value' => nl2br($newValue),
                            'old_value' => $isCreated ? '' : nl2br(($asset->getOriginal($field)))
                        ];
                        break;
                    case 'bidding_package':
                        $newBiddingPackage = empty($newValue) ? null :  AssetSetting::find($newValue);
                        $oldBiddingPackage = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : AssetSetting::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newBiddingPackage) ? $newBiddingPackage->name : '',
                            'old_value' => isset($oldBiddingPackage) ? $oldBiddingPackage->name : ''
                        ];
                        break;
                    case 'asset_supplier_id':
                        $newAssetSupplier = empty($newValue) ? null :  AssetSetting::find($newValue);
                        $oldAssetSupplier = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : AssetSetting::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newAssetSupplier) ? $newAssetSupplier->name : '',
                            'old_value' => isset($oldAssetSupplier) ? $oldAssetSupplier->name : ''
                        ];
                        break;
                    case 'asset_short_name_id':
                        $newAssetShortName = empty($newValue) ? null :  AssetSetting::find($newValue);
                        $oldAssetShortName = $isCreated ? null : (empty($asset->getOriginal($field)) ? null : AssetSetting::find($asset->getOriginal($field)));
                        $result[$field] = [
                            'new_value' => isset($newAssetShortName) ? $newAssetShortName->name : '',
                            'old_value' => isset($oldAssetShortName) ? $oldAssetShortName->name : ''
                        ];
                        break;
                    default:
                        $result[$field] = [
                            'new_value' => $newValue,
                            'old_value' => $isCreated ? '' : ($asset->getOriginal($field))
                        ];
                        break;
                }
            }
        }
        return $result;
    }

    /**
     * Get list logs
     * @param int $taskId
     * @param int $type
     * @param int $pageSize
     */
    public function getListLogs($assetId = null, $type = null)
    {
        $logs = AssetLog::select([
            'asset_logs.id',
            'asset_logs.log',
            'asset_logs.created_at as time',
            'users.id as user_id',
            'users.avatar',
            DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as user_name')
        ])
            ->leftjoin('users', 'users.id', 'asset_logs.created_by')

            ->orderBy('asset_logs.id', 'asc');

        if (isset($assetId)) {
            $logs = $logs->where('asset_id', $assetId);
        }
        // Pagination
        $logs = $logs->get();

        return $logs;
    }

    /**
     * Get data in param
     * @param int $taskId
     * @param int $type
     * @param int $pageSize
     */
    public function getDataInParam($param)
    {
        $manufacturingDate = !empty($param['manufacturing_date']) ? Carbon::createFromFormat('d/m/Y', $param['manufacturing_date'])->format('Y-m-d') : null;
        $purchaseDate = !empty($param['purchase_date']) ? Carbon::createFromFormat('d/m/Y', $param['purchase_date'])->format('Y-m-d') : null;
        $usageDate = !empty($param['usage_date']) ? Carbon::createFromFormat('d/m/Y', $param['usage_date'])->format('Y-m-d') : null;
        $data = [
            'name' => $param['asset_name'],
            'name_in_contract' => $param['name_in_contract'],
            'asset_short_name_id' => $param['name_short'],
            'management_unit_id' => $param['management_unit_id'],
            'type_id' => $param['type_id'],
            'department_id' => $param['department_id'],
            'user_id' => $param['user_use_asset'] ?? null,
            'asset_code' => $param['asset_code'],
            'handover_record_code' => $param['handover_record_code'],
            'seri_number' => $param['seri_number'],
            'asset_supplier_id' => $param['supplier'],
            'manufacturer' => $param['manufacturer'],
            'country_of_manufacture' => $param['country_of_manufacture'],
            'manufacturing_date' => $manufacturingDate,
            'asset_category' => $param['asset_category'],
            'purchase_date' => $purchaseDate,
            'usage_date' => $usageDate,
            'condition_id' => $param['condition_id'],
            'source_of_origin' => $param['source_of_origin'],
            'premises' => $param['premises'],
            'location' => $param['asset_location'],
            'description' => $param['asset_description'],
            'note' => $param['note'],
            'residual_value' => !empty($param['residual_value']) ? str_replace(',', '', $param['residual_value']) : null,
            'original_price' => !empty($param['original_price']) ? str_replace(',', '',  $param['original_price']) : null,
            'bidding_package' => $param['bidding_package_id'] ?? null
        ];
        return $data;
    }

    /**
     * Get list asset
     * @param $params
     * @param $pageSize
     * @param $orderBy
     * @return array|Application|RedirectResponse|Redirector|array
     */
    public function getAssetList($params, $pageSize, $orderBy = [], $isExport = false)
    {
        if (isset($params['from']) && isset($params['to']) && Carbon::createFromFormat('d/m/Y', $params['from']) > Carbon::createFromFormat('d/m/Y', $params['to'])) {
            return [[], 0, 0, false];
        }
        $user = Auth::user();
        $role = $user->hasRole([Role::ROLE_SYSTEM_MANAGER, Role::ROLE_ASSET_MANAGER]);

        $stringHelper = new StringHelper();
        $columns = [
            'assets.id',
            'assets.name',
            'asset_short_names.name as short_name',
            'assets.asset_code',
            'assets.handover_record_code',
            'assets.seri_number',
            'assets.original_price',
            'assets.residual_value',
            'property_management_agency.name AS management_agency_name',
            'property_types.name AS type_name',
            'departments.name AS department_name',
            'assets.purchase_date AS purchase_date',
            DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'),
            'users.id AS user_id',
            'property_status.id AS status_id',
            'property_status.name AS status_name',
            'asset_bidding_packages.name as bidding_package_name',
            'asset_suppliers.name as supplier',
            'assets.management_unit_id'
        ];

        if ($isExport) {
            $columns = array_merge($columns,
                [
                    'assets.description',
                    'assets.note',
                    'assets.country_of_manufacture',
                    'assets.usage_date',
                    'assets.asset_category',
                    'assets.manufacturer',
                    'asset_settings.name as source_of_origin',
                ]
            );
        }

        $assets = Asset::query()
            ->select($columns)
            ->leftjoin('users', 'assets.user_id', 'users.id')
            ->leftjoin('property_management_agency', 'assets.management_unit_id', 'property_management_agency.id')
            ->leftjoin('property_types', 'assets.type_id', 'property_types.id')
            ->leftjoin('property_status', 'assets.condition_id', 'property_status.id')
            ->leftJoin('asset_settings as asset_bidding_packages', 'assets.bidding_package', '=', 'asset_bidding_packages.id')
            ->leftJoin('asset_settings as asset_short_names', 'assets.asset_short_name_id', '=', 'asset_short_names.id')
            ->leftJoin('asset_settings as asset_suppliers', 'assets.asset_supplier_id', '=', 'asset_suppliers.id')
            ->leftjoin('departments', 'assets.department_id', 'departments.id')
            ->getAssetByRole();

            if ($isExport) {
                $assets->leftJoin('asset_settings', 'assets.source_of_origin', 'asset_settings.id');
            }

        // Order By
        if (isset($orderBy)) {
            foreach ($orderBy as $key => $value) {
                $assets = $assets->orderBy($key, $value);
            }
        }
        // Filter
        $existManagementAgency = false;
        if (isset($params['management_agency'])) {
            $checkExistManagementAgency = PropertyManagementAgency::find($params['management_agency']);
            if (empty($checkExistManagementAgency)) {
                $existManagementAgency = true;
            } else {
                $assets = $assets->where('assets.management_unit_id', $params['management_agency']);
            }
        }

        if (isset($params['type'])) {
            $assets = $assets->where('assets.type_id', $params['type']);
        }

        if (isset($params['name'])) {
            $search = $stringHelper->formatStringWhereLike($params['name']);
            $assets = $assets->where(function($query) use ($search) {
                $query->where('assets.name', 'LIKE', '%' . $search . '%')
                    ->orWhere('assets.name_in_contract', 'LIKE', '%' . $search . '%');
            });
        }

        if (isset($params['short_name']) && is_array($params['short_name'])) {
            $assets = $assets->whereIn('assets.asset_short_name_id', $params['short_name']);
        }

        if (isset($params['code'])) {
            $code = $stringHelper->formatStringWhereLike($params['code']);
            $assets = $assets->where('assets.asset_code', 'LIKE', '%' . $code . '%');
        }

        if (isset($params['department'])) {
            $assets = $assets->where('assets.department_id', $params['department']);
        }

        if (isset($params['user'])) {
            $assets = $assets->where('assets.user_id', $params['user']);
        }

        if (isset($params['seri_number'])) {
            $seri_number = $stringHelper->formatStringWhereLike($params['seri_number']);
            $assets = $assets->where('assets.seri_number', 'LIKE', '%' . $seri_number . '%');
        }

        if (isset($params['supplier']) && is_array($params['supplier'])) {
            $assets = $assets->whereIn('assets.asset_supplier_id', $params['supplier']);
        }

        if (isset($params['status'])) {
            $assets = $assets->whereIn('assets.condition_id', $params['status']);
        }

        //Filter bidding package
        if (isset($params['bidding_package'])) {
            $assets = $assets->whereIn('assets.bidding_package', $params['bidding_package']);
        }

        if (isset($params['from'])) {
            $assets = $assets->where(function($query) use ($params) {
                $query->where('assets.purchase_date', '>=', Carbon::createFromFormat('d/m/Y', $params['from'])->format('Y-m-d 00:00:00'));
            });
        }
        if (isset($params['to'])) {
            $assets = $assets->where(function($query) use ($params) {
                $query->where('assets.purchase_date', '<=', Carbon::createFromFormat('d/m/Y', $params['to'])->format('Y-m-d 23:59:59'));
            });
        }

        $totalOriginPrice = $this->formatDecimal($assets->sum('assets.original_price'));
        $totalResidualValue = $this->formatDecimal($assets->sum('assets.residual_value'));
        // Pagination
        if ($pageSize == null) {
            $assets = $assets->get();
        } else {
            $assets = $assets->paginate($pageSize);
        }
        return [$assets, $totalOriginPrice, $totalResidualValue, $existManagementAgency];
    }

    /**
     * Get filter HTML
     * @param $request
     * @param $fields
     * @return string
     */
    public function getFilterHtml($request, $fields = [])
    {
        $filterHtml = "";
        $dateFormatManager = new DateFormatManager();
        foreach ($fields as $field) {
            $value = '';
            if ($request->has($field) && $request->$field != null) {
                $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
                $tagSpanClose = '</span>';
                switch ($field) {
                    case 'management_agency':
                        $management_agency = \App\Models\PropertyManagementAgency::where('id', $request->management_agency)->get();
                        foreach ($management_agency as $item) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($item->name) . $tagSpanClose;
                        }
                        break;
                    case 'type':
                        $types = \App\Models\PropertyType::query()->where('id', $request->type)->get();
                        foreach ($types as $type) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($type->name) . $tagSpanClose;
                        }
                        break;
                    case 'name':
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->name) . $tagSpanClose;
                        break;
                    case 'short_name':
                        $shortNames = AssetSetting::whereIn("id", $request->short_name)->select("name")->get();
                        foreach ($shortNames as $shortName) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($shortName->name) . $tagSpanClose;
                        }
                        break;
                    case 'code':
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->code) . $tagSpanClose;
                        break;
                    case 'department':
                        $departments = \App\Models\Department::query()->where('id', $request->department)->get();
                        foreach ($departments as $department) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($department->name) . $tagSpanClose;
                        }
                        break;
                    case 'user':
                        $members = \App\User::where('id', $request->user)->select('first_name', 'last_name')->get();
                        foreach ($members as $member) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($member->first_name . ' ' . $member->last_name) . $tagSpanClose;
                        }
                        break;
                    case 'seri_number':
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->seri_number) . $tagSpanClose;
                        break;
                    case 'supplier':
                        $suppliers = AssetSetting::whereIn("id", $request->supplier)->select("name")->get();
                        foreach ($suppliers as $supplier) {
                            $value .= $tagSpanOpen . StringHelper::escapeHtml($supplier->name) . $tagSpanClose;
                        }
                        break;
                    case 'status':
                        $listIdStatus = $request->status;
                        if (count($listIdStatus) > 0) {
                            $status = \App\Models\PropertyStatus::query()->whereIn('id', $listIdStatus)->get();
                            foreach ($status as $statusItem) {
                                $value .= $tagSpanOpen . StringHelper::escapeHtml($statusItem->name) . $tagSpanClose;
                            }
                        }
                        break;
                    case 'bidding_package':
                        $listIdBiddingPackages = $request->bidding_package;
                        if (count($listIdBiddingPackages) > 0) {
                            $biddingPackages = AssetSetting::query()->whereIn('id', $listIdBiddingPackages)->get();
                            foreach ($biddingPackages as $biddingPackageItem) {
                                $value .= $tagSpanOpen . StringHelper::escapeHtml($biddingPackageItem->name) . $tagSpanClose;
                            }
                        }
                        break;
                    default:
                        $value .= $tagSpanOpen . StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $filterHtml .= $value;
            }
        }
        return $filterHtml;
    }

    /**
     * Get backgroundStatus
     * @param integer $status
     */
    public function getBackgroundStatus($status){
        $assetStatus = PropertyStatus::find($status);
        if(empty($assetStatus)){
            return '';
        }
        return $assetStatus->color;
    }

    public static function formatDecimal($value) {
        $parts = explode('.', $value);
        $formattedValue = number_format($value, 3, '.', ',');
        if (empty($parts[1])) {
            $formattedValue = rtrim($formattedValue, '0');
            $formattedValue = rtrim($formattedValue, '.');
        }
        return $formattedValue;
    }

    /**
     * get file invalid
     */
    public function filterFileValid($listFile, $maxSize, $isImg = false){
        $fileFilter = [];
        $removeFile = [];
        foreach ($listFile as $value) {
            $val = json_decode($value, true);
            $checkSize = false;
            $maxFile = $maxSize * 1024 * 1024;
            $stringHelper = new StringHelper;
            if(Storage::disk(FILESYSTEM)->exists($val['file_path'])){
                $checkSize = Storage::disk(FILESYSTEM)->size($val['file_path']) <= $maxFile;
            }
            $checkIsImg = true;
            if($isImg){
                $checkIsImg =  $stringHelper->isImageFileByExtensionAll($val['file_path']);
            }
            if($checkIsImg && $checkSize){
                $fileFilter[] = $value;
            }else{
                $removeFile[] = $val['file_path'];
            }
        }
        return [$fileFilter, $removeFile];
    }

    /**
     * Sort property status
     *
     * @param Request $request
     * @return mix
     */
    public function sortRecord($model, $request, $type) {
        $sort = (new SortableManager())->sort(ucfirst($model), $request->itemCurrent, $request->itemPrev, $type);
        return $sort;
    }

    /**
     * Get list management agency in role asset manager
     * @return array|mixed
     */
    public function getListAssetManagementAgencyByUser()
    {
        $user = Auth::user();
        $rolesName = $user->getRoleNames();
        $listAssetManagementAgency = [];
        if ($rolesName->contains(Role::ROLE_ASSET_MANAGER)) {
            $listAssetManagementAgency = !empty($user->asset_property_management_agency) ? json_decode($user->asset_property_management_agency) : [];
            if (count($listAssetManagementAgency) === 1 && $listAssetManagementAgency[0] === PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY) {
                $listPropertyManagementAgency = PropertyManagementAgency::query()->pluck('id')->toArray();
                $listAssetManagementAgency = $listPropertyManagementAgency;
            }
        }
        return $listAssetManagementAgency;
    }

    /**
     * Get data insert management agency
     * @param      $managementAgencyValue
     * @param null $roles
     * @param bool $checkRole
     * @return false|string|null
     */
    public function getDataInsertManagementAgency($managementAgencyValue, $roles = null, bool $checkRole = false)
    {
        $dataInsertManagementAgency = [];
        //Set data insert asset_property_management_agency
        if (in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $managementAgencyValue)) {
            $dataInsertManagementAgency[] = PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY;
        } else {
            $dataInsertManagementAgency = array_map('intval', $managementAgencyValue);
        }

        $dataInsertManagementAgency = !empty($dataInsertManagementAgency) ? json_encode($dataInsertManagementAgency) : null;

        $roleAssetManager = Role::query()
            ->select('id')
            ->where('name',Role::ROLE_ASSET_MANAGER)
            ->pluck('id')
            ->toArray();

        //Check if not role asset manager
        if ($checkRole) {
            $listRoles = !empty($roles) ? $roles : [];
            if (!in_array($roleAssetManager[0], $listRoles) || (count($listRoles) === 1 && $listRoles[0] === null)) {
                $dataInsertManagementAgency = null;
            }
        }
        return $dataInsertManagementAgency;
    }

    public function checkRoleUpdate($asset) {
        /*
            User has role update/delete asset: 
            - User has System Manager OR 
            - User has Asset Manager role manage all agency OR 
            - User has Asset Manager role manage some agency and this asset belong to their manage agency
        */
        $listAssetManagementAgency = $this->getListAssetManagementAgencyByUser();
        $user = Auth::user();
        return $user->hasRole([Role::ROLE_SYSTEM_MANAGER]) 
            || ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && in_array(PropertyManagementAgency::CHOOSE_ALL_MANAGEMENT_AGENCY, $listAssetManagementAgency))
            || ($user->hasRole([Role::ROLE_ASSET_MANAGER]) && in_array($asset->management_unit_id, $listAssetManagementAgency));
    }
}
