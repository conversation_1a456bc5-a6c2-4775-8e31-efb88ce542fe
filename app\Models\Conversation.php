<?php

namespace App\Models;

use App\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
class Conversation extends Model
{
    protected $table = 'conversations';

    const LIST_TYPE_API = [0, 2];

    const TYPE_MULTI_USER = 0;
    const TYPE_MY_CHAT = 1;
    const TYPE_TWO_USER = 2;
    const HIDE = 1;
    const NOT_HIDE = 2;
    const FILTER_UNREAD = 1;
    const FILTER_TO_UNREAD = 2;
    const FILTER_TWO_USER = 3;
    const FILTER_GROUP_CHAT = 4;
    const FILTER_MUTED = 5;
    const FILTER_WITH_HIDDEN = 6;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'type', 'avatar', 'desciption', 'is_hide'
    ];

    /**
     * The roles that belong to the user.
     */
    public function list_user()
    {
        return $this->belongsToMany(User::class, 'conversation_participants', 'conversation_id', 'user_id');
    }

    /**
     * Get the conversation participant of the conversation
     * @return HasMany
     */
    public function conversationParticipant(): HasMany
    {
        return $this->hasMany(ConversationParticipant::class);
    }

    /**
     * Get the messages of the conversation
     * @return HasMany
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }
}
