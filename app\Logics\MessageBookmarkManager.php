<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Models\Message;
use App\Models\MessageBookmark;
use App\Services\EncryptService;
use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MessageBookmarkManager
{

    const BOOKMARK_STATUS = 1;
    const UNBOOKMARK_STATUS = 0;

    protected $encryptService;

    public function __construct() {
        $this->encryptService = new EncryptService();
    }
    /**
     * Bookmark Message
     *
     * @param $userId
     * @param $params
     * @return void
     * @throws Exception
     */
    public function store($userId, $request = [])
    {
        try {
            //check permission message user bookmark is in conversation what user is in this
            $messageId = $request['message_id'];
            $messageQr = Message::query()
                ->join('conversation_participants', function ($join) use ($userId) {
                    $join->on('conversation_participants.conversation_id', 'messages.conversation_id')
                        ->where('conversation_participants.user_id', $userId);
                })
                ->join('conversations', function ($join) use ($userId) {
                    $join->on('conversation_participants.conversation_id', '=', 'conversations.id');
                });
            $message = $messageQr->where('messages.id', $messageId)->first();

            if (!$message) {
                return ['status_error' => Response::HTTP_FORBIDDEN];
            }
            DB::beginTransaction();
            $queryBookMarkData = MessageBookmark::query()
                ->where('user_id', '=', $userId)
                ->where('message_id', '=', $messageId);

            $bookMarkMessage = $queryBookMarkData->first();
            if ($bookMarkMessage) {
                //if message is bookmarked => remove bookmark
                $queryBookMarkData->delete();
                $action = __('message.message.action.unbookmark');
                $dataSocket = [
                    'conversation_id' => $message->conversation_id,
                    'messages_id' => $messageId,
                    'reply_id' => $message->reply_id,
                    "receiver" => [$userId],
                    "status_bookmark" => self::UNBOOKMARK_STATUS,
                ];
            } else {
                //if message is not bookmarked => bookmark it
                $action = __('message.message.action.bookmark');
                $savedBookmark = MessageBookmark::updateOrCreate([
                    'user_id' => $userId,
                    'message_id' => $messageId
                ]);
                // Conversation info sub query 
                $conversationManager = new ConversationManager();
                $conversationInfoSubQr = $conversationManager->getConversationInfoSubQr($userId);

                // Get conversation information to return socket data
                $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
                $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
                $data = $messageQr->joinSub($conversationInfoSubQr, 'conversationInfoSubQr', function ($join) {
                    $join->on('messages.conversation_id', '=', 'conversationInfoSubQr.conversation_id');
                })
                    ->join('users', 'users.id', '=', 'messages.user_id')
                    ->leftJoin('conversation_pins', function ($join) use ($userId) {
                        $join->on('conversation_pins.conversation_id', 'conversationInfoSubQr.conversation_id')
                        ->where('conversation_pins.user_id', '=', $userId);
                    })
                    ->where('messages.id', $messageId)
                    ->select([
                        'messages.id AS messages_id',
                        DB::raw($decryptedContentText),
                        'messages.content_file',
                        'messages.reply_id',
                        DB::raw($decryptedQuoteText),
                        DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") AS time_send_message'),
                        'messages.user_id AS sender_id',
                        'users.avatar AS sender_avatar',
                        DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as sender_name'),
                        'messages.conversation_id AS conversation_id',
                        'conversationInfoSubQr.conversation_name',
                        'conversationInfoSubQr.conversation_avatar',
                        'conversation_participants.admin as is_admin',
                        'conversation_participants.is_mute as is_mute',
                        DB::raw('IF(conversation_pins.user_id IS NULL, 0, 1) as conversation_pin'),
                        'conversationInfoSubQr.conversation_type'
                    ])->first();

                $dataSocket = [
                    'id' => $savedBookmark->id,
                    'receiver' => [$userId],
                    'status_bookmark' => self::BOOKMARK_STATUS,
                    'messages_id' => $messageId,
                    'content_text' => $data->content_text,
                    'content_file' => $data->content_file,
                    'reply_id' => $data->reply_id,
                    'quote_text' => $data->quote_text,
                    'time_send_message' => $data->time_send_message,
                    'sender_id' => $data->sender_id,
                    'sender_avatar' => $data->sender_avatar,
                    'sender_name' => $data->sender_name,
                    'conversation_id' => $data->conversation_id,
                    'conversation_name' => $data->conversation_name,
                    'conversation_avatar' => $data->conversation_avatar,
                    'is_admin' => $data->is_admin,
                    'is_mute' => $data->is_mute,
                    'conversation_pin' => $data->conversation_pin,
                    'conversation_type' => $data->conversation_type,
                ];
            }
            DB::commit();
            //send data via socket
            $socketManager = new SocketManager();
            $socketManager->emit(SocketEvent::BOOKMARK_MESSAGE, $dataSocket);

            return $action;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("[MessageBookmarkManager][storeBookMark] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageBookmarkManager][storeBookMark] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }

    /**
     * Get list bookmark message
     * @param $userId
     * @param $params
     */
    public function index($userId, $params = [])
    {
        $perPage = array_key_exists('per_page', $params) && is_numeric($params['per_page']) ?
            (int)$params['per_page'] : PER_PAGE;

        // Conversation info sub query
        $conversationManager = new ConversationManager();
        $conversationInfoSubQr = $conversationManager->getConversationInfoSubQr($userId);

        // List bookmark message query
        $bookmarkMessageQr = MessageBookmark::query()
            ->join('messages', 'message_bookmarks.message_id', '=', 'messages.id')
            ->join('users', 'users.id', '=', 'messages.user_id')
            ->joinSub($conversationInfoSubQr, 'conversationInfoSubQr', function ($join) {
                $join->on('messages.conversation_id', '=', 'conversationInfoSubQr.conversation_id');
            })
            ->leftJoin('conversation_participants', function($join) use ($userId) {
                $join->on('conversation_participants.conversation_id', 'conversationInfoSubQr.conversation_id')
                ->where('conversation_participants.user_id', '=', $userId); 
            })
            ->leftJoin('conversation_pins', function ($join) use ($userId) {
                $join->on('conversation_pins.conversation_id', 'conversationInfoSubQr.conversation_id')
                ->where('conversation_pins.user_id', '=', $userId);
            })
            ->where('message_bookmarks.user_id', '=', $userId);

        if (!empty($params['last_record_id'])) {
            $bookmarkMessageQr->where('message_bookmarks.id', '<', $params['last_record_id']);
        }
        $decryptedContentText = $this->encryptService->getDecryptedRaw('messages.content_text', 'content_text');
        $decryptedQuoteText = $this->encryptService->getDecryptedRaw('messages.quote_text', 'quote_text');
        $listBookmarkMessage = $bookmarkMessageQr
            ->select([
                'message_bookmarks.id AS id',
                'messages.id AS messages_id',
                DB::raw($decryptedContentText),
                'messages.content_file',
                'messages.reply_id',
                DB::raw($decryptedQuoteText),
                DB::raw('DATE_FORMAT(messages.created_at, "%H:%i %d/%m/%Y") AS time_send_message'),
                DB::raw('(CASE WHEN JSON_CONTAINS(messages.remind_users, "' . $userId . '") THEN 1 ELSE 0 END) AS is_to'),
                'messages.user_id AS sender_id',
                'users.avatar AS sender_avatar',
                DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as sender_name'),
                'messages.conversation_id AS conversation_id',
                'conversationInfoSubQr.conversation_name',
                'conversationInfoSubQr.conversation_avatar',
                'conversationInfoSubQr.conversation_type',
                'conversation_participants.admin as is_admin',
                'conversation_participants.is_mute as is_mute',
                DB::raw('IF(conversation_pins.user_id IS NULL, 0, 1) as conversation_pin')
            ])
            ->distinct()
            ->orderBy('message_bookmarks.id', 'DESC')
            ->paginate($perPage);

        return $listBookmarkMessage;
    }
}
