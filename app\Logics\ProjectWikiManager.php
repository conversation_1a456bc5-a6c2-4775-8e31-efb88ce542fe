<?php

namespace App\Logics;

use App\Models\Wiki;
use App\Models\WikiDocument;
use App\Models\WikiVersion;
use Illuminate\Database\Eloquent\Model;

class ProjectWikiManager extends Model
{
    /**
     * get wiki default
     */
    public function getWiki($projectId, $slug, $pageId = null)
    {
        $wikiDefault = Wiki::select(
            'project_wikis.id',
            'project_wikis.title',
            'project_wikis.slug',
            'project_wikis.project_id',
            'project_wikis.parent',
            'project_wikis.default',
            'project_wikis.level',
            'project_wikis.created_by',
            'project_wikis.updated_by',
            'project_wikis.created_at',
            'project_wikis.updated_at',
            'projects.id as projectId',
            'projects.name',
            'users.first_name',
            'users.last_name',
            'wiki_versions.id as verId',
            'wiki_versions.project_wiki_id',
            'wiki_versions.content as content',
            'wiki_versions.note',
            'wiki_versions.version_id',
            'wiki_versions.status',
        )
            ->leftjoin('projects', 'projects.id', 'project_wikis.project_id')
            ->leftjoin('wiki_versions', 'project_wikis.id', 'wiki_versions.project_wiki_id')
            ->leftJoin('users', 'project_wikis.created_by', 'users.id')
            ->where("project_wikis.project_id", $projectId)
            ->where("wiki_versions.status", WikiVersion::ACTIVE);
            
            if($pageId){
                $wikiDefault = $wikiDefault
                    ->where("project_wikis.id", $pageId)
                    ->first();
            }elseif($slug){
                $wikiDefault = $wikiDefault
                    ->where("project_wikis.slug", $slug)
                    ->orderBy("project_wikis.created_at",'DESC')
                    ->first();
            }else{
                $wikiDefault = $wikiDefault
                    ->where("project_wikis.slug", "wiki")
                    ->orderBy("project_wikis.default",'DESC')
                    ->first();
            }
        return $wikiDefault;
    }

    public function getWikiFiles($projectId, $pageId)
    {
        $wikiFiles = WikiDocument::select(
            'wiki_documents.id',
            'wiki_documents.project_wiki_id',
            'wiki_documents.file_name',
            'wiki_documents.description',
            'wiki_documents.file_path',
            'wiki_documents.file_size',
            'wiki_documents.created_at',
            'wiki_documents.created_by',
            'users.first_name',
            'users.last_name',
            'project_wikis.id as pageId',
        )
            ->leftJoin('users', 'wiki_documents.created_by', 'users.id')
            ->leftjoin('project_wikis', 'project_wikis.id', 'wiki_documents.project_wiki_id')
            ->where("project_wikis.project_id", $projectId)
            ->where("project_wikis.id", $pageId)
            ->get();
        return $wikiFiles;
    }

    /**
     * create wiki
     */
    public function createWiki($param)
    {
        $wiki = Wiki::create($param);
        return $wiki;
    }

    /**
     * show list wiki compare
     */
    public function getWikiCompare($projectId, $slug)
    {
        $wiki = Wiki::select(
            'project_wikis.id',
            'project_wikis.title',
            'project_wikis.slug',
            'project_wikis.project_id',
            'project_wikis.parent',
            'project_wikis.default',
            'project_wikis.level',
            'project_wikis.created_by',
            'project_wikis.updated_by',
            'project_wikis.created_at',
            'project_wikis.updated_at',
            'users.first_name',
            'users.last_name',
            'wiki_versions.id as verId',
            'wiki_versions.project_wiki_id',
            'wiki_versions.content as content',
            'wiki_versions.note',
            'wiki_versions.version_id',
            'wiki_versions.status',
            'wiki_versions.created_by as version_create_by',
            'wiki_versions.created_at as version_created_at',
        )->leftJoin('users', 'project_wikis.created_by', 'users.id')
        ->leftjoin('wiki_versions', 'project_wikis.id', 'wiki_versions.project_wiki_id')
        ->where("project_wikis.project_id", $projectId)
        ->where("project_wikis.slug", $slug)
        ->where('wiki_versions.deleted_at', null)
        ->orderBy('wiki_versions.version_id', 'DESC');

        return $wiki;
    }

    public function getPageParent($data, $parent = 0, $level = 0)
    {
        
        $result = [];
        foreach($data as $key => $item){
            if($item['parent'] == $parent){
                $item['level'] = $level;
                $result[] = $item;
                $child = $this->getPageParent($data, $item['id'], $level+1);
                $result = array_merge($result, $child);
            }
        }
        return $result;
    }
    
    public function getLevel($wiki,$level){
        if($wiki->wikiPages->count() > 0){
            foreach ($wiki->wikiPages as $key => $value) {
                $value->level = $level + 1;
                $value->save();
                $this->getLevel($value,$value->level);
            }
        }
    }
    public function updateLevel($wiki){
        if (is_null($wiki->wikiParent)) { 
            $wiki->level = 1;  
            $wiki->save();
            $this->getLevel($wiki,$wiki->level);
        }else{
            $wiki->level = $wiki->wikiParent->level + 1;    
            $wiki->save();
            $this->getLevel($wiki,$wiki->level);
        }
    }

    public function getIdChild($wiki,$array){
        if($wiki->wikiPages->isNotEmpty()){
            foreach ($wiki->wikiPages as $key => $value) {
                $array[] = $value->id;
                $array = $this->getIdChild($value,$array);      
            }
        }
        return $array;
    }
    public function checkChild($wiki,$parent){
        $array = Array();
        $array[] = $wiki->id;
        $array = $this->getIdChild($wiki,$array);
        return in_array($parent,$array);
    }
}
