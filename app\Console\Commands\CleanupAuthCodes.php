<?php

namespace App\Console\Commands;

use App\Models\Attendance;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CleanupAuthCodes extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:passport_cleanup_auth_tables  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete revoked or expired authorization records';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        DB::table('oauth_access_tokens')
            ->where('revoked', '1')
            ->orWhere('expires_at', '<', now()->subMinutes(10))
            ->delete();

        DB::table('oauth_refresh_tokens')
            ->where('revoked', '1')
            ->orWhere('expires_at', '<', now()->subMinutes(10))
            ->delete();

        DB::table('oauth_auth_codes')
            ->where('revoked', '1')
            ->orWhere('expires_at', '<', now()->subMinutes(10))
            ->delete();

        $this->info('Old authorization records cleaned up.');
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->daily();
    }
}
