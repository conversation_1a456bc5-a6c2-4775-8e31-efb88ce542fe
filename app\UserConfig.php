<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserConfig extends Model
{
    const PUSH_FIREBASE_NOTIFICATION = 'push_notification';
    const PUSH_EMAIL_NOTIFICATION = 'email_notification';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_configs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'key',
        'value'
    ];

    public $timestamps = true;

    protected $guarded = [];

    protected $casts = [
        'value' => 'array',
    ];
}
