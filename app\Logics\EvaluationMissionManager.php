<?php
namespace App\Logics;
use App\Models\Evaluation;



class EvaluationMissionManager{
    /**
     * create evaluation content template
     *
     * @param  $item
     * @return $item
     */
    public function evaluationContentTemplate($item)
    {
        for($i=0; $i < count($item); $i++){
            unset($item[$i]['label']);
            if(isset($item[$i]['subs'])){
                $item[$i]['subs'] = $this->evaluationContentTemplate($item[$i]['subs']);
            }else{
                $item[$i]['score'] = '';
                $item[$i]['comment'] = '';
            }
        }

        return $item;
    }
    /**
     * creat form html score and comment
     *
     * @param $item, $evaluationResultIndex, $point
     * @return $point
     */
    public function getHtmlScore($item , $evaluationResultIndex='', $point = null,$isEvaluationWeek=false) {
        for ($i = 0; $i < count($item); $i++) {
            if (isset($item[$i]['subs'])) {
                $point .= $this->getHtmlScore($item[$i]['subs'], $evaluationResultIndex.$i.']',null,$isEvaluationWeek);
            }
            else {
                $point .=   '<td><select class="form-control score" style ="width:140px;" required name="score[]" '.($isEvaluationWeek ? 'disabled' : '').'><option value="">'.trans('language.evaluation_select_score').'</option>';
                $point .= '<option value="'.Evaluation::NOT_EVALUATE.'" '.($item[$i]['score'] == Evaluation::NOT_EVALUATE?'selected':'').'>'.trans('language.not_evaluate').'</option>';
                for ($option=0; $option < 11; $option++) {
                    $option = strval($option);
                    $point .= '<option value="'.$option.'"'.  ($option === $item[$i]['score'] ? 'selected' : '') . '>'.$option.'</option>';
                }
                if($isEvaluationWeek){
                    if ($item[$i]['score'] == null){
                        $point .= '<option value="" selected>-</option>';
                    }else if(!in_array($item[$i]['score'],range(0, 10))){
                        $point .= '<option value="'.$item[$i]['score'].'" selected>'.$item[$i]['score'].'</option>';
                    }
                }
                $point .='</select></td>'.'<td>
                        <textarea  rows="1" cols="40" class="form-control input" maxlength="2000" name="comment[]" placeholder = "'. trans('language.evaluation_comment').'" >'.$item[$i]['comment'].'</textarea></td></tr>';

            }
        }
        return $point;
    }
    /**
     * update score and comment
     *
     * @param  $item, $arrScore, $arrComment
     * @return $item, $arrScore, $arrComment
     */
    public function updateContent($item, $arrScore, $arrComment)
    {
        for($i=0; $i < count($item); $i++){
            if(isset($item[$i]['subs'])){
               [ $item[$i]['subs'],$arrScore, $arrComment] = $this->updateContent($item[$i]['subs'], $arrScore, $arrComment);

            }else{
                $item[$i]['score'] = reset($arrScore);
                $item[$i]['comment'] = reset($arrComment);
                array_shift($arrScore);
                array_shift($arrComment);
            }
        }
        return [$item, $arrScore, $arrComment];
    }
}
