<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\UpdateLanguageRequest;
use App\Logics\LanguageManager;
use App\Models\Language;
use App\Utils\LogUtil;
use Exception;
use Illuminate\Support\Facades\App;

class LanguageController extends AbstractApiController
{
    /**
     * @var LanguageManager
     */
    protected $languageManager;

    public function __construct(LanguageManager $languageManager)
    {
        $this->languageManager = $languageManager;
    }

    /**
     * Get all language active of systems.
     *
     * @throws Exception
     */
    public function index()
    {
        try {
            $result = $this->languageManager->getLanguages();
            return $this->renderJsonResponse($result, __('language.success'));
        } catch (Exception $exception) {
            LogUtil::error($exception->getMessage());
            throw new Exception($exception->getMessage());
        }
    }

    /**
     *
     * Update language
     *
     * @param UpdateLanguageRequest $request
     * @return json
     */
    public function updateLanguage(UpdateLanguageRequest $request)
    {
        try {
            $language = Language::find($request->input('language'));
            if($language){
                $locale = $language->name;
                App::setLocale($locale);
                session()->put('locale', $locale);
            }
            $this->languageManager->updateLanguage($request);
            return $this->renderJsonResponse(null, __('language.success'));
        } catch (Exception $exception) {
            LogUtil::error($exception->getMessage());
            throw new Exception($exception->getMessage());
        }
    }
}