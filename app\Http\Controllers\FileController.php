<?php

namespace App\Http\Controllers;

use App\Traits\ImageTrait;
use App\Traits\StorageTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;


class FileController extends Controller
{
    use StorageTrait;
    use ImageTrait;
    /**
     * Download file from Storage
     *
     * @param Request $request
     * @return mixed
     */
    public function download(Request $request){
        $file_path = $request->path;
        if (!Storage::disk(FILESYSTEM)->exists($file_path)) {
            abort(500);
        }
        return Storage::disk(FILESYSTEM)->download($file_path);
    }

    /**
     * Upload temporary document file
     *
     * @param Request $request
     * @return Response
     */
    public function uploadTemp(Request $request)
    {
        $file = $request->file('file');
        $fileType = $file->getClientOriginalExtension();
        $fileName =  $file->getClientOriginalName();
        if($request->resize){
            $file = $this->resizeImage($file->getRealPath(), IMAGE_ATTACHMENT_WIDTH);
            $filePath = $this->uploadFileByStream($file, TEMP_DIR.'/'.Str::random(25). '.' . $fileType);
        }
        else{
            $filePath = $this->uploadFile($file, TEMP_DIR.'/'.Str::random(25). '.' . $fileType);
        }
        $data = [
            'file_name' => $fileName,
            'file_path' => $filePath
        ];
        return response()->json([
            'status' => 200,
            'data' => $data
        ], Response::HTTP_OK);
    }

    /**
     * Remove temporary document file
     *
     * @param Request $request
     * @return Response
     *
     */
    public function removeTemp(Request $request)
    {
        $filePath = $request->file_path;
        // Check file path must belong to granted delete permission directory
        if (strpos($filePath, TEMP_DIR) != 0) {
            return response()->json([
                'status' => 403,
            ], Response::HTTP_FORBIDDEN);
        }

        $this->deleteFile($filePath);

        return response()->json([
            'status' => 200,
            'data' => $filePath,
        ], Response::HTTP_OK);
    }
    /**
     * get file size
     *
     */
    public function getFileSize($fileSize){
        if(is_numeric($fileSize)){
            if($fileSize > 0 && $fileSize < 1024)
                $fileSize = (string)round($fileSize,2) . ' B';
            elseif($fileSize >= 1024 && $fileSize < 1048576)
                $fileSize = (string)round($fileSize/1024,2) . ' KB';
            elseif($fileSize >= 1048576 && $fileSize < 1000000000)
                $fileSize = (string)round($fileSize/1048576,2) . ' MB';
            else
                $fileSize = (string)round($fileSize /1000000000,2) . ' GB';
        }
        return $fileSize;
    }
    /**
     * View temporary document file
     *
     * @param $filename
     * @return Response
     */
    public function showTmp($filename)
    {
        if (!Storage::disk(FILESYSTEM)->exists(TEMP_DIR . '/'.$filename)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->response(TEMP_DIR . '/'.$filename);
    }
    public function showCoding($id,$filename)
    {
        $questionDir = str_replace('{challenge_id}', $id, CONTEST_QUESTION_DIR).'/'.$filename;
        if (!Storage::disk(FILESYSTEM)->exists($questionDir)) {
            return back();
        }
        return Storage::disk(FILESYSTEM)->response($questionDir);
    }
}
