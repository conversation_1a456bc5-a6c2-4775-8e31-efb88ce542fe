<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Requests\UserConfigRequest;
use App\Logics\UserConfigManager;
use Exception;
use Illuminate\Support\Facades\Log;

class UserConfigController extends AbstractApiController
{
    /**
     * @var
     */
    protected $userConfigManager;

    public function __construct(
        UserConfigManager $userConfigManager
    )
    {
        $this->userConfigManager = $userConfigManager;
    }

    /**
     * Update or create config user setting
     * @param UserConfigRequest $request
     * @return json|Respond
     * @throws Exception
     */
    public function update(UserConfigRequest $request)
    {
        try {
            $data = $this->userConfigManager->updateOrCreateConfig($request);
            if (empty($data)) {
                return $this->respondBadRequest(trans('message.user.settingUserConfig.userConfig.fail'));
            }
            return $this->respondCreated($data, trans('message.user.settingUserConfig.userConfig.success'));
        } catch (Exception $e) {
            Log::error("[UserConfigController][update] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[UserConfigController][update] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }


    /**
     * Get list config user setting
     * @return json
     * @throws Exception
     */
    public function list()
    {
        try {
            $data = $this->userConfigManager->getConfig();
            return $this->renderJsonResponse($data, trans('message.user.settingUserConfig.listUserConfig.success'));
        } catch (Exception $e) {
            Log::error("[UserConfigController][getSettingConfig] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[UserConfigController][getSettingConfig] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
}