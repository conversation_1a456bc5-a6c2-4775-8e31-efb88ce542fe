<?php

namespace App\Exports\Timekeeping;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use App\Models\Attendance;
use App\Models\FoodOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class TimeKeepingMultiSheetExport implements FromCollection, WithMultipleSheets
{
    protected $allStaffs;
    protected $date;
    protected $holidays;

    function __construct($allStaffs, $date, $holidays, $isBeeTechCompany) {
        $this->allStaffs = $allStaffs;
        $this->date = $date;
        $this->holidays = $holidays;
        $this->isBeeTechCompany = $isBeeTechCompany;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->allStaffs;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $month = Carbon::createFromFormat('d/m/Y','01/'. $this->date)->month;
        $year = Carbon::createFromFormat('d/m/Y','01/'.$this->date)->year;
        $numDays = Carbon::createFromFormat('d/m/Y','01/'. $this->date)->endOfMonth()->day;
        $date = [
            'numDays' => $numDays,
            'month' => $month,
            'year' => $year,
        ];
        $sheets[] = new SalarySheetExport($this->allStaffs->toArray(), $numDays, $month, $year);

        $attendances = Attendance::select(
            'attendances.user_id',
            DB::raw('DATE(checked_at) as date'),
            DB::raw('TIME(MIN(checked_at)) as checked_in'),
            DB::raw('TIME(MAX(checked_at)) as checked_out'),
            DB::raw('CASE 
                WHEN MIN(food_orders.user_id) IS NOT NULL 
                THEN true ELSE false 
            END lunch')
        )
        ->leftJoin('food_orders', function($join) {
            $join->on('food_orders.user_id', 'attendances.user_id')
            ->join('food_menus', 'food_menus.id', 'food_orders.menu_id')
            ->whereRaw('DATE(attendances.checked_at) = DATE(food_menus.date)')
            ->whereNull('food_orders.deleted_at');
        })
        ->whereRaw('MONTH(checked_at) = '.$month)
        ->whereRaw('YEAR(checked_at) = '.$year)
        ->groupBy(DB::raw('attendances.user_id, DATE(checked_at)'))
        ->orderBy(DB::raw('attendances.user_id, DATE(checked_at)'))
        ->get();

        $notTimekeepingStillEatings = FoodOrder::select('food_orders.user_id', DB::raw('GROUP_CONCAT( food_menus.date ) as list_date'))
            ->leftjoin('food_menus', 'food_menus.id', 'food_orders.menu_id')
            ->whereRaw('MONTH(food_menus.date) ='. $month)
            ->whereRaw('YEAR(food_menus.date) =' .$year)
            ->groupBy('food_orders.user_id')
            ->get()->keyBy('user_id')->toArray();

        $cashLunchLocations = explode(',', env('CASH_LUNCH_LOCATIONS'));

        foreach ($this->allStaffs as $idx => $staff) {
            $notTimekeepingStillEatingsByUser = isset($notTimekeepingStillEatings[$staff->id]) ? explode(',', $notTimekeepingStillEatings[$staff->id]['list_date']) : [];
            $attendance = $attendances->where('user_id', $staff->id)->values();
            $sheetName = $this->handleSheetName($staff);
            $sheets[] = new TimekeepingSheetExport($sheetName, $attendance, $date, $staff, $idx, $this->holidays, $this->isBeeTechCompany, $cashLunchLocations, $notTimekeepingStillEatingsByUser);
        }
        return $sheets;
    }

    /**
     * Handle sheet name for export timekeeping sheet
     *
     * @param array $staff
     * @return string
     */
    public static function handleSheetName($staff) {
        $fullName = strtoupper(Str::slug($staff['first_name'].' '.$staff['last_name'], ' '));
        $sheetName = $staff['id'] . '. ' . $fullName;
        $sheetName = Str::limit($sheetName, 31, '');

        return $sheetName;
    }
}
