<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Api\json;
use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Requests\ChangePasswordRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class ChangePasswordController extends AbstractApiController
{
    /**
     * Change user's password
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(ChangePasswordRequest $request) {
        try {
            $user = Auth::user();
            if (Hash::check($request->old_password, $user->password)) {
                $user->password = Hash::make($request->new_password);
                $user->save();
                return response()->json([
                    'status' => 200,
                    'msg' => trans('message.update_succeed'),
                ]);
            } else {
                return response()->json([
                    'status' => 302,
                    'msg' => trans('message.wrong_password'),
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
                'msg' => trans('message.server_error'),
            ]);
        }
    }

    /**
     * Update password by user
     * @param Request $request
     * @return json
     */
    public function updatePassword(Request $request)
    {
        try {
            $oldPassword = $request->old_password;
            $newPassword = $request->new_password;
            $user = Auth::user();
            if (Hash::check($oldPassword, $user->password)) {
                $user->password = Hash::make($newPassword);
                $user->save();
                return $this->renderJsonResponse([], trans('message.update_succeed'));
            } else {
                return $this->renderJsonResponse([], trans('message.wrong_password'), Response::HTTP_FOUND);
            }
        } catch (\Exception $e) {
            return $this->respondInternalError(trans('message.server_error'));
        }
    }
}
