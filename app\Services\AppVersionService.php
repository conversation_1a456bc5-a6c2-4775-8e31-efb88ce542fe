<?php

namespace App\Services;

use App\Helpers\StringHelper;
use Illuminate\Http\Request;
use App\Models\Tenant\AppVersion;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class AppVersionService
{
    const CACHE_TTL = 86400; // 1 day in seconds
    const APP_VERSION_CACHE = 'app_version:latest:';

    //Api logic
    /**
     * Check version for API response
     * 
     * @param int $platform
     * @param string $currentVersion
     * @return array
     */
    public function checkVersion(int $platform, string $currentVersion): array
    {
        $latestVersion = $this->getLatestVersionCached($platform);

        if (!$latestVersion) {
            return [
                'current_version' => $currentVersion,
                'latest_version' => null,
                'has_new_version' => false,
                'force_update' => false,
                'mode' => AppVersion::MODE_PREVIEW,
                'published_at' => null,
                'description' => null
            ];
        }

        $comparison = version_compare($currentVersion, $latestVersion->version);

        return [
            'current_version' => $currentVersion,
            'latest_version' => $latestVersion->version,
            'has_new_version' => $comparison < 0,
            'force_update' => $comparison < 0 ? $latestVersion->force_update : false,
            'mode' => $comparison > 0 ? AppVersion::MODE_PREVIEW : AppVersion::MODE_PRODUCTION,
            'published_at' => $latestVersion->published_at ? $latestVersion->published_at->toDateTimeString() : null,
            'description' => $latestVersion->description
        ];
    }

    /**
     * Generate cache key for platform
     * 
     * @param int $platform
     * @return string
     */
    private function getCacheKey(int $platform): string
    {
        return self::APP_VERSION_CACHE . $platform;
    }

    /**
     * Get latest published version with cache (no prefix)
     * 
     * @param int $platform
     * @return AppVersion|null
     */
    private function getLatestVersionCached(int $platform): ?AppVersion
    {
        $redis = Redis::connection('cache');
        $key = $this->getCacheKey($platform);

        // Get from Redis without prefix
        $cached = $redis->get($key);

        if ($cached !== null) {
            return unserialize($cached);
        }

        // Get fresh data and cache it
        $version = $this->latestPublishedVersion($platform);

        if ($version) {
            $redis->setex($key, self::CACHE_TTL, serialize($version));
        }

        return $version;
    }

    /**
     * Clear all version cache completely (Redis only)
     * 
     * @return void
     */
    public function clearAllVersionCache(): void
    {
        $redis = Redis::connection('cache');

        $versionKeys = $redis->keys(self::APP_VERSION_CACHE . '*');

        if (!empty($versionKeys)) {
            $redis->del($versionKeys);
            Log::debug('[AppVersionService][clearAllVersionCache] Deleted ' . count($versionKeys) . ' version cache keys');
        }
    }

    /**
     * Get latest published version for platform without cache
     * Supports flexible version formats: 1.2, 1.2.3, 1.2.4.5.6, etc.
     *
     * @param int $platform
     * @return AppVersion|null
     */
    private function latestPublishedVersion(int $platform): ?AppVersion
    {
        // Get only the IDs and versions for comparison
        $versions = AppVersion::published()
            ->platform($platform)
            ->select('id', 'version')
            ->get();

        if ($versions->isEmpty()) {
            return null;
        }

        // Find the latest published version ID
        $latest = null;
        foreach ($versions as $version) {
            if ($latest === null) {
                $latest = $version;
                continue;
            }

            $compare = version_compare($version->version, $latest->version);
            if (
                $compare > 0 ||
                ($compare === 0 && $version->id > $latest->id)
            ) {
                $latest = $version;
            }
        }

        return AppVersion::find($latest->id);
    }

    // CRUD logic 
    /**
     * Get all versions with pagination and filters for admin panel
     * 
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getAllVersions(Request $request): \Illuminate\Database\Eloquent\Builder
    {
        $query = AppVersion::query();
        $stringHelper = new StringHelper();

        // Apply search filter
        if ($request->has('search')) {
            $searchTerm = $stringHelper->formatStringWhereLike($request->search);
            $query->where(function ($q) use ($searchTerm) {
                $q->where('version', 'like', '%' . $searchTerm . '%')
                    ->orWhere('description', 'like', '%' . $searchTerm . '%')
                    ->orWhere('release_notes', 'like', '%' . $searchTerm . '%');
            });
        }

        // Apply platform filter
        if (isset($request->platform) && $request->platform !== '') {
            $query->where('platform', (int) $request->platform);
        }

        // Apply status filter
        if (isset($request->status) && $request->status !== '') {
            $query->where('status', (int) $request->status);
        }

        // Default sort by created_at desc
        $query->orderBy('created_at', 'desc');

        return $query;
    }

    /**
     * Get latest versions for both platforms
     * 
     * @return array
     */
    public function getLatestVersions(): array
    {
        return [
            'ios' => $this->latestPublishedVersion(AppVersion::IOS),
            'android' => $this->latestPublishedVersion(AppVersion::ANDROID)
        ];
    }
}
