<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\CheckVersionRequest;
use App\Services\AppVersionService;
use Exception;
use Illuminate\Support\Facades\Log;

class CheckVersionController extends AbstractApiController
{
    protected $appVersionService;

    /**
     * Constructor
     * 
     * @param AppVersionService $appVersionService
     */
    public function __construct(AppVersionService $appVersionService)
    {
        $this->appVersionService = $appVersionService;
    }

    /**
     * Check app version
     *
     * @param CheckVersionRequest $request
     * @return json
     * @throws Exception
     */
    public function checkVersion(CheckVersionRequest $request)
    {
        try {
            $platform = $request->device;
            $currentVersion = $request->version;
             
            // Get version data from service
            $versionData = $this->appVersionService->checkVersion($platform, $currentVersion);
            
            // Return success response with version data           
            return $this->renderJsonResponse($versionData,__('message.version_check_success'));

        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}