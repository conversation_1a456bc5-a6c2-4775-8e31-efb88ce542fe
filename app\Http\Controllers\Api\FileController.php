<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Controllers\Controller;
use App\Logics\FileManager;
use App\Http\Requests\UploadFileRequest;
use App\Models\Message;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FileController extends AbstractApiController
{
    protected $fileManager;

    public function __construct(FileManager $fileManager)
    {
        $this->fileManager = $fileManager;
    }


    /**
     * handle upload file
     * @param UploadFileRequest $request
     * @return json
     * @throws Exception
     */
    public function upload(UploadFileRequest $request)
    {
        try {
            $data = $this->fileManager->handleUploaded($request);
            if(empty($data)){
                return $this->respondBadRequest(__('message.uploadFile.error'));
            }
            return $this->renderJsonResponse($data,__('message.uploadFile.success'));
        } catch(Exception $e) {
            Log::error("[UploadFileController][upload] error " . $e->getMessage());
            throw new Exception('[UploadFileController][upload] error ' . $e->getMessage());
        }
    }
    
    /**
     * get file
     * @param \Illuminate\Http\Request $request
     */
    public function get(Request $request){
        $filePath = $request->get('filePath');
        $download = $request->get('download');
        if (!isset($request->filePath) || !Storage::disk(FILESYSTEM)->exists($filePath)) {
            return $this->respondNotFound(__('api::messages.file.notFound'));
        }
        
        $infoPath = pathinfo(Storage::disk(FILESYSTEM)->path($filePath, PATHINFO_EXTENSION));
        $extension = $infoPath['extension'];
        $typeFile = $this->fileManager->checkExtensionUpload($extension);

        if(!$download && ($typeFile == Message::EXTENSION_VIDEO || $typeFile == Message::EXTENSION_RADIO)){
            $rangeHeader = request()->header('Range');
            $fileContents = Storage::disk(FILESYSTEM)->get($filePath);
            $headers = ['Content-Type' => Storage::disk(FILESYSTEM)->mimeType($filePath)];
            if ($rangeHeader) {
                return self::getResponseStream(FILESYSTEM, $filePath, $fileContents, $rangeHeader, $headers);
            } else {
                $httpStatusCode = 200;
                return response($fileContents, $httpStatusCode, $headers);
            }
        }
        if ($download) {
            return Storage::disk(FILESYSTEM)->download($request->get('filePath'), $request->get('fileName'));
        }

        return Storage::disk(FILESYSTEM)->response($request->get('filePath'));
    }
    
    /**
     * 
     * @param string $disk
     * @param string $fullFilePath
     * @param string $fileContents
     * @param string $rangeRequestHeader
     * @param array  $responseHeaders
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public static function getResponseStream($disk, $fullFilePath, $fileContents, $rangeRequestHeader, $responseHeaders) {
        $stream = Storage::disk($disk)->readStream($fullFilePath);
        $fileSize = strlen($fileContents);
        $fileSizeMinusOneByte = $fileSize - 1; //because it is 0-indexed. https://www.w3.org/Protocols/rfc2616/rfc2616-sec14.html#sec14.16
        list($param, $rangeHeader) = explode('=', $rangeRequestHeader);
        if (strtolower(trim($param)) !== 'bytes') {
            abort(400, "Invalid byte range request"); //Note, this is not how https://stackoverflow.com/a/29997555/470749 did it
        }
        list($from, $to) = explode('-', $rangeHeader);
        if ($from === '') {
            $end = $fileSizeMinusOneByte;
            $start = $end - intval($from);
        } elseif ($to === '') {
            $start = intval($from);
            $end = $fileSizeMinusOneByte;
        } else {
            $start = intval($from);
            $end = intval($to);
        }
        $length = $end - $start + 1;
        $httpStatusCode = 206;
        $responseHeaders['Content-Range'] = sprintf('bytes %d-%d/%d', $start, $end, $fileSize);
        $responseStream = response()->stream(function() use ($stream, $start, $length) {
            fseek($stream, $start, SEEK_SET);
            echo fread($stream, $length);
            fclose($stream);
        }, $httpStatusCode, $responseHeaders);
        return $responseStream;
    }
}
