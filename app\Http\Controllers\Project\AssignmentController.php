<?php

namespace App\Http\Controllers\Project;

use App\Enums\WorkingTypeEnum;
use App\Http\Controllers\Controller;
use App\Models\Assignment;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\AssignmentRequest;
use Illuminate\Http\JsonResponse;
use App\Models\SiteSetting;

class AssignmentController extends Controller
{
    /**
     * Display a listing of the resource.
     * 
     * @param Request $request
     * @return Response|JsonResponse
     */
    public function index(Request $request)
    {
        $dataScreen = AssignmentController::getDataScreenAssignments($request);
        $types = [
            Assignment::TYPE_OFFICIAL_STAFF => trans('message.filter_official_staff'),
            Assignment::TYPE_OUTSOURCER => trans('message.filter_outsourcer')
        ];
        $filterHtml = $this->getFilterHtml($request, $types, ['typeAssignments']);
        if($request->ajax()){
            $response = [
                'data' => $dataScreen,
            ];
            return response()->json($response, Response::HTTP_OK);
        }
        return view('assignment.index',['filterHtml' =>$filterHtml,'types' => $types, 'assignments' => $dataScreen["assignments"], 'projects' => $dataScreen["projects"]]);
    }

    /**
     * Update the specified resource.
     *
     * @param AssignmentRequest $request
     * @return JsonResponse
     */
    public function update(AssignmentRequest $request)
    {
        // Update or create the assignment.
        Assignment::updateOrCreate([
            'user_id' => $request->user_id,
        ],[
            'projects' => $request->projects,
            'note' => $request->note,
        ]);

        $dataScreen = AssignmentController::getDataScreenAssignments($request);

        return response()->json([
            'code' => Response::HTTP_OK, 
            'message' => 'Success', 
            'data' => ['assignments' => $dataScreen["assignments"], 'projects' => $dataScreen["projects"]]], 
            Response::HTTP_OK
        );
    }

    /**
     * get data for screen
     *
     * @return Response
     */
    public function getDataScreenAssignments($request)
    {
        $calSalaries = SiteSetting::select('value')->where('id',SiteSetting::SALARY_PROCESS_ID)->first();
        $calSalaries = json_decode($calSalaries->value);
        $typeOfficialStaff = array_slice($calSalaries, 0, 2); // get type official staff 
        $typeAssignments = $request->get('typeAssignments') ?? [];
        $assignments = Assignment::select(
            'assignments.id',
            'assignments.projects',
            'assignments.note',
            'users.id as user_id',
            'users.salary_calculation_method',
            DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as user_name'),
            DB::raw('(CASE WHEN assignments.projects is not null THEN 1 ELSE 0 END) AS project_sort'),
        );
        if(!empty($typeAssignments) && count($typeAssignments) < 2){
            foreach ($typeAssignments as $type){
                if ($type == Assignment::TYPE_OFFICIAL_STAFF) {
                    $assignments->whereIn('users.salary_calculation_method', $typeOfficialStaff);
                }
                else{
                    $assignments->where(function($query) use ($typeOfficialStaff){
                        $query->whereNotIn('users.salary_calculation_method', $typeOfficialStaff)
                            ->orwhereNull('users.salary_calculation_method');
                    });
                }
            }
        }
        $assignments = $assignments->rightJoin('users', 'users.id', '=', 'assignments.user_id')
        ->whereIn('working_type', [WorkingTypeEnum::PART_TIME, WorkingTypeEnum::FULL_TIME, WorkingTypeEnum::INTERN])
        ->whereNull('users.deleted_at')
        ->whereNull('users.ended_at')
        ->orderBy('project_sort')
        ->orderBy('users.last_name')
        ->orderBy('users.first_name')->get()->toArray();

        $projects = Project::select('name')
        ->get();

        return ["assignments" => $assignments, "projects" => $projects];
    }

    /**
     * Get html filter
     * @param Request $request
     * @param array $fields
     */

    public function getFilterHtml(Request $request, $types, $fields = []){
        $filterHtml = "";
        foreach ($fields as $field){
            if ($request->has($field) && $request->$field != null){
                if($field == 'typeAssignments'){
                    foreach($request->typeAssignments as $type){
                        if($type == Assignment::TYPE_OFFICIAL_STAFF){
                            $filterHtml= '<span class="badge badge-primary badge-filter bgr">'.$types[$type].'</span> ';
                        }
                        if($type== Assignment::TYPE_OUTSOURCER){
                            $filterHtml.= '<span class="badge badge-primary badge-filter bgr">'.$types[$type].'</span> ';
                        }
                    }
                }
            }
        }
        return $filterHtml;
    }
}
