<?php
define('APP_API_URL',env('APP_API_URL'));
define('API_VERSION','/v1');
define('API_FULL_URL', APP_API_URL . API_VERSION);

// Working time
define('MORNING_START', '08:00:00');
define('MORNING_END', '12:00:00');
define('AFTERNOON_START', '13:30:00');
define('AFTERNOON_END', '17:30:00');
define('FOOD_ORDER_TIME', '10:00');
define('FULL_TIME', 8); // 8 hours
define('PADDING_TIME', 300); // 300 seconds = 5 minutes
define('WORKING_SATURDAY',false);
define('WORKING_SUNDAY',false);
define('LATE_JOIN_EXCEED', 2);

// Export timekeeping
define('RESET_LEAVE_DAY_YEARLY_MONTH', 12);
define('RESET_LEAVE_DAY_YEARLY_HOURS', 24);

// Upload directories
define('USER_DIR', 'users');
define('CHECKED_IMAGE_DIR', 'checked_image_logs');
define('DOCUMENT_TEMP_DIR','tmp/documents');
define('DOCUMENT_DIR','projects/{project_id}/documents');
define('TASK_ATTACHMENT_DIR','projects/{project_id}/tasks/{task_id}/attachments');
define('ASSET_ATTACHMENT_DIR','asset/{asset_id}/attachments');
define('ASSET_BIDDING_PACKAGE_ATTACHMENT_DIR','asset_bidding_package/{bidding_package_id}/attachments');
define('INVENTORY_ATTACHMENT_DIR','inventory/{inventory_id}/attachments');
define('CONTEST_ANSWER_DIR','contest/{challenge_id}/answer');
define('CONTEST_QUESTION_DIR','contest/{challenge_id}/question');

define('MENU_DIR','menu');
define('REQUEST_DIR','request/{request_id}');
define('TEMP_DIR','tmp');
define('FILE_DIR','files/');
define('DOWNLOADS_DIR', 'downloads');
define('USER_AVATAR_WIDTH', 320);
define('IMAGE_ATTACHMENT_WIDTH', 1024);
define('USER_FACE_IMAGE_WIDTH', 320);
define('WIKI_DIR','projects/{project_id}/wiki/page/{page_id}/document');

// BeeID API
define('API_UPDATE_LEAVE_DAYS', env('BEEID_API_URL').'/beeid/api/update_leave_days');
define('API_EXPORT_TIMEKEEPING', env('BEEID_API_URL').'/beeid/api/export_timekeeping');
define('API_VALIDATE_USER_FACE', env('BEEID_API_URL').'/beeid/api/validate_user_face');
define('API_SET_USER_FACE_RECOGNITION', env('BEEID_API_URL').'/beeid/api/set_user_face_recognition');
define('EXPORT_TIMEKEEPING_FILENAME', 'BeeTech_Salary_:month_:year.xls');

// Queue jobs
define('QUEUE_SET_USER_FACE_RECOGNITION', 'default');
define('QUEUE_RESET_PASSWORD_NOTIFICATION', 'default');
define('QUEUE_CREATE_EVALUATION', 'default');
define('QUEUE_MAIL', 'default');


// Coding contest
define('CODING_CONTEST_DIR', 'coding_contest');
define('CODING_CONTEST_CHALLENGE_DIR', CODING_CONTEST_DIR.'/challenges');
define('CODING_CONTEST_TMP_DIR', CODING_CONTEST_DIR.'/tmp');

// Separators
define('GROUP_CONCAT_SEPARATOR', 'Ͼ');
define('CONCAT_SEPARATOR', 'Ͽ');

define('FILESYSTEM', env('USE_TENANT', false) ? 'tenant' : env('FILESYSTEM_DRIVER', 'local'));

//Notification
define('TIME_PUSH_NOTIFICATION_BEFORE_MEETING',5);

//Site Setting
define('WIDTH_PERCENT_TABLE',92);


define('SEPARATOR_ORDER', '◞');

//Report test
define('REPORT_TEST_CC_TO_MAIL','<EMAIL>');

//salary calculation method
define('PART_TIME', 'Partime');
define('FULL_TIME_SALARY','Fulltime');
define('TTS_1', 'TTS giai đoạn 1');
define('TTS_2_FULL_TIME', 'TTS giai đoạn 2 fulltime');
define('TTS_2_PART_TIME', 'TTS giai đoạn 2 partime');
define('FREELANCER', 'Freelancer');
define('MENTOR', 'Mentor');

// personal status
define('OFFICIAL', 'Chính thức');
define('PROBATION', 'Thử việc');
define('INTERN', 'TTS');
define('FOREIGNER', 'Người nước ngoài');
define('COLLABORATOR', 'Cộng tác viên');

define('TEMPLATE_EXPORT_ASSET', 'Template_phụ_lục_chi_tiết_kiểm_kê.xlsx');
//API
define('PER_PAGE', 20);
define('SFTP_PATH',env('SFTP_PATH', ''));

define('MESSAGE_DIR','messages/');

define('CONVERSATION_AVATAR_WIDTH', 320);
define('CONVERSATION_DIR','conversations/');
define('KEY_USER_CONFIG', 'notification');

define('keyPushNotification', env('KEY_PUSH_NOTIFICATION', 'AAAAXovgVMs:APA91bHC1IpXnTFDHiEtnrN4ivMrl9x0oT9SVhWfJjv8au1QQCPJY0evdpmXe16sJGfNy-tHIVZAotzXQ6eqgri_OYEO-NoM5gSbxlaByBipWA3pQKjtPh_IJB36MkCeQ8RcFcJE31Kt'));
define('ios_schema',env('IOS_SCHEME', ''));
define('ios_store',env('IOS_STORE', ''));
define('android_schema',env('ANDROID_SCHEME', ''));
define('android_package',env('ANDROID_PACKAGE', ''));

define('FCM', env('FIREBASE_URL', 'https://fcm.googleapis.com/v1/projects/bee-chat-dev-be6a3/messages:send'));
define('FIREBASE_CREDENTIALS_PATH', env('FIREBASE_CREDENTIALS_PATH', 'bee-chat-dev-be6a3-firebase-adminsdk-uiapt-663d089c36.json'));
define('GG_MAP_API_URL', env('GG_MAP_API_URL', 'https://maps.googleapis.com/maps/api/geocode/json?'));
define('GOOGLE_MAPS_API_KEY', env('GOOGLE_MAPS_API_KEY', ''));
define('GG_MAP_API_OPTION_LOCATION_TYPE', env('GG_MAP_API_OPTION_LOCATION_TYPE', 'ROOFTOP'));

//Site
define('BEEID','BeeID');
define('BEECHAT','BEECHAT');
