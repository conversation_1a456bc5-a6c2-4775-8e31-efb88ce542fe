<?php

namespace App\Http\Requests;

use App\Models\Attendance;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class TimeKeepingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'checked_type' => 'required|numeric',
            'latitude' => [
                'numeric',
                Rule::requiredIf(fn() => $this->input('checked_type') == Attendance::GPS_CHECK),
            ],
            'longitude' => [
                'numeric',
                Rule::requiredIf(fn() => $this->input('checked_type') == Attendance::GPS_CHECK),
            ],
        ];
    }

    /**
     * Message validate.
     * 
     * @return array
     */
    public function messages(): array
    {
        return [
            'checked_type.required' => __('message.request.input_required', ['attribute' => __('validation.attributes.checked_type')]),
            'checked_type.numeric' => __('message.request.input_numeric', ['attribute' => __('validation.attributes.checked_type')]),

            'latitude.required' => __('message.request.input_required', ['attribute' => __('validation.attributes.latitude')]),
            'latitude.numeric' => __('message.request.input_numeric', ['attribute' => __('validation.attributes.latitude')]),

            'longitude.required' => __('message.request.input_required', ['attribute' => __('validation.attributes.longitude')]),
            'longitude.numeric' => __('message.request.input_numeric', ['attribute' => __('validation.attributes.longitude')]),
        ];
    }
}
