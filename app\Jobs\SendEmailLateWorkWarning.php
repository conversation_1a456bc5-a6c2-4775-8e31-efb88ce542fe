<?php

namespace App\Jobs;

use App\Mail\LateWorkWarning;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendEmailLateWorkWarning extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $info;

    /**
     * Create a new job instance.
     *
     * @param $info
     */
    public function __construct($info, $websiteId=null)
    {
        // Inherit from the parent
        parent::__construct($websiteId);

        $this->info = $info;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Inherit from the parent
        parent::handle();

        $email = new LateWorkWarning($this->info);
        Mail::to($this->info['admin_email'])->send($email);
    }
}
