<?php

namespace App\Jobs\Notification;

use App\Helpers\NotificationHelper;
use App\Jobs\BaseQueue;
use App\Logics\ContactManager;
use Exception;
use Illuminate\Support\Facades\Log;
use App\Logics\UserConfigManager;
use App\UserConfig;

class sendNotificationContact extends BaseQueue
{

    /**
     * @var
     */
    protected $dataNotification;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($dataNotification, $websiteId = null)
    {
        parent::__construct($websiteId);
        $this->dataNotification = $dataNotification;
    }

    /**
     * Execute the job.
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        parent::handle();
        try {
            // Set sub domain name. 
            $subDomain = $this->getSubDomainFormHostname();
            $this->dataNotification['subdomain'] = $subDomain;
            
            $receiverUserId = $this->dataNotification['receiver_user_id'];
            $userConfigManager = new UserConfigManager();
            $hasUserConfigPushNotification = $userConfigManager->checkPushNotification(
                UserConfig::PUSH_FIREBASE_NOTIFICATION,
                $receiverUserId
            );
            if($hasUserConfigPushNotification){
                $deviceToken = NotificationHelper::getDeviceToken([$receiverUserId]);
                NotificationHelper::sendNotifyUserDevice($receiverUserId, $deviceToken, $this->dataNotification, ContactManager::NOTIFICATION_CONTACT);
            }
        } catch (Exception $ex) {
            Log::error("[sendNotificationContact][handle] error " . $ex->getLine() . $ex->getMessage());
            throw new Exception('[sendNotificationContact][handle] error ' . $ex->getLine() . $ex->getMessage());
        }
    }
}
