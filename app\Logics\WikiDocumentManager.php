<?php

namespace App\Logics;

use App\Models\WikiDocument;
use Illuminate\Database\Eloquent\Model;
use App\Traits\StorageTrait;
use Illuminate\Support\Facades\URL;

class WikiDocumentManager extends Model
{

    /**
     * get wiki document
     */
    public function getWikiDocument($id)
    {
        $file = WikiDocument::where('wiki_documents.id', $id)->first();
        return $file;
    }
    use StorageTrait;
    /**
     * Save saveWikiDocument
     */
    public function saveWikiDocument($data ,$wikiDocuments, $descriptions, $path, $updateColumn) {
        $files = [];
        $hostname = URL::to('/');
        if($wikiDocuments){
            foreach( $wikiDocuments as $key => $wikiDocument){
                // Move attachment files from temporary directory to attachment directory
                $wikiDocument = json_decode($wikiDocument, true);
                $fileName = basename($wikiDocument['file_path']);
                $destinationPath = $path.$fileName;
                $fileSize = $this->moveFile($wikiDocument['file_path'],$destinationPath);
                // Save saveWikiDocument
                $wikiDoc = new WikiDocument();
                $wikiDoc->project_wiki_id = $data->project_wiki_id;
                $wikiDoc->file_size = $fileSize;
                $wikiDoc->file_name = $wikiDocument['file_name'];
                $wikiDoc->file_path = $destinationPath;
                if($descriptions){
                    $wikiDoc->description = $descriptions[$key];
                }
                $wikiDoc->save();
                array_push($files, $wikiDoc);

                $imageUrlPrevious = $hostname. "/tmp/" . $fileName;
                $imageUrlCurrent = $hostname. "/wiki-document/" . $wikiDoc->id;
                $data->$updateColumn = str_replace($imageUrlPrevious, $imageUrlCurrent, $data->$updateColumn);
            }
        }
        $data->save();
        return $files;
    }
}
