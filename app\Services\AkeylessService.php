<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AkeylessService
{
    protected $accessId;
    protected $accessKey;

    public function __construct()
    {
        $this->accessId = config('services.akeyless.access_id');
        $this->accessKey = config('services.akeyless.access_key');
    }

    /**
     * Get the Akeyless token using access ID and access key.
     *
     * @return string
     * @throws \Exception
     */
    public function getToken(): string
    {
        $response = Http::timeout(30)
            ->retry(3, 1000)
            ->post('https://api.akeyless.io/auth', [
                'access-id' => $this->accessId,
                'access-key' => $this->accessKey,
            ]);

        if ($response->successful()) {
            $data = $response->json();

            if (!isset($data['token'])) {
                throw new \Exception('Token not found in Akeyless response');
            }

            return $data['token'];
        }

        Log::error('Akeyless Auth failed', [
            'status' => $response->status(),
            'response' => $response->body()
        ]);

        throw new \Exception('Akeyless Auth failed: ' . $response->body());
    }

    /**
     * Get a secret from Akeyless vault.
     *
     * @param string $secretPath
     * @param int|null $version
     * @return string
     * @throws \Exception
     */
    public function getSecret(string $secretPath, ?int $version = null): string
    {
        // Validate version if provided
        if ($version !== null && $version <= 0) {
            throw new \Exception("Version must be a positive integer, got: {$version}");
        }

        // If version is specified, fetch directly without caching
        if ($version !== null) {
            return $this->fetchSecret($secretPath, $version);
        }

        // Only cache for latest version
        $cacheKey = 'akeyless_secret_' . md5($secretPath);
        return Cache::remember($cacheKey, 86400, function () use ($secretPath) {
            return $this->fetchSecret($secretPath);
        });
    }

    /**
     * Fetch secret from Akeyless API
     */
    private function fetchSecret(string $secretPath, ?int $version = null): string
    {
        $token = $this->getToken();

        $payload = [
            'names' => [$secretPath],
            'token' => $token,
        ];

        if ($version !== null) {
            $payload['version'] = $version;
        }

        $response = Http::timeout(30)
            ->retry(3, 1000)
            ->post('https://api.akeyless.io/get-secret-value', $payload);

        if ($response->successful()) {
            $data = $response->json();

            if (!isset($data[$secretPath])) {
                throw new \Exception("Secret '{$secretPath}' not found in response");
            }

            return $data[$secretPath];
        }

        Log::error('Failed to fetch secret from Akeyless', [
            'secret_path' => $secretPath,
            'version' => $version,
            'status' => $response->status(),
            'response' => $response->body()
        ]);

        throw new \Exception('Failed to fetch secret: ' . $response->body());
    }
}