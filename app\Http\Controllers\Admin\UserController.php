<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\RequestHelper;
use App\Helpers\StringHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateUserFormRequest;
use App\Jobs\SendMailCreateUser;
use App\Jobs\SetUserFaceRecognition;
use App\Logics\AssetManager;
use App\Logics\UserManager;
use App\Models\PropertyManagementAgency;
use App\Models\Role;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\Language;
use App\Models\UserDevice;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\ListEmployeeExport;
use App\Logics\DateFormatManager;

class UserController extends Controller
{
    const PER_PAGE = 15;

    protected $assetManager;

    public function __construct(AssetManager $assetManager)
    {
        $this->assetManager = $assetManager;
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Get all users
        $userManager = new UserManager();
        $users = $userManager->getAllUsers($request);

        // Pagination
        $perPage = $request->has('per_page') ? $request->input('per_page') : self::PER_PAGE;
        $users = $users->sortable()->paginate($perPage);

        // Redirect to last page if page parameter greater than last page
        if ($users->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $users->lastPage()]));
        }
        // Redirect to first page if page parameter less than 0
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        $is_filter = "";
        $fields = ['id','keyword','email','phone','gender','position','address','start_at'];
        foreach ($fields as $field){
            $tagSpanOpen = '<span class="badge badge-primary badge-filter bgr">';
            $tagSpanClose = '</span>';
            $value = '';
            if ($request->has($field) && $request->$field!= null){
                switch ($field){
                    case 'id':
                        $idItems = array_filter(array_map("trim",explode(",", StringHelper::escapeHtml($request->id))));
                        if(!empty($idItems)){
                        foreach($idItems as $idItem){
                            $value .= $tagSpanOpen ."#". $idItem . $tagSpanClose;
                            }
                        }
                        break;
                    case 'gender':
                        foreach ($request->gender as $gender){
                            $value .= $tagSpanOpen . trans('language.genders')[$gender] . $tagSpanClose;
                        }
                        break;
                    case 'position':
                        $positions = \App\Models\Position::whereIn('id',$request->position)->get();
                        foreach ($positions as $position){
                            $value .= $tagSpanOpen . $position->name . $tagSpanClose;
                        }
                        break;
                    default:
                        $value = $tagSpanOpen.StringHelper::escapeHtml($request->$field) . $tagSpanClose;
                        break;
                }
                $is_filter.= $value;
            }
        }

        return view('admin.user.index', [
            'users' => $users,
            'is_filter'=>$is_filter,
        ]);
    }

    /**
     * Create a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create() {
        // Store redirect url in session
        $languages = Language::select('id','name','display_name')->get();
        $listManagementAgency = PropertyManagementAgency::query()->select(['id', 'name'])->orderBy('sort_order')->get();
        $roleAssetManager = Role::query()
            ->select('id')
            ->where('name',Role::ROLE_ASSET_MANAGER)
            ->pluck('id')
            ->toArray();
        if((new RequestHelper())->parseRequestUri(url()->previous()) == route('admin.user.index')) {
            session(['redirect.admin.user.create' => url()->previous()]);
        }

        return view('admin.user.create',[
            'languages' => $languages,
            'listManagementAgency' => $listManagementAgency,
            'roleAssetManager' => $roleAssetManager
        ]);
    }

    /**
     * Store a new resource.
     *
     * @param CreateUserFormRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(CreateUserFormRequest $request) {
        DB::beginTransaction();
        try {
            //--- 1. Insert user to database ---
            $started_at = isset($request->started_at) ?  (Carbon::createFromFormat('d/m/Y', $request->started_at)) : Carbon::now() ;
            $userManager = new UserManager();
            $password = Str::random(16);
            if ($request->filled('password')) {
                $password = trim($request->input('password'));
            }
            $managementAgencyValue = $request->management_agency ?? [];
            $dataInsertManagementAgency = $this->assetManager->getDataInsertManagementAgency($managementAgencyValue);

            $fullName = $request->input('first_name') . ' '. $request->input('last_name');
            $user = $userManager->createUser(
                [
                    'first_name' => $request->first_name,
                    'last_name' => $request->last_name,
                    'name' => $request->check_name,
                    'name_search' => (new StringHelper)->transformSearchFullname($fullName),
                    'email' => $request->email,
                    'personal_email' => $request->personal_email,
                    'identity_card' => $request->identity_card,
                    'id_issued_place' => $request->id_issued_place,
                    'id_issued_at' =>  isset($request->id_issued_at)?Carbon::createFromFormat('d/m/Y', $request->id_issued_at):$request->id_issued_at,
                    'banking_account' => $request->banking_account,
                    'prefecture_id' => $request->prefecture_id,
                    'district_id' => $request->district_id,
                    'commune_id' => $request->commune_id,
                    'address' => $request->address,
                    'birthday' => isset($request->birthday)?Carbon::createFromFormat('d/m/Y', $request->birthday):$request->birthday,
                    'gender' => $request->gender,
                    'phone' => $request->phone,
                    'department_id' => $request->department,
                    'working_type' => $request->working_type,
                    'position_id' => $request->position,
                    'started_at' => $started_at,
                    'signed_at' => isset($request->signed_at)?Carbon::createFromFormat('d/m/Y', $request->signed_at):$request->signed_at,
                    'ended_at' => isset($request->ended_at)?Carbon::createFromFormat('d/m/Y', $request->ended_at):$request->ended_at,
                    'password' => bcrypt($password),
                    'language_id' => $request->language,
                    'number_dependents' => $request->number_dependents,
                    'number_social_insurance' => $request->number_social_insurance,
                    'list_dependents' => $request->list_dependents,
                    'work_place_id' => $request->work_place_id,
                    'company_insurance' => $request->company_insurance,
                    'company_contract' => $request->company_contract,
                    'monthly_allowance'=> $request->monthly_allowance,
                    'personnel_status' => $request->personnel_status,
                    'salary_calculation_method' => $request->salary_calculation_method,
                    'hour_register' => $request->hour_register,
                    'percent_finish' => $request->percent_finish,
                    'outbound_parking' => $request->has('outbound_parking'),
                    'position_allowance' => $request->has('position_allowance'),
                    'bta_allowance' => $request->has('bta_allowance'),
                    'started_at_phase2' => isset($request->started_at_phase2)?Carbon::createFromFormat('d/m/Y', $request->started_at_phase2):$request->started_at_phase2,
                    'vehicle_type' =>  $request->vehicle_type,
                    'social_insurance_fee' => $request->social_insurance_fee,
                    'asset_property_management_agency' => $dataInsertManagementAgency
                ],
                $request->avatar,
                $request->face_image
            );

            //--- Save role---
            $user->syncRoles($request->role);

            //--- Create conversation My chat---
            $conversation = Conversation::create([
                'name' => null,
                'type' => Conversation::TYPE_MY_CHAT,
                'avatar' => null,
                'desciption' => null,
                'is_hide' => 2,
            ]);
            $dataCreate = array(
                'conversation_id' => $conversation['id'],
                'user_id' => $user->id,
                'admin' => 1,
                'status' => 1,
                'created_at' => Carbon::now()
            );
            ConversationParticipant::create($dataCreate);
            
            // update user info to timekeeping device.
            $isBeeTechCompany = env('USE_TENANT', false) 
                && app(\Hyn\Tenancy\Environment::class)->website()->id == env('BEETECH_COMPANY_ID', '');
            if ($isBeeTechCompany) {
                $userManager->updateUserInfoToTimekeepingDevice($user, false, $request->face_image);
            }
            
            //--- 2. Add set_user_face_recognition job to queue ---
            // dispatch(new SetUserFaceRecognition($user))->onQueue(QUEUE_SET_USER_FACE_RECOGNITION);

            // Send registration email to the created user
            $protocol = request()->secure() ? 'https://' : 'http://';
            $fqdn = $_SERVER['SERVER_NAME'];
            $userInfo['email'] = $user->email;
            $userInfo['password'] = $password;
            $userInfo['language_id'] = $user->language_id;
            $userInfo['domain'] = $protocol.$fqdn;

            $websiteId = null;
            if (env('USE_TENANT', false)) {
                $websiteId = app(\Hyn\Tenancy\Environment::class)->website()->id;
            }
            dispatch(new SendMailCreateUser($userInfo, $websiteId))->onQueue(QUEUE_MAIL);

            DB::commit();
            return redirect()->route('admin.user.index')->with([
                'status_succeed' => trans('message.create_succeed')
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->route('admin.user.index')->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Check if user exist then return User, else return error message
     *
     * @param $user_id
     * @param bool $deleted
     * @return array|User
     */
    private function checkUserExist($user_id, $deleted=false) {
        if ($deleted) {
            $user = User::withTrashed()->find($user_id);
        } else {
            $user = User::find($user_id);
        }

        if ($user == null) {
            return [
                'status' => 302,
                'msg' => trans('message.user_not_exist'),
                'url_callback' => back()->getTargetUrl(),
            ];
        }

        return $user;
    }

    /**
     * Show the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = $this->checkUserExist($id, false);

        // Return error message if user not exist
        if (!$user instanceof User) {
            return back()->with([
                'status_failed' => isset($user['msg']) ? $user['msg'] : ''
            ]);
        }

        // Store redirect url in session
        if((new RequestHelper())->parseRequestUri(url()->previous()) == route('admin.user.index')) {
            session(['redirect.admin.user.show' => url()->previous()]);
        }

        return view('admin.user.show', [
            'user' => $user,
        ]);
    }

    /**
     * Edit the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = $this->checkUserExist($id, true);

        // Return error message if user not exist
        if (!$user instanceof User) {
            return back()->with([
                'status_failed' => isset($user['msg']) ? $user['msg'] : ''
            ]);
        }
        $deleted = false;
        // Check user deleted
        if ($user->deleted_at != null) {
            $deleted = true;
        }
        $userRole = $user->roles->pluck('id')->toArray();

        // Store redirect url in session
        if((new RequestHelper())->parseRequestUri(url()->previous()) == route('admin.user.index')) {
            session(['redirect.admin.user.edit' => url()->previous()]);
        }
        $languages = Language::select('id','name','display_name')->get();
        $listManagementAgency = PropertyManagementAgency::query()->select(['id', 'name'])->orderBy('sort_order')->get();
        $roleAssetManager = Role::query()
            ->select('id')
            ->where('name',Role::ROLE_ASSET_MANAGER)
            ->pluck('id')
            ->toArray();
        return view('admin.user.edit', [
            'user' => $user,
            'userRole' => $userRole,
            'deleted' => $deleted,
            'languages' => $languages,
            'listManagementAgency' => $listManagementAgency,
            'roleAssetManager' => $roleAssetManager
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return User|array
     */
    public function destroy($id)
    {
        $user = $this->checkUserExist($id);
        DB::table('oauth_access_tokens')->where('user_id',$id)->delete();
        UserDevice::where('user_id',$id)->delete();
        // Return error message if user not exist
        if (!$user instanceof User) {
            return $user;
        }

        if ($user->delete()) {
            $user->update([
                'name' => null,
            ]);
        }

        return [
            'status' => 200,
            'msg' => [
                'title' => trans('language.success'),
                'text' => trans('message.delete_user_succeed'),
            ],
        ];
    }

    /**
     * Restore the specified resource from storage.
     *
     * @param int $id
     * @return void
     */
    public function restore($id)
    {
        $user = $this->checkUserExist($id, true);

        // Return error message if user not exist
        if (!$user instanceof User) {
            return $user;
        }
        $user->restore();

        return [
            'status' => 200,
            'msg' => [
                'title' => trans('language.success'),
                'text' => trans('message.restore_user_succeed'),
            ],
        ];
    }

     /**
     *
     * export list employee to excel
     *
     * @param  Request $request
     * @return Responses
     */
    public function exportFile(Request $request){
        // Get all users
        $users = new UserManager();
        $users = $users->getAllUsers($request);
        $users = $users->get();

        // create array data export
        $index = 1;
        $result = [];
        $dateFormatManager = new DateFormatManager();
        foreach ($users as $user){
            $item['number_order'] = $index;
            $item['name_employee'] = $user->first_name.' '.$user->last_name;
            $item['name'] = $user->name;
            $item['email'] = $user->email;
            $item['personal_email'] = $user->personal_email;
            $item['phone'] = $user->phone;
            $item['gender'] =  isset(trans('language.genders')[$user->gender])?trans('language.genders')[$user->gender]:'';
            $item['birthday'] = $user->birthday ? $dateFormatManager->dateFormatLanguage($user->birthday,'d/m/Y') : '';
            $item['work_place_name'] = $user->work_place_name;
            $item['department_name'] = $user->department_name;
            $item['position_name'] = $user->position_name;
            $item['working_type'] = isset(trans('language.working_types')[$user->working_type])?trans('language.working_types')[$user->working_type]:'';
            $hometown = '';
            if (isset($user->commune->name)) {
                $hometown .= $user->commune->name.', ';
            }
            if (isset($user->district->name)) {
                $hometown .= $user->district->name.', ';
            }
            if (isset($user->prefecture->name)) {
                $hometown .= $user->prefecture->name;
            }
            $item['hometown'] = $hometown;
            $item['address'] = $user->address;
            $item['identity_card'] = $user->identity_card;
            $item['id_issued_place'] = $user->id_issued_place;
            $item['id_issued_at'] = $user->id_issued_at ? $dateFormatManager->dateFormatLanguage($user->id_issued_at,'d/m/Y') : '';
            $item['banking_account'] = $user->banking_account;
            $item['number_social_insurance'] = $user->number_social_insurance;
            $item['number_dependents'] = $user->number_dependents;
            $item['signed_at'] = $user->signed_at ? $dateFormatManager->dateFormatLanguage($user->signed_at,'d/m/Y') : '';
            $item['started_at'] = $user->started_at ? $dateFormatManager->dateFormatLanguage($user->started_at,'d/m/Y') : '';
            $item['ended_at'] = $user->ended_at ? $dateFormatManager->dateFormatLanguage($user->ended_at,'d/m/Y') : '';
            $index += 1;
            $result[] = $item;
        }
        $pathPrefix = env('USE_TENANT', false) ? app(\Hyn\Tenancy\Website\Directory::class)->path() : '';
        $path = TEMP_DIR . '/'. Str::random(25).'.xlsx';
        $nameFile = trans('language.employee_list').'_'.Carbon::now()->format('d-m-Y-H-i').'.xlsx';
        Excel::store(new ListEmployeeExport($result), $pathPrefix.$path);
        return Storage::disk(FILESYSTEM)->download($path, $nameFile);
    }
    
    public function ajaxEmailSuggest(Request $request){
        $firstName = $request->firstName;
        $lastName = $request->lastName;
        $arrWordsFirstName = explode(" ", $firstName);
        $arrWordsLastName = explode(" ", $lastName);
        $acronymFirstName = "";
        $acronymLastName = "";
        $count = 0;
        
        if (count($arrWordsLastName) >= 2 ){
            $lastElementLastName = Str::slug(end($arrWordsLastName));
            array_pop($arrWordsLastName);
            foreach ($arrWordsLastName as $w) {
                $acronymLastName .= mb_substr($w, 0, 1);
            }
        }
        else{
            $lastElementLastName = Str::slug($lastName);
        }
        foreach ($arrWordsFirstName as $w) {
            $acronymFirstName .= mb_substr($w, 0, 1);
        }
        
        do{
            $emailSuggest = $lastElementLastName  . '.' . Str::slug(strtolower($acronymFirstName)) . Str::slug(strtolower($acronymLastName)) . ($count == 0 ? "": ($count <= 9 ? "0$count" : $count )) . '@beetechsoft.vn';
            $count++;
            $result = User::withTrashed()->select('email')
                ->where('users.id', '!=', 0)
                ->where('email',$emailSuggest)
                ->first();
        }while(!empty($result));
        return response()->json([
            'emailSuggest' => $emailSuggest
        ]);
    }
}
