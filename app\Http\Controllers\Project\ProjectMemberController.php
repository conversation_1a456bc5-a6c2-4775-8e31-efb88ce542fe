<?php

namespace App\Http\Controllers\Project;

use App\Http\Requests\ProjectMemberRequest;
use App\Logics\ProjectMemberManager;
use App\Logics\UserManager;
use App\Models\Project;
use App\Models\ProjectMember;
use App\Models\ProjectRole;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\ProjectGroupRole;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;

class ProjectMemberController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param $request
     * @return \Illuminate\Http\Response
     */
    public function store(ProjectMemberRequest $request,$id)
    {
        DB::beginTransaction();
        try {
            $preUrl = empty(URL::previous())?route('project.members',['id'=>$id]):(URL::previous());
            $userId = Auth::id();

            //Check user is manager
            $userManager = new UserManager();
            $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

            if (!$isManager) {
                return redirect($preUrl);
            }
            $members = $request->member;
            $role_id = $request->position;
            $project_id = $request->id;
            $created_at = isset($request->created_at)? \Carbon\Carbon::createFromFormat('d/m/Y', $request->created_at): Carbon::now();
            foreach($members as $member){
                $hasProjectRole = $userManager->hasProjectRole($member,$project_id,$role_id);
                if($hasProjectRole == false){
                    $projectMember = new ProjectMember();
                    $projectMember->project_id = $project_id;
                    $projectMember->user_id = $member;
                    $projectMember->role_id = $role_id;
                    $projectMember->created_at = $created_at;
                    $projectMember->save();
                }
            }

            DB::commit();
            return redirect($preUrl)->with([
                'status_succeed' => trans('message.create_member_succeed')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect($preUrl)->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Get member
     *
     * @param Illuminate\Http\Request $request
     */
    public function ajaxGetMember(Request $request)
    {
        $userId = auth()->id();
        $projectId = $request->projectId;
        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$projectId,ProjectRole::ProjectManager);

        if (!$isManager) {
            return response()->json(Response::HTTP_FORBIDDEN);
        }

        $columns = [
            'project_members.user_id',
            'users.first_name',
            'users.last_name',
            'users.email',
            'users.avatar',
            DB::raw('group_concat(project_members.role_id) as roles')
        ];
        $member = ProjectMember::select($columns)
            ->leftJoin('users','users.id','project_members.user_id')
            ->where('project_members.user_id',$request->userId)
            ->where('project_members.project_id',$projectId)
            ->groupby('project_members.user_id')
            ->first();
        if($member == null){
            return response()->json(Response::HTTP_NOT_FOUND);
        }
        return response()->json($member);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param ProjectMemberRequest $request
     * @return \Illuminate\Http\Response
     */
    public function update(ProjectMemberRequest $request,$id)
    {
        DB::beginTransaction();
        try {
            $preUrl = empty(URL::previous())?route('project.members',['id'=>$id]):(URL::previous());
            $userId = Auth::id();

            //Check user is manager
            $userManager = new UserManager();
            $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

            if (!$isManager) {
                return redirect($preUrl);
            }
            $roles = $request->position;
            $member = $request->member;
            // Get all old role of member            
            $oldRole = ProjectMember::where('project_id', $id)
            ->where('user_id', $member)
            ->select('role_id')
            ->get();
            foreach ($oldRole as $role) {
                $oldRoles[] = (string)$role->role_id;
            }

            $deleteRoles = array_diff($oldRoles, $roles);
            //Delete role
            ProjectMember::where('project_id', $id)
            ->where('user_id', $member)
            ->whereIn('role_id', $deleteRoles)
            ->delete();
            //Update created_at 
            $created_at = isset($request->created_at)? \Carbon\Carbon::createFromFormat('d/m/Y', $request->created_at): Carbon::now();
            $updateRoles = array_intersect($oldRoles, $roles);
            ProjectMember::where('project_id', $id)
            ->where('user_id', $member)
            ->whereIn('role_id', $updateRoles)
            ->update(['created_at' => $created_at]);
            
            $checkHasRoleManager = ProjectMember::where('project_id', $id)
            ->where('role_id', ProjectGroupRole::PROJECT_MANAGER)
            ->select('role_id')
            ->get();
            if(count($checkHasRoleManager) <= 0){
                return back()->with([
                    'status_failed' => trans('message.update_project_failed')
                ]);
            }
            $createRoles = array_diff($roles, $oldRoles);
            foreach($createRoles as $role){
                //Add new role member
                $projectMember = new ProjectMember();
                $projectMember->project_id = $id;
                $projectMember->user_id = $member;
                $projectMember->role_id = $role;
                $projectMember->created_at = $created_at;
                $projectMember->created_by = $userId;
                $projectMember->save();
            }
            
            DB::commit();
            return redirect($preUrl)->with([
                'status_succeed' => trans('message.update_succeed')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect($preUrl)->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request,$id)
    {
        $userId = Auth::id();

        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        if (!$isManager) {
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'msg' => trans('message.not_permission_delete_member'),
                'url_callback' => back()->getTargetUrl(),
            ];
        }
        $checkHasRoleManager = ProjectMember::where('project_id', $id)
            ->where('role_id', ProjectGroupRole::PROJECT_MANAGER)
            ->where('user_id','!=',$request->userId)
            ->select('role_id')
            ->get();
        if(count($checkHasRoleManager) <= 0){
            return [
                'status' => Response::HTTP_FORBIDDEN,
                'msg' => [
                    'title' => trans('message.update_project_failed')
                ],
                'url_callback' => back()->getTargetUrl(),
            ];
        }
        ProjectMember::where('user_id',$request->userId)
            ->where('project_id',$id)
            ->delete();
        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'text' => trans('message.delete_member_succeed')
            ],
            'url_callback' => back()->getTargetUrl(),
        ];
    }
}
