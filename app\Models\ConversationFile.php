<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ConversationFile extends Model
{
    protected $table = 'conversation_files';
    use SoftDeletes;
    const TYPE_IMAGE = 1;
    const TYPE_VIDEO = 2;
    const TYPE_RADIO = 3;
    const TYPE_FILE_OTHER = 4;
    const TYPE_LINK = 5;
    const FILTER_YESTERDAY = 1;
    const FILTER_LAST_WEEK = 2;
    const FILTER_LAST_MONTH = 3;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['message_id','name','thumbnail','path','size'];
}
