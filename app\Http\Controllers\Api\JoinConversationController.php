<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Api\AbstractApiController;
use App\Http\Requests\AcceptOrRejectConversationRequest;
use App\Logics\JoinConversationManager;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Logics\ConversationManager;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class JoinConversationController extends AbstractApiController
{
    /**
     * @var JoinConversationManager
     */

    protected $conversationJoinManager;

    public function __construct(JoinConversationManager $conversationJoinManager)
    {
        $this->conversationJoinManager = $conversationJoinManager;
    }
    /**
     * get list request join room
     *
     * @param   Request  $request
     *
     * @return  json
     */
    public function getListRequestJoin(Request $request)
    {
        try {
            $idUser = Auth::guard('api')->user()->id;
            $conversationId = $request->get("conversation_id");
            if(empty($conversationId)){
                return $this->respondBadRequest(__('message.conversation.join.listJoinRequest.fail'));
            }
            $data = $this->conversationJoinManager->getListRequestJoin($idUser, $conversationId);
            return $this->renderJsonResponse($data, __('message.conversation.join.listJoinRequest.success'));
        } catch (Exception $e) {
            Log::error("[ConversationController][listConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationController][listConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * accept or reject request join room
     *
     * @param   Request  $request
     *
     * @return  json
     */
    public function acceptOrRejectJoinConversation(AcceptOrRejectConversationRequest $request)
    {
        try {
            $data = $this->conversationJoinManager->acceptOrRejectJoinConversation($request);
            return $this->renderJsonResponse($data, __('message.conversation.join.accept.success'));
        } catch (Exception $e) {
            Log::error("[ConversationController][listConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationController][listConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
    /**
     * request to join conversation
     * @param int $conversationId
     * @return Respond|json
     * @throws Exception
     */

     public function requestConversation($conversationId)
     {
         try {
            [$data,$msg] = $this->conversationJoinManager->requestJoinConversation($conversationId);
             if (isset($data['status_error'])) {
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
                if ($data['status_error'] == Response::HTTP_BAD_REQUEST) {
                    return $this->respondBadRequest(__('message.conversation.join.request.exists_participant'));
                }
            }
            return $this->renderJsonResponse($data, $msg);
         } catch (Exception $e) {
             Log::error("[ConversationController][requestConversation] error " . $e->getMessage());
             throw new Exception('[ConversationController][requestConversation] error ' . $e->getMessage());
         }
     }
}
