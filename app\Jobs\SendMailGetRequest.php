<?php

namespace App\Jobs;

use App\Mail\NotifyMailRequest;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendMailGetRequest extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $number;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user,$number,$websiteId=null)
    {
        parent::__construct($websiteId);
        $this->user = $user;
        $this->number = $number;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $email = new NotifyMailRequest($this->number, $this->user);
        Mail::to($this->user->email)->send($email);
    }
}
