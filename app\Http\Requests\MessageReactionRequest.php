<?php

namespace App\Http\Requests;

use App\Models\Message;
use App\Models\MessageReaction;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class MessageReactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $userId = Auth::guard('api')->user()->id;
        $rules = [
            'message_id' => ['required','numeric',function ($attribute, $value, $fail) use ($userId) {
                $message = Message::query()
                    ->join('conversation_participants',function($join) use ($userId){
                        $join->on('conversation_participants.conversation_id','messages.conversation_id')
                            ->where('conversation_participants.user_id',$userId);
                    })
                    ->where('messages.id', $value)
                    ->select([
                        'messages.id',
                        'messages.user_id'
                    ])
                    ->first();
                if (!$message) {
                    return $fail(trans('message.request.input_exists', ['attribute' => "Tin nhắn"]));
                }
            }],
            'reaction' => "nullable|in:". implode(',', MessageReaction::LIST_TYPE_REACTION)
        ];
        return $rules;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        $messages = [
            'message_id.required' => trans('message.request.input_required', ['attribute' => "Tin nhắn"]),
            'message_id.numeric' => trans('message.request.input_integer', ['attribute' => "Tin nhắn"]),
            'reaction.in' => trans('message.request.input_exists', ['attribute' => "Loại tương tác"]),
        ];
        return $messages;
    }
}
