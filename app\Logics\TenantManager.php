<?php

namespace App\Logics;

use App\User;
use App\Models\Tenant\Hostname;
use App\Models\Tenant\Website;
use Hyn\Tenancy\Repositories\HostnameRepository;
use Hyn\Tenancy\Repositories\WebsiteRepository;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Exception;
use Illuminate\Support\Facades\DB;

class TenantManager
{
    public function tenantExists($tenantname)
    {
        $baseUrl = config('app.url_base');
        $fqdn = "{$tenantname}.{$baseUrl}";
        return Hostname::withTrashed()->where('fqdn', $fqdn)->exists();
    }

    public function registerTenant($tenant_form)
    {
        // Create a website
        $website = new Website;
        app(WebsiteRepository::class)->create($website);

        // Create a hostname
        $hostname = new Hostname;
        $baseUrl = config('app.url_base');
        $hostname->fqdn = "{$tenant_form['sub_domain']}.{$baseUrl}";
        $hostname = app(HostnameRepository::class)->create($hostname);
        app(HostnameRepository::class)->attach($hostname, $website);

        return [
            'website' => $website,
            'hostname' => $hostname
        ];
    }

    public function addAdmin($tenant_form, $password)
    {
        $admin = User::create([
            'name' => 'Admin',
            'last_name' => 'Admin',
            'email' => $tenant_form['email'],
            'password' => Hash::make($password)
        ]);
        $admin->guard_name = 'web';
        return $admin;
    }

    public function initDatabase() {
        Artisan::call('db:seed', ['--class' => 'PermissionSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'SiteSettingSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'TaskStatusSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'TaskPrioritiesSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'LanguageSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'ProjectGroupRoleSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'ProjectRoleSeeder', '--force' => true]);
        Artisan::call('db:seed', ['--class' => 'TaskTypeSeeder', '--force' => true]);
        $sql_file = base_path().'/database/seeds/prefectures_districts_communes_table.sql';
        DB::unprepared(file_get_contents($sql_file));
        // Artisan::call('passport:install');
    }

    /**
     * Check tenant exists by website id if exist website, hostname, company profile and admin of website
    */
    public function checkTenantExists($website_id, $trashed=1) {
        try {
            switch ($trashed) {
                case 0:
                    $website = Website::onlyTrashed();
                case 1:
                    $website = Website::withoutTrashed();
                case 2:
                    $website = Website::withTrashed();
            }
            $website = $website
                ->where('id', $website_id)
                ->with(["hostnames" => function ($q) use ($trashed) {
                    if ($trashed === 0) {
                        $q->onlyTrashed();
                    } elseif ($trashed === 2) {
                        $q->withTrashed();
                    }
                }])
                ->with(["companyprofiles" => function ($q) use ($trashed) {
                    if ($trashed === 0) {
                        $q->onlyTrashed();
                    } elseif ($trashed === 2) {
                        $q->withTrashed();
                    }
                }])
                ->first();

            if (is_null($website) || count($website->hostnames) == 0 || count($website->companyprofiles) == 0) {
                return null;
            }
        } catch(Exception $e) {
            Log::error($e);
            return null;
        }
        return $website;
    }

    public function updateTenant($tenant, $tenant_form)
    {
        if (empty($tenant->hostnames) || empty($tenant->companyprofiles)) {
            return null;
        }
        $tenant->companyprofiles[0]->update([
            'name' => $tenant_form['name'],
            'email' => $tenant_form['email'],
            'address' => $tenant_form['address'],
            'phone' => $tenant_form['phone'],
        ]);

        $hostname = $tenant->hostnames[0];
        $baseUrl = config('app.url_base');
        $hostname->fqdn = "{$tenant_form['sub_domain']}.{$baseUrl}";
        app(HostnameRepository::class)->update($hostname);

        return $hostname;
    }
}
