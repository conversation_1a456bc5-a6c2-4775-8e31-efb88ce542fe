<?php

namespace App\Http\Controllers\Api;

use App\Logics\ConversationParticipantManager;
use App\Models\ConversationParticipant;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class ConversationParticipantController extends AbstractApiController
{
    /**
     * @var ConversationParticipantManager
     */

    protected $conversationParticipantManager;

    public function __construct(ConversationParticipantManager $conversationParticipantManager)
    {
        $this->conversationParticipantManager = $conversationParticipantManager;
    }

    /**
     * list conversation participant
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function listConversationParticipant(Request $request)
    {
        try {
            $data = $this->conversationParticipantManager->getAllParticipantsOfConversation($request);
            $msg = __('message.conversationParticipant.listConversationParticipant.success');
            if (empty($data) || !empty($data['status_error'])) {
                $msg = __('message.conversationParticipant.listConversationParticipant.fail');
                return $this->respondForbidden($msg);
            }
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationParticipantController][listConversationParticipant] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationParticipantController][listConversationParticipant] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * update participant in conversation
     * @param $conversationId
     * @param ConversationParticipantStoreRequest $request
     * @return json
     * @throws Exception
     */
    public function update($conversationId, Request $request)
    {
        try {
            $data = $this->conversationParticipantManager->update($conversationId, Auth::guard('api')->user(), $request);
            $msg = __('message.conversationParticipant.update.success');
            if (isset($data['status_error'])) {
                $msg = __('message.conversationParticipant.update.fail');
                if ($data['status_error'] == ConversationParticipant::ERROR_PERMISSION) {
                    return $this->respondBadRequest($msg);
                }
                if ($data['status_error'] == ConversationParticipant::ERROR_EMPTY_ADMIN) {
                    $msg = __('message.conversationParticipant.update.empty_admin');
                    return $this->renderJsonResponse([], $msg, Response::HTTP_NOT_FOUND);
                }
                if ($data['status_error'] == ConversationParticipant::ERROR_NOT_FOUND) {
                    $msg = __('message.conversationParticipant.update.not_found');
                    return $this->renderJsonResponse([], $msg, Response::HTTP_NOT_FOUND);
                }
                return $this->respondForbidden($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationParticipantController][updateConversationParticipant] error " . $e->getMessage());
            throw new Exception('[ConversationParticipantController][updateConversationParticipant] error ' . $e->getMessage());
        }
    }

    /**
     * Get list member not been added in group conversation
     * @param         $conversationId
     * @param Request $request
     * @return json|mixed
     * @throws Exception
     */
    public function listMemberNotBeenAddInConversation($conversationId, Request $request)
    {
        try {
            $data = $this->conversationParticipantManager->listMemberNotBeenAddInConversation($conversationId, $request);
            $msg = __('message.conversationParticipant.listMemberNotBeenAddInConversation.fail');
            if (isset($data['status_error'])) {
                if ($data['status_error'] == Response::HTTP_NOT_FOUND) {
                    return $this->respondNotFound($msg);
                }
                return $this->respondForbidden($msg);
            }
            $msg = __('message.conversationParticipant.listMemberNotBeenAddInConversation.success');
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationParticipantController][listMemberNotBeenAddInConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationParticipantController][listMemberNotBeenAddInConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }

    /**
     * list senders
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function listSenders(Request $request)
    {
        try {
            $data = $this->conversationParticipantManager->listSenders($request);
            $msg = __('message.conversationParticipant.listSenders.success');
            if (empty($data) || !empty($data['status_error'])) {
                $msg = __('message.conversationParticipant.listSenders.fail');
                return $this->respondForbidden($msg);
            }
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error($e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }
}
