<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ReportTestMail extends Mailable
{
    use Queueable, SerializesModels;

    public $info;
    public $filePath;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($info, $filePath)
    {
        $this->info = $info;
        $this->filePath = $filePath;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
                    ->subject(trans('language.mail_report_test.subject', ['projectName' => $this->info['project_name']]))
                    ->view('mail.mail_report_test', ['info' => $this->info])
                    ->attach(storage_path().'/app/'.$this->filePath);
    }
}
