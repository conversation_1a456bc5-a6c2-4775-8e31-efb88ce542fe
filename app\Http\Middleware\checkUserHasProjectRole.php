<?php

namespace App\Http\Middleware;

use Closure;

class checkUserHasProjectRole
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $userManager = new \App\Logics\UserManager();
        $isManager = $userManager->hasProjectRole(auth()->id(), $request->projectId, \App\Models\ProjectRole::ProjectManager);
        if (!$isManager) {
            abort(403);
        }
        return $next($request);
    }
}
