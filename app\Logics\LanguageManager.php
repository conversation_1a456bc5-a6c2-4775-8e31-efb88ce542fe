<?php

namespace App\Logics;
use App\Models\Language;
use App\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LanguageManager
{
    /**
     * Get all system languages active.
     *
     * @throws Exception
     */
    public function getLanguages()
    {
        try {
            return Language::query()->active()
                ->select([
                    'id',
                    'code',
                    'name',
                    'display_name',
                    'flag',
                    'sort_no'
                ])
                ->orderBy('sort_no')
                ->get();
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     *
     * update language
     *
     * @param $request
     * @return void
     */
    public function updateLanguage($request)
    {
        try {
            $language = $request->input('language');
            $loginId = Auth::guard('api')->user()->id;
            DB::beginTransaction();
            $this->updateUserLanguage($loginId, $language);
            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }
    
    /**
     * update user language
     *
     * @param $userId
     * @param $language
     * @return void
     */
    private function updateUserLanguage($userId, $language)
    {
        User::query()
            ->where('id', $userId)
            ->update(['language_id' => $language]);
    }
}