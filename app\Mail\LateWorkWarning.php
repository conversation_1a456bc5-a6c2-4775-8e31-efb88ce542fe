<?php

namespace App\Mail;

use App\Models\Language;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LateWorkWarning extends BaseMail
{
    use Queueable, SerializesModels;

    protected $info;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($info)
    {
        $language_id = isset($info['language_id']) ? $info['language_id'] : null;      
        parent::__construct($language_id);
        $this->info = $info;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
            ->subject(trans('language.mail_late_work_warning.subject', ['late_day' => $this->info['late_day']]))
            ->view('mail.mail_late_work_warning', ['info' => $this->info]);
    }
}
