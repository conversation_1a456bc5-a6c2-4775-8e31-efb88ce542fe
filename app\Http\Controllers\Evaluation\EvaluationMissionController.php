<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Http\Requests\EvaluationMissionRequest;
use App\Logics\EvaluationManager;
use App\Logics\EvaluationMissionManager;
use App\Logics\EvaluationResultManager;
use App\Models\Evaluation;
use App\Models\EvaluationForm;
use App\Models\Project;
use App\Models\ProjectMember;
use App\Models\SiteSetting;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Response;

class EvaluationMissionController extends Controller
{
    const PAGINATION = 15;
    const MIN_SCORE = 0;
    const MAX_SCORE = 10;
    const MIN_SCORE_REQUIRE = 5;
    const MAX_SCORE_REQUIRE = 8;
    /**
     * view list member evaluation
     *
     */
    public function index(Request $request, $pageSize=self::PAGINATION)
    {
        $userId = Auth::id();
        $dateNow = Carbon::today();
        $evaluations = Evaluation::select(
            'evaluations.form_id',
            'evaluations.user_id',
            'evaluations.updated_at',
            'evaluations.created_at',
            'evaluation_forms.name',
            'evaluation_forms.to',
            'users.id AS user_id',
            'users.avatar',
            DB::raw("CONCAT_WS(' ', users.first_name, users.last_name) AS full_name")
        )->join('users','users.id', '=','evaluations.user_id')
        ->join('evaluation_forms','evaluation_forms.id', '=','evaluations.form_id')
        ->whereDate('evaluation_forms.from','<=',$dateNow)
        ->whereDate('evaluation_forms.to','>=',$dateNow)
        ->where('evaluations.evaluator_id', $userId)
        ->when($request->input('sort') == null, function($query) {
            $query->orderByRaw('form_id DESC, updated_at ASC');
        });
        $sortable = ['evaluation_forms.name', 'users.last_name', 'evaluations.updated_at', 'evaluation_forms.to'];
        $direction = in_array($request->direction, ['asc', 'desc']) ? $request->direction : 'asc';
        if (in_array($request->sort, $sortable)) {
            $evaluations = $evaluations->orderBy($request->sort, $direction);
        }
        $evaluations = $evaluations->paginate($pageSize);
        return view('evaluation.mission.index',['evaluations' => $evaluations]);
    }
    /**
     * view table evaluation member
     *
     * @param Request $request
     * @return Response
     */
    public function edit(Request $request)
    {

        $userId = Auth::id();
        $dateNow = Carbon::today();
        $user = User::withTrashed()->findOrFail($request->user_id);
        $evaluation = Evaluation::select(
            'evaluations.form_id',
            'evaluations.content',
            'evaluation_forms.is_evaluation_week'
            )
            ->join('evaluation_forms','evaluation_forms.id', '=','evaluations.form_id')
            ->whereDate('evaluation_forms.from','<=',$dateNow)
            ->whereDate('evaluation_forms.to','>=',$dateNow)
            ->where('evaluations.user_id',$request->user_id)
            ->where('evaluations.form_id',$request->form_id)
            ->where('evaluations.evaluator_id', $userId)->first();

        if($evaluation == null){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.evaluation_not_exist'),
                ],
            ];
        }
        $evaluationContent = json_decode($evaluation->content, true);

        $evaluationForm = EvaluationForm::findOrFail($request->form_id);
        $evaluationFormContent = $evaluationForm->content;
        $evaluationMissionManager = new EvaluationMissionManager();
        if($evaluationFormContent){
            $evaluationFormContent = json_decode($evaluationFormContent, true);
            $formContent['subs'] = $evaluationFormContent;

            if ($evaluationContent == null) {

                // Create evaluation content matching with eveation form content
                $evaluationContent = $evaluationMissionManager->evaluationContentTemplate($evaluationFormContent);
            }
        }


        // create html score and comment

        $score = strval($evaluationMissionManager->getHtmlScore($evaluationContent,'',null,$evaluation->is_evaluation_week));
        $arrScore = explode('</tr>', $score);
        unset($arrScore[count($arrScore)-1]);


        $evaluationManager = new EvaluationManager();
        $maxDepth = $evaluationManager->getEvaluationFormDepth($formContent);
        $itemHtml = $evaluationManager->getEvaluationFromHtml($formContent, 0, $maxDepth)[0];
        $itemRows = explode('</tr>', $itemHtml);
        foreach ($itemRows as $itemRow) {
            $allRows[] = '<tr>'.$itemRow;
        }
        unset($allRows[count($allRows)-1]);
        for ($i=0; $i < count($allRows); $i++) {
            $allRows[$i] .= $arrScore[$i];
        }

        $formHtml = implode('', $allRows);

        // Get all projects of user
        $minTimeProject = SiteSetting::minTimeProject();
        $projects = ProjectMember::join('projects', 'project_members.project_id', '=', 'projects.id')
        ->where('project_members.user_id', $request->user_id)
        ->whereRaw(DB::raw('project_members.project_id in (Select project_id from project_members where user_id = '. $userId .')'))
        ->where(function($query) use ($evaluationForm) {
            $query->where('projects.started_at', '<=', $evaluationForm->ended_at)
                ->whereRaw("(projects.ended_at IS NULL OR projects.ended_at >= '".$evaluationForm->started_at."')");
        })
        // Check the evaluator must be added to the project before the evaluation end
        ->whereRaw("DATEDIFF('" . $evaluationForm->ended_at . "', project_members.created_at) + 1 >= ". $minTimeProject)
        ->whereRaw("(projects.ended_at IS NULL OR DATEDIFF(projects.ended_at, project_members.created_at) + 1 >= ". $minTimeProject.")")
        ->select('project_members.project_id', 'projects.name')
        ->groupBy('project_members.project_id')
        ->get();

        // link project together

        $nameProject = '';
        for ($i = 0; $i < count($projects); $i++) {
            if ($i == count($projects) - 1) {
                $nameProject .= $projects[$i]->name;
            } else {
                $nameProject .= $projects[$i]->name . ', ';
            }
        }

        $html= view('evaluation.mission.partials.form',[
            'user' => $user,
            'form_id' => $evaluation->form_id,
            'maxDepth' => $maxDepth,
            'evaluationFormContent' => $formHtml,
            'project' => $nameProject,
            'evaluationContent' => $evaluationContent,
            'isEvaluationWeek' => $evaluation->is_evaluation_week

        ])->render();
        return [
            'status' => Response::HTTP_OK,
            'html' => $html,
            'isEvaluationWeek' => $evaluation->is_evaluation_week
        ];
    }
    /**
     * update evaluation member
     *
     * @param Request $request
     * @return Response
     */
    public function update(EvaluationMissionRequest $request)
    {
        $evaluationForm = EvaluationForm::select('content', 'ended_at', 'started_at','is_evaluation_week')->find($request->form_id);
        $userId = Auth::id();
        $arrScore = $request->score;
        $arrComment = $request->input('comment');
        for($i=0;$i<count($arrScore);$i++){
            $score = intval($arrScore[$i]);
            if(is_null($score) || (($score < self::MIN_SCORE || $score > self::MAX_SCORE) && ($arrScore[$i]!=Evaluation::NOT_EVALUATE)) ){
                return redirect()
                    ->route('evaluation.mission.index')
                    ->with([
                        'status_failed' => trans('message.evaluation_failed'),
                    ]);
            }
            if (($score < self::MIN_SCORE_REQUIRE || $score >= self::MAX_SCORE_REQUIRE) && ($arrScore[$i]!=Evaluation::NOT_EVALUATE) && !$arrComment[$i] && !$evaluationForm->is_evaluation_week) {
                return redirect()
                ->route('evaluation.mission.index')
                ->with([
                    'status_failed' => trans('message.evaluation_failed'),
                ]);
            }
        }
        $content = json_decode($request->arrayScore, true);
        $evaluationMissionManager = new EvaluationMissionManager();
        [$content,$arrScore, $arrComment] = $evaluationMissionManager->updateContent($content, $arrScore, $arrComment);

        $content = json_encode($content, true);
        Evaluation::where('user_id',$request->id)
                    ->where('form_id',$request->form_id)
                    ->where('evaluator_id', $userId)->update([
                        'content' => $content,
                        'updated_by' => $userId
                    ]);

        // Update score evaluationResult
        $evaluationResultManager = new EvaluationResultManager();
        $evaluationResultManager->updateScore($request->form_id,$request->id);

        return redirect()
        ->route('evaluation.mission.index')
        ->with([
            'status_succeed' => trans('message.evaluation_succeed'),
        ]);
    }

    /**
     * Get user not evaluate
     *
     * @param Request $request
     * @return Response
     */
    public function getUserNotEvaluate(Request $request){
        $userId = Auth::id();
        $dateNow = Carbon::today();
        $evaluations = Evaluation::select(
                'evaluations.form_id',
                'evaluations.user_id',
                'evaluations.evaluator_id',
                'evaluation_forms.name as form_name',
                'evaluation_forms.to as deadline',
                DB::raw("CONCAT_WS(' ', users.first_name, users.last_name) AS full_name"),
                DB::raw("CONCAT_WS(' ', evaluator.first_name, evaluator.last_name) AS evaluator_full_name")
            )
            ->join('users','users.id', '=','evaluations.user_id')
            ->join('users as evaluator','evaluator.id', '=','evaluations.evaluator_id')
            ->join('evaluation_forms','evaluation_forms.id', '=','evaluations.form_id')
            ->where('evaluation_forms.from','<=',$dateNow)
            ->where('evaluation_forms.to','>=',$dateNow)
            ->where('evaluations.created_at', DB::raw('evaluations.updated_at'));

            $sortable = ['evaluation_forms.name', 'evaluator.last_name', 'users.last_name', 'evaluation_forms.to'];
            $direction = in_array($request->direction, ['asc', 'desc']) ? $request->direction : 'asc';
            if (in_array($request->sort, $sortable)) {
                $evaluations = $evaluations->orderBy($request->sort, $direction);
            }
            $evaluations = $evaluations->paginate(self::PAGINATION);
        return view('evaluation.mission.not-evaluate',['evaluations' => $evaluations]);
    }
}
