<?php

namespace App\Exports\Asset;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class AssetMultiSheetExport implements WithMultipleSheets
{
    use Exportable;

    protected $result;
    protected $dataTable;

    public function __construct($result, $dataTable)
    {
        $this->result = $result;
        $this->dataTable = $dataTable;
    }

    /**
     * @inheritDoc
     */
    public function sheets(): array
    {
        $sheets = array();
        $sheets[] = new AssetTemplateSheetExport(trans('language.asset_list'), $this->result, $this->dataTable);
        $sheets[] = new AssetDataSheetExport(trans('language.asset_list_options'), $this->dataTable);
        return $sheets;
    }
}