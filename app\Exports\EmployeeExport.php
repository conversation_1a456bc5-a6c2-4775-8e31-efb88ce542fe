<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class EmployeeExport implements FromCollection, WithHeadings, WithColumnWidths, WithStyles
{
    function __construct($result) {
        $this->result = $result;
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    { 
        return (collect($this->result));
    }
    /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            trans('language.number_order'),
            trans('language.name_employee'),
            trans('language.working_time_excel'),
            trans('language.day_off'),
            trans('language.late_work'),
            trans('language.KPI_score')
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 10,
            'B' => 40,
            'C' => 20,
            'D' => 25,
            'E' => 25,
            'F' => 35,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->getStyle('A1:F1')->applyFromArray(array(
            'borders' => [
                'all' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ],
            'fill' => [
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => '4F81BD',
                ],
            ],
            'font' => [
                'color' => [
                    'rgb' => 'F8F9FA',
                ],
            ],
            'alignment' => [
                'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            ]  
        ));
        $sheet->getStyle('A1:F'.(count($this->result)))->getAlignment()->setWrapText(true);
        $sheet->getStyle('A1:F'.(count($this->result)+1))->applyFromArray(array(
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => array(
                        'rgb' => '000000'
                    )
                ]
            ]
        ));
    } 
}
