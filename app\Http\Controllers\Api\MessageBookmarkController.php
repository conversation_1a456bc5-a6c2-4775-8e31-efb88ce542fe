<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\BookMarkRequest;
use App\Logics\MessageBookmarkManager;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class MessageBookmarkController extends AbstractApiController
{
    protected $messageBookmarkManager;
    public function __construct(MessageBookmarkManager $messageBookmarkManager){
        $this->messageBookmarkManager = $messageBookmarkManager;
    }
    /**
     * Show list bookmark message
     * @param $request
     * @return json
     * @throws Exception
     */
    public function index(Request $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $params = $request->all();
            $data = $this->messageBookmarkManager->index($userId, $params);
            $msg = __('message.message.listBookmarkMessage.success');
            return $this->respondWithPagination($data, $msg);
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * bookmark/unbookmark message
     *
     * @param BookMarkRequest $request
     * @return void
     */
    public function store(BookMarkRequest $request)
    {
        try {
            $userId = Auth::guard('api')->user()->id;
            $params = $request->all();
            $data = $this->messageBookmarkManager->store($userId, $params);
            if (isset($data['status_error'])) {
                $msg = __('message.message.bookmarkMessage.fail');
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
                return $this->respondBadRequest($msg);
            }
            $msg = __('message.message.bookmarkMessage.success', ['action' => $data]);
            return $this->renderJsonResponse(null, $msg);
        } catch (Exception $e) {
            Log::error($e->getMessage() . PHP_EOL . $e->getTraceAsString());
            throw $e;
        }
    }
}
