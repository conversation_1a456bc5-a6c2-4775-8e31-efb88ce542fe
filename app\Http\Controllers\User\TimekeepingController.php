<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Logics\DateFormatManager;
use App\Logics\TaskManager;
use App\Logics\TimekeepingManager;
use App\Logics\UserManager;
use App\Logics\WorkingTimeManager;
use App\Models\Attendance;
use App\Models\Holiday;
use App\Models\SiteSetting;
use App\Models\WorkPlace;
use App\Traits\StorageTrait;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Request as ModelsRequest;

class TimekeepingController extends Controller
{
    const PER_PAGE = 31;
    use StorageTrait;

    protected $timekeepingManager;

    public function __construct(TimekeepingManager $timekeepingManager)
    {
        $this->timekeepingManager = $timekeepingManager;
    }

    public function index(Request $request)
    {
        $user_id[] = Auth::user()->id;
        $userManager = new UserManager();
        $taskManager = new TaskManager();
        $user = $userManager->getUserWithLeaveDay($user_id, $request->has('deleted'))->first();
        $filterHtml = $taskManager->getFilterHtml($request,['choose_month']);

        // Get timekeeping of logged in user
        $timekeepingManager = new TimekeepingManager();
        $start_date = empty($request->start_date)?$request->start_date:Carbon::createFromFormat('d/m/Y',$request->start_date);
        $end_date = empty($request->end_date)?$request->end_date:Carbon::createFromFormat('d/m/Y',$request->end_date);
        $user_attendances = $timekeepingManager->getUserAttendances($user_id, $start_date, $end_date, $request->sort, null,  $request->direction);

        $user_attendances = $user_attendances->paginate(self::PER_PAGE);

        if ($request->page > $user_attendances->lastPage()) {
            return redirect($request->fullUrlWithQuery(["page" => $user_attendances->lastPage()]));
        }

       $choose_month = isset($request->choose_month)?$request->choose_month:date('m/Y');
       //Xử lý lấy ra choose_month
       $arrTime = explode('/',$choose_month);

       $month = $arrTime[0];
       $year = $arrTime[1];
       $timeFormatYmd = $year . '-' . $month . '-01';

       $nextTime = date('m/Y',mktime(0, 0, 0, $month+1,1, $year));
       $prevTime = date('m/Y',mktime(0, 0, 0, $month-1,1, $year));
       $currentTime = date('m/Y');;

       $ipTimekeeping = false;
       $userWorkplace = \App\User::find(Auth::id())->work_place_id;
       $siteSetting = SiteSetting::find(SiteSetting::IP_TIMEKEEPING);
       if(in_array($request->ip(), json_decode($siteSetting->value)) && $userWorkplace != \App\Models\WorkPlace::VP_HN){
        $ipTimekeeping = true;
       }
       
       return view('user.timekeeping.index2', [
           'user' => $user,
           'attendances' => $user_attendances,
           'filterHtml' => $filterHtml,
           'choose_month' => $choose_month,
           'nextTime' => $nextTime,
           'prevTime' => $prevTime,
           'currentTime' => $currentTime,
           'timeFormatYmd' => $timeFormatYmd,
           'ipTimekeeping' => $ipTimekeeping,
       ]);
   }

    public function getCheckedImage(Request $request){
        $filePath = $request -> path;
        if (strpos($filePath, CHECKED_IMAGE_DIR) != 0) {
            return null;
        }
        $image = $this->getFile($filePath);
        return $image;
    }

    /**
     * Get data timekeeping for user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDataTimekeepingUser(Request $request){
        $userId = Auth::user()->id;
        $data = $this->timekeepingManager->getUserTimeSheet($request->choose_month, $userId);
        return response()->json($data);
    }

    /**
     * user timekeeping
     *
     * @param Request $request
     */
    public function userSelfTimekeeping(Request $request){
        DB::beginTransaction();
        try {
            $siteSetting = SiteSetting::find(SiteSetting::IP_TIMEKEEPING);
            $userWorkplace = User::find(Auth::id())->work_place_id;
            if($userWorkplace != WorkPlace::VP_HN && in_array($request->ip(), json_decode($siteSetting->value) )){
                Attendance::create([
                    'user_id'=> Auth::id(),
                    'checked_at' => Carbon::now(),
                    'checked_type' => Attendance::MANUAL_CHECK,
                ]);
                DB::commit();
                return redirect()->back()->with([
                    'status_succeed' => trans('message.timekeeping_succeed')
                ]);
            }
            return redirect()->back()->with([
                'status_failed' => trans('message.can_not_timekeeping')
            ]);
        }catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
}
