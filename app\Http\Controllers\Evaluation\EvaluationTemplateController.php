<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\EvaluationTemplate;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class EvaluationTemplateController extends Controller
{
    const PAGINATION = 15;
    /**
     * Show list of evaluation emplate 
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $evaluationTemplates = EvaluationTemplate::select('id', 'name', 'created_by', 'updated_at', 'is_draft')
                                ->sortable(['updated_at' => 'desc'])->paginate(self::PAGINATION);
        return view('evaluation.template.index', ['evaluationTemplates'=>$evaluationTemplates]);
    }

    /**
    * show creating  evaluation template screen
    *
    * @return \Illuminate\Http\Response
    */
    public function create()
    {
        return view('evaluation.template.create');
    }

     /**
     * Show evaluation template detail
     * 
     * @param int $id;
     *
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $evaluationTemplate = EvaluationTemplate::find($id);
        if ($evaluationTemplate){
            return view('evaluation.template.edit', $evaluationTemplate, ['evaluationTemplate' => $evaluationTemplate]);
        }
    }

     /**
     * store  evaluation template
     *
     * @param Request $request
     */
    public function store(Request $request)
    {
        $evaluationTemplate = new EvaluationTemplate();
        $evaluationTemplate->content = json_encode($request->content);
        if ($request->name){
            $evaluationTemplate->name = $request->name;
        } else {
            $evaluationTemplate->name = trans('language.evaluation_temporary')." ".Carbon::now()->format('d-m-Y H:i:s');
        }
        $evaluationTemplate->is_draft = $request->is_draft;
        $evaluationTemplate->save();
        return route('evaluation.template.update', ['id'=>$evaluationTemplate->id]);
    }

      /**
     * update  evaluation template
     *
     * @param Request $request
     * @param int $id
     */
    public function update($id, Request $request)
    {
        $evaluationTemplate = EvaluationTemplate::find($id);
        if ($evaluationTemplate){
            $evaluationTemplate->content = json_encode($request->content);
            if ($request->name){
                $evaluationTemplate->name = $request->name;
            } else {
                $evaluationTemplate->name = trans('language.evaluation_temporary')." ".Carbon::now()->format('d-m-Y H:i:s');
            }
            $evaluationTemplate->is_draft = $request->is_draft;
            $evaluationTemplate->save(); 
        }
    }

    /**
     * delete  evaluation template
     *
     * @param int $id
     */
    public function destroy($id)
    {
        $evaluationTemplate = evaluationTemplate::find($id);
        if($evaluationTemplate){
            $evaluationTemplate->delete();
        }
    }
}