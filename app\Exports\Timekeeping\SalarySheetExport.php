<?php

namespace App\Exports\Timekeeping;

use App\Models\Attendance;
use App\Models\SpecialRequest;
use Carbon\Carbon;use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Support\Str;

class SalarySheetExport implements WithHeadings, WithColumnWidths, WithStyles
{
    protected $allStaffs;
    protected $numDays;
    protected $month;
    protected $year;
    const AMOUNT_OF_DEDUCTION_DEPENDS = 4400000;
    const SOCIAL_INSURANCE_PAYMENT_LEVEL = 10.5;
    const USER_PARTIAL_CONTRIBUTORS = [27, 73];

    function __construct($allStaffs, $numDays, $month, $year) {
        $this->allStaffs = $allStaffs;
        $this->numDays = $numDays;
        $this->month = $month;
        $this->year = $year;
    }
     /**
     * Returns headers for report
     * @return array
     */
    public function headings(): array {
        return [
            "STT",
            "ID",
            "Họ & Tên",
            "Tài khoản ngân hàng",
            'Email',
            "Lương cơ bản",
            'Giờ công trong tháng',
            'Giờ công thực tế',
            'Giờ phép sử dụng',
            'Giờ phép còn lại',
            "Lương cơ bản tháng trước",
            "Giờ công tháng trước",
            "Giờ OT",
            "% Hoàn thành công việc",
            'Lương tháng',
            "Lương mentor theo buổi",
            'Số buổi dạy mentor',
            "Số tiền Trợ cấp BTA",
            "Trợ cấp BTA",
            "Trợ cấp ăn trưa",
            "Trợ cấp gửi xe",
            "Số tiền phụ cấp chức vụ",
            "Thông tin Phụ cấp chức vụ",
            'Trợ cấp khác',
            "Truy lĩnh",
            "Truy lĩnh tiền mặt",
            "Trợ cấp gửi xe ngoài",
            'Tổng thu nhập',
            'Ăn trưa',
            "Gửi xe",
            "Truy thu",
            "Truy thu tiền mặt",
            "BHXH",
            "Giảm trừ phụ thuộc",
            "Thu nhập tính thuế",
            "Thuế thu nhập cá nhân",
            'Tổng khấu trừ',
            "Lương chuyển khoản",
            "Chi nhánh",
            "Trạng thái nhân sự",
            "Công ty ký hợp đồng",
            "Hình thức tính lương",
            "Công ty đóng bảo hiểm xã hội",
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 8,
            'B' => 8,
            'C' => 25,
            'D' => 25,
            'E' => 30,
            'F' => 20,
            'G' => 15,
            'H' => 15,
            'I' => 15,
            'J' => 15,
            'K' => 20,
            'L' => 20,
            'M' => 15,
            'N' => 20,
            'O' => 20,
            'P' => 20,
            'Q' => 15,
            'R' => 20,
            'S' => 20,
            'T' => 20,
            'U' => 20,
            'V' => 25,
            'W' => 20,
            'X' => 20,
            'Y' => 20,
            'Z' => 25,
            'AA' => 25,
            'AB' => 20,
            'AC' => 15,
            'AD' => 15,
            'AE' => 20,
            'AF' => 20,
            'AG' => 20,
            'AH' => 20,
            'AI' => 20,
            'AJ' => 15,
            'AK' => 20,
            'AL' => 25,
            'AM' => 20,
            'AN' => 20,
            'AO' => 25,
            'AP' => 15,
            'AQ' => 20,
        ];
    }

    public function styles(Worksheet $sheet)
    {
        $sheet->setTitle('Lương');

        $alignRight = [
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ]  
        ];
        $alignCenter = [
            'alignment' => [
                'horizontal' =>  Alignment::HORIZONTAL_CENTER,
            ]  
        ];

        $sheet->getStyle('A1:AQ1')->applyFromArray(array(
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'argb' => '3366FF',
                ],
            ],
            'font' => [
                'color' => [
                    'argb' => 'FFFFFF',
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
                'wrapText' => true,
            ]
        ));
        $sheet->freezePane('D2');
        $sheet->getRowDimension(1)->setRowHeight(30);
        $num = count($this->allStaffs) + 1;
        $sheet->getStyle('A2:B'.$num)->applyFromArray($alignCenter);
        $sheet->getStyle('D2:D'.$num)->applyFromArray($alignCenter);

        $sheet->getStyle('B'.($num+1))->applyFromArray(array(
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ]
        ));
        $sheet->getStyle('E'.($num+1).':AR'.($num+1).'')->applyFromArray(array(
            'font' => [
                'bold' => true,
                'color' => [
                    'argb' => 'FF0000',
                ],
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_RIGHT,
            ]
        ));
        $sheet->getStyle('E2:I'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AH2:AH'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AI2:AI'.$num)->applyFromArray($alignCenter);
        $sheet->getStyle('M2:W'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AB2:AC'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AD2:AE'.$num)->applyFromArray($alignCenter);
        $sheet->getStyle('AE2:AF'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AH2:AJ'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('AK2:AP'.$num)->applyFromArray($alignRight);
        $sheet->getStyle('A1:AQ'.($num+1))->applyFromArray(array(
            'font' => [
                'size'  => 10,
                'name'  => 'Arial'
            ],
        ));

        $dateFomat = $this->year.'-'.$this->month;
        foreach ($this->allStaffs as $i => $staff) {
            $row = $i + 2;
            $fullName = strtoupper(Str::slug($staff['first_name'].' '.$staff['last_name'], ' '));
            $sheetName = $staff['id'].'. '.$staff['first_name'].' '.$staff['last_name'];
            
            // Handle sheet name
            $sheetName = TimeKeepingMultiSheetExport::handleSheetName($staff);
            // special request salary
            $specialRequest = SpecialRequest::select(
                'id', 
                'month', 
                'user_id',
                'freelancer',
                'ot',
                'percent_work', 
                'receive_arrear_cash', 
                'collect_arrear',
                'receive_arrear',
                'bta_bonus'
            )
            ->where('user_id', $staff['id'])
            ->whereRaw('MONTH(month) ='. $this->month)
            ->whereRaw('YEAR(month) =' .$this->year)
            ->first();
            
              // special request salary last month
            $specialRequestLastMonth = SpecialRequest::select(
                    'id', 
                    'month', 
                    'user_id',
                    'freelancer',
                    'ot',
                    'percent_work', 
                    'receive_arrear_cash', 
                    'collect_arrear',
                    'receive_arrear',
                    'bta_bonus'
            )
            ->where('user_id', $staff['id'])
            ->whereMonth('month',($this->month - 1) == 0 ? 12 : $this->month - 1)
            ->whereYear('month',($this->month - 1 ) == 0 ? $this->year - 1 : $this->year)
            ->first();
  
            // Write staff to salary sheet
            $sheet->setCellValue("A$row", ($i+1));
            $sheet->setCellValue("B$row", $staff['id']);
            $sheet->setCellValue("C$row", $fullName);
            $sheet->setCellValue("D$row", $staff['banking_account']);
            $sheet->setCellValue("E$row", $staff['email']);
            $sheet->setCellValue("F$row", "0");
            $sheet->setCellValue("L$row", "0");
            $sheet->setCellValue("H$row", "='".$sheetName."'!D".($this->numDays+3)."");
            $sheet->setCellValue("I$row", "='".$sheetName."'!E".($this->numDays+2)."");
            $sheet->setCellValue("J$row", "='".$sheetName."'!E".($this->numDays+7).""); // gio phep côn lai
            $sheet->setCellValue("K$row", "0");
            $sheet->setCellValue("AM$row", $staff['work_place_name']);
            $sheet->setCellValue("M$row", isset($specialRequestLastMonth) && $specialRequestLastMonth->ot != null ? $specialRequestLastMonth->ot : "");
            $sheet->setCellValue("O$row", "='".$sheetName."'!B".($this->numDays+12)."");
            $sheet->setCellValue("P$row", "0");
            $sheet->setCellValue("Q$row", "='".$sheetName."'!D".($this->numDays+8)."");
            $sheet->setCellValue("R$row", "='".$sheetName."'!B".($this->numDays+16)."");
            $sheet->setCellValue("S$row", $staff['bta_allowance'] ? 'Trợ cấp BTA' : '');
            $sheet->setCellValue("T$row", "='".$sheetName."'!B".($this->numDays+13)."");
            $sheet->setCellValue("U$row", "='".$sheetName."'!B".($this->numDays+24)."");
            $sheet->setCellValue("V$row", "");
            $sheet->setCellValue("W$row", $staff['position_allowance'] ? 'Phụ cấp chức vụ' : '');
            $sheet->setCellValue("AL$row", "='".$sheetName."'!B".($this->numDays+26)."");
            $sheet->setCellValue("Y$row", isset($specialRequest) && $specialRequest->receive_arrear != null ? $specialRequest->receive_arrear : "");
            $sheet->setCellValue("Z$row", isset($specialRequest) && $specialRequest->receive_arrear_cash != null ? $specialRequest->receive_arrear_cash : "");
            $sheet->setCellValue("AA$row", $staff['outbound_parking'] ? 'Trợ cấp gửi xe ngoài': '');
            $sheet->setCellValue("N$row", isset($specialRequest) && $specialRequest->percent_work != null ? $specialRequest->percent_work . "%" : "");
            $sheet->setCellValue("AC$row", "='".$sheetName."'!B".($this->numDays+14)."");
            $sheet->setCellValue("AD$row", "='".$sheetName."'!B".($this->numDays+25)."");
            // Caculate collection = 1%  basic salary if collumn AG <> 0 or id in USER_PARTIAL_CONTRIBUTORS
            $collectArrear = isset($specialRequest) && $specialRequest->collect_arrear  != null ? $specialRequest->collect_arrear  : 0;
            $sheet->setCellValue("AE$row", "=IF(AG$row<>0, F$row*1% + $collectArrear, $collectArrear)");
            if (in_array($staff['id'], self::USER_PARTIAL_CONTRIBUTORS)) {
                $sheet->setCellValue("AE$row", "=F$row*1% + $collectArrear");
            }
            $sheet->setCellValue("AF$row", "='".$sheetName."'!B".($this->numDays+29)."");
            $sheet->setCellValue("AG$row", "='".$sheetName."'!B".($this->numDays+17));
            $sheet->setCellValue("AH$row", ($staff['ended_at'] == null || strtotime($staff['ended_at']) >= strtotime($this->year . '-' . $this->month . '-' . '01')) 
                ? "='" . $sheetName . "'!B" . ($this->numDays + 19) : '0');
            $sheet->setCellValue("AI$row", "='".$sheetName."'!B".($this->numDays+23)."");
            $sheet->setCellValue("AJ$row", "='".$sheetName."'!B".($this->numDays+20)."");
            $sheet->setCellValue("AQ$row", $staff['company_insurance']);
            if($staff['salary_calculation_method'] == FULL_TIME_SALARY || $staff['salary_calculation_method'] == TTS_2_FULL_TIME || (isset($specialRequest) && $specialRequest->freelancer != null && $staff['salary_calculation_method'] == FREELANCER))
            {
                $sheet->setCellValue("G$row", "='".$sheetName."'!D".($this->numDays+4)."");
            }
            $sheet->setCellValue("AN$row", $staff['ended_at'] ? trans('language.retired') : $staff['personnel_status']);
            $sheet->setCellValue("AO$row", $staff['company_contract']);
            $sheet->setCellValue("AP$row", $staff['salary_calculation_method']);
            $sheet->setCellValue("AB$row", "=O$row+R$row+T$row+U$row+V$row+X$row+Y$row+Z$row");
            $sheet->setCellValue("AK$row", "=(AC$row+AD$row+AE$row+AF$row+AG$row)+AJ$row");
            //hyperlink
            $sheet->getCell("C$row")->getHyperlink()->setUrl("sheet://'".$sheetName."'!A1");
            
            if(($staff['ended_at'] != null || $staff['deleted_at'] != null) && Carbon::parse($dateFomat)->format('Y-m') > Carbon::parse($staff['ended_at'])->format('Y-m')){
                $sheet->getStyle("A$row:AQ$row")->applyFromArray(array(
                    'font' => [
                        'strikethrough' => true,
                    ],
                ));
            }
        }
        $sheet->getStyle('D2:D'.($num+1))
        ->getNumberFormat()
        ->setFormatCode(NumberFormat::FORMAT_NUMBER);
        $sheet->getStyle('E2:G'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('AH2:AH'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('M2:R'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('S2:W'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('AC2:AC'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('AE2:AF'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('AH2:AJ'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('AK2:AQ'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('X2:AG'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        $sheet->getStyle('O2:AB'.($num+1))
        ->getNumberFormat()
        ->setFormatCode('#,##0');
        // Write total in salary sheet
        $sheet->setCellValue('B'.($num+1), 'Tổng');
        $sheet->setCellValue('AJ'.($num+1), '=SUM(AJ2:AJ'.($num).')');
        $sheet->setCellValue('F'.($num+1), '=SUM(F2:F'.($num).')');
        $sheet->setCellValue('AF'.($num+1), '=SUM(AF2:AF'.($num).')');
        $sheet->setCellValue('AL'.($num+1), '=SUM(AL2:AL'.($num).')');
        $sheet->setCellValue('AG'.($num+1), '=SUM(AG2:AG'.($num).')');
        $sheet->setCellValue('AC'.($num+1), '=SUM(AC2:AC'.($num).')');
        $sheet->setCellValue('M'.($num+1), '=SUM(M2:M'.($num).')');
        $sheet->setCellValue('AI'.($num+1), '=SUM(AI2:AI'.($num).')');
        $sheet->setCellValue('P'.($num+1), '=SUM(P2:P'.($num).')');
        $sheet->setCellValue('AH'.($num+1), '=SUM(AH2:AH'.($num).')');
        $sheet->setCellValue('R'.($num+1), '=SUM(R2:R'.($num).')');
        $sheet->setCellValue('S'.($num+1), '=SUM(S2:S'.($num).')');
        $sheet->setCellValue('T'.($num+1), '=SUM(T2:T'.($num).')');
        $sheet->setCellValue('U'.($num+1), '=SUM(U2:U'.($num).')');
        $sheet->setCellValue('V'.($num+1), '=SUM(V2:V'.($num).')');
        $sheet->setCellValue('AD'.($num+1), '=SUM(AD2:AD'.($num).')');
        $sheet->setCellValue('Y'.($num+1), '=SUM(Y2:Y'.($num).')');
        $sheet->setCellValue('AE'.($num+1), '=SUM(AE2:AE'.($num).')');
        $sheet->setCellValue('Z'.($num+1), '=SUM(Z2:Z'.($num).')');

    }
}