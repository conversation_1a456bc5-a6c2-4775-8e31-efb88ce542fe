<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Routing\Route;

class CheckDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the URL to redirect to on a validation error.
     *
     * @return string
     */
    protected function getRedirectUrl()
    {
        return route('employee.orderLunch');
    }
    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        if (isset($this->choose_date)) {
            $this->merge([
                'choose_date' => $this->choose_date,
            ]);
        } 
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "choose_date" => 'nullable|date_format:d/m/Y'
        ];
    }
}
