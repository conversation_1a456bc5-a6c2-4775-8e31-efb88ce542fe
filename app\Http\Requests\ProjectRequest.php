<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'project_name' => 'required|max:1000|unique:projects,name,'.$this->id,
            'started_at' => 'nullable|date_format:d/m/Y',
            'ended_at' => 'nullable|date_format:d/m/Y|after_or_equal:started_at',
            'parent_project' => 'nullable|exists:projects,id'
        ];
    }
}
