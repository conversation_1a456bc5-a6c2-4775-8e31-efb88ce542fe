<?php


namespace App\Logics;


use App\Models\Evaluation;
use App\Models\EvaluationForm;
use App\Models\EvaluationResult;
use App\Models\ProjectGroupRole;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EvaluationResultManager
{
    public function updateScore($formId, $userId){

        $minTimeProject = SiteSetting::minTimeProject();
        // Get Evaluation form
        $evaluationForm = EvaluationForm::select('id','content', 'ended_at', 'started_at')->find($formId);
        // get list evaluations
        $evaluations = Evaluation::select(
                'evaluation_forms.id AS form_id',
                'evaluations.content',
                'evaluations.user_id',
                'evaluations.evaluator_id',
                DB::raw('min(project_roles.group_role_id) as group_role')
            )
            ->leftJoin('evaluation_forms', 'evaluation_forms.id', '=', 'evaluations.form_id')
            ->leftJoin('project_members', function ($join) use ($evaluationForm,$minTimeProject){
                $join->on('project_members.user_id','=', 'evaluations.evaluator_id')
                    ->whereRaw(
                    'project_id IN (
                        SELECT project_members.project_id FROM project_members
                        LEFT JOIN projects on projects.id = project_members.project_id
                        WHERE project_members.user_id = evaluations.user_id
                        AND DATEDIFF(?,project_members.created_at) + 1 >= ? AND (projects.ended_at IS NULL or DATEDIFF(projects.ended_at,project_members.created_at) + 1 >= ?)
                        GROUP BY project_id)',[$evaluationForm->ended_at, $minTimeProject, $minTimeProject]);
            })
            ->leftJoin('projects', 'projects.id', '=', 'project_members.project_id')
            ->join('project_roles','project_members.role_id','project_roles.id')
            ->where('evaluations.user_id', $userId)
            ->where('evaluations.form_id', $formId)
            ->whereRaw("DATEDIFF('" . $evaluationForm->ended_at . "', project_members.created_at) + 1 >= ". $minTimeProject)
            ->whereRaw("(projects.ended_at IS NULL OR DATEDIFF(projects.ended_at, project_members.created_at) + 1 >= ". $minTimeProject.")")
            ->where('project_members.created_at','<=', DB::raw('evaluation_forms.ended_at'))
            ->where('evaluations.content','!=','')
            ->whereNotNull('evaluations.content')
            ->groupBy(
                'evaluator_id',
                'evaluations.content',
                'evaluations.user_id',
                'evaluation_forms.id'
            )
            ->orderBy('evaluation_forms.created_at', 'DESC')
            ->get();

        // Caculator score
        if (count($evaluations) != 0){
            $evaluationManager = new EvaluationManager();
            $score = $evaluationManager->getScore(json_decode($evaluationForm->content, true),$evaluations);
            $score = $score == Evaluation::NOT_EVALUATE ? 0 : $score;
        }else{
            $score = null;
        }

        // Update score
        $result = EvaluationResult::where('user_id',$userId)
            ->where('form_id',$formId)
            ->update([
                'score' => $score==null?$score:round($score,2)
            ]);
        return $result;
    }
}
