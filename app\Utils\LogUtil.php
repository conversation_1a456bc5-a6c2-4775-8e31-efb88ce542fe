<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * Author: BaoDV
 * Author Email: <EMAIL>
 *
 * Created on March, 3/31/2025, by vanba
 */
class LogUtil
{
    /**
     * Write log
     *
     * @param string $level
     * @param string $message
     * @param array $context
     */
    public static function log(string $level, $message, array $context = [])
    {
        $logChannel = env('LOG_CHANNEL', 'daily');
        // Write log to chanel
        if ($message instanceof Throwable) {
            $context = array_merge($context, [
                'file' => $message->getFile(),
                'line' => $message->getLine()
            ]);
            $message = $message->getMessage();
        }

        Log::channel($logChannel)->log($level, $message, $context);


//        if ($level === 'error') {
//            Log::channel('slack')->error($message, $context);
//        }
    }

    /**
     * Write log info.
     *
     * @param $message
     * @param $context
     * @return void
     */
    public static function info($message, $context = [])
    {
        self::log('info', $message, $context);
    }

    /**
     * Write log error.
     *
     * @param $message
     * @param $context
     * @return void
     */
    public static function error($message, $context = [])
    {
        self::log('error', $message, $context);
    }

    /**
     * Write log debug.
     *
     * @param $message
     * @param $context
     * @return void
     */
    public static function debug($message, $context = [])
    {
        self::log('debug', $message, $context);
    }

    /**
     * Write log warning.
     *
     * @param $message
     * @param $context
     * @return void
     */
    public static function warning($message, $context = [])
    {
        self::log('warning', $message, $context);
    }
}
