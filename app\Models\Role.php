<?php

namespace App\Models;

use Spatie\Permission\Models\Role as BaseRole;
use Hyn\Tenancy\Traits\UsesTenantConnection;

class Role extends BaseRole
{
    use UsesTenantConnection;

    public const ROLE_SYSTEM_MANAGER = 'SystemManager';
    public const ROLE_OPERATION_MANAGER = 'OperationManager';
    public const ROLE_LEADER = 'Leader';
    public const ROLE_PROJECT_MANAGER = 'ProjectManager';
    public const ROLE_ORDER_MANAGER = 'OrderManager';
    public const ROLE_CODING_MANAGER = 'CodingManager';
    public const ROLE_PROJECT_MANAGER_COEFFICIENT = 5;
    public const ROLE_LEADER_COEFFICIENT = 3;
    public const ROLE_STAFF_COEFFICIENT = 1;
    public const ROLE_EMPLOYEE = '';
    public const ROLE_ASSET_MANAGER = 'AssetManager';
    public const ROLE_BTA_CUSTOMER_MANAGER = 'BTACustomerManager';
}
