<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AssignmentRequest extends FormRequest
{
    public function rules()
    {
        return [
            'user_id' => 'required|integer|exists:users,id',
            'project' => 'nullable|string',
            'note' => 'nullable|string',
        ];
    }

    public function authorize()
    {
        return true;
    }
}