<?php

namespace App\Console\Commands;

use App\Models\Tenant\Devices;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\DB;

class UpdateDevice extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_status_devices_offline  {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update status of all devices is offline';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        try {
            DB::beginTransaction();
            Devices::where('status', Devices::STATUS_ONLINE)->update(['status' => Devices::STATUS_OFFLINE]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->hourlyAt(0);
    }
}
