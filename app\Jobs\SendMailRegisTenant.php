<?php

namespace App\Jobs;

use App\Mail\MailRegisTenant;
use Illuminate\Bus\Queueable;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendMailRegisTenant extends BaseQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data;

    /**
     * Create a new job instance.
     *
     * @param $data
     */
    public function __construct($data, $websiteId=null)
    {
        // Inherit from the parent
        parent::__construct($websiteId);

        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Inherit from the parent
        parent::handle();

        $data = $this->data;
        $email = new MailRegisTenant($this->data);
        Mail::to($data['email'])->send($email);
    }
}
