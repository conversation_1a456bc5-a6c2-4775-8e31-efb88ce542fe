<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Logics\ConversationPinManager;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use \Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\AbstractApiController;

class ConversationPinController extends AbstractApiController
{
    /**
     * @var ConversationService
     */
    
     protected $conversationPinManager;
     
    public function __construct(ConversationPinManager $conversationPinManager)
    {
        $this->conversationPinManager = $conversationPinManager;
    }
    
    /**
     * pin conversation of user
     * @param Request $request
     * @return json
     * @throws Exception
     */
    public function pinConversation(Request $request){
        try {
            $idUser = Auth::guard('api')->user()->id;
            $conversationId = $request->conversation_id;
            [$data,$action] = $this->conversationPinManager->pinConversation($idUser, $conversationId);
            $msg =  __('message.conversation.pin.success', ['action' => $action]);
            if (isset($data['status_error'])) {
                $msg = __('message.conversation.pin.fail', ['action' => $action]);
                if ($data['status_error'] == Response::HTTP_FORBIDDEN) {
                    return $this->respondForbidden($msg);
                }
                return $this->respondBadRequest($msg);
            }
            return $this->renderJsonResponse($data, $msg);
        } catch (Exception $e) {
            Log::error("[ConversationController][pinConversation] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception("[ConversationController][pinConversation] line " . $e->getLine() . " error " . $e->getMessage());
        }
    }
}
