<?php

namespace App\Models;

use App\Support\MessageCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Message extends Model
{
    protected $table = 'messages';
    use SoftDeletes;
    
    const EXTENSION_IMAGE = 1;
    const EXTENSION_VIDEO = 2;
    const EXTENSION_RADIO = 3;
    const EXTENSION_FILE_OTHER = 4;
    
    const LIST_TYPE_MESSAGE = [1, 2, 3];
    
    const TYPE_MESSAGE_TEXT = 1;
    const TYPE_MESSAGE_FILE = 2;
    const TYPE_MESSAGE_EMOJI = 3;
    
    const ERROR_PERMISSION = 1;
    const ERROR_DATA = 2;
    const SENT_GROUP = 1;
    const SCROLL_NEW_MESSAGE = 1;
    const SCROLL_OLD_MESSAGE = 2;
    const REPLY_IS_NOT_READ = 0;
    const REPLY_IS_READ = 1;
    const NOT_EDITED = 0;
    const EDITED = 1;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['user_id','conversation_id','content_text','content_file','remind_users','reply_id', 'quote_text', 'is_sent_group','group_message'];
    
    /**
     * add attribute
     */

    protected $appends = ['list_reaction'];

    /**
     * cast attribute
     */
    protected $casts = [
        'list_user_replies' => 'array',
        'message_reply' => 'array',
    ];
     
    /**
     * Get the content_file .
     *
     * @return array
     */

    public function getListReactionAttribute()
    {
        $data = [];
        if (isset($this->attributes['list_reaction'])) {
            $data = json_decode($this->attributes['list_reaction'], true);
            if (!empty($data)) {
                $data = array_values(array_unique($data, SORT_REGULAR));
            }
        }
        return $data;
    }

    /**
     * Get the content_file .
     *
     * @return array
     */

     public function getListUserRepliesAttribute()
     {
         $data = [];
         if (isset($this->attributes['list_user_replies'])) {
             $data = json_decode($this->attributes['list_user_replies'], true);
             if (!empty($data)) {
                 $data = array_values(array_unique($data, SORT_REGULAR));
             }
         }
         return $data;
    }
    
    public function newCollection(array $models = [])
    {
        return new MessageCollection($models);
    }
}
