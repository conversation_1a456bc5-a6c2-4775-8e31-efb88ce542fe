<?php

namespace App\Console\Commands;

use App\Logics\ProjectManager;
use App\Logics\TaskManager;
use App\Models\Project;
use App\Models\ProjectTask;
use App\Models\TaskStatus;
use App\Traits\StorageTrait;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class UpdateColumnIsSlow extends BaseCommand
{
    use StorageTrait;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_column_is_slow {--website_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update column is_slow of projects, tasks';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        try {
            DB::beginTransaction();
            // get list task
            $tasks = ProjectTask::select([
                    'id',
                    'started_at',
                    'ended_at',
                    'progress',
                    'is_slow',
                    DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child')
                ])
                ->where('status', '!=', TaskStatus::TASK_STATUS_CLOSE)
                ->whereNull('parent_task')
                ->get();
            $taskManager = new TaskManager();
            // Update task
            if (!empty($tasks)) {
                foreach ($tasks as $task){
                    if($task->number_child > 0){
                        $taskManager->updateSlowTaskHasChild($task);
                    }else{
                        $isSlow = $taskManager->checkIsSlow($task);
                        $task->is_slow = $isSlow?ProjectTask::IS_SLOW:ProjectTask::NOT_SLOW;
                        $task->save();
                    }
                }
            }
            // get list project
            $projects = Project::select([
                    'id',
                    'started_at',
                    'ended_at',
                    'progress',
                    'is_slow'
                ])
                ->whereNull('deleted_at')
                ->get();
            // Update project
            if (!empty($projects)) {
                foreach ($projects as $project){
                    $isSlow = (new ProjectManager())->checkIsSlow($project);
                    $project->is_slow = $isSlow?Project::IS_SLOW:Project::NOT_SLOW;
                    $project->save();
                }
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $e->getMessage();
        }
    }

    public function scheduleCommand(Schedule $schedule)
    {
        parent::scheduleCommand($schedule)->dailyAt('00:00');
    }
}
