<?php

namespace App\Http\Requests;

use App\Models\Conversation;
use App\Rules\MaxNormalizedLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ConversationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'type' => 'required|in:' . implode(',', Conversation::LIST_TYPE_API),
            'avatar' => 'nullable|mimes:jpeg,png,jpg,gif,svg,jfif|max:10240',
            'name' => 'required|max:500',
            'desciption' => ['nullable', new MaxNormalizedLength(10000)],
        ];
        if ((int) request()->type === Conversation::TYPE_TWO_USER) {
            $rules['users'] = 'required';
        }
        return $rules;
    }

    public function messages(): array
    {
        return [
            'name.required' => trans('message.request.input_required', ['attribute' => trans('validation.attributes.name_conversation')]),
            'name.max' => trans('validation.max.string', ['attribute' => __('validation.attributes.name_conversation')]),
        ];
    }
}
