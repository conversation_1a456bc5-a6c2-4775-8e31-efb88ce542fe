<?php

namespace App\Console\Commands;

use App\Logics\EvaluationResultManager;
use App\Models\EvaluationResult;

class UpdateScoreKPI extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:update_score_kpi  {--website_id=} {--form_id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update score KPI';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        parent::handle();
    }

    // Implement the command here
    protected function _handle() {
        $form_id = $this->option('form_id');
        $evaluationUser = EvaluationResult::where('form_id',$form_id)->get();

        $evaluationResultManager = new EvaluationResultManager();
        if ($evaluationUser != null){
            foreach ($evaluationUser as $user){
                $evaluationResultManager->updateScore($form_id,$user->user_id);
            }
        }
    }
}
