<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use K<PERSON>lik\ColumnSortable\Sortable;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sprint extends Model
{
    use Sortable;
    use SoftDeletes;

    const INITIAL = 0;
    const ACTIVE = 1;
    const COMPLETE = 2;

    protected $table = 'project_sprints';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name','project_id','status','started_at','ended_at'];
    protected $guarded = [];

    /**
     * Save id user created or updated
     */
    protected static function boot()
    {
        parent::boot();

        self::creating(function ($data) {
            $data->created_by = auth()->id();
        });
        self::saving (function ($data) {
            $data->updated_by = auth()->id();
        });
    }

    public static function checkStatus($status)
    {
        $html = '';
        switch ($status) {
            case self::INITIAL:
                $html .= "<span>" . trans('language.start') . "</span>";
                break;
            case self::ACTIVE:
                $html .= "<span>" . trans('language.complete') . "</span>";
                break;
            default:
                $html .= "<span>" . trans('language.start') . "</span>";
                break;
        }
        return $html;
    }

}
