<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EncryptService
{
    /**
     * Get encrypted key from Akeyless
     *
     * @return string
     * @return string
     * @throws Exception
     */
    public function getEncryptedKey(): string
    {
        $akeylessService = new AkeylessService();
        $secretPath = config('services.akeyless.secret_path');
        return $akeylessService->getSecret($secretPath);
    }

    /**
     * Basic decrypt: AES_DECRYPT(FROM_BASE64(column), 'key') AS alias
     * @param string $column
     * @param string|null $alias
     * @return string
     * @throws Exception
     */
    public function getDecryptedRaw(string $column, ?string $alias = null): string
    {
        $encryptKey = $this->getEncryptedKey();
        $sql = "AES_DECRYPT(FROM_BASE64({$column}), '{$encryptKey}')";

        if ($alias) {
            $sql .= " AS {$alias}";
        }

        return $sql;
    }

    /**
     * Process key like MySQL does - XOR folding to 16 bytes
     *   This method creates a fixed-length key by:
     *   - Creating an empty byte array of $length
     *   - Iterating through each character in the original key
     *   - Applying XOR between ASCII value of character and array element
     *   - Ensures compatibility with MySQL's AES_ENCRYPT/AES_DECRYPT key processing
     * @param string $key
     * @param int $length
     * @return string
     */
    private function generatePassphrase(string $key, int $length = 16): string
    {
        $result = array_fill(0, $length, 0);
        foreach (str_split($key) as $i => $char) {
            $result[$i % $length] ^= ord($char);
        }
        return implode(array_map("chr", $result));
    }

    /**
     * Encrypt data with PHP compatible with MySQL AES_ENCRYPT
     * @param string|null $data
     * @return string
     * @throws Exception
     */
    public function encryptedData(?string $data): string
    {
        if ($data === null) {
            return '';
        }

        $originalKey = $this->getEncryptedKey();
        $processedKey = $this->generatePassphrase($originalKey);

        $encrypted = openssl_encrypt($data, 'AES-128-ECB', $processedKey, OPENSSL_RAW_DATA);

        if ($encrypted === false) {
            throw new Exception('Encryption failed');
        }

        return base64_encode($encrypted);
    }

    /**
     * Encrypt quote text with empty check, using PHP compatible with MySQL AES_ENCRYPT
     * @param string|null $data
     * @return string|null
     * @throws Exception
     */
    public function encryptedQuoteData(?string $data): ?string
    {
        if (empty($data)) {
            return null;
        }

        return $this->encryptedData($data);
    }
}
