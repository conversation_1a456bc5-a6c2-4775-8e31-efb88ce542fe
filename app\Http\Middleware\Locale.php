<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use App\Models\Language;

class Locale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if($user){
            $language = Language::find($user->language_id);
            if($language){
                $locale = $language->name;
                App::setLocale($locale);
                session()->put('locale', $locale);
            }
        } else {
            App::setLocale(session()->get('locale'));
        }
        return $next($request); 
    }
}
