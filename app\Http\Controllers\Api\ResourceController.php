<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Logics\ProjectManager;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Logics\ResourceManager;
use Illuminate\Support\Facades\Auth;

class ResourceController extends Controller
{
     /**
     * get list meeting rooms.
     *
     * @return \Illuminate\Http\Response
     */
    public function getMeetingRoom() {
        
        $resourceManager = new ResourceManager();
        $data = $resourceManager->getMeetingRoom();
                
        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => trans('message.success'),
            'data' => $data
        ], Response::HTTP_OK);
    }
    /**
     * get projects user.
     *
     * @return \Illuminate\Http\Response
     */
    public function getProjectsUser() {
        $userId = Auth::id();
        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        return response()->json([
            'code' => Response::HTTP_OK,
            'message' => trans('message.success'),
            'data' => $projects
        ], Response::HTTP_OK);
    }
}
