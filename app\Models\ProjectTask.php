<?php

namespace App\Models;

use App\Logics\TaskManager;
use App\Models\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Kyslik\ColumnSortable\Sortable;
use Illuminate\Support\Facades\Auth;
use App\Models\TaskBookmark;

/**
 * Class ProjectTask
 * @property integer $id
 * @property integer $project_id
 * @property string $name
 * @property string $description
 * @property date $started_at
 * @property date $ended_at
 * @property integer $type
 * @property integer $status
 * @property integer $priority
 * @property integer $user_id
 * @property integer $estimated_time
 * @property integer $progress
 * @property integer $parent_task
 * @property integer $created_by
 * @property integer $updated_by
 * @property date $created_at
 * @property date $updated_at
 * @property integer $is_slow
 */

class ProjectTask extends Model
{
    use Sortable;

    protected $guarded = [];

    const BOOKMARKED = 1; // Bookmarked
    const NOT_BOOKMARKED = 0; // Not Bookmarked
    const IS_SLOW = 1;
    const NOT_SLOW = 0;
    const WARNING = 1;

    const SPRINT_INITIALIZE = 0;
    const SPRINT_ACTIVE = 1;
    const SPRINT_COMPLETE = 2;
    public $sortable = [
        'id',
        'updated_at',
        'status'
    ];

    protected $casts = [
        'reason' => 'array'
   ];

    /**
     * Get User is assigned the job
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(\App\User::class,'user_id','id');
    }


    /**
     * Get the project that owns the task.
     */
    public function project()
    {
        return $this->belongsTo(\App\Models\Project::class);
    }

    /**
     * Scope check the user has permission to access the task
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeCheckUserPermission($query, $userId) {
        return $query->leftJoin('projects','projects.id','project_tasks.project_id')
            ->where(function($query) use ($userId){
                $query->whereIn('project_tasks.project_id',function($subQuery) use ($userId){
                    $subQuery->select('project_id')
                        ->distinct()
                        ->from('project_members')
                        ->where('user_id', $userId);
                })
                    ->orwhere('projects.public','=',1);
            });
    }

    /**
     * Scope calculate new progress, estimated_time, started_at, ended_at of the task from child tasks
     *
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeUpdateTask($query) {
        return $query->addSelect(
            DB::raw('SUM(child_tasks.progress * child_tasks.estimated_time) / SUM(child_tasks.estimated_time) AS updated_progress'),
            DB::raw('SUM(child_tasks.estimated_time) AS updated_estimated_time'),
            DB::raw('MIN(child_tasks.started_at) AS updated_started_at'),
            DB::raw('MAX(child_tasks.ended_at) AS updated_ended_at')
        )
        ->leftJoin('project_tasks AS child_tasks', 'child_tasks.parent_task', '=', 'project_tasks.id')
        ->groupBy('project_tasks.id');
    }

    /**
     * Automatically update parent tasks when task change
     */
    protected static function boot()
    {
        parent::boot();

        $taskManager = new TaskManager();

        // When a task is added, all ancestor task are updated
        self::created(function($model) use ($taskManager) {
            $taskManager->updateAncestorTasksAndProject($model);
        });
        // When a task changes estimated_time or progress, all ancestor task are updated
        self::updated(function($model) use ($taskManager) {
            if($model->isDirty('estimated_time') || $model->isDirty('progress')
            || $model->isDirty('started_at') || $model->isDirty('ended_at')
            || $model->isDirty('parent_task') || $model->isDirty('project_id')) {
                $taskManager->updateAncestorTasksAndProject($model);
            }
        });
        // When a task is deleted, all ancestor task are updated
        self::deleted(function($model) use ($taskManager) {
            $taskManager->updateAncestorTasksAndProject($model);
        });
    }

    public function checkBookmark(){
        $taskBookmark = TaskBookmark::where('task_id', $this->id)
                        ->where('user_id', Auth::id())
                        ->get();
        if ($taskBookmark->count()){
            return  self::BOOKMARKED;
        } else {
            return self::NOT_BOOKMARKED;
        }
    }

    /**
     * get all level tasks from parent to child
     */
    public function _nLevelTask(){
        $columns = [
            'project_tasks.id',
            'project_tasks.parent_task',
            'project_tasks.started_at',
            'project_tasks.ended_at',
            'project_tasks.progress',
            'projects.name as project_name',
            'projects.id as project_id',
            'task_types.name as type_name',
            'task_types.id as type_id',
            'task_status.name as status_name',
            'task_status.id as status_id',
            'task_priorities.name as priority_name',
            'task_priorities.id as priority_id',
            'project_tasks.estimated_time',
            'project_tasks.name',
            'users.first_name',
            'users.last_name',
            'users.id as user_id',
            'projects.started_at as project_start',
            'projects.ended_at as project_end',
            'projects.progress as project_progress',
            'projects.description as project_description',
            'project_tasks.is_slow',
            'project_tasks.warning',
            'project_tasks.level',
            DB::raw('CONCAT_WS(" " , users.first_name, users.last_name) as user_name'),
            DB::raw('(SELECT count(*) FROM project_tasks as tasks_child WHERE tasks_child.parent_task = project_tasks.id) as number_child'),
            'project_tasks.key_member',
            'project_tasks.created_by',
        ];

        $userId = Auth::id();

        return $this->hasMany(ProjectTask::class, 'parent_task')->with(['_nLevelTask' => function($q1) use ($columns, $userId){
            $q1->select(
                $columns
            )
            ->checkUserPermission($userId)
            ->leftJoin('users','project_tasks.user_id','users.id')
            ->leftJoin('task_types','project_tasks.type','task_types.id')
            ->leftJoin('task_status','project_tasks.status','task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id');
        }]);
    }

}
