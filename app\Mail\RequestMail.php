<?php

namespace App\Mail;

use App\Models\Language;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestMail extends BaseMail
{
    use Queueable, SerializesModels;

    protected $arrRequestInfor;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($arrRequestInfor,$approverTag)
    {
        $language_id = isset($approverTag['language_id']) ? $approverTag['language_id'] : null;
        parent::__construct($language_id);
        $this->arrRequestInfor = $arrRequestInfor;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), env(config('mail.from.name')))
            ->subject(trans('language.mail_request.subject'))
            ->view('mail.mail_request', ['arrRequestInfor' => $this->arrRequestInfor]);
    }
}
