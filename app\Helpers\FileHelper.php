<?php
namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileHelper {
    
    /**
     * Handle Upload Image
     * @param string $uploadPath
     * @param $name
     * @param $request
     * @return string
     */
    public static function handleUploadFile($uploadPath, $name, $request)
    {
        $fullPath = '';
        if (!$request->hasFile($name)) {
            return $fullPath;
        }
        $file = $request->file($name);
        $saveName = date('YmdHis') . '_' . sha1(Str::uuid()) . '.' . $file->getClientOriginalExtension();
        $fullPath = $uploadPath . $saveName;
        if (!Storage::disk(FILESYSTEM)->exists($uploadPath)) {
            Storage::disk(FILESYSTEM)->makeDirectory($uploadPath);
        }
        Storage::disk(FILESYSTEM)->put($fullPath, file_get_contents($file));
        return $fullPath;
    }
}