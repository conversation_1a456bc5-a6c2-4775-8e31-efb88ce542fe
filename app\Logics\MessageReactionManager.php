<?php

namespace App\Logics;

use App\Enums\SocketEvent;
use App\Models\Message;
use App\Models\MessageReaction;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use \Illuminate\Http\Response;

class MessageReactionManager
{
    public function store($userId, $request){
        try {
            $reaction = $request->input('reaction');
            $messageId = $request->input('message_id');
            $currentUser = Auth::guard('api')->user();
            $message = Message::query()
                    ->join('conversation_participants',function($join) use ($userId){
                        $join->on('conversation_participants.conversation_id','messages.conversation_id')
                            ->where('conversation_participants.user_id',$userId);
                    })
                    ->where('messages.id', $messageId)
                    ->select([
                        'messages.id',
                        'messages.user_id',
                        'messages.conversation_id',
                        'messages.reply_id'
                    ])
                    ->first();
            if(!$message){
                return ['status_error' => Response::HTTP_FORBIDDEN];
            }
            $messageReaction = MessageReaction::where([
                'user_id' => $userId,
                'message_id' => $messageId,
            ])->first();
            if($messageReaction){
                if(empty($reaction)){
                    $messageReaction->delete();
                }else{
                    $messageReaction->reaction = $reaction;
                    $messageReaction->save();
                }
            }else{
                if(empty($reaction)){
                    return ['status_error' => Response::HTTP_BAD_REQUEST];
                }else{
                    $messageReaction = MessageReaction::create([
                        'user_id' => $userId,
                        'message_id' => $messageId,
                        'reaction' => $reaction,
                    ]);
                }
            }
            // Get list participant in convention.
            $dataConversationParticipant = ConversationParticipantManager::GetConversationParticipant($message->conversation_id, null, ['id','user_id', 'status', 'updated_at']);;
            
            // send notification to socket
            app(SocketManager::class)->emit(SocketEvent::REACTION, [
                'user_id' => $userId,
                'message_id' => $messageId,
                'reply_id' => $message->reply_id,
                'conversation_id' => $message->conversation_id,
                'reaction' => $reaction,
                'avatar' => $currentUser->avatar,
                'name' => $currentUser->full_name,
                'receiver' => $dataConversationParticipant->pluck('user_id')->toArray()
            ]);
            return $messageReaction;
        } catch (Exception $e) {
            Log::error("[MessageReactionManager][store] line " . $e->getLine() . " error " . $e->getMessage());
            throw new Exception('[MessageReactionManager][store] line ' . $e->getLine() . ' error ' . $e->getMessage());
        }
    }
}