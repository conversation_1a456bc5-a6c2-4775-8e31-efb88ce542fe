<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Requests\TenantFormRequest;
use App\Jobs\SendMailRegisTenant;
use App\Models\Tenant\Devices;
use Hyn\Tenancy\Environment;
use App\Models\Tenant\CompanyProfile;
use App\Models\Tenant\Website;
use App\Http\Controllers\Controller;
use App\Logics\TenantManager;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Illuminate\Support\Str;
use App\Models\Role;

class TenantController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return View
     */
    public function index(Request $request)
    {
        $tenants = Website::select(
            'websites.id',
            'websites.uuid',
            'websites.created_at',
            'websites.updated_at',
            'websites.deleted_at',
            'hostnames.fqdn',
            'companyprofiles.name',
            'companyprofiles.email',
            'companyprofiles.phone',
            'companyprofiles.address'
        )
        ->withTrashed()
        ->join('hostnames', 'websites.id', '=', 'hostnames.website_id')
        ->join('companyprofiles', 'websites.id', '=', 'companyprofiles.website_id')
        ->where('hostnames.fqdn', '!=', env('HEADQUARTER_SUBDOMAIN', 'admin'). '.' . env('APP_URL_BASE', 'localhost'))
        ->sortable()
        ->paginate(10);

        // Redirect to last page if page parameter greater than last page
        if ($tenants->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $tenants->lastPage()]));
        }

        return view('tenant.index', ['tenants' => $tenants]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return View
     */
    public function create()
    {
        return view('tenant.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     */
    public function store(TenantFormRequest $request)
    {
        try {
            $tenant_form = $request->all();

            // Check sub-domain must be not exists
            $tenantManager = new TenantManager();
            if ($tenantManager->tenantExists($tenant_form['sub_domain'])) {
                return back()->with([
                    'status_failed' => trans('message.sub_domain_exists')
                ]);
            }

            // Create a new tenant
            $tenant = $tenantManager->registerTenant($tenant_form);
            app(Environment::class)->tenant($tenant["website"]);

            // Initialize databse
            $tenantManager->initDatabase();

            // Register new companyprofile
            $companyProfile = CompanyProfile::create([
                'website_id' => $tenant["website"]->id,
                'name' => $tenant_form['name'],
                'email' => $tenant_form['email'],
                'address' => $tenant_form['address'],
                'phone' => $tenant_form['phone'],
            ]);

            $password = Str::random(16);
            $admin = $tenantManager->addAdmin($tenant_form, $password);
            $admin->assignRole(Role::ROLE_SYSTEM_MANAGER);


            // Send mail create a new tenant successfully
            $data['email'] = $tenant_form['email'];
            $data['password'] = $password;
            $data['domain'] = (request()->secure() ? 'https://' : 'http://') . $tenant_form['sub_domain'] . '.' . env('APP_URL_BASE', 'localhost');
            dispatch(new SendMailRegisTenant($data, $tenant["website"]->id))->onQueue(QUEUE_MAIL);

            return redirect()->route('tenant.index')->with([
                'status_succeed' => trans('message.create_succeed')
            ]);
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }


    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return View | RedirectResponse
     */
    public function edit(Request $request, $id)
    {
        // Check tenant exists
        $tenantManager = new TenantManager();
        $tenant = $tenantManager->checkTenantExists($id, 2);

        if (empty($tenant)) {
            return redirect()->route('tenant.index');
        }

        // Get all devices of the company
        app(Environment::class)->tenant($tenant);
        $devices = Devices::where('website_id',$id)->sortable()->paginate(10);

        // Redirect to last page if page parameter greater than last page
        if ($devices->lastPage() < $request->page) {
            return redirect($request->fullUrlWithQuery(['page' => $devices->lastPage()]));
        }
        // Redirect to first page if page parameter less than 0
        if ($request->page < 0) {
            return redirect($request->fullUrlWithQuery(['page' => 1]));
        }

        return view('tenant.edit', ['tenant' => $tenant, 'devices' => $devices]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return RedirectResponse
     */
    public function update(TenantFormRequest $request, $id)
    {
        // Check tenant exists
        $tenantManager = new TenantManager();
        $tenant = $tenantManager->checkTenantExists($id, 2);

        if (empty($tenant)) {
            return redirect()->route('tenant.index');
        }

        try {
            $tenant_form = $request->all();
            $tenant = $tenantManager->updateTenant($tenant, $tenant_form);

            return back()->with([
                'status_succeed' => trans('message.update_succeed')
            ]);
        } catch (\Exception $e) {
            Log::error($e);
            return back()->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return RedirectResponse
     */
    public function destroy($id)
    {
        try {
            // Check tenant exists
            $tenantManager = new TenantManager();
            $tenant = $tenantManager->checkTenantExists($id);

            if (!(isset($tenant))) {
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    'msg' => [
                        'title' => trans('language.failure'),
                        'text' => trans('message.company_not_exist'),
                    ],
                ];
            }

            $website = $tenant;
            $website->companyprofiles[0]->delete();
            $website->hostnames[0]->delete();
            $website->delete();

            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success'),
                    'text' => trans('message.delete_company_succeed'),
                ],
            ];
        } catch (\Exception $e) {
            Log::error($e);
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.server_error'),
                ],
            ];
        }
    }

    /**
     * Restore the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function restore($id)
    {
        try {
            // Check tenant exists
            $tenantManager = new TenantManager();
            $tenant = $tenantManager->checkTenantExists($id, 0);

            if (!(isset($tenant))) {
                return [
                    'status' => Response::HTTP_NOT_FOUND,
                    'msg' => [
                        'title' => trans('language.failure'),
                        'text' => trans('message.company_not_exist'),
                    ],
                ];
            }

            $website = $tenant;
            $website->companyprofiles[0]->restore();
            $website->hostnames[0]->restore();
            $website->restore();

            return [
                'status' => Response::HTTP_OK,
                'msg' => [
                    'title' => trans('language.success'),
                    'text' => trans('message.restore_company_succeed'),
                ],
            ];
        } catch (\Exception $e) {
            Log::error($e);
            return [
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => trans('message.server_error'),
                ],
            ];
        }
    }
}
