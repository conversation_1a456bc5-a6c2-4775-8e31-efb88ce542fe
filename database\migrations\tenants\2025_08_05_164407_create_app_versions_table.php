<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAppVersionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('app_versions', function (Blueprint $table) {
            $table->id('id');
            $table->string('version', 20);
            $table->tinyInteger('platform');
            $table->boolean('force_update')->default(0);
            $table->tinyInteger('status')->default(0);
            $table->text('release_notes')->nullable();
            $table->string('description', 500)->nullable();
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            $table->unique(['platform', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('app_versions');
    }
}
