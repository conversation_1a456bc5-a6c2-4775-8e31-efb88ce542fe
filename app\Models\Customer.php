<?php

namespace App\Models;

use App\User;
use App\Models\BTASetting;
use App\Models\Prefecture;
use Hyn\Tenancy\Traits\UsesTenantConnection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use K<PERSON>lik\ColumnSortable\Sortable;

class Customer extends Model
{
    use SoftDeletes;
    use Sortable;
    use UsesTenantConnection;

    protected $table = 'customers';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'creation_date',
        'user_id',
        'name',
        'age',
        'gender',
        'job',
        'address',
        'level_of_inquiry',
        'target',
        'language_proficiency',
        'modes_study',
        'concerns',
        'level_care',
        'feedback',
        'data_channels',
        'note'
    ];

    public $sortable = [
        'creation_date',
        'name',
        'age',
        'gender',
        'job',
        'address',
        'level_of_inquiry',
        'target',
        'language_proficiency',
        'modes_study',
        'concerns',
        'level_care',
        'feedback',
        'data_channels',
        'note'
    ];

    public function ageSetting()
    {
        return $this->belongsTo(BTASetting::class, 'age', 'id');
    }

    public function genderSetting()
    {
        return $this->belongsTo(BTASetting::class, 'gender', 'id');
    }

    public function jobSetting()
    {
        return $this->belongsTo(BTASetting::class, 'job', 'id');
    }

    public function levelOfInquirySetting()
    {
        return $this->belongsTo(BTASetting::class, 'level_of_inquiry', 'id');
    }

    public function feedbackSetting()
    {
        return $this->belongsTo(BTASetting::class, 'feedback', 'id');
    }

    public function levelCareSetting()
    {
        return $this->belongsTo(BTASetting::class, 'level_care', 'id');
    }

    public function targetSetting()
    {
        return $this->belongsTo(BTASetting::class, 'target', 'id');
    }

    public function languageProficiencySetting()
    {
        return $this->belongsTo(BTASetting::class, 'language_proficiency', 'id');
    }

    public function modesStudySetting()
    {
        return $this->belongsTo(BTASetting::class, 'modes_study', 'id');
    }

    public function concernsSetting()
    {
        return $this->belongsTo(BTASetting::class, 'concerns', 'id');
    }

    public function dataChannelsSetting()
    {
        return $this->belongsTo(BTASetting::class, 'data_channels', 'id');
    }

    public function prefecture()
    {
        return $this->belongsTo(Prefecture::class, 'address', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}
