<?php

namespace App\Exports\Report;

use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ReportMultiSheetExport implements WithMultipleSheets
{
    use Exportable;

    protected $data;
    protected $invalidCharacters = array('*', ':', '/', '\\', '?', '[', ']');

    function __construct($data) {
        $this->data = $data;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        $sheets[] = new FirstSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.function_screen")), $this->data['function_screen']);
        $sheets[] = new SecondSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.person_charge")), $this->data['user_name']);
        $sheets[] = new ThirdSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.bug_classify")), $this->data['bug_classify_name']);
        $sheets[] = new FourthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.range_classify")), $this->data['bug_range_name']);
        $sheets[] = new FifthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.reason_classify")), $this->data['bug_reason_name']);
        $sheets[] = new SixthSheetExport(str_replace($this->invalidCharacters, ' - ', trans("language.severity")), $this->data['bug_severity_name']);

        return $sheets;
    }
}
