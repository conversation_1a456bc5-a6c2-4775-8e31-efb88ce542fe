<?php

namespace App\Http\Controllers\Evaluation;

use App\Http\Controllers\Controller;
use App\Logics\EvaluationManager;
use App\Models\Evaluation;
use App\Models\EvaluationResult;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use App\Logics\TaskManager;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EvaluationResultController extends Controller
{
    const PER_PAGE = 15;
    public function index(Request $request)
    {
        // get the id of the logged in user
        $userID = Auth::guard('web')->user()->id;
        $evaluationManager = new EvaluationManager;
        $filterHtml = $evaluationManager->getFilterHtml($request,['project','member','start_at','end_at']);

        $evaluationResults = EvaluationResult::select([
                'evaluation_results.score',
                'evaluation_forms.name as form_name',
                'evaluation_results.form_id',
                'evaluation_forms.created_at'
            ])
            ->join('evaluation_forms','evaluation_forms.id','evaluation_results.form_id')
            ->where('evaluation_results.user_id', $userID);

        if (!empty($request->start_at)) {
            $start_at = $request->start_at;
            $start_at = Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');
            $evaluationResults = $evaluationResults->where(function ($query) use ($start_at) {
                $query->where('evaluation_forms.from', '>=', $start_at)
                    ->orwhere('evaluation_forms.to', '>=', $start_at);
            });
        }
        if (!empty($request->end_at)) {
            $end_at = $request->end_at;
            $end_at = Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
            $evaluationForms = $evaluationResults->where(function ($query) use ($end_at) {
                $query->where('evaluation_forms.from', '<=', $end_at)
                    ->orwhere('evaluation_forms.to', '<=', $end_at);
            });
        }

        $sortable = ['evaluation_forms.name', 'evaluation_forms.created_at', 'evaluation_results.score'];
        $direction = in_array($request->direction, ['asc', 'desc']) ? $request->direction : 'asc';
        if (in_array($request->sort, $sortable)) {
            $evaluationResults = $evaluationResults->orderBy($request->sort, $direction);
        }
        $evaluationResults = $evaluationResults->paginate(self::PER_PAGE);
        return view('evaluation.result.index', [
            'data' => $evaluationResults,
            'filterHtml' => $filterHtml,
        ]);
    }
    /**
     * Get data effort
     *
     */
    public function getDataEffort(Request $request)
    {
        $userID = Auth::guard('web')->user()->id;

        $evaluationForms = EvaluationResult::select([
            'evaluation_results.score',
            'evaluation_forms.name',
            'evaluation_forms.id',
            'evaluation_forms.created_at',
            'evaluation_forms.to',
        ])
            ->join('evaluation_forms','evaluation_forms.id','evaluation_results.form_id')
            ->where('evaluation_results.user_id',$userID)
            ->orderBy('evaluation_forms.to','DESC');

        if (empty($request->end_at) && empty($request->start_at)) {
            $evaluationForms = $evaluationForms->limit(10);
        } else {
            if (!empty($request->start_at)) {
                $start_at = $request->start_at;
                $start_at = Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');
                $evaluationForms = $evaluationForms->where(function($query)  use ( $start_at){
                    $query->where('evaluation_forms.from','>=',  $start_at)
                        ->orwhere('evaluation_forms.to', '>=',  $start_at);
                });
            }
            if (!empty($request->end_at)) {
                $end_at = $request->end_at;
                $end_at = Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
                $evaluationForms = $evaluationForms->where(function($query)  use ($end_at){
                    $query->where('evaluation_forms.from','<=',  $end_at)
                        ->orwhere('evaluation_forms.to', '<=',  $end_at);
                });
            }
        }
        $evaluationForms = $evaluationForms->get()->sortBy('to');
        $date = [];
        $name = [];
        $result = [];
        foreach($evaluationForms as $evaluationForm){
            $date[] = Carbon::createFromFormat('Y-m-d H:i:s', $evaluationForm->to)->format('d-m-Y');
            $name[] = $evaluationForm->name;
            $result[] = isset($evaluationForm->score)?$evaluationForm->score:0;
        }
        $dataset = [];
        $dataset[] = [
            "data"=> $result,
            "label" =>  trans('language.score') ,
            "borderColor" => "#0791A3",
            "fill" => false,
            "lineTension" => 0,
            "spanGaps" => true,
            "pointBorderWidth" => 4
        ];

        $data['date'] =  $date;
        $data['name'] =  $name;
        $data['score'] = $dataset;

        return response()->json($data);
    }


    public function store()
    {

    }

    public function update()
    {

    }

    /**
     * array pagination
     *
     * @param array $items
     * @param int $perPage
     */
    public function paginate($items, $perPage, $page = null, $options = [])
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }
}
