<?php

namespace App\Http\Controllers\Project;

use App\Helpers\RequestHelper;
use App\Http\Requests\ProjectRequest;
use App\Logics\EventManager;
use App\Logics\WorkingTimeManager;
use App\Logics\ProjectManager;
use App\Logics\ProjectMemberManager;
use App\Logics\TaskManager;
use App\Logics\TaskLogManager;
use App\Logics\UserManager;
use App\Models\BugRange;
use App\Models\BugTag;
use App\Models\Project;
use App\Http\Controllers\Controller;
use App\Logics\AttachmentManager;
use App\Logics\ProjectDocumentManager;
use App\Logics\ResourceManager;
use App\Models\ProjectMember;
use App\Models\ProjectPin;
use App\Models\ProjectRole;
use App\Models\ProjectTask;
use App\Models\TaskAction;
use App\Models\TaskPriority;
use App\Models\TaskStatus;
use App\Models\TaskType;
use App\Services\ProjectMemberService;
use App\Services\TaskStatusService;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;
use App\Models\TaskLog;
use PDF;
use App\Models\SiteSetting;
use App\Models\TaskAttachment;
use Illuminate\Support\Str;
use App\Traits\StorageTrait;
use App\Helpers\StringHelper;

class ProjectController extends Controller
{
    use StorageTrait;
    const PAGE_SIZE_PROJECTS = 9;
    const PAGE_SIZE_GANTT = 100;
    const PAGE_TASK_LOG = 15;
    
    private $taskStatusService;
    
    private $projectMemberService;
    
    public function __construct(
        TaskStatusService $taskStatusService,
        ProjectMemberService $projectMemberService
    )
    {
        $this->taskStatusService = $taskStatusService;
        $this->projectMemberService = $projectMemberService;
    }

    /**
     * Display a listing of the resource.
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
     */
    public function index(Request $request)
    {
        $userId = Auth::id();

        // Sort
        $projects = Project::select(
                'projects.id',
                'projects.name',
                'projects.description',
                'projects.started_at',
                'projects.ended_at',
                'projects.created_by',
                'projects.public',
                'projects.progress',
                'projects.is_slow'
            )->checkUserPermission($userId)
            ->with(['userCreateProject' => function($q) {
                $q->select('id', 'first_name', 'last_name');
            }])
            ->with(['members' => function($q) {
                $q->select('id', 'first_name', 'last_name', 'avatar')->distinct();
            }]);

        if ($request->status){
            if ($request->status == Project::STATUS_OPEN){
                $projects = $projects->whereNull('projects.deleted_at');
            } else if ($request->status == Project::STATUS_CLOSE){
                $projects = $projects->whereNotNull('projects.deleted_at');
            }
        } else {
            $projects = $projects->whereNull('projects.deleted_at');
        }

        $sort = isset($request->sort) ? $request->sort :'id';
        $direction = isset($request->direction) ? $request->direction :'DESC';
        $projects = $projects
            ->groupBy('projects.id')->orderBy($sort,$direction)
            ->paginate(self::PAGE_SIZE_PROJECTS);

        $displayId = SiteSetting::displayTaskProjectId();
         
        $projectIds = $this->projectMemberService->getProjectIds($userId);
        $countTasks = $this->taskStatusService->countTask($userId, $projectIds);
        $taskLogs = TaskLog::join('project_tasks','task_logs.task_id', 'project_tasks.id')
            ->join('projects', 'project_tasks.project_id', 'projects.id')
            ->leftJoin('users', 'task_logs.created_by', 'users.id')
            ->leftJoin('task_types', 'project_tasks.type', 'task_types.id')
            ->leftJoin('task_status', 'project_tasks.status', 'task_status.id')
            ->leftJoin('task_priorities','project_tasks.priority','task_priorities.id')
            ->where('task_logs.created_by', $userId)
            ->whereIn('project_tasks.project_id',$projectIds);

        if ($request->start_date) {
            $startDate  = date_create_from_format("d/m/Y",$request->start_date );
            if ($startDate ) {
                $startDate  = date_format($startDate ,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '>=', $startDate );
            }
        }
        if ($request->end_date){
            $endDate = date_create_from_format("d/m/Y",$request->end_date);
            if ($endDate){
                date_modify($endDate, "+1 days");
                $endDate = date_format($endDate,"Y-m-d");
                $taskLogs = $taskLogs->where('task_logs.updated_at', '<', $endDate);
            }
        }

        $taskLogs = $taskLogs->orderBy('task_logs.updated_at', 'desc')
            ->select('task_logs.*', 'project_tasks.name as task_name', 'project_tasks.status', 'projects.name as project_name',
                'users.id as userId', 'users.first_name', 'users.last_name', 'users.avatar', 'task_types.name as task_type', 'task_types.id as type_id',
                'task_status.name as task_status_name',
                'task_priorities.name as priority_name',
                'task_priorities.id as priority_id',
            )
            ->paginate(self::PAGE_TASK_LOG);

        $tasks = TaskLog::join('project_tasks','task_logs.task_id', 'project_tasks.id')
            ->groupBy('task_logs.task_id')->groupBy(DB::raw('date(task_logs.updated_at)'))
            ->where('task_logs.created_by', $userId)
            ->whereIn('project_tasks.project_id',$projectIds);

        if ($taskLogs->count()){
            $tasks = $tasks->havingRaw('max(task_logs.updated_at)>=?', [$taskLogs->last()->updated_at])
                ->havingRaw('min(task_logs.updated_at)<=?', [$taskLogs->first()->updated_at]);
        }
        $tasks = $tasks->select("task_logs.task_id", DB::raw('max(task_logs.updated_at) as last'))
            ->orderBy('last', 'desc')
            ->get();

        $userRequests = \App\Models\Request::query()->where('created_by', \auth()->user()->id)
            ->where('status', \App\Models\Request::STATUS_WAITING)
            ->get();
        $totalEditCheckIn = 0;
        $totalRequestOt = 0;
        $totalRequestOfDay = 0;
        foreach ($userRequests as $request) {
            if ($request->type == \App\Models\Request::TYPE_CHECKIN || $request->type == \App\Models\Request::TYPE_CHECKOUT) {
                $totalEditCheckIn++;
            }
            
            if ($request->type == \App\Models\Request::TYPE_OT) {
                $totalRequestOt++;
            }

            if ($request->type == \App\Models\Request::TYPE_VACATION) {
                $totalRequestOfDay++;
            }
        }
        $user = Auth::user();
        $userManager = new UserManager();
        $userCurrent = $userManager->getUserWithLeaveDay(Auth::user()->id, false)->first();
        $dayLeave = $userCurrent->userRemainingLeaveDay[0]->hours ?? 0;
        return view('projects.index',[
            'dayLeave'=>$dayLeave, 
            'user'=>$user, 
            'projects'=>$projects, 
            'displayId'=>$displayId,
            'countTasks'=>$countTasks,
            'tasks'=>$tasks,
            'taskLogs'=>$taskLogs,
            'totalEditCheckIn'=> $totalEditCheckIn,
            'totalRequestOt'=> $totalRequestOt,
            'totalRequestOfDay'=> $totalRequestOfDay,
            'userRequests'=> $userRequests,
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $userId = Auth::id();

        // Get all projects of the user to select parent project
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId, null , null , false);

        // Get list Project_roles
        $projectRoles = ProjectRole::select('id', 'name')->orderby('id')->get();
        // Get list user
        $users = User::select('id', 'first_name', 'last_name', 'email')->orderby('id')->get();
        return view('projects.add',[
            'users' => $users,
            'projectRoles'=>$projectRoles,
            'projects'=>$projects
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param ProjectRequest $request
     * @return \Illuminate\Http\Response
     */
    public function store(ProjectRequest $request)
    {
        try {
            $userId = Auth::id();
            if (!empty($request->parent_project)){
                // Check user has permission with parent project
                $userPermission = Project::select('projects.id')
                    ->where('id', $request->parent_project)
                    ->checkUserPermission($userId)
                    ->first();

                if (!isset($userPermission)) {
                    return redirect()->route('project.index');
                }
            }

            // Insert new project
            $project = new Project();
            $project->name = $request->project_name;
            $project->description = StringHelper::escapeHtmlForSummernote($request->description);
            $project->started_at = empty($request->started_at)?$request->started_at:\Carbon\Carbon::createFromFormat('d/m/Y', $request->started_at);
            $project->ended_at = empty($request->ended_at)?$request->ended_at:\Carbon\Carbon::createFromFormat('d/m/Y', $request->ended_at);
            $project->public = $request->has('project_public');
            $project->parent_project = $request->parent_project;
            $project->created_by =  $userId;
            $project->updated_by =  $userId;
            $project->is_slow = (new ProjectManager())->checkIsSlow($project) ? Project::IS_SLOW : Project::NOT_SLOW;
            $project->save();

            $users = $request->users;
            $checkPM = false;

            if($users != null){
                $users = array_unique($users);
                foreach($users as $user){
                    $array = explode("-", $user);
                    if($array[1] == $userId && $array[0] == 1){
                        $checkPM = true;
                    }
                    $created_at = ($array[2] != "null")? \Carbon\Carbon::createFromFormat('d/m/Y', $array[2]): Carbon::now();
                    $projectMember = new ProjectMember();
                    $projectMember->project_id =  $project->id;
                    $projectMember->user_id = $array[1];
                    $projectMember->role_id = $array[0];
                    $projectMember->created_at = $created_at;
                    $projectMember->created_by = $userId;
                    $projectMember->save();
                }
            }

            if($checkPM == false){
                // Add the user is a member of this project
                $projectMember = new ProjectMember();
                $projectMember->project_id = $project->id;
                $projectMember->user_id = $userId;
                $projectMember->created_at = Carbon::now();
                $projectMember->role_id = 1;
                $projectMember->save();
            }
            $data = Project::find($project->id);
            $destinationPath = str_replace(['{project_id}', '/tasks/{task_id}'], [$project->id, null], TASK_ATTACHMENT_DIR).'/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_PROJECT,$request->documents, $request->descriptionDocument, $destinationPath, 'description');
            DB::commit();
            return redirect()->route('project.index')->with([
                'status_succeed' => trans('message.create_project_succeed')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->route('project.index')->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }

    /**
     * Show the project
     *
     * @param $id
     *
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request,$id)
    {
        $userId = auth()->id();

        // Check user has permission with the project
        $userPermission = Project::select('projects.id')
            ->where('id', $id)
            ->checkUserPermission($userId)
            ->first();
        if (!isset($userPermission)) {
            return redirect()->route('project.index');
        }

        // Get the project
        $project = Project::select('id', 'name', 'created_at', 'created_by', 'updated_at', 'public', 'parent_project','description','started_at', 'ended_at', 'deleted_at','progress')
            ->where('id', $id)
            ->with(['userCreateProject' => function($q) {
                $q->select('id', 'first_name', 'last_name', 'avatar');
            }])
            ->with(['parentProject' => function($q) {
                $q->select('id', 'name');
            }])
            ->first();

        // Get all sub projects of the project which the user is a member of the sub project
        $subProjects = Project::select('id','name')
            ->where('parent_project', '=', $id)
            ->leftJoin('project_members', 'project_members.project_id', 'projects.id')
            ->where(function($query) use ($userId) {
                $query->where('projects.public','=',1)
                    ->orWhere('project_members.user_id', '=', $userId);
            })
            ->distinct()
            ->get();

        // Get all members of the project group by role
        $groupMembers = User::select(
            DB::raw("GROUP_CONCAT(CONCAT_WS(' ',CONCAT_WS('".CONCAT_SEPARATOR."',users.id, users.first_name),users.last_name) SEPARATOR '".GROUP_CONCAT_SEPARATOR."') AS members"),
            'project_roles.id AS project_id',
            DB::raw('MIN(project_roles.name) AS project_role_name'))
            ->join('project_members', 'project_members.user_id', 'users.id')
            ->where('project_members.project_id', $id)
            ->join('project_roles', 'project_roles.id', 'project_members.role_id')
            ->groupBy('project_roles.id')
            ->get();
        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        $projectRoles = [];
        $users = [];
        if($isManager){
            // Get list Project_roles
            $projectRoles = ProjectRole::select('id', 'name')->orderby('id')->get();
            // Get list user
            $users = User::select('id', 'first_name', 'last_name', 'email')->orderby('id')->get();
        }

        // sum estimated_time project
        $projectEstimatedTime = ProjectTask::where('project_id',$id)
            ->where('parent_task', NULL)
            ->sum('estimated_time');

        // get document project
        $projectDocumentManager = new ProjectDocumentManager;
        $projectDocuments = $projectDocumentManager->getAllDocument($search_document = null,$id);
        // Count open project and close project task type
        $taskProjects = TaskType::select(
            DB::raw('COUNT(IF(project_tasks.status != '.TaskStatus::TASK_STATUS_CLOSE.', 1, null)) AS taskOpen'),
            DB::raw('COUNT(IF(project_tasks.status = '.TaskStatus::TASK_STATUS_CLOSE.', 1, null) ) AS taskClose'),
            'task_types.name')
        ->leftJoin('project_tasks', function ($q) use ($id){
            $q->on('project_tasks.type', 'task_types.id')
            ->where('project_tasks.project_id', $id);
        })
        ->groupBy('task_types.id')
        ->get();

        //get task files
        $taskFiles = TaskAttachment::leftJoin('users','task_attachments.created_by','users.id')
        ->where([
            ['task_attachments.related_id',$id],
            ['task_attachments.type', TaskAttachment::TYPE_PROJECT]
        ])
        ->select([
            'task_attachments.id',
            'task_attachments.related_id',
            'task_attachments.type',
            'task_attachments.file_name',
            'task_attachments.file_path',
            'task_attachments.file_size',
            'task_attachments.description',
            'task_attachments.created_by',
            'task_attachments.updated_by',
            'task_attachments.created_at',
            'task_attachments.updated_at',
            'users.first_name',
            'users.last_name',
        ])
        ->get();
        return view('projectX.view',[
            'project' => $project,
            'subProjects' => $subProjects,
            'groupMembers'=> $groupMembers,
            'projectRoles'=>$projectRoles,
            'users'=>$users,
            'isManager'=>$isManager,
            'projectDocument'=> $projectDocuments,
            'projectEstimatedTime' => $projectEstimatedTime,
            'taskProjects' => $taskProjects,
            'taskFiles' => $taskFiles,
            'userId' => $userId
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $userId = Auth::id();
        $project = Project::select('id', 'name', 'description', 'started_at', 'ended_at', 'public', 'parent_project')
            ->find($id);
        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        //Check project exist
        if($project == null || $isManager == false){
            return redirect()->route('project.index');
        }

        // Get all projects which the user is a member of the project
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId, null , null , false);

        // Get list Project_roles
        $projectRoles = ProjectRole::select('id', 'name')->orderby('id')->get();
        // Get list user
        $projectMembers = User::select(
            DB::raw("GROUP_CONCAT(project_roles.name SEPARATOR ', ') AS role_name"),
            DB::raw("GROUP_CONCAT(project_roles.id SEPARATOR '".GROUP_CONCAT_SEPARATOR."') AS role_id"),
            'users.id AS user_id',
            DB::raw('min(project_members.created_at) as created_at_member'),
            DB::raw("CONCAT_WS(' ',users.first_name, users.last_name) as user_name"))
            ->join('project_members', 'project_members.user_id', 'users.id')
            ->where('project_members.project_id', $id)
            ->join('project_roles', 'project_roles.id', 'project_members.role_id')
            ->groupBy('project_members.user_id')
            ->get();
        $users = User::select('id', 'first_name', 'last_name', 'email')->orderby('id')->get();

        return view('projectX.edit',[
            'projectMembers'=>$projectMembers,
            'users' => $users,
            'projectRoles'=>$projectRoles,
            'project'=>$project,
            'projects'=>$projects
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param ProjectRequest $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(ProjectRequest $request, $id)
    {
        DB::beginTransaction();
        try {
            $userId = Auth::id();
            $project = Project::select('projects.*')->find($id);

            //Check user is manager
            $userManager = new UserManager();
            $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

            //Check project exist
            if($project == null || $isManager == false){
                return redirect()->route('project.index');
            }

            // Update project
            $project->name = $request->project_name;
            $project->description = StringHelper::escapeHtmlForSummernote($request->description);
            $project->started_at = empty($request->started_at)?$request->started_at:\Carbon\Carbon::createFromFormat('d/m/Y', $request->started_at);
            $project->ended_at = empty($request->ended_at)?$request->ended_at:\Carbon\Carbon::createFromFormat('d/m/Y', $request->ended_at);
            $project->public = $request->has('project_public');
            $project->parent_project = $request->parent_project;
            $project->updated_by =  $userId;
            $project->is_slow = (new ProjectManager())->checkIsSlow($project) ? Project::IS_SLOW : Project::NOT_SLOW;
            $project->save();

            //update user project
            $users = isset($request->users)?$request->users:[];
            $checkHasManager = false;
            foreach($users as $user){
                $arrayCheck = explode("-", $user);
                $checkHasManager = (new ProjectManager())->checkHasProjectManager($arrayCheck[0]);
                if($checkHasManager == true) {
                    break;
                }
            }
            if($checkHasManager == false){
                return back()->with([
                    'status_failed' => trans('message.update_project_failed')
                ]);
            }
            $projectMembers = ProjectMember::select(
                'user_id',
                'role_id',
                'created_at'
            )->where('project_id',$id)->get();

            foreach($projectMembers as $value){
                $usersData[] = $value->role_id.'-'.$value->user_id.'-'. Date('d/m/Y',strtotime($value->created_at ));
            }
            $deleteRoles = array_diff($usersData, $users);

            //Delete role
            ProjectMember::where('project_id', $id)
            ->whereIn(DB::raw('CONCAT_WS("-",role_id,user_id,DATE_FORMAT(created_at, "%d/%m/%Y"))'),$deleteRoles)
            ->delete();

            $createRoles = array_diff($users, $usersData);
            $createRoles = array_unique($createRoles);
            foreach($createRoles as $user){
                $array = explode("-", $user);
                $created_at = ($array[2] != "null")? \Carbon\Carbon::createFromFormat('d/m/Y', $array[2]): Carbon::now();
                $projectMember = new ProjectMember();
                $projectMember->project_id =  $project->id;
                $projectMember->user_id = $array[1];
                $projectMember->role_id = $array[0];
                $projectMember->created_at = $created_at;
                $projectMember->created_by = $userId;
                $projectMember->save();
            }

            $data = Project::find($id);
            $destinationPath = str_replace(['{project_id}', '/tasks/{task_id}'], [$project->id, null], TASK_ATTACHMENT_DIR).'/';
            (new AttachmentManager())->saveAttachments($data, TaskAttachment::TYPE_PROJECT,$request->documents, $request->descriptionDocument, $destinationPath, 'description');
            DB::commit();
            return redirect()->route('project.show',['id'=>$id])->with([
                'status_succeed' => trans('message.update_project_succeed')
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("File: ".$e->getFile().'---Line: '.$e->getLine()."---Message: ".$e->getMessage());
            return redirect()->route('project.update',['id'=>$id])->with([
                'status_failed' => trans('message.server_error')
            ]);
        }
    }
    /**
     * Close a projects.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function close($id)
    {
        $userId = Auth::id();
        $project = Project::select('id','deleted_at')
        ->find($id);

        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        if ($project == null || $isManager == false) {
            return redirect()->route('project.index');
        }

        $project->ended_at = Carbon::now();
        $project->deleted_at = Carbon::now();
        $project->save();

        return redirect()->route('project.show', ['id' => $id])->with([
            'status_succeed' => trans('message.close_project_succeed')
        ]);
    }
    /**
     * open a projects.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function open($id)
    {
        $userId = Auth::id();
        $project = Project::select('id','deleted_at')
        ->find($id);

        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        if ($project == null || $isManager == false) {
            return redirect()->route('project.index');
        }

        $project->deleted_at = null;
        $project->save();

        return redirect()->route('project.show', ['id' => $id])->with([
            'status_succeed' => trans('message.open_project_succeed')
        ]);
    }
    /**
     * Show all members of the projects
     *
     * @param $id
     */
    public function members(Request $request,$id) {
        $userId = Auth::id();
        $project = Project::select('projects.id','projects.name')->checkUserPermission($userId)->find($id);

        if(empty($project)){
            return redirect()->route('project.index');
        }

        // Get list members
        $projectMemberManager = new ProjectMemberManager();
        $members = $projectMemberManager->getList($id,$request->all());

        //Check user is manager
        $userManager = new UserManager();
        $isManager = $userManager->hasProjectRole($userId,$id,ProjectRole::ProjectManager);

        $users = [];
        if($isManager){
            // Get list user
            $users = User::select('id', 'first_name', 'last_name', 'email')->orderby('id')->get();
        }

        // Get list Project_roles
        $projectRoles = ProjectRole::select('id', 'name')->orderby('id')->get();

        $selectedUser=[];
        if(is_array($request->member_id)){
            $selectedUser = User::whereIn('id',$request->member_id)->get();
        }

        return view('projectX.members', [
            'project' => $project,
            'projectRoles' => $projectRoles,
            'members'=> $members,
            'users' => $users,
            'isManager' => $isManager,
            'selectedUser' => $selectedUser
        ]);
    }

    /**
     * Show calender of the projects
     *
     * @param Request $request
     */
    public function calendar(Request $request){
        $userId = Auth::id();

        // Get task list
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['project', 'taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','time']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        $time = isset($request->time)?$request->time:date('m/Y');
        //Xử lý lấy ra time
        $arrTime = explode('/',$time);

        $month = $arrTime[0];
        $year = $arrTime[1];

        $timeFormatYmd = $year . '-' . $month . '-01';
        $nextTime = date('m/Y',mktime(0, 0, 0, $month+1,1, $year));
        $prevTime = date('m/Y',mktime(0, 0, 0, $month-1,1, $year));

        $currentTime = date('m/Y');
        $resourceManager = new ResourceManager();
        $resources = $resourceManager->getMeetingRoom();

        return view('projects.calendar',[
            'time' => $time,
            'nextTime' => $nextTime,
            'prevTime' => $prevTime,
            'currentTime' => $currentTime,
            'timeFormatYmd' => $timeFormatYmd,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'projects' => $projects,
            'filterHtml' => $filterHtml,
            'resources' => $resources,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
        ]);
    }

    /**
     * Show calender of a project
     *
     * @param Request $request
     * @param $id
     */
    public function calendarByProject(Request $request,$id){
        $userId = auth()->id();

        // Check user has permission with the project
        $project = Project::select('projects.*')
            ->where('id', $id)
            ->checkUserPermission($userId)
            ->first();
        if (!isset($project)) {
            return redirect()->route('project.index');
        }

        // Get task list
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['project', 'taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','time']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);
        $time = isset($request->time)?$request->time:date('m/Y');

        //Xử lý lấy ra time
        $arrTime = explode('/',$time);

        $month = $arrTime[0];
        $year = $arrTime[1];

        $timeFormatYmd = $year . '-' . $month . '-01';

        $nextTime = date('m/Y',mktime(0, 0, 0, $month+1,1, $year));
        $prevTime = date('m/Y',mktime(0, 0, 0, $month-1,1, $year));

        $currentTime = date('m/Y');
        $resourceManager = new ResourceManager();
        $resources = $resourceManager->getMeetingRoom();
        return view('projectX.calendar', [
            'project'=>$project,
            'time' => $time,
            'nextTime' => $nextTime,
            'prevTime' => $prevTime,
            'currentTime' => $currentTime,
            'timeFormatYmd' => $timeFormatYmd,
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'projects' => $projects,
            'filterHtml' => $filterHtml,
            'resources' => $resources,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
        ]);
    }

    /**
     * Get data calendar
     *
     * @param Request $request
     */
    public function getDataCalendar(Request $request){
        $time = isset($request->time)?$request->time:date('m/Y');
        //Xử lý lấy ra time
        $arrTime = explode('/',$time);
        $month = $arrTime[0];
        $year = $arrTime[1];
        $from = '01/'.$month.'/'.$year;
        $to = cal_days_in_month(CAL_GREGORIAN, $month, $year).'/'.$month.'/'.$year;
        $params = (new RequestHelper())->getParamsFromRequest($request);
        $params['from'] = $from;
        $params['to'] = $to;
        if(isset($request->project)){
            $params['project_id'] = $request->project;
        }
        $taskManager = new \App\Logics\TaskManager;
        $result = $taskManager->getTaskCalendar($params);

        $events = (new EventManager())->getListEventCalendar($params,true);
        $result = array_merge($result,$events);
        
        return response()->json($result);
    }

    /**
     * Show gantt chart
     *
     * @param $id
     */
    public function gantt(Request $request) {
        $userId = Auth::id();

        if(count($request->all()) == 0){
            $request->merge([
                'status' => ['0'],
            ]);
        }

        if (!isset($request->from)) {
            $request->merge([
                'from' => Carbon::now()->startOfWeek()->subWeeks(3)->format('d/m/Y'),
            ]);
        }
        if (!isset($request->to)) {
            $request->merge([
                'to' => Carbon::now()->addMonths(TaskManager::ADD_MONTHS)->endOfWeek()->subDays(1)->format('d/m/Y')
            ]);
        }

        //get param by request
        $params = (new RequestHelper())->getParamsFromRequest($request);

        // Get html filter
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['project', 'taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','from','to']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        [$tasks] = $taskManager->getTaskListInGantt($params, self::PAGE_SIZE_GANTT, ['project_tasks.project_id' => 'DESC','project_tasks.id' => 'DESC',]);

        return view('projects.gantt',[
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'projects' => $projects,
            'filterHtml' => $filterHtml,
            'tasks' => $tasks,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
        ]);
    }

    /**
     * Show gantt chart by Project
     *
     * @param $id
     */
    public function ganttByProject(Request $request,$id) {
        $userId = Auth::id();

        if(count($request->all()) == 0){
            $request->merge([
                'status' => ['0'],
            ]);
        }

        if (!isset($request->from)) {
            $request->merge([
                'from' => Carbon::now()->startOfWeek()->subWeeks(3)->format('d/m/Y'),
            ]);
        }
        if (!isset($request->to)) {
            $request->merge([
                'to' => Carbon::now()->addMonths(TaskManager::ADD_MONTHS)->endOfWeek()->subDays(1)->format('d/m/Y')
            ]);
        }
        //get param by request
        $params = (new RequestHelper())->getParamsFromRequest($request);
        $params['project'] = $request->id;

        // Get html filter
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['project', 'taskId','parentTask','taskTitle','type','status','priority','member','bug_range','bug_tag','from','to']);

        //Get list task_status
        $taskStatuses = TaskStatus::select(['id','name'])->orderby('sort_order')->get();

        //Get list task_type
        $taskTypes = TaskType::select(['id','name'])->orderby('sort_order')->get();

        // Get list task_priorities
        $taskPriorities = TaskPriority::select(['id','name'])->orderby('sort_order')->get();

        // Get list bug_tags
        $bugTags = BugTag::select(['id','name'])->orderby('id', 'DESC')->get();

        // Get list bug_ranges
        $bugRanges = BugRange::select(['id','name'])->orderby('id')->get();

        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        // Get the project
        $project = Project::find($request->id, ['id', 'name']);
        [$tasks] = $taskManager->getTaskListInGantt($params, self::PAGE_SIZE_GANTT, ['project_tasks.project_id' => 'DESC','project_tasks.id' => 'DESC',]);

        return view('projectX.gantt',[
            'taskStatuses' => $taskStatuses,
            'taskTypes' => $taskTypes,
            'taskPriorities' => $taskPriorities,
            'projects' => $projects,
            'filterHtml' => $filterHtml,
            'project' => $project,
            'tasks' => $tasks,
            'bugTags' => $bugTags,
            'bugRanges' => $bugRanges,
        ]);
    }

    /**
     * Get data gantt
     *
     */
    public function getDataGantt(Request $request){
        // Get list task
        $taskManager = new TaskManager();
        $params = (new RequestHelper())->getParamsFromRequest($request);
        if (empty($params['from'])) {
            $params['from'] = Carbon::now()->startOfWeek()->subWeeks(3)->format('d/m/Y');
        }
        if (empty($params['to'])) {
            $params['to'] = Carbon::now()->addMonths(TaskManager::ADD_MONTHS)->endOfWeek()->subDays(1)->format('d/m/Y');
        }
        [$tasks] = $taskManager->getTaskListInGantt($params, self::PAGE_SIZE_GANTT, ['project_tasks.project_id' => 'DESC','project_tasks.id' => 'DESC',]);
        $result = $taskManager->convertDataTaskToDataGantt($tasks);
        return response()->json($result);
    }

    /**
     * Pin to taskbar
     *
     */
    public function pin(Request $request){
        $userId = Auth::id();
        $projectId = $request->id;
        $pin = $request->pin;

        $project = Project::select('projects.*')
            ->where('id', $projectId)
            ->checkUserPermission($userId)
            ->first();
        if (!isset($project)) {
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => $project->name,
                    'text' => trans('message.project_not_exist')
                ],
            ];
        }

        if($pin == 'false'){
            ProjectPin::where('user_id',$userId)->where('project_id',$projectId)->delete();
            return [
                'status' => Response::HTTP_OK,
                'pin' => $request->pin,
                'projectId' => $request->id
            ];
        }

        // Checked project has been pinned
        $checkProjectPin = (new ProjectManager())->checkProjectPin($projectId,$userId);
        if ($checkProjectPin){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => $project->name,
                    'text' => trans('message.project_pinned')
                ],
            ];
        }

        $projectPin = new ProjectPin();
        $projectPin->user_id = $userId;
        $projectPin->project_id = $projectId;
        $projectPin->created_by = $userId;
        $projectPin->created_at = \Carbon\Carbon::now();
        $projectPin->sort_order_at = \Carbon\Carbon::now();

        $projectPin->save();

        //Get html
        $html = view('partials.project-pin-header',[
            'currentRouteName' => URL::current(),
            'projectRoutes' => [
                'project.index',
                'project.create',
                'projects.tasks',
                'projects.gantt',
                'projects.calendar',
                'projects.activities',
                'projects.effort',
            ],
            'projectPin' => $project
        ])->render();

        return [
            'status' => Response::HTTP_OK,
            'pin' => $request->pin,
            'projectId' => $request->id,
            'html' => $html,
        ];
    }

    /**
     * Sort project pin
     *
     */
    public function sortProjectPin(Request $request){
        $userId = Auth::id();
        $prev = ProjectPin::where('user_id',$userId)->where('project_id',$request->itemPrev)->first();
        $next = ProjectPin::where('user_id',$userId)->where('project_id',$request->itemNext)->first();

        if (empty($prev) && empty($next)){
            return [
                'status' => Response::HTTP_NOT_FOUND,
                'msg' => [
                    'title' => trans('language.failure'),
                    'text' => ''
                ],
            ];
        }

        $maxTime = isset($prev)?strtotime($prev->sort_order_at):time();
        $minTime = isset($next)?strtotime($next->sort_order_at):0;

        $randTime = rand($minTime, $maxTime);
        ProjectPin::where('user_id',$userId)->where('project_id',$request->itemCurrent)->update(['sort_order_at' => date('Y-m-d H:i:s', $randTime)]);

        return [
            'status' => Response::HTTP_OK,
            'msg' => [
                'title' => trans('language.success'),
                'text' => ''
            ],
        ];
    }

    public function effort(Request $request){
        $userId = Auth::id();
        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        //get filter html
        $taskManager =  new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['project','member','start_at','end_at']);
        return view('projects.effort',[
            'projects' => $projects,
            'filterHtml' => $filterHtml,
        ]);
    }

    public function effortByProject($id, Request $request){
        $userId = Auth::id();
        // Get all projects of the user
        $projectManager = new ProjectManager();
        $projects = $projectManager->getProjectList($userId);

        // Get the project
        $project = Project::find($id, ['id', 'name']);

        //get filter
        $taskManager =  new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['member','start_at','end_at']);

        return view('projectX.effort', [
            'projects' => $projects,
            'project' => $project,
            'filterHtml' => $filterHtml,
        ]);
    }

    /**
     * Get data effort
     *
     */
    public function getDataEffort(Request $request){
        $userId = Auth::id();
        $type = isset($request->type)?$request->type:'date';
        $fomatDate = $type=='month'?'Y-m':'Y-m-d';

        if (empty($request->start_at)&&empty($request->end_at)){
            $end_at = date('Y-m-d');
            $start_at = $type=='month'?date('Y-m-d',strtotime('-6 months',strtotime(date('Y-m-').'01'))):date('Y-m-d',strtotime('-30 days',strtotime($end_at)));
        }
        else if (empty($request->start_at)){
            $end_at = $type=='month'?'01/'.$request->end_at:$request->end_at;
            $end_at = \Carbon\Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
            $start_at = $type=='month'?date('Y-m-d',strtotime('-6 months',strtotime($end_at))):date('Y-m-d',strtotime('-30 days',strtotime($end_at)));
        }
        else if (empty($request->end_at)){
            $start_at = $type=='month'?'01/'.$request->start_at:$request->start_at;
            $start_at = \Carbon\Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');
            $end_at = $type=='month'?date('Y-m-d',strtotime('+6 months ',strtotime($start_at))):date('Y-m-d',strtotime('+30 days',strtotime($start_at)));

        }else{
            $start_at = $type=='month'?'01/'.$request->start_at:$request->start_at;
            $start_at = \Carbon\Carbon::createFromFormat('d/m/Y', $start_at)->format('Y-m-d');

            $end_at = $type=='month'?'01/'.$request->end_at:$request->end_at;
            $end_at = \Carbon\Carbon::createFromFormat('d/m/Y', $end_at)->format('Y-m-d');
        }
        if ($type == 'month'){
            $end_at = getdate(strtotime($end_at));
            $numberDays = cal_days_in_month(CAL_GREGORIAN, $end_at['mon'], $end_at['year']);
            $end_at = $end_at['year'].'-'.$end_at['mon'].'-'.$numberDays;
        }

        $end_at = $end_at .' 23:59:59';

        // Get list task_action
        $taskActions = TaskAction::select('task_actions.*')
                        -> join('project_tasks','project_tasks.id','task_actions.task_id')
                        -> whereBetween('action_at',[$start_at,$end_at])
                        -> orderBy('task_id','asc')
                        -> orderBy('action_at','asc');

        if (isset($request->project)){
            $taskActions = $taskActions->where('project_tasks.project_id',$request->project);
        }

        if (isset($request->member)){
            $taskActions = $taskActions->whereIn('task_actions.user_id',$request->member);
        }
        $taskActions = $taskActions->get();
        $data = [];
        $i = 0;
        $workingTimeManager = new WorkingTimeManager();

        while ($i < count($taskActions)){
            if ($taskActions[$i]->type == TaskAction::STOP){
                $arrTimes = $workingTimeManager->getWorkTime($start_at.' '.SiteSetting::morningStart(),$taskActions[$i]->action_at);
                $i += 1;
            }else{
                if (isset($taskActions[$i+1]) && $taskActions[$i+1]->task_id == $taskActions[$i]->task_id){
                    $arrTimes = $workingTimeManager->getWorkTime($taskActions[$i]->action_at,$taskActions[$i+1]->action_at);
                    $i += 2;
                }else{
                    $arrTimes = $workingTimeManager->getWorkTime($taskActions[$i]->action_at,$end_at);
                    $i += 1;
                }
            }
            foreach ($arrTimes as $date=>$time){
                $date = date($fomatDate,strtotime($date));
                if (isset($data[$date])){
                    $data[$date] += $time;
                }else{
                    $data[$date] = $time;
                }
            }
        }

        // Convert data to json effort
        $result = [];
        $step = $type == 'month'?'month':'day';
        $time = strtotime($start_at);
        while ($time <=  strtotime($end_at)){
            $date = date($fomatDate,$time);
            $item = [
                'date' => $date,
                'effort'=> isset($data[$date])?(string)$data[$date]:0,
                'url'=> $request->isProjectX?route('project.tasks',['id'=>$request->project]):route('projects.tasks')
            ];
            $result[] = $item;
            $time = strtotime("+1 " . $step, $time);
        }
        return response()->json($result);
    }

    /**
     * export PDF file
     *
     * @param  \Illuminate\Http\Request  $request
     * @return file PDF
     */
    public function exportPdf(Request $request) {
    	$pdf = PDF::loadView('projects.pdf-effort', ['data' => $request->chart]);
        $pdf->setPaper('A4', 'landscape');

    	return $pdf -> download(trans('language.effort_chart').'.pdf');
    }

     /**
     * Get list activities
     *
     * @param Request $request
     *
     * @return View
     */
    public function activities(Request $request){
        $taskManager = new TaskManager();
        $filterHtml = $taskManager->getFilterHtml($request,['start_date','end_date']);
        $taskLogManager = new TaskLogManager();
        [$taskLogs,$tasks] = $taskLogManager->getDataActivity($request, null);
        $displayId = SiteSetting::displayTaskProjectId();
        return view('projects.activities', [
            'taskLogs'=>$taskLogs,
            'tasks'=>$tasks,
            'filterHtml' => $filterHtml,
            'displayId' => $displayId
        ]);
    }

    /**
     * Get list activities by project_id
     *
     * @param Request $request
     * @param int $id;
     *
     * @return View
     */
    public function activitiesByProject(Request $request, $id){
        $userId = Auth::id();
        $project = Project::where('projects.id', $id)->checkUserPermission($userId)
                ->select('projects.*')->first();
        $displayId = SiteSetting::displayTaskProjectId();
        if ($project){
            $taskLogManager = new TaskLogManager();
            [$taskLogs,$tasks] = $taskLogManager->getDataActivity($request, $id);
            return view('projectX.activities', [
                'taskLogs'=>$taskLogs,
                'tasks'=>$tasks,
                'project'=>$project,
                'displayId' => $displayId]);
        }
    }
}
